{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue", "mtime": 1751513794206}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["KubernetesInfo.vue"], "names": [], "mappings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file": "KubernetesInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full kubernetes-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.k8s') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs default-active-key=\"k8s_api_server\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"k8s_api_server\" tab=\"API Servers\">\r\n        <a-table\r\n          :columns=\"apiServerColumns\"\r\n          :data-source=\"apiServerData\"\r\n          :rowKey=\"(record) => record.address\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_api_server'\"\r\n          :loading=\"loadingApiServers\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_ingress\" tab=\"Ingresses\">\r\n        <a-table\r\n          :columns=\"ingressColumns\"\r\n          :data-source=\"ingressData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_ingress'\"\r\n          :loading=\"loadingIngresses\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_gateway\" tab=\"Gateways\">\r\n        <a-table\r\n          :columns=\"gatewayColumns\"\r\n          :data-source=\"gatewayData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_gateway'\"\r\n          :loading=\"loadingGateways\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_virtual_service\" tab=\"Virtual Services\">\r\n        <a-table\r\n          :columns=\"virtualServiceColumns\"\r\n          :data-source=\"virtualServiceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_virtual_service'\"\r\n          :loading=\"loadingVirtualServices\"\r\n        >\r\n\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_service\" tab=\"Services\">\r\n        <a-table\r\n          :columns=\"serviceColumns\"\r\n          :data-source=\"serviceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_service'\"\r\n          :loading=\"loadingServices\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_network_policy\" tab=\"Network Policies\">\r\n        <a-table\r\n          :columns=\"networkPolicyColumns\"\r\n          :data-source=\"networkPolicyData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_network_policy'\"\r\n          :loading=\"loadingNetworkPolicies\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_pod\" tab=\"Pods\">\r\n        <a-table\r\n          :columns=\"podColumns\"\r\n          :data-source=\"podData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_pod'\"\r\n          :loading=\"loadingPods\"\r\n          :scroll=\"{ x: 1500 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_node\" tab=\"Nodes\">\r\n        <a-table\r\n          :columns=\"nodeColumns\"\r\n          :data-source=\"nodeData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_node'\"\r\n          :loading=\"loadingNodes\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_secret\" tab=\"Secrets\">\r\n        <a-table\r\n          :columns=\"secretColumns\"\r\n          :data-source=\"secretData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_secret'\"\r\n          :loading=\"loadingSecrets\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_config_map\" tab=\"ConfigMaps\">\r\n        <a-table\r\n          :columns=\"configMapColumns\"\r\n          :data-source=\"configMapData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_config_map'\"\r\n          :loading=\"loadingConfigMaps\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role\" tab=\"Roles\">\r\n        <a-table\r\n          :columns=\"roleColumns\"\r\n          :data-source=\"roleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role'\"\r\n          :loading=\"loadingRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role_binding\" tab=\"Role Bindings\">\r\n        <a-table\r\n          :columns=\"roleBindingColumns\"\r\n          :data-source=\"roleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role_binding'\"\r\n          :loading=\"loadingRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role\" tab=\"Cluster Roles\">\r\n        <a-table\r\n          :columns=\"clusterRoleColumns\"\r\n          :data-source=\"clusterRoleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role'\"\r\n          :loading=\"loadingClusterRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role_binding\" tab=\"Cluster Role Bindings\">\r\n        <a-table\r\n          :columns=\"clusterRoleBindingColumns\"\r\n          :data-source=\"clusterRoleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role_binding'\"\r\n          :loading=\"loadingClusterRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_serviceaccount_permissions\" tab=\"ServiceAccount Perms\">\r\n        <a-table\r\n          :columns=\"serviceAccountPermissionsColumns\"\r\n          :data-source=\"serviceAccountPermissionsData\"\r\n          :rowKey=\"(record) => record.id\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_serviceaccount_permissions'\"\r\n          :loading=\"loadingServiceAccountPermissions\"\r\n          :scroll=\"{ x: 1200 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    JsonDetailModal\r\n  },\r\n  name: 'KubernetesInfo',\r\n  data() {\r\n    return {\r\n      activeTab: 'k8s_api_server',\r\n      apiServerData: [],\r\n      ingressData: [],\r\n      gatewayData: [],\r\n      virtualServiceData: [],\r\n      serviceData: [],\r\n      networkPolicyData: [],\r\n      podData: [],\r\n      nodeData: [],\r\n      secretData: [],\r\n      configMapData: [],\r\n      roleData: [],\r\n      roleBindingData: [],\r\n      clusterRoleData: [],\r\n      clusterRoleBindingData: [],\r\n      serviceAccountPermissionsData: [],\r\n      loadingApiServers: false,\r\n      loadingIngresses: false,\r\n      loadingGateways: false,\r\n      loadingVirtualServices: false,\r\n      loadingServices: false,\r\n      loadingNetworkPolicies: false,\r\n      loadingPods: false,\r\n      loadingNodes: false,\r\n      loadingSecrets: false,\r\n      loadingConfigMaps: false,\r\n      loadingRole: false,\r\n      loadingRoleBinding: false,\r\n      loadingClusterRole: false,\r\n      loadingClusterRoleBinding: false,\r\n      loadingServiceAccountPermissions: false,\r\n      apiServerColumns: [\r\n        { title: 'Node ID', dataIndex: 'node_id', key: 'node_id' },\r\n        { title: 'Address', dataIndex: 'address', key: 'address' },\r\n      ],\r\n      ingressColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'ingress_status',\r\n          key: 'ingress_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Ingress Status')\r\n        },\r\n      ],\r\n      gatewayColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'gateway_status',\r\n          key: 'gateway_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Status')\r\n        },\r\n      ],\r\n      virtualServiceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'virtual_service_status',\r\n          key: 'virtual_service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Status')\r\n        },\r\n      ],\r\n      serviceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'service_status',\r\n          key: 'service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Service Status')\r\n        },\r\n      ],\r\n      networkPolicyColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n      ],\r\n      podColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Pod Status')\r\n        },\r\n      ],\r\n      nodeColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Node Status')\r\n        },\r\n      ],\r\n      secretColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Data')\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'secret_type',\r\n          key: 'secret_type',\r\n          width: 120\r\n        },\r\n      ],\r\n      configMapColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'ConfigMap Data')\r\n        },\r\n      ],\r\n      roleColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      roleBindingColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      clusterRoleColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Aggregation Rule',\r\n          dataIndex: 'aggregationRule',\r\n          key: 'aggregationRule',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Aggregation Rule')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      clusterRoleBindingColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      serviceAccountPermissionsColumns: [\r\n        {\r\n          title: 'Pod Name',\r\n          dataIndex: 'pod_name',\r\n          key: 'pod_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Service Account',\r\n          dataIndex: 'service_account',\r\n          key: 'service_account',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Permissions',\r\n          dataIndex: 'permissions',\r\n          key: 'permissions',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            try {\r\n              const permissions = Array.isArray(text) ? text : (text ? JSON.parse(text) : []);\r\n\r\n              let displayText = 'No permissions';\r\n              if (permissions && permissions.length > 0) {\r\n                const summary = permissions.map(p => `${p.type}: ${p.name}`).join(', ');\r\n                displayText = summary.length > 50 ? summary.slice(0, 50) + '...' : summary;\r\n              }\r\n\r\n              return (\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n                  <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">\r\n                    {displayText}\r\n                  </span>\r\n                  <a-button\r\n                    type=\"link\"\r\n                    style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n                    onClick={() => this.showDetailModal('ServiceAccount Permissions', permissions)}\r\n                  >\r\n                    View\r\n                  </a-button>\r\n                </div>\r\n              );\r\n            } catch (error) {\r\n              console.error('Error parsing permissions:', error);\r\n              return 'Error parsing data';\r\n            }\r\n          }\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      initialLoad: true,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      this.initialLoad = true;\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      this[`loading${this.capitalizeFirstLetter(resourceType)}`] = true;\r\n      try {\r\n        let response;\r\n        response = await axios.get(`/api/k8s/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = [];\r\n      } finally {\r\n        this[`loading${this.capitalizeFirstLetter(resourceType)}`] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutK8s = camelCase.replace('k8s_', '');\r\n\r\n      // 特殊处理 serviceaccount_permissions\r\n      if (withoutK8s === 'serviceaccount_permissions') {\r\n        return 'serviceAccountPermissionsData';\r\n      }\r\n\r\n      const camelized = withoutK8s.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\r\n      return camelized + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.apiServerData = [];\r\n      this.ingressData = [];\r\n      this.gatewayData = [];\r\n      this.virtualServiceData = [];\r\n      this.serviceData = [];\r\n      this.networkPolicyData = [];\r\n      this.podData = [];\r\n      this.nodeData = [];\r\n      this.secretData = [];\r\n      this.configMapData = [];\r\n      this.roleData = [];\r\n      this.roleBindingData = [];\r\n      this.clusterRoleData = [];\r\n      this.clusterRoleBindingData = [];\r\n      this.serviceAccountPermissionsData = [];\r\n    },\r\n    renderComplexData(text, title) {\r\n      if (!text || text === 'None') return '-';\r\n\r\n      // 简化显示文本生成逻辑\r\n      const displayText = Array.isArray(text) ? `Array(${text.length})` :\r\n                         typeof text === 'object' && text !== null ?\r\n                           Object.entries(text).slice(0, 2).map(([k, v]) =>\r\n                             `${k}: ${typeof v === 'object' ? JSON.stringify(v).substring(0, 15) : v}`\r\n                           ).join(', ') + (Object.keys(text).length > 2 ? '...' : '') :\r\n                           String(text).length > 50 ? String(text).slice(0, 50) + '...' : String(text);\r\n\r\n      return (\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; padding-right: 8px;\">\r\n          <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">{displayText}</span>\r\n          <a-button\r\n            type=\"link\"\r\n            style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n            onClick={() => this.showDetailModal(title, text)}\r\n          >\r\n            View\r\n          </a-button>\r\n        </div>\r\n      );\r\n    },\r\n\r\n    showDetailModal(title, data) {\r\n      // 使用JsonDetailModal组件的showDetailModal方法\r\n      this.$refs.jsonDetailModal.showDetailModal(title, data);\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.kubernetes-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}