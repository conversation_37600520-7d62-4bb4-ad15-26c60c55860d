{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue?vue&type=style&index=0&id=4f6a9ce0&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue", "mtime": 1751513794206}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoua3ViZXJuZXRlcy1jYXJkIHsNCiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNik7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCn0NCg=="}, {"version": 3, "sources": ["KubernetesInfo.vue"], "names": [], "mappings": ";AAqzBA;AACA;AACA;AACA", "file": "KubernetesInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full kubernetes-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.k8s') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs default-active-key=\"k8s_api_server\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"k8s_api_server\" tab=\"API Servers\">\r\n        <a-table\r\n          :columns=\"apiServerColumns\"\r\n          :data-source=\"apiServerData\"\r\n          :rowKey=\"(record) => record.address\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_api_server'\"\r\n          :loading=\"loadingApiServers\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_ingress\" tab=\"Ingresses\">\r\n        <a-table\r\n          :columns=\"ingressColumns\"\r\n          :data-source=\"ingressData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_ingress'\"\r\n          :loading=\"loadingIngresses\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_gateway\" tab=\"Gateways\">\r\n        <a-table\r\n          :columns=\"gatewayColumns\"\r\n          :data-source=\"gatewayData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_gateway'\"\r\n          :loading=\"loadingGateways\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_virtual_service\" tab=\"Virtual Services\">\r\n        <a-table\r\n          :columns=\"virtualServiceColumns\"\r\n          :data-source=\"virtualServiceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_virtual_service'\"\r\n          :loading=\"loadingVirtualServices\"\r\n        >\r\n\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_service\" tab=\"Services\">\r\n        <a-table\r\n          :columns=\"serviceColumns\"\r\n          :data-source=\"serviceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_service'\"\r\n          :loading=\"loadingServices\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_network_policy\" tab=\"Network Policies\">\r\n        <a-table\r\n          :columns=\"networkPolicyColumns\"\r\n          :data-source=\"networkPolicyData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_network_policy'\"\r\n          :loading=\"loadingNetworkPolicies\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_pod\" tab=\"Pods\">\r\n        <a-table\r\n          :columns=\"podColumns\"\r\n          :data-source=\"podData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_pod'\"\r\n          :loading=\"loadingPods\"\r\n          :scroll=\"{ x: 1500 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_node\" tab=\"Nodes\">\r\n        <a-table\r\n          :columns=\"nodeColumns\"\r\n          :data-source=\"nodeData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_node'\"\r\n          :loading=\"loadingNodes\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_secret\" tab=\"Secrets\">\r\n        <a-table\r\n          :columns=\"secretColumns\"\r\n          :data-source=\"secretData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_secret'\"\r\n          :loading=\"loadingSecrets\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_config_map\" tab=\"ConfigMaps\">\r\n        <a-table\r\n          :columns=\"configMapColumns\"\r\n          :data-source=\"configMapData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_config_map'\"\r\n          :loading=\"loadingConfigMaps\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role\" tab=\"Roles\">\r\n        <a-table\r\n          :columns=\"roleColumns\"\r\n          :data-source=\"roleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role'\"\r\n          :loading=\"loadingRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role_binding\" tab=\"Role Bindings\">\r\n        <a-table\r\n          :columns=\"roleBindingColumns\"\r\n          :data-source=\"roleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role_binding'\"\r\n          :loading=\"loadingRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role\" tab=\"Cluster Roles\">\r\n        <a-table\r\n          :columns=\"clusterRoleColumns\"\r\n          :data-source=\"clusterRoleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role'\"\r\n          :loading=\"loadingClusterRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role_binding\" tab=\"Cluster Role Bindings\">\r\n        <a-table\r\n          :columns=\"clusterRoleBindingColumns\"\r\n          :data-source=\"clusterRoleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role_binding'\"\r\n          :loading=\"loadingClusterRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_serviceaccount_permissions\" tab=\"ServiceAccount Perms\">\r\n        <a-table\r\n          :columns=\"serviceAccountPermissionsColumns\"\r\n          :data-source=\"serviceAccountPermissionsData\"\r\n          :rowKey=\"(record) => record.id\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_serviceaccount_permissions'\"\r\n          :loading=\"loadingServiceAccountPermissions\"\r\n          :scroll=\"{ x: 1200 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    JsonDetailModal\r\n  },\r\n  name: 'KubernetesInfo',\r\n  data() {\r\n    return {\r\n      activeTab: 'k8s_api_server',\r\n      apiServerData: [],\r\n      ingressData: [],\r\n      gatewayData: [],\r\n      virtualServiceData: [],\r\n      serviceData: [],\r\n      networkPolicyData: [],\r\n      podData: [],\r\n      nodeData: [],\r\n      secretData: [],\r\n      configMapData: [],\r\n      roleData: [],\r\n      roleBindingData: [],\r\n      clusterRoleData: [],\r\n      clusterRoleBindingData: [],\r\n      serviceAccountPermissionsData: [],\r\n      loadingApiServers: false,\r\n      loadingIngresses: false,\r\n      loadingGateways: false,\r\n      loadingVirtualServices: false,\r\n      loadingServices: false,\r\n      loadingNetworkPolicies: false,\r\n      loadingPods: false,\r\n      loadingNodes: false,\r\n      loadingSecrets: false,\r\n      loadingConfigMaps: false,\r\n      loadingRole: false,\r\n      loadingRoleBinding: false,\r\n      loadingClusterRole: false,\r\n      loadingClusterRoleBinding: false,\r\n      loadingServiceAccountPermissions: false,\r\n      apiServerColumns: [\r\n        { title: 'Node ID', dataIndex: 'node_id', key: 'node_id' },\r\n        { title: 'Address', dataIndex: 'address', key: 'address' },\r\n      ],\r\n      ingressColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'ingress_status',\r\n          key: 'ingress_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Ingress Status')\r\n        },\r\n      ],\r\n      gatewayColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'gateway_status',\r\n          key: 'gateway_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Status')\r\n        },\r\n      ],\r\n      virtualServiceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'virtual_service_status',\r\n          key: 'virtual_service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Status')\r\n        },\r\n      ],\r\n      serviceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'service_status',\r\n          key: 'service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Service Status')\r\n        },\r\n      ],\r\n      networkPolicyColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n      ],\r\n      podColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Pod Status')\r\n        },\r\n      ],\r\n      nodeColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Node Status')\r\n        },\r\n      ],\r\n      secretColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Data')\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'secret_type',\r\n          key: 'secret_type',\r\n          width: 120\r\n        },\r\n      ],\r\n      configMapColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'ConfigMap Data')\r\n        },\r\n      ],\r\n      roleColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      roleBindingColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      clusterRoleColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Aggregation Rule',\r\n          dataIndex: 'aggregationRule',\r\n          key: 'aggregationRule',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Aggregation Rule')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      clusterRoleBindingColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      serviceAccountPermissionsColumns: [\r\n        {\r\n          title: 'Pod Name',\r\n          dataIndex: 'pod_name',\r\n          key: 'pod_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Service Account',\r\n          dataIndex: 'service_account',\r\n          key: 'service_account',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Permissions',\r\n          dataIndex: 'permissions',\r\n          key: 'permissions',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            try {\r\n              const permissions = Array.isArray(text) ? text : (text ? JSON.parse(text) : []);\r\n\r\n              let displayText = 'No permissions';\r\n              if (permissions && permissions.length > 0) {\r\n                const summary = permissions.map(p => `${p.type}: ${p.name}`).join(', ');\r\n                displayText = summary.length > 50 ? summary.slice(0, 50) + '...' : summary;\r\n              }\r\n\r\n              return (\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n                  <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">\r\n                    {displayText}\r\n                  </span>\r\n                  <a-button\r\n                    type=\"link\"\r\n                    style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n                    onClick={() => this.showDetailModal('ServiceAccount Permissions', permissions)}\r\n                  >\r\n                    View\r\n                  </a-button>\r\n                </div>\r\n              );\r\n            } catch (error) {\r\n              console.error('Error parsing permissions:', error);\r\n              return 'Error parsing data';\r\n            }\r\n          }\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      initialLoad: true,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      this.initialLoad = true;\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      this[`loading${this.capitalizeFirstLetter(resourceType)}`] = true;\r\n      try {\r\n        let response;\r\n        response = await axios.get(`/api/k8s/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = [];\r\n      } finally {\r\n        this[`loading${this.capitalizeFirstLetter(resourceType)}`] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutK8s = camelCase.replace('k8s_', '');\r\n\r\n      // 特殊处理 serviceaccount_permissions\r\n      if (withoutK8s === 'serviceaccount_permissions') {\r\n        return 'serviceAccountPermissionsData';\r\n      }\r\n\r\n      const camelized = withoutK8s.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\r\n      return camelized + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.apiServerData = [];\r\n      this.ingressData = [];\r\n      this.gatewayData = [];\r\n      this.virtualServiceData = [];\r\n      this.serviceData = [];\r\n      this.networkPolicyData = [];\r\n      this.podData = [];\r\n      this.nodeData = [];\r\n      this.secretData = [];\r\n      this.configMapData = [];\r\n      this.roleData = [];\r\n      this.roleBindingData = [];\r\n      this.clusterRoleData = [];\r\n      this.clusterRoleBindingData = [];\r\n      this.serviceAccountPermissionsData = [];\r\n    },\r\n    renderComplexData(text, title) {\r\n      if (!text || text === 'None') return '-';\r\n\r\n      // 简化显示文本生成逻辑\r\n      const displayText = Array.isArray(text) ? `Array(${text.length})` :\r\n                         typeof text === 'object' && text !== null ?\r\n                           Object.entries(text).slice(0, 2).map(([k, v]) =>\r\n                             `${k}: ${typeof v === 'object' ? JSON.stringify(v).substring(0, 15) : v}`\r\n                           ).join(', ') + (Object.keys(text).length > 2 ? '...' : '') :\r\n                           String(text).length > 50 ? String(text).slice(0, 50) + '...' : String(text);\r\n\r\n      return (\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; padding-right: 8px;\">\r\n          <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">{displayText}</span>\r\n          <a-button\r\n            type=\"link\"\r\n            style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n            onClick={() => this.showDetailModal(title, text)}\r\n          >\r\n            View\r\n          </a-button>\r\n        </div>\r\n      );\r\n    },\r\n\r\n    showDetailModal(title, data) {\r\n      // 使用JsonDetailModal组件的showDetailModal方法\r\n      this.$refs.jsonDetailModal.showDetailModal(title, data);\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.kubernetes-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n\r\n\r\n"]}]}