import json
from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, ForeignKey, Text
from sqlalchemy.orm import relationship
from .config_datamodel import Base


def safe_json_loads(json_str):
    """Safely parse JSON string to Python object"""
    if not json_str:
        return {}
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return {}


class K8sAPIServer(Base):
    __tablename__ = 'k8s_api_server'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    address = Column(String)

    host = relationship("HostConfig", back_populates="k8s_api_server")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'address': self.address
        }


class K8sIngress(Base):
    __tablename__ = 'k8s_ingress'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    ingress_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_ingress")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'ingress_status': safe_json_loads(self.ingress_status)
        }


class K8sGateway(Base):
    __tablename__ = 'k8s_gateway'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    gateway_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_gateway")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'gateway_status': safe_json_loads(self.gateway_status)
        }


class K8sVirtualService(Base):
    __tablename__ = 'k8s_virtualservice'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    virtual_service_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_virtualservice")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'virtual_service_status': safe_json_loads(self.virtual_service_status)
        }


class K8sService(Base):
    __tablename__ = 'k8s_service'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    service_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_service")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'service_status': safe_json_loads(self.service_status)
        }


class K8sPod(Base):
    __tablename__ = 'k8s_pod'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    pod_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_pod")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'pod_status': safe_json_loads(self.pod_status)
        }


class K8sNode(Base):
    __tablename__ = 'k8s_node'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)
    pod_status = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_node")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec),
            'pod_status': safe_json_loads(self.pod_status)
        }


class K8sNetworkPolicy(Base):
    __tablename__ = 'k8s_network_policy'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    spec = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_network_policy")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'spec': safe_json_loads(self.spec)
        }


class K8sSecret(Base):
    __tablename__ = 'k8s_secret'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    data = Column(Text)
    secret_type = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_secret")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'data': safe_json_loads(self.data),
            'secret_type': self.secret_type
        }


class K8sConfigMap(Base):
    __tablename__ = 'k8s_configmap'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    data = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_configmap")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'data': safe_json_loads(self.data)
        }


class K8sRole(Base):
    __tablename__ = 'k8s_role'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    rules = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_role")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'rules': safe_json_loads(self.rules)
        }


class K8sRoleBinding(Base):
    __tablename__ = 'k8s_role_binding'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    namespace = Column(String)
    name = Column(String)
    meta_data = Column(Text)
    roleRef = Column(Text)
    subjects = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_role_binding")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'namespace': self.namespace,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'roleRef': safe_json_loads(self.roleRef),
            'subjects': safe_json_loads(self.subjects)
        }


class K8sClusterRole(Base):
    __tablename__ = 'k8s_cluster_role'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    name = Column(String)
    meta_data = Column(Text)
    aggregationRule = Column(Text)
    rules = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_cluster_role")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'aggregationRule': safe_json_loads(self.aggregationRule),
            'rules': safe_json_loads(self.rules)
        }


class K8sClusterRoleBinding(Base):
    __tablename__ = 'k8s_cluster_role_binding'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'))
    name = Column(String)
    meta_data = Column(Text)
    roleRef = Column(Text)
    subjects = Column(Text)

    host = relationship("HostConfig", back_populates="k8s_cluster_role_binding")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'name': self.name,
            'meta_data': safe_json_loads(self.meta_data),
            'roleRef': safe_json_loads(self.roleRef),
            'subjects': safe_json_loads(self.subjects)
        }


class K8sServiceAccountPermission(Base):
    __tablename__ = 'k8s_serviceaccount_permissions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    node_id = Column(Integer, ForeignKey('host_config.id', ondelete='CASCADE'), nullable=False)
    pod_name = Column(Text)
    namespace = Column(Text)
    service_account = Column(Text)
    permissions = Column(Text)  # JSON格式存储所有权限信息

    host = relationship("HostConfig", back_populates="k8s_serviceaccount_permissions")

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'node_id': self.node_id,
            'pod_name': self.pod_name,
            'namespace': self.namespace,
            'service_account': self.service_account,
            'permissions': self.permissions
        }

    def __repr__(self):
        return f"<K8sServiceAccountPermission(id={self.id}, pod={self.pod_name}, sa={self.service_account})>"
