import json
import base64


def build_remote_commands(node, proxy_ip, server_port=9998):
    python = "run_env/bin/python.exe"

    # Build encoded agent info for download and upload operations.
    download_agent_info = base64.b64encode(
        json.dumps({"host_ip": node.ip, "file_name": "application_snapshot_agent.zip"}).encode()
    ).decode()

    upload_agent_info = base64.b64encode(
        json.dumps({"host_ip": node.host_name}).encode()
    ).decode()

    cmd_list = [
        # Download and verify commands
        f'curl -s http://{proxy_ip}:{server_port}/api/file/download/{download_agent_info} -o application_snapshot_agent.zip',
        'if [ ! -f application_snapshot_agent.zip ] || [ ! -s application_snapshot_agent.zip ]; then echo "Agent download failed"; exit 1; fi',
        'unzip -o application_snapshot_agent.zip',
        'if [ ! -d "./run_env" ] || [ ! -d "./snapshot" ]; then echo "Agent unzip failed"; exit 1; fi',
        'chmod +x ./run_env/bin/python.exe',
        'chmod +x ./snapshot/testssl.sh-3.2/testssl.sh',
        'cp ./snapshot/testssl.sh-3.2/hexdump /usr/bin/',
        'chmod +x /usr/bin/hexdump',
        f'{python} main.py {node.host_name} {node.ip} >/dev/null',
        f'cp log/agent.log {node.host_name}_{node.ip}_agent.log',
        # Upload commands with existence check
        f'''
        for file in \
            "{node.host_name}_{node.ip}_process_info.pkl" \
            "{node.host_name}_{node.ip}_os_package_info.pkl" \
            "{node.host_name}_{node.ip}_hardware_info.pkl" \
            "{node.host_name}_{node.ip}_filesystem_info.pkl" \
            "{node.host_name}_{node.ip}_port_info.pkl" \
            "{node.host_name}_{node.ip}_k8s_info.pkl" \
            "{node.host_name}_{node.ip}_docker_info.pkl" \
            "{node.host_name}_{node.ip}_crictl_info.pkl" \
            "{node.host_name}_{node.ip}_agent.log"
        do
            if [ -f "$file" ]; then
                curl -F "file=@$file" "http://{proxy_ip}:{server_port}/api/file/upload/{upload_agent_info}"
            fi
        done
        '''
    ]

    script_create_cmd = f'cd /root && rm -rf .test && mkdir .test && cd .test && echo -e \'#!/bin/bash\\n' + '\\n'.join(
        cmd.replace("!", "\\!") for cmd in cmd_list) + f'\' > generated_script.sh && chmod +x generated_script.sh'

    return script_create_cmd
