<template>
  <div class="proxy-selector">
    <!-- 检测IP按钮 -->
    <a-button
      class="nav-style-button"
      @click="fetchReachableIps"
      :loading="isDetecting"
      :disabled="disabled"
    >
      {{ $t('common.detectReachableIps') || '检测可达IP' }}
    </a-button>

    <!-- IP选择下拉框 -->
    <a-select
      v-if="reachableIps.length"
      v-model="selectedIpValue"
      style="width: 100%; margin-top: 16px;"
      :placeholder="$t('tool.selectReachableIp') || '选择可达IP'"
      :disabled="disabled"
    >
      <a-select-option v-for="ip in reachableIps" :key="ip" :value="ip">
        {{ ip }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';

export default {
  name: 'ProxySelector',
  props: {
    // 是否禁用控件
    disabled: {
      type: Boolean,
      default: false
    },
    // 初始选中的IP
    value: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isDetecting: false,
      reachableIps: [],
      selectedIpValue: this.value
    };
  },
  computed: {
  },
  watch: {
    // 监听外部传入的value变化
    value(newValue) {
      this.selectedIpValue = newValue;
    },
    // 监听内部selectedIpValue变化，向外发送事件
    selectedIpValue(newValue) {
      this.$emit('input', newValue);
      this.$emit('change', newValue);
    }
  },
  methods: {
    async fetchReachableIps() {
      this.isDetecting = true;
      try {
        const response = await axios.get('/api/proxy/detect');
        this.reachableIps = response.data.reachable_ips;
        if (this.reachableIps.length) {
          this.selectedIpValue = this.reachableIps[0];
        }
      } catch (error) {
        console.error('Error detecting reachable IPs:', error);
        this.$notify.error({
          title: 'Error',
          message: 'Failed to detect reachable IPs'
        });
      } finally {
        this.isDetecting = false;
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
