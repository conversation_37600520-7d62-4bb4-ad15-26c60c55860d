<template>
  <div>
    <a-tabs :active-key="activeTab" @change="handleTabChange">
      <a-tab-pane key="host" :tab="$t('sidebar.hostConfig')">
        <HostConfig />
      </a-tab-pane>
      <a-tab-pane key="cbh" :tab="$t('sidebar.cbhConfig')">
        <CbhConfig />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import HostConfig from "@/components/Cards/HostConfig.vue";
import CbhConfig from "@/components/Cards/CbhConfig.vue";

export default {
  components: { HostConfig, CbhConfig },
  props: {
    defaultTab: {
      type: String,
      default: 'host'
    }
  },
  data() {
    return {
      activeTab: this.defaultTab
    }
  },
  watch: {
    '$route': {
      immediate: true, // 初始化时立即执行
      handler(to) {
        const targetTab = to.hash.replace('#',  '') || 'host'
        if (targetTab !== this.activeTab)  {
          this.activeTab  = targetTab
          console.log('Tab  updated from route:', targetTab)
        }
      }
    }
  },
  methods: {
    handleTabChange(key) {
      console.log('Tab  changed to:', key)
      this.$router.replace({
        path: '/config',
        hash: `#${key}`,
        query: this.$route.query
      }).catch(err => {
        if (!err.message.includes('Avoided  redundant navigation')) {
          console.error('Navigation  error:', err)
        }
      })
    }
  }
}
</script>

