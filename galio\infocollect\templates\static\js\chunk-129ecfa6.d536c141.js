(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-129ecfa6"],{"1d21":function(t,e,s){"use strict";s.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("a-row",{attrs:{type:"flex",gutter:24}},[e("a-col",{staticClass:"mb-24",attrs:{span:24}},[e("TaskPanel")],1)],1)],1)},a=[],o=function(){var t=this,e=t._self._c;return e("a-card",{staticClass:"header-solid h-full task-card",attrs:{bordered:!1,bodyStyle:{padding:"8px 16px"},headStyle:{borderBottom:"1px solid #e8e8e8"}}},[e("div",{staticClass:"steps-container"},[e("a-steps",{staticClass:"steps-flow",attrs:{current:t.currentStepComputed,size:"small"}},[e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{staticClass:"step-icon",attrs:{type:"apartment"}})]},proxy:!0}])}),e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{staticClass:"step-icon",attrs:{type:"global"}})]},proxy:!0}])}),e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-tooltip",{attrs:{title:t.getPlayIconTooltip}},[e("a-icon",{staticClass:"step-icon",class:{clickable:t.selectedIp&&t.selectedRowKeys.length>0&&!t.isProcessing,"ready-to-start":t.selectedIp&&t.selectedRowKeys.length>0&&!t.isProcessing},style:{color:t.selectedIp&&t.selectedRowKeys.length>0&&!t.isProcessing?"#3b4149":"#d9d9d9"},attrs:{type:"play-circle"},on:{click:function(e){t.selectedIp&&t.selectedRowKeys.length>0&&!t.isProcessing&&t.handleStart()}}})],1)]},proxy:!0}])})],1)],1),e("a-card",{staticStyle:{margin:"0 0 16px"},attrs:{size:"small",title:t.$t("common.configureNodes")}},[e("node-selector",{attrs:{"project-file":t.currentProject,disabled:t.isProcessing},on:{input:t.onNodesSelected},model:{value:t.selectedRowKeys,callback:function(e){t.selectedRowKeys=e},expression:"selectedRowKeys"}})],1),e("a-card",{staticStyle:{"margin-bottom":"16px"},attrs:{size:"small",title:t.$t("common.configureProxy")}},[e("proxy-selector",{attrs:{disabled:t.isProcessing},on:{change:t.handleProxyChange},model:{value:t.selectedIp,callback:function(e){t.selectedIp=e},expression:"selectedIp"}})],1),e("task-progress-card",{attrs:{"task-type":"task","is-processing":t.isProcessing}})],1)},c=[],i=(s("0643"),s("76d6"),s("fec3")),n=s("2f62"),l=s("4f4d"),d=s("524b"),h=s("5a3b"),p=s("9304"),u=s("c9da"),k={mixins:[l["a"],d["a"]],components:{ProxySelector:h["a"],TaskProgressCard:p["a"],NodeSelector:u["a"]},data(){return{selectedRowKeys:[],selectedIp:null,currentStep:0}},computed:{...Object(n["e"])(["activeTask","currentProject","sidebarColor"]),taskId:{get(){var t;return null===(t=this.activeTask)||void 0===t?void 0:t.task_id},set(t){this.$store.dispatch("updateTask",t?{task_id:t}:null)}},currentStepComputed(){return this.isProcessing?1:0===this.selectedRowKeys.length?-1:this.selectedRowKeys.length>0&&!this.selectedIp?0:2},getPlayIconTooltip(){return this.isProcessing?"Task is in progress...":this.selectedRowKeys.length?this.selectedIp?"Click to start collection!":"Please select a proxy IP":"Please select nodes first"}},created(){if(!this.checkDatabaseStatus())return;const t=localStorage.getItem("taskInfo_"+this.currentProject);if(t){const{projectFile:e}=JSON.parse(t);e===this.currentProject?this.checkActiveTask():(localStorage.removeItem("taskInfo_"+this.currentProject),localStorage.removeItem("taskCompleted_"+this.currentProject),this.$store.dispatch("updateTask",null))}},methods:{...Object(n["b"])(["addNotification"]),checkDatabaseStatus(){return!!this.currentProject||(this.$notify.error({title:"Database Error",message:"No project database selected. Please select a project first."}),this.$router.push("/projects"),!1)},onNodesSelected(t){this.selectedRowKeys=t,this.selectedRowKeys.length?this.currentStep=1:this.currentStep=0},handleProxyChange(t){this.selectedIp=t},async handleStart(){if(!this.checkDatabaseStatus())return;if(!this.selectedRowKeys.length||!this.selectedIp)return void this.$notify.warning({title:"No Nodes or Proxy Selected",message:"Please select one or more nodes and a reachable IP to collect the data."});this.isProcessing=!0,this.notificationSent=!1;const t=localStorage.getItem("taskInfo_"+this.currentProject);if(t)try{const{taskId:e}=JSON.parse(t);e&&this.clearTaskNotificationMark(e,"task",this.currentProject)}catch(e){console.error("Error clearing previous task notification:",e)}try{const{data:t}=await i["a"].post("/api/task/collect",{targets:this.selectedRowKeys,proxy_ip:this.selectedIp,dbFile:this.currentProject});t&&t.task_id&&(localStorage.setItem("taskInfo_"+this.currentProject,JSON.stringify({taskId:t.task_id,projectFile:this.currentProject})),localStorage.removeItem("taskCompleted_"+this.currentProject),this.taskId=t.task_id,this.startPolling(t.task_id,"task","task"))}catch(s){console.error("Error starting task:",s),this.$notify.error({title:"Task Start Failed",message:s.message||"Server connection error."}),this.isProcessing=!1}},async checkActiveTask(){try{const t=localStorage.getItem("taskInfo_"+this.currentProject),e=localStorage.getItem("taskCompleted_"+this.currentProject);if(t){const{taskId:s,projectFile:r}=JSON.parse(t);if(r!==this.currentProject)throw new Error("Task belongs to different project");const a=await i["a"].get("/api/task/"+s);if(a.data&&(this.$store.dispatch("updateTask",a.data),a.data.nodes)){const t=Object.values(a.data.nodes),r=t.every(t=>["success","failed"].includes(t.status));r||e?r&&(this.isProcessing=!1,localStorage.setItem("taskCompleted_"+this.currentProject,"true")):(this.isProcessing=!0,this.startPolling(s,"task","task"))}}}catch(t){console.error("Error checking active task:",t),localStorage.removeItem("taskInfo_"+this.currentProject),localStorage.removeItem("taskCompleted_"+this.currentProject)}},activated(){this.checkActiveTask()}},watch:{currentProject:{handler(t,e){t!==e&&(this.$store.dispatch("updateTask",null),this.stopPolling(),this.checkActiveTask())},immediate:!0}}},g=k,f=(s("6777"),s("2877")),y=Object(f["a"])(g,o,c,!1,null,"c1256e74",null),P=y.exports,m={components:{TaskPanel:P}},S=m,I=Object(f["a"])(S,r,a,!1,null,null,null);e["default"]=I.exports},6777:function(t,e,s){"use strict";s("cc90")},cc90:function(t,e,s){}}]);
//# sourceMappingURL=chunk-129ecfa6.d536c141.js.map