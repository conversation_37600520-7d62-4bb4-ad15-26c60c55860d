<template>

	<!-- Layout Header ( Navbar ) -->
	<a-layout-header >
		<div class="header-col header-brand">
			<a-button 
				type="text"
				class="trigger-button"
				@click="$emit('toggle')"
			>
				<menu-unfold-outlined v-if="collapsed" />
				<menu-fold-outlined v-else />
			</a-button>
			<h6>SecTest Copilot</h6>

			<!-- Trigger <PERSON>ton For Navigation Menu For Small Screens -->
			<a-button type="link" @click="collapseNav = collapseNav ? 0 : 1 " class="btn-menu-trigger">
				<svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"/></svg>
			</a-button>
			<!-- Trigger Button For Navigation Menu For Small Screens -->

		</div>
		<div class="header-col header-nav">
			<!-- Collapsible Navigation Menu For Small Screens -->
			<div class="menu-small">

				<!-- Collapsible Component For Navigation Menu For Small Screens -->
				<a-collapse v-model="collapseNav" accordion>
					<a-collapse-panel key="1">

						<!-- Navigation Menu For Small Screens -->
						<a-menu mode="vertical">
						</a-menu>
						<!-- / Navigation Menu For Small Screens -->
					</a-collapse-panel>
				</a-collapse>
				<!-- / Collapsible Component For Navigation Menu For Small Screens -->

			</div>
			<!-- / Collapsible Navigation Menu For Small Screens -->

		</div>
	</a-layout-header>
	<!-- / Layout Header ( Navbar ) -->

</template>

<script>
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';

export default {
	components: {
		MenuFoldOutlined,
		MenuUnfoldOutlined,
	},
	data() {
		return {
			// Collapse navigation value.
			// Binded model property for "Collapsible Navigation Menu" collapsed status .
			collapseNav: 0,
		}
	},
	props: {
		collapsed: {
			type: Boolean,
			default: false
		}
	},
	emits: ['toggle']
}
</script>

<style lang="scss" scoped>

	.nav-link svg {
		margin-right: 5px;
		vertical-align: middle;
	}
	.nav-link span {
		vertical-align: middle;
	}
	.ant-menu-submenu-popup {
		width: 100%;
	}
	.trigger-button {
		font-size: 18px;
		margin-right: 12px;
		padding: 0 24px;
		cursor: pointer;
		transition: color 0.3s;

		&:hover {
			color: #1890ff;
		}
	}

</style>