<template>
  <a-card
    :bordered="false"
    class="header-solid h-full kubernetes-card"
    :bodyStyle="{ padding: 0 }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >
    <!-- 引用JsonDetailModal组件 -->
    <JsonDetailModal ref="jsonDetailModal" />

    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32" height="32" viewBox="0 0 32 32" :class="`text-${sidebarColor}`">
              <path fill="currentColor" d="m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.k8s') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchActiveTabData" />
        </div>
      </div>
    </template>

    <a-tabs default-active-key="k8s_api_server" @change="handleTabChange">
      <a-tab-pane key="k8s_api_server" tab="API Servers">
        <a-table
          :columns="apiServerColumns"
          :data-source="apiServerData"
          :rowKey="(record) => record.address"
          :pagination="pagination"
          v-if="activeTab === 'k8s_api_server'"
          :loading="loadingApiServers"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_ingress" tab="Ingresses">
        <a-table
          :columns="ingressColumns"
          :data-source="ingressData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_ingress'"
          :loading="loadingIngresses"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_gateway" tab="Gateways">
        <a-table
          :columns="gatewayColumns"
          :data-source="gatewayData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_gateway'"
          :loading="loadingGateways"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_virtual_service" tab="Virtual Services">
        <a-table
          :columns="virtualServiceColumns"
          :data-source="virtualServiceData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_virtual_service'"
          :loading="loadingVirtualServices"
        >

        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_service" tab="Services">
        <a-table
          :columns="serviceColumns"
          :data-source="serviceData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_service'"
          :loading="loadingServices"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_network_policy" tab="Network Policies">
        <a-table
          :columns="networkPolicyColumns"
          :data-source="networkPolicyData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_network_policy'"
          :loading="loadingNetworkPolicies"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_pod" tab="Pods">
        <a-table
          :columns="podColumns"
          :data-source="podData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_pod'"
          :loading="loadingPods"
          :scroll="{ x: 1500 }"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_node" tab="Nodes">
        <a-table
          :columns="nodeColumns"
          :data-source="nodeData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_node'"
          :loading="loadingNodes"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_secret" tab="Secrets">
        <a-table
          :columns="secretColumns"
          :data-source="secretData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_secret'"
          :loading="loadingSecrets"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_config_map" tab="ConfigMaps">
        <a-table
          :columns="configMapColumns"
          :data-source="configMapData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_config_map'"
          :loading="loadingConfigMaps"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_role" tab="Roles">
        <a-table
          :columns="roleColumns"
          :data-source="roleData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_role'"
          :loading="loadingRole"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_role_binding" tab="Role Bindings">
        <a-table
          :columns="roleBindingColumns"
          :data-source="roleBindingData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_role_binding'"
          :loading="loadingRoleBinding"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_cluster_role" tab="Cluster Roles">
        <a-table
          :columns="clusterRoleColumns"
          :data-source="clusterRoleData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_cluster_role'"
          :loading="loadingClusterRole"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_cluster_role_binding" tab="Cluster Role Bindings">
        <a-table
          :columns="clusterRoleBindingColumns"
          :data-source="clusterRoleBindingData"
          :rowKey="(record) => record.name"
          :pagination="pagination"
          v-if="activeTab === 'k8s_cluster_role_binding'"
          :loading="loadingClusterRoleBinding"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="k8s_serviceaccount_permissions" tab="ServiceAccount Perms">
        <a-table
          :columns="serviceAccountPermissionsColumns"
          :data-source="serviceAccountPermissionsData"
          :rowKey="(record) => record.id"
          :pagination="pagination"
          v-if="activeTab === 'k8s_serviceaccount_permissions'"
          :loading="loadingServiceAccountPermissions"
          :scroll="{ x: 1200 }"
        >
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';
import JsonDetailModal from '../Widgets/JsonDetailModal.vue';

export default {
  components: {
    RefreshButton,
    JsonDetailModal
  },
  name: 'KubernetesInfo',
  data() {
    return {
      activeTab: 'k8s_api_server',
      apiServerData: [],
      ingressData: [],
      gatewayData: [],
      virtualServiceData: [],
      serviceData: [],
      networkPolicyData: [],
      podData: [],
      nodeData: [],
      secretData: [],
      configMapData: [],
      roleData: [],
      roleBindingData: [],
      clusterRoleData: [],
      clusterRoleBindingData: [],
      serviceAccountPermissionsData: [],
      loadingApiServers: false,
      loadingIngresses: false,
      loadingGateways: false,
      loadingVirtualServices: false,
      loadingServices: false,
      loadingNetworkPolicies: false,
      loadingPods: false,
      loadingNodes: false,
      loadingSecrets: false,
      loadingConfigMaps: false,
      loadingRole: false,
      loadingRoleBinding: false,
      loadingClusterRole: false,
      loadingClusterRoleBinding: false,
      loadingServiceAccountPermissions: false,
      apiServerColumns: [
        { title: 'Node ID', dataIndex: 'node_id', key: 'node_id' },
        { title: 'Address', dataIndex: 'address', key: 'address' },
      ],
      ingressColumns: [
        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },
        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Spec')
        },
        {
          title: 'Status',
          dataIndex: 'ingress_status',
          key: 'ingress_status',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Ingress Status')
        },
      ],
      gatewayColumns: [
        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },
        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 300,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Gateway Spec')
        },
        {
          title: 'Status',
          dataIndex: 'gateway_status',
          key: 'gateway_status',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Gateway Status')
        },
      ],
      virtualServiceColumns: [
        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },
        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 300,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Virtual Service Spec')
        },
        {
          title: 'Status',
          dataIndex: 'virtual_service_status',
          key: 'virtual_service_status',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Virtual Service Status')
        },
      ],
      serviceColumns: [
        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },
        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Spec')
        },
        {
          title: 'Status',
          dataIndex: 'service_status',
          key: 'service_status',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Service Status')
        },
      ],
      networkPolicyColumns: [
        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },
        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Spec')
        },
      ],
      podColumns: [
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 200,
          customRender: (text) => this.renderComplexData(text, 'Spec')
        },
        {
          title: 'Status',
          dataIndex: 'pod_status',
          key: 'pod_status',
          width: 200,
          customRender: (text) => this.renderComplexData(text, 'Pod Status')
        },
      ],
      nodeColumns: [
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Spec',
          dataIndex: 'spec',
          key: 'spec',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Spec')
        },
        {
          title: 'Status',
          dataIndex: 'pod_status',
          key: 'pod_status',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Node Status')
        },
      ],
      secretColumns: [
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Data',
          dataIndex: 'data',
          key: 'data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Data')
        },
        {
          title: 'Type',
          dataIndex: 'secret_type',
          key: 'secret_type',
          width: 120
        },
      ],
      configMapColumns: [
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Data',
          dataIndex: 'data',
          key: 'data',
          width: 300,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'ConfigMap Data')
        },
      ],
      roleColumns: [
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Rules',
          dataIndex: 'rules',
          key: 'rules',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')
        },
      ],
      roleBindingColumns: [
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Role Ref',
          dataIndex: 'roleRef',
          key: 'roleRef',
          width: 250,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Role Reference')
        },
        {
          title: 'Subjects',
          dataIndex: 'subjects',
          key: 'subjects',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')
        },
      ],
      clusterRoleColumns: [
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Aggregation Rule',
          dataIndex: 'aggregationRule',
          key: 'aggregationRule',
          width: 250,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Aggregation Rule')
        },
        {
          title: 'Rules',
          dataIndex: 'rules',
          key: 'rules',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')
        },
      ],
      clusterRoleBindingColumns: [
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: 150
        },
        {
          title: 'Metadata',
          dataIndex: 'meta_data',
          key: 'meta_data',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Metadata')
        },
        {
          title: 'Role Ref',
          dataIndex: 'roleRef',
          key: 'roleRef',
          width: 250,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'Role Reference')
        },
        {
          title: 'Subjects',
          dataIndex: 'subjects',
          key: 'subjects',
          width: 200,
          ellipsis: true,
          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')
        },
      ],
      serviceAccountPermissionsColumns: [
        {
          title: 'Pod Name',
          dataIndex: 'pod_name',
          key: 'pod_name',
          width: 150
        },
        {
          title: 'Namespace',
          dataIndex: 'namespace',
          key: 'namespace',
          width: 120
        },
        {
          title: 'Service Account',
          dataIndex: 'service_account',
          key: 'service_account',
          width: 150
        },
        {
          title: 'Permissions',
          dataIndex: 'permissions',
          key: 'permissions',
          width: 300,
          ellipsis: true,
          customRender: (text) => {
            try {
              const permissions = Array.isArray(text) ? text : (text ? JSON.parse(text) : []);

              let displayText = 'No permissions';
              if (permissions && permissions.length > 0) {
                const summary = permissions.map(p => `${p.type}: ${p.name}`).join(', ');
                displayText = summary.length > 50 ? summary.slice(0, 50) + '...' : summary;
              }

              return (
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span style="flex: 1; overflow: hidden; text-overflow: ellipsis;">
                    {displayText}
                  </span>
                  <a-button
                    type="link"
                    style="flex-shrink: 0; padding: 0 8px; min-width: 50px;"
                    onClick={() => this.showDetailModal('ServiceAccount Permissions', permissions)}
                  >
                    View
                  </a-button>
                </div>
              );
            } catch (error) {
              console.error('Error parsing permissions:', error);
              return 'Error parsing data';
            }
          }
        },
      ],
      pagination: {
        pageSize: 100,
      },
      initialLoad: true,
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp(newIp) {
      this.resetData();
      this.initialLoad = true;
      if (newIp) {
        this.fetchActiveTabData();
      }
    },
  },
  mounted() {
    if (this.selectedNodeIp) {
      this.fetchActiveTabData();
    }
  },
  methods: {
    handleTabChange(key) {
      this.activeTab = key;
      this.fetchActiveTabData();
    },
    async fetchActiveTabData() {
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        this.resetData();
        return;
      }
      const resourceType = this.activeTab;
      this[`loading${this.capitalizeFirstLetter(resourceType)}`] = true;
      try {
        let response;
        response = await axios.get(`/api/k8s/${resourceType}/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject
          }
        });
        const data = response.data;
        this[`${this.camelCaseToDataName(resourceType)}`] = data;
      } catch (error) {
        console.error(`Error fetching ${resourceType}:`, error);
        this[`${this.camelCaseToDataName(resourceType)}`] = [];
      } finally {
        this[`loading${this.capitalizeFirstLetter(resourceType)}`] = false;
      }
    },
    camelCaseToDataName(camelCase) {
      const withoutK8s = camelCase.replace('k8s_', '');

      // 特殊处理 serviceaccount_permissions
      if (withoutK8s === 'serviceaccount_permissions') {
        return 'serviceAccountPermissionsData';
      }

      const camelized = withoutK8s.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      return camelized + 'Data';
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    resetData() {
      this.apiServerData = [];
      this.ingressData = [];
      this.gatewayData = [];
      this.virtualServiceData = [];
      this.serviceData = [];
      this.networkPolicyData = [];
      this.podData = [];
      this.nodeData = [];
      this.secretData = [];
      this.configMapData = [];
      this.roleData = [];
      this.roleBindingData = [];
      this.clusterRoleData = [];
      this.clusterRoleBindingData = [];
      this.serviceAccountPermissionsData = [];
    },
    renderComplexData(text, title) {
      if (!text || text === 'None') return '-';

      // 简化显示文本生成逻辑
      const displayText = Array.isArray(text) ? `Array(${text.length})` :
                         typeof text === 'object' && text !== null ?
                           Object.entries(text).slice(0, 2).map(([k, v]) =>
                             `${k}: ${typeof v === 'object' ? JSON.stringify(v).substring(0, 15) : v}`
                           ).join(', ') + (Object.keys(text).length > 2 ? '...' : '') :
                           String(text).length > 50 ? String(text).slice(0, 50) + '...' : String(text);

      return (
        <div style="display: flex; align-items: center; justify-content: space-between; padding-right: 8px;">
          <span style="flex: 1; overflow: hidden; text-overflow: ellipsis;">{displayText}</span>
          <a-button
            type="link"
            style="flex-shrink: 0; padding: 0 8px; min-width: 50px;"
            onClick={() => this.showDetailModal(title, text)}
          >
            View
          </a-button>
        </div>
      );
    },

    showDetailModal(title, data) {
      // 使用JsonDetailModal组件的showDetailModal方法
      this.$refs.jsonDetailModal.showDetailModal(title, data);
    }
  },
};
</script>

<style scoped lang="scss">
.kubernetes-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}
</style>


