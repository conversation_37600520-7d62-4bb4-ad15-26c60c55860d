//
// Header layout styles
//

// Project Name Display
.layout-dashboard .project-name-display {
	display: flex;
	align-items: center;
	padding: 4px 12px;
	margin: 0 2px;
	color: #1890ff;
	font-weight: 500;
	font-size: 14px;
	border-radius: 4px;
	background-color: rgba(24, 144, 255, 0.1);
	border: 1px solid rgba(24, 144, 255, 0.2);
	position: relative;

	&:not(:last-child)::after {
		content: "";
		position: absolute;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		height: 14px;
		width: 1px;
		background-color: rgba(0, 0, 0, 0.15);
	}

	.project-name-text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 100px;
		font-weight: 500;
	}

	@media (max-width: 992px) {
		.project-name-text {
			max-width: 80px;
		}
	}

	@media (max-width: 768px) {
		padding: 3px 8px;
		font-size: 12px;
		margin: 1px;

		.project-name-text {
			max-width: 60px;
		}
	}
}

// Page Header
.layout-dashboard .ant-layout-header {
	background: transparent;
	height: auto;
	padding: 2px;
	margin: 10px 4px;
	line-height: inherit;
	border-radius: 12px;
	transition: .2s;

	@media( min-width: $md ){
		margin: 10px 20px;
	}
}

// Fixed Header Styles
.layout-dashboard.navbar-fixed {
	.ant-layout>div>.ant-affix .ant-layout-header {
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9));
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
		margin: 0 8px;
		padding: 12px 16px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.03);
		border-radius: 0 0 15px 15px;
		backdrop-filter: blur(12px);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	&.has-sidebar .ant-layout>div>.ant-affix {
		z-index: 1;
		@media( min-width: $lg ){
			z-index: 10;
		}
	}

	.node-selector {
		.ant-dropdown-menu {
			min-width: 50px;
		}
	}
}

// Header Components Styles
// Language Switcher
.layout-dashboard .language-switcher {
	display: flex;
	align-items: center;
	padding: 0 8px;
	cursor: pointer;
	color: #111827;
	transition: all 0.3s;

	&:hover {
		color: #1890ff;
	}

	.language-text {
		margin-left: 5px;
		font-size: 14px;
	}
}

.layout-dashboard .active-language {
	color: #1890ff;
	font-weight: 500;
}

// Header Navigation Buttons
.layout-dashboard .header-nav-buttons {
	display: flex;
	align-items: center;
	margin-left: 16px;
	flex-wrap: nowrap;
	overflow-x: auto;

	@media (max-width: 576px) {
		margin-left: 8px;
		flex-wrap: wrap;
	}
}

.layout-dashboard .header-nav-button {
	display: flex;
	align-items: center;
	padding: 4px 10px;
	margin: 0 1px;
	color: rgba(0, 0, 0, 0.45);
	transition: all 0.3s;
	text-decoration: none;
	font-size: 14px;
	position: relative;
	border-radius: 4px;
	flex-shrink: 0;

	&:hover {
		color: #40a9ff;
		background-color: rgba(24, 144, 255, 0.1);

		.anticon-down {
			opacity: 1;
			transform: rotate(180deg);
		}
	}

	&.active {
		color: white;
		font-weight: 500;
		position: relative;
	}

	&:not(:last-child)::after {
		content: "";
		position: absolute;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		height: 14px;
		width: 1px;
		background-color: rgba(0, 0, 0, 0.15);
	}

	.label {
		font-weight: normal;
	}

	.anticon-down {
		font-size: 10px;
		margin-left: 2px;
		transition: transform 0.3s;
		opacity: 0.65;
	}

	@media (max-width: 768px) {
		padding: 3px 8px;
		font-size: 12px;
		margin: 1px;
	}
}

// Node Selector
.layout-dashboard .node-selector {
	margin-left: 16px;
	margin-right: 36px;
	display: inline-block;

	@media (max-width: 768px) {
		margin-left: 8px;
		margin-right: 16px;
	}
}

.layout-dashboard .node-selector-link {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	color: #111827;
	transition: all 0.3s;
	border-radius: 6px;

	&:hover {
		color: #1890ff;
		background: #e6f7ff;

		.anticon-down {
			opacity: 1;
			transform: rotate(180deg);
		}
	}

	svg {
		margin-right: 8px;
	}

	.anticon-down {
		font-size: 12px;
		transition: transform 0.3s;
		opacity: 0.65;
	}
}

.layout-dashboard .node-name {
	margin: 0 8px;
	max-width: 150px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.layout-dashboard .node-menu-item {
	display: flex;
	flex-direction: column;
	gap: 1px;

	.host-name {
		color: #6B7280;
		font-size: 12px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.ip-address {
		font-weight: 500;
		color: #111827;
	}
}

// Node dropdown menu hover style
.node-menu .ant-dropdown-menu-item:hover {
	color: #1890ff !important;
}

// Navigation Button Transparency
.layout-dashboard .nav-btn-transparent {
	opacity: 0.8 !important;
}

// Header Notification Dropdown
.layout-dashboard .header-notifications-dropdown {
	@media( max-width: $sm ){
		left: 20px !important;
		right: 20px !important;
	}
}

.layout-dashboard.navbar-fixed .header-notifications-dropdown {
	position: fixed;
}

.layout-dashboard .header-notifications-list {
	background: #ffffff;
	box-shadow: $shadow-1;
	border-radius: 12px;
	min-width: 176px;
	padding: 16px 8px;

	ul.ant-dropdown-menu-items {
		padding: 0;
		margin: 0;
	}

	.ant-list-item {
		padding: 5px 16px;
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.ant-list-item-meta-title {
		line-height: normal;
	}

	.ant-avatar {
		width: 36px;
		height: 36px;
		background-color: $color-gray-2;
		box-shadow: $shadow-2;
		display: flex;
		justify-content: center;
		align-items: center;

		svg {
			width: 22px;
			height: 22px;
		}
	}

	.ant-list-item-meta-description {
		color: $color-gray-7;
		font-size: 12px;
		line-height: 12px;
		font-weight: $fw-semibold;

		svg {
			width: 14px;
			height: 14px;
			fill: $color-gray-7;
			vertical-align: middle;
			margin-right: 4px;

			path {
				fill: $color-gray-7;
			}
		}

		span {
			vertical-align: middle;
		}
	}
}

// Breadcrumbs
.layout-dashboard .ant-breadcrumb {
	>span {
		.ant-breadcrumb-link a {
			color: $color-gray-7;
		}
		&:last-child .ant-breadcrumb-link {
			color: $color-gray-12;
		}
	}
}

.layout-dashboard .ant-page-header-heading {
	margin-top: 0;
	
	.ant-page-header-heading-title {
		font-weight: $fw-bold;
		font-size: 16px;
		line-height: 20px;
		margin-top: 5px;
	}
}

// Header Control
.layout-dashboard .header-control {
	display: flex;
	flex-direction: row-reverse;
	align-items: center;
	margin-top: 10px;

	@media( min-width: $md ){
		margin-top: 0;
	}

	.ant-btn-link {
		height: auto;
		padding: 0 7px;
		margin: 0;
		box-shadow: none;
		color: $color-gray-7;
	}

	.ant-dropdown-link {
		padding: 0 7px;
		margin: 0;
	}

	svg {
		vertical-align: middle;
		path {
			fill: $color-gray-12;
		}
	}

	.header-search {
		width: 200px;
		margin: 0 7px 0 0;

		@media( min-width: $md ){
			margin: 0 7px;
		}

		svg {
			path {
				fill: $color-gray-7;
			}
		}

		.ant-input {
			font-weight: $fw-semibold;
			color: $color-gray-7;
			&:not(:first-child) {
				padding-left: 32px;
			}
			&:not(:last-child) {
				padding-right: 11px;
			}
		}

		&.loading .ant-input:not(:last-child) {
			padding-right: 30px;
		}

		.ant-input-suffix .ant-input-search-icon {
			display: none;

			&.anticon-loading {
				display: block;
			}
		}
	}

	.sidebar-toggler {
		display: block;
		@media( min-width: $lg ){
			display: none;
		}
	}
}

// Global Header Dropdown Styles (outside of layout-dashboard scope)
//.header-node-dropdown {
//	min-width: 240px;
//
//	.node-menu .ant-dropdown-menu-item {
//		padding: 8px 12px;
//	}
//}