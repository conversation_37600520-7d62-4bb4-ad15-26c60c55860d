from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import relationship
from .config_datamodel import Base
import json


class CrictlHostConfig(Base):
    __tablename__ = 'crictl_host_config'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    version_info = Column(Text)
    runtime_info = Column(Text)
    is_root_user = Column(Boolean)

    host = relationship("HostConfig", back_populates="crictl_host_config")

    def to_dict(self):
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'is_root_user': self.is_root_user
        }
        try:
            data['version_info'] = json.loads(self.version_info) if self.version_info else {}
            data['runtime_info'] = json.loads(self.runtime_info) if self.runtime_info else {}
        except json.JSONDecodeError:
            data['version_info'] = {}
            data['runtime_info'] = {}
        return data


class CrictlPod(Base):
    __tablename__ = 'crictl_pod'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    pod_id = Column(String)
    pod_metadata = Column(Text)
    state = Column(String)
    created_at = Column(String)
    network = Column(Text)
    labels = Column(Text)
    annotations = Column(Text)

    host = relationship("HostConfig", back_populates="crictl_pods")

    def to_dict(self):
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'pod_id': self.pod_id,
            'state': self.state,
            'created_at': self.created_at
        }

        try:
            data['metadata'] = json.loads(self.pod_metadata) if self.pod_metadata else {}
        except json.JSONDecodeError:
            data['metadata'] = {}

        for field in ['network', 'labels', 'annotations']:
            try:
                data[field] = json.loads(getattr(self, field)) if getattr(self, field) else {}
            except json.JSONDecodeError:
                data[field] = {}

        return data


class CrictlContainer(Base):
    __tablename__ = 'crictl_container'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    container_id = Column(String)
    mounts = Column(Text)
    processes = Column(Text)
    exposures = Column(Text)
    env = Column(Text)
    inspect_data = Column(Text)

    host = relationship("HostConfig", back_populates="crictl_containers")

    def to_dict(self):
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'container_id': self.container_id
        }

        # 处理JSON字段
        json_fields = {
            'mounts': self.mounts,
            'processes': self.processes,
            'exposures': self.exposures,
            'env': self.env
        }

        for field, value in json_fields.items():
            try:
                data[field] = json.loads(value) if value else []
            except json.JSONDecodeError:
                data[field] = []

        # 处理inspect_data
        try:
            data['inspect_data'] = json.loads(self.inspect_data) if self.inspect_data else {}
        except json.JSONDecodeError:
            data['inspect_data'] = {}

        return data
