<template>
  <a-form :form="form" layout="vertical">
    <!-- Spider工具配置 - 编辑脚本和运行按钮在一行 -->
    <a-form-item :label="$t('tool.editScript')">
      <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
        <!-- 脚本编辑 -->
        <div style="flex: 1; min-width: 200px;">
          <script-editor
            v-model="spiderScriptContent"
            :script-tabs="spiderScriptTabs"
            :default-tab="activeSpiderScriptTab"
            :disabled="isProcessing"
            :filename="'spider_script.sh'"
            :success-message="'Spider script saved successfully'"
            @script-saved="onScriptSaved"
          />
        </div>
        
        <!-- 运行按钮 -->
        <div style="flex: 0 0 auto; min-width: 100px;">  
          <a-button
            class="nav-style-button"
            :loading="isProcessing"
            :disabled="isProcessing || !spiderScriptContent"
            @click="runSpider"
            style="width: 100%;"
          >
            <a-icon type="play-circle" /> {{ $t('tool.runSpider') || '运行Spider' }}
          </a-button>
        </div>
      </div>
    </a-form-item>
  </a-form>
</template>

<script>
import { mapState } from 'vuex';
import { getSpiderScriptNames, getScriptContent } from '@/assets/scripts';
import ScriptEditor from '@/components/common/ScriptEditor.vue';

export default {
  name: 'SpiderToolTab',
  components: {
    ScriptEditor
  },
  props: {
    isProcessing: {
      type: Boolean,
      default: false
    },
    selectedRowKeys: {
      type: Array,
      required: true
    },
    selectedIp: {
      type: String,
      default: null
    },
    currentProject: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      spiderScriptContent: '',
      spiderScriptTabs: [],
      activeSpiderScriptTab: 'spider_command',
      spiderToolPath: 'infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\sSpider\\static_scan\\scan_tools.zip',
      spiderSavePath: 'infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\sSpider\\upload\\task',
      spiderScriptPath: 'infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\start.bat',
    };
  },
  computed: {
    ...mapState(['sidebarColor'])
  },
  created() {
    // 初始化Spider脚本选项卡
    this.spiderScriptTabs = getSpiderScriptNames();
    // 加载默认Spider脚本内容
    this.loadSpiderScriptContent(this.activeSpiderScriptTab);
  },
  methods: {
    loadSpiderScriptContent(tabKey) {
      this.activeSpiderScriptTab = tabKey;
      this.spiderScriptContent = getScriptContent(tabKey);
    },
    onScriptSaved(path) {
      this.spiderScriptPath = path;
      this.$emit('script-saved', path);
    },
    runSpider() {
      if (!this.selectedRowKeys.length || !this.selectedIp) {
        this.$notify.warning({
          title: '未选择节点或代理',
          message: '请选择一个或多个节点和一个可用的代理IP来运行工具。'
        });
        return;
      }

      // 准备请求数据
      const requestData = {
        targets: this.selectedRowKeys,
        proxy_ip: this.selectedIp,
        script_content: this.spiderScriptContent,
        dbFile: this.currentProject
      };

      this.$emit('run-spider', requestData);
    },
    getToolData() {
      return {
        spiderScriptContent: this.spiderScriptContent,
        spiderScriptPath: this.spiderScriptPath,
        spiderToolPath: this.spiderToolPath,
        spiderSavePath: this.spiderSavePath
      };
    }
  }
};
</script>
