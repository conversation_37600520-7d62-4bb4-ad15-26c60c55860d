<template>
  <div>
    <!-- 组件不直接渲染模态框，而是提供方法 -->
  </div>
</template>

<script>
import Vue from 'vue';
import VueJsonPretty from 'vue-json-pretty';

export default {
  name: 'JsonDetailModal',
  methods: {
    /**
     * 显示JSON详情模态框
     * @param {string} title 模态框标题
     * @param {object|string} data 要显示的数据
     * @param {object} options 配置选项
     */
    showDetailModal(title, data, options = {}) {
      // 计算响应式尺寸
      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);
      const contentHeight = Math.min(700, window.innerHeight * 0.6);

      // 生成唯一ID
      const ids = {
        search: `search-${Date.now()}`,
        counter: `counter-${Date.now()}`,
        theme: `theme-${Date.now()}`
      };

      // 默认使用暗色主题
      let isDarkTheme = true;

      // 创建标题、搜索框和复制按钮
      const header = (
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="display: flex; align-items: center;">
            <a-icon type="code" style="margin-right: 8px; font-size: 16px;" />
            <span style="font-weight: 500;">{title}</span>
          </div>
          <div style="display: flex; align-items: center;">
            <div id={ids.counter} style="margin-right: 10px; min-width: 60px; text-align: right; color: #666;"></div>
            <a-input
              id={ids.search}
              placeholder="搜索 (Enter: ↓  Shift+Enter: ↑)"
              allowClear
              prefix={<a-icon type="search" style="color: rgba(0,0,0,.25)" />}
              style="width: 250px;"
            />
            <a-button
              id={ids.theme}
              type="link"
              icon="bg-colors"
              style="margin-left: 8px; color: #1890ff; font-size: 16px;"
              title="切换主题"
            />
            <a-button
              id="copy-btn"
              type="link"
              icon="copy"
              style="margin-left: 8px; color: #1890ff; font-size: 16px;"
              title="复制内容"
            />
          </div>
        </div>
      );

      // 准备内容元素
      const contentElement = typeof data === 'object' ? (
        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class="json-container theme-dark" id="json-container">
        </div>
      ) : (
        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>
          {String(data)}
        </div>
      );

      // 创建模态框
      this.$root.$confirm({
        title: header,
        content: contentElement,
        width: modalWidth,
        okText: options.okText || '关闭',
        icon: null,
        cancelButtonProps: { style: { display: 'none' } },
        class: 'detail-modal',
        maskClosable: false, // 防止点击外部关闭
        getContainer: () => document.body.appendChild(document.createElement('div'))
      });

      // 在模态框内容区域渲染VueJsonPretty组件
      setTimeout(() => {
        if (typeof data === 'object') {
          // 查找JSON容器
          const container = document.getElementById('json-container');
          if (container) {
            // 创建VueJsonPretty组件实例
            const JsonViewer = new Vue({
              render: h => h(VueJsonPretty, {
                props: {
                  data: data,
                  deep: Infinity, // 设置为Infinity以默认展开所有节点
                  showDoubleQuotes: true,
                  showLength: true,
                  showLineNumbers: true,  // 添加行号显示
                },
                style: {
                  height: '100%',
                  overflow: 'auto'
                }
              })
            });

            // 挂载组件
            JsonViewer.$mount();
            container.appendChild(JsonViewer.$el);
          }
        }

        // 获取搜索相关元素
        const searchInput = document.getElementById(ids.search);
        const counterElement = document.getElementById(ids.counter);

        // 搜索功能变量
        let matches = [];
        let currentMatchIndex = -1;

        // 如果有搜索框，添加搜索功能
        if (searchInput && counterElement) {
          // 高亮匹配项函数
          const highlightMatches = (searchTerm) => {
            // 重置匹配
            matches = [];
            currentMatchIndex = -1;

            // 清除计数器
            counterElement.textContent = '';

            if (!searchTerm) return;

            try {
              // 查找所有键和值节点
              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');

              // 创建正则表达式
              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');

              // 移除之前的高亮
              document.querySelectorAll('.vjs-search-match').forEach(el => {
                el.classList.remove('vjs-search-match');
              });

              document.querySelectorAll('.vjs-search-current').forEach(el => {
                el.classList.remove('vjs-search-current');
              });

              // 查找匹配项
              jsonNodes.forEach(node => {
                const text = node.textContent;
                let match;
                regex.lastIndex = 0;

                while ((match = regex.exec(text)) !== null) {
                  matches.push({
                    node: node,
                    text: match[0]
                  });

                  // 防止无限循环
                  if (match.index === regex.lastIndex) {
                    regex.lastIndex++;
                  }
                }
              });

              // 更新计数器
              if (matches.length === 0) {
                counterElement.textContent = '无匹配项';
                return;
              }

              counterElement.textContent = `0/${matches.length}`;

              // 为所有匹配项添加高亮类
              matches.forEach(match => {
                match.node.classList.add('vjs-search-match');
              });
            } catch (error) {
              console.error('搜索错误:', error);
            }
          };

          // 导航到特定匹配项
          const navigateToMatch = (index) => {
            if (matches.length === 0) return;

            // 确保索引在有效范围内
            index = Math.max(0, Math.min(matches.length - 1, index));

            // 移除之前匹配项的当前高亮
            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {
              matches[currentMatchIndex].node.classList.remove('vjs-search-current');
            }

            // 更新当前索引和计数器
            currentMatchIndex = index;
            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;

            // 高亮当前匹配项
            const currentMatch = matches[currentMatchIndex];
            if (currentMatch) {
              currentMatch.node.classList.add('vjs-search-current');

              // 确保父节点是展开的
              let parent = currentMatch.node.parentElement;
              while (parent) {
                if (parent.classList && parent.classList.contains('vjs-tree-node')) {
                  // 如果节点是折叠的，点击展开按钮
                  if (!parent.classList.contains('is-expanded')) {
                    const expandBtn = parent.querySelector('.vjs-tree-brackets');
                    if (expandBtn) expandBtn.click();
                  }
                }
                parent = parent.parentElement;
              }

              // 滚动到匹配项
              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          };

          // 添加搜索输入事件
          let searchTimeout;
          searchInput.addEventListener('input', (e) => {
            // 使用防抖优化性能
            if (searchTimeout) clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
              highlightMatches(e.target.value.trim());
            }, 300);
          });

          // 添加键盘导航事件
          searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);
            }
          });
        }

        // 添加主题切换功能
        const themeButton = document.getElementById(ids.theme);
        if (themeButton) {
          themeButton.addEventListener('click', () => {
            const container = document.querySelector('.json-container');
            if (container) {
              // 切换主题类
              if (container.classList.contains('theme-light')) {
                container.classList.remove('theme-light');
                container.classList.add('theme-dark');
                container.style.backgroundColor = '#1e1e1e';
                isDarkTheme = true;
              } else {
                container.classList.remove('theme-dark');
                container.classList.add('theme-light');
                container.style.backgroundColor = '#fff';
                isDarkTheme = false;
              }
            }
          });
        }

        // 添加复制功能
        const copyButton = document.getElementById('copy-btn');
        if (copyButton) {
          copyButton.addEventListener('click', () => {
            try {
              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);

              // 使用现代Clipboard API
              if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy)
                  .then(() => {
                    this.$message.success(this.$t('common.copiedToClipboard'));
                  })
                  .catch(err => {
                    console.error('复制失败:', err);
                    this.$message.error('复制失败');
                  });
              } else {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = textToCopy;
                document.body.appendChild(textArea);
                textArea.select();
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                  this.$message.success(this.$t('common.copiedToClipboard'));
                } else {
                  this.$message.error(this.$t('common.copyFailed'));
                }
              }
            } catch (err) {
              this.$message.error(this.$t('common.copyFailed'));
            }
          });
        }
      }, 300);
    }
  }
};
</script>

<style lang="scss">
/* 覆盖详情弹窗的样式 */
.detail-modal {
  .ant-modal-body {
    padding: 12px !important;
  }

  .ant-modal-confirm-content {
    margin-top: 12px !important;
    margin-bottom: 12px !important;
  }

  .ant-modal-confirm-btns {
    margin-top: 16px !important;
    margin-bottom: 8px !important;
  }

  /* 复制按钮样式 */
  #copy-btn {
    transition: all 0.3s;

    &:hover {
      color: #40a9ff !important;
      transform: scale(1.1);
    }

    &:active {
      color: #096dd9 !important;
    }
  }

  /* 搜索匹配项样式 */
  .vjs-search-match {
    background-color: rgba(255, 255, 0, 0.3);
    border-radius: 2px;
  }

  .vjs-search-current {
    background-color: rgba(255, 165, 0, 0.6);
    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);
  }

  /* 主题样式 */
  .json-container.theme-dark {
    .vjs-tree {
      background-color: #1e1e1e !important;
      color: #d4d4d4 !important;

      .vjs-key {
        color: #9cdcfe !important; /* 浅蓝色 */
      }

      .vjs-value.vjs-value-string {
        color: #ce9178 !important; /* 橙红色 */
      }

      .vjs-value.vjs-value-number {
        color: #b5cea8 !important; /* 浅绿色 */
      }

      .vjs-value.vjs-value-boolean {
        color: #569cd6 !important; /* 蓝色 */
      }

      .vjs-value.vjs-value-null {
        color: #c586c0 !important; /* 紫色 */
      }

      .vjs-tree-brackets {
        color: #d4d4d4 !important; /* 浅灰色 */
      }
    }
  }
}

/* 调整内容区域的左侧边距 */
.ant-modal-confirm-body {
  padding-left: 12px !important;
}

/* 调整内容区域的图标和文本间距 */
.ant-modal-confirm-body > .anticon {
  display: none !important;
}

/* 调整标题和内容的左侧边距 */
.ant-modal-confirm-body > .ant-modal-confirm-title,
.ant-modal-confirm-body > .ant-modal-confirm-content {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* 调整内容区域的下边距 */
.detail-modal .ant-modal-confirm-content {
  margin-bottom: 8px !important; /* 适当的下边距 */
}

/* 调整底部按钮区域的上下边距 */
.detail-modal .ant-modal-confirm-btns {
  margin-top: 8px !important; /* 减小上边距 */
  margin-bottom: 4px !important;
  padding-top: 0 !important; /* 移除上内边距 */
  padding-bottom: 0 !important;
  border-top: none !important; /* 移除分隔线 */
}

/* 调整底部按钮本身的样式 */
.detail-modal .ant-modal-confirm-btns button {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding: 6px 16px !important;
  height: auto !important;
}
</style>
