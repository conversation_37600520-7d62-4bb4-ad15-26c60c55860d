from flask import Blueprint, jsonify
from log.logger import log_error
from services.docker_service import DockerService
from routes.database_wraps import with_database_session

bp = Blueprint('docker', __name__)

resource_mapping = {
    "docker_host_config": "get_docker_host_config",
    "docker_container": "get_docker_containers",
    "docker_network": "get_docker_networks",
}


@bp.route('/<resource_type>/<string:node_ip>', methods=['GET'])
@with_database_session
def get_docker_resource(resource_type, node_ip, db):
    docker_service = DockerService(db)
    node = docker_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    method_name = resource_mapping.get(resource_type)

    if not method_name:
        return jsonify({"error": "Resource type not supported"}), 400

    method = getattr(docker_service, method_name, None)

    if not method:
        return jsonify({"error": "Service method not found"}), 500

    try:
        result = method(node.id)
        if result is None:
            return jsonify([]), 200

        return jsonify(result), 200

    except Exception as e:
        log_error(f"Error processing request: {e}")
        return jsonify({"error": f"Internal server error: {e}"}), 500
