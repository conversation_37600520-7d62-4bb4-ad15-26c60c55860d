<template>
  <div class="layout-content">
    <a-card :bordered="false" class="criclebox">
      <template #title>
        <div class="card-header-wrapper">
          <div class="header-wrapper">
            <div class="logo-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" height="20" width="20" :class="`text-${sidebarColor}`">
                <path :fill="'currentColor'" d="M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"/>
              </svg>
            </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.testcase') }}</h6>
          </div>
          <div>
            <RefreshButton @refresh="fetchTestcases(currentPage)" />
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" @submit.prevent="handleSearch">
          <a-form-item label="Name">
            <a-input v-model="searchForm.name" placeholder="Search by name" allowClear />
          </a-form-item>
          <a-form-item label="Level">
            <a-select v-model="searchForm.level" placeholder="Select level" style="width: 120px" allowClear>
              <a-select-option value="level 0">Level 0</a-select-option>
              <a-select-option value="level 1">Level 1</a-select-option>
              <a-select-option value="level 2">Level 2</a-select-option>
              <a-select-option value="level 3">Level 3</a-select-option>
              <a-select-option value="level 4">Level 4</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="Prepare Condition">
            <a-input v-model="searchForm.prepare_condition" placeholder="Search in prepare condition" allowClear />
          </a-form-item>
          <a-form-item label="Test Steps">
            <a-input v-model="searchForm.test_steps" placeholder="Search in test steps" allowClear />
          </a-form-item>
          <a-form-item label="Expected Result">
            <a-input v-model="searchForm.expected_result" placeholder="Search in expected result" allowClear />
          </a-form-item>
          <a-form-item>
            <a-button html-type="submit" :class="`bg-${sidebarColor}`" style="color: white" :loading="loading">
              <a-icon type="search" />
              Search
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">
              <a-icon type="reload" />
              Reset
            </a-button>
          </a-form-item>
        </a-form>
        <div class="search-result-count" v-if="testcases.length > 0">
          <a-tag color="blue">Found: {{ total }} test cases</a-tag>
        </div>
      </div>

      <!-- Table -->
      <a-table
        :columns="columns"
        :data-source="testcases"
        :loading="loading"
        :pagination="{
          total: total,
          pageSize: 100,
          current: currentPage,
          showSizeChanger: false,
          showQuickJumper: true,
          onChange: handlePageChange
        }"
        :scroll="{ x: 1500 }"
      >
        <!-- Custom column renders -->
        <template #Testcase_LastResult="{ text }">
          <a-tag :color="getResultColor(text)">
            {{ text || 'N/A' }}
          </a-tag>
        </template>

        <template #Testcase_Level="{ text }">
          <a-tag :color="getLevelColor(text)">
            {{ text || 'N/A' }}
          </a-tag>
        </template>

        <template #lastModified="{ text }">
          {{ formatDate(text) }}
        </template>

        <template #action="{ record }">
          <a-space>
            <a-button type="link" @click="viewDetails(record)">
              View Details
            </a-button>
          </a-space>
        </template>
      </a-table>

      <!-- Details Modal -->
      <TestCaseDetailModal
        :visible="detailsVisible"
        :testcase="selectedTestcase"
        @close="detailsVisible = false"
      />
    </a-card>
  </div>
</template>

<script>
import axios from '@/api/axiosInstance';
import moment from 'moment';
import {mapState} from "vuex";
import RefreshButton from '../Widgets/RefreshButton.vue';
import TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';

export default {
  components: {
    RefreshButton,
    TestCaseDetailModal
  },
  name: 'TestCases',
  data() {
    return {
      loading: false,
      testcases: [],
      total: 0,
      currentPage: 1,
      detailsVisible: false,
      selectedTestcase: null,
      searchForm: {
        name: '',
        level: undefined,
        prepare_condition: '',
        test_steps: '',
        expected_result: ''
      },
      columns: [
        {
          title: '#',
          dataIndex: 'index',
          key: 'index',
          width: 100,
          align: 'center',
          customRender: (text, record, index) => {
            return ((this.currentPage - 1) * 100) + index + 1;
          }
        },
        {
          title: 'Test Case ID',
          dataIndex: 'Testcase_Number',
          key: 'Testcase_Number',
          width: 130,
          ellipsis: true,
          customRender: (text, record) => {
            return <a onClick={() => this.viewDetails(record)} style="color: #1890ff; cursor: pointer;">{text}</a>;
          }
        },
        {
          title: 'Name',
          dataIndex: 'Testcase_Name',
          key: 'Testcase_Name',
          width: 200,
          // ellipsis: true,
        },
        {
          title: 'Level',
          dataIndex: 'Testcase_Level',
          key: 'Testcase_Level',
          width: 100,
          slots: { customRender: 'Testcase_Level' },
        },
        {
          title: 'Prepare Condition',
          dataIndex: 'Testcase_PrepareCondition',
          key: 'Testcase_PrepareCondition',
          width: 250,
          ellipsis: true,
        },
        {
          title: 'Test Steps',
          dataIndex: 'Testcase_TestSteps',
          key: 'Testcase_TestSteps',
          width: 400,
          ellipsis: true,
        },
        {
          title: 'Expected Result',
          dataIndex: 'Testcase_ExpectedResult',
          key: 'Testcase_ExpectedResult',
          width: 400,
          ellipsis: true,
        },
        // {
        //   title: 'Action',
        //   key: 'action',
        //   fixed: 'right',
        //   width: 120,
        //   slots: { customRender: 'action' },
        // },
      ],
    };
  },
  created() {
    this.fetchTestcases();
  },
    computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  methods: {
    async fetchTestcases(page = 1) {
      this.loading = true;
      try {
        // 构建查询参数
        const params = {
          page: page,
          page_size: 100
        };

        // 添加搜索参数
        if (this.searchForm.name) params.name = this.searchForm.name;
        if (this.searchForm.level) params.level = this.searchForm.level;
        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;
        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;
        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;

        const response = await axios.get('/api/testcase/', { params });
        this.testcases = response.data.data;
        this.total = response.data.total;
      } catch (error) {
        console.error('Error fetching testcases:', error);
        this.$message.error('Failed to load test cases');
      } finally {
        this.loading = false;
      }
    },

    // 搜索处理函数
    handleSearch() {
      this.currentPage = 1; // 重置到第一页
      this.fetchTestcases(1);
    },

    // 重置搜索表单
    resetSearch() {
      this.searchForm = {
        name: '',
        level: undefined,
        prepare_condition: '',
        test_steps: '',
        expected_result: ''
      };
      this.currentPage = 1;
      this.fetchTestcases(1);
    },
    formatDate(date) {
      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';
    },
    getResultColor(result) {
      const colors = {
        'PASS': 'success',
        'FAIL': 'error',
        'BLOCKED': 'warning',
        'NOT RUN': 'default',
      };
      return colors[result] || 'default';
    },
    getLevelColor(level) {
      const colors = {
        'level 0': 'red',
        'level 1': 'orange',
        'level 2': 'green',
        'level 3': 'blue',
        'level 4': 'purple',
      };
      return colors[level] || 'default';
    },
    viewDetails(record) {
      this.selectedTestcase = record;
      this.detailsVisible = true;
    },
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchTestcases(page);
    },
  },
};
</script>

<style lang="scss" scoped>

.criclebox {
  background: #fff;
  border-radius: 12px;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;

  .ant-form-item {
    margin-bottom: 12px;
  }

  .search-result-count {
    margin-top: 1px;
    padding: 0 1px;
  }
}


</style>
