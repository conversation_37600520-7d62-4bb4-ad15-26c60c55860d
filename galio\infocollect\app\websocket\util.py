import requests
import csv
import io

from app.models.node import Node
from log.logger import log_error


def fetch_aibash_cmd(raw_input, cluster_config):
    """
    发送 POST 请求到 AI Bash 生成 API，获取执行计划。

    :param raw_input: 原始输入命令。
    :param cluster_config: 集群配置信息。
    :return: (执行计划结果, 是否调用成功)
    """
    url = "http://100.85.151.241:9034/agent/generate/aibash"
    data = {
        "raw_input": raw_input,
        "cluster_config": cluster_config
    }

    try:
        response = requests.post(url, json=data, timeout=10)  # 设置超时时间为 10 秒
        result = response.json()
        if response.status_code == 200:
            execution_results = result.get("execution_results", '')
            return execution_results, True
        else:
            log_error(f"状态码：{response.status_code}")
            log_error(f"错误信息：{response.text}")
            return result.get("detail", ""), False
    except requests.exceptions.Timeout:
        log_error("连接超时，请检查网络或服务器状态。")
        return "连接超时，请检查网络或服务器状态", False
    except requests.exceptions.RequestException as e:
        log_error(f"请求异常：{e}")
        return "请求异常", False


def nodes_to_csv_string(nodes):
    """
    将节点信息转换为 CSV 格式的字符串。

    :param nodes: 节点字典，键为 IP 地址，值为 Node 对象。
    :return: CSV 格式的字符串。
    """
    fieldnames = ['host_name', 'ip', 'ssh_port', 'login_user', 'login_pwd', 'switch_root_cmd', 'switch_root_pwd']

    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=fieldnames)

    writer.writeheader()

    for node in nodes.values():
        row = {
            'host_name': node.host_name,
            'ip': node.ip,
            'ssh_port': node.ssh_port,
            'login_user': node.login_user,
            'login_pwd': node.login_pwd,
            'switch_root_cmd': node.switch_root_cmd,
            'switch_root_pwd': node.switch_root_pwd
        }
        writer.writerow(row)

    return output.getvalue()


# 示例调用
def main():

    raw_input = "查询MSS-MPS主机ip地址"
    cluster_config = """
    cluster_name,hostname,ip,ssh_port,login_user,login_pwd,switch_cmd,switch_pwd,k8s_flag
    MetaStudio,MSS-MPS,*************,22,root,DigitalAsset!+=@2033,,,True
    MetaStudio,MSS-TTSS-LAB,*************,22,root,DigitalAsset!+=@2033,,,True
    """

    execution_plan, success = fetch_aibash_cmd(raw_input, cluster_config)
    if success:
        print(f"\nAI Bash 执行计划：")
        print("-" * 50)
        for host_info in execution_plan:
            print(f"主机 IP：{host_info['host_ip']}")
            print(f"登录用户名：{host_info['login_username']}")
            print(f"执行命令：{host_info['execute_cmd']}")
            print("-" * 20)
    else:
        print("调用失败，请检查日志。")


if __name__ == "__main__":
    # main()

    nodes = {}

    # 添加测试节点信息
    node1 = Node(
        host_name="Test-Server1",
        ip="*************",
        ssh_port=22,
        login_user="root",
        login_pwd="password123",
        switch_root_cmd="su -",
        switch_root_pwd="root_password"
    )
    nodes[node1.ip] = node1

    node2 = Node(
        host_name="Test-Server2",
        ip="*************",
        ssh_port=22,
        login_user="admin",
        login_pwd="admin_password",
        switch_root_cmd="sudo su -",
        switch_root_pwd="sudo_password"
    )
    nodes[node2.ip] = node2
    csv_string = nodes_to_csv_string(nodes)
    print(csv_string)
