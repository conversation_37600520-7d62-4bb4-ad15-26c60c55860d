(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49d38e76"],{"13d5":function(e,t,s){"use strict";var o=s("23e7"),i=s("d58f").left,r=s("a640"),a=s("2d00"),n=s("605d"),l=r("reduce"),c=!n&&a>79&&a<83;o({target:"Array",proto:!0,forced:!l||c},{reduce:function(e){return i(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"1da2":function(e,t,s){},"3c90":function(e,t,s){"use strict";s("1da2")},"405d":function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e._self._c;return t("div",[t("RepositoryConfig")],1)},i=[],r=function(){var e=this,t=e._self._c;return t("div",[t("a-card",{staticClass:"header-solid repository-config-card",attrs:{bordered:!1},scopedSlots:e._u([{key:"title",fn:function(){return[t("a-row",{attrs:{type:"flex",align:"middle"}},[t("a-col",{attrs:{span:12}},[t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("repositoryConfig.title")))])]),t("a-col",{staticClass:"text-right",attrs:{span:12}},[t("div",{staticClass:"button-groups"},[t("div",{staticClass:"button-group"},[t("a-button",{staticClass:"nav-style-button action-button",attrs:{icon:"plus"},on:{click:e.addNewRow}},[e._v(" "+e._s(e.$t("repositoryConfig.addRepository"))+" ")]),t("a-button",{staticClass:"nav-style-button action-button",attrs:{icon:"export",disabled:0===e.selectedRowKeys.length},on:{click:e.exportSelectedRepositories}},[e._v(" "+e._s(e.$t("repositoryConfig.exportSelected"))+" ")]),t("a-button",{staticClass:"nav-style-button action-button",attrs:{icon:"download",disabled:0===e.selectedRowKeys.length},on:{click:e.downloadSelectedRepositories}},[e._v(" "+e._s(e.$t("repositoryConfig.downloadSelected"))+" ")]),t("a-button",{staticClass:"nav-style-button action-button delete-button",attrs:{type:"danger",icon:"delete",disabled:0===e.selectedRowKeys.length},on:{click:e.deleteSelectedRepositories}},[e._v(" "+e._s(e.$t("repositoryConfig.deleteSelected"))+" ")])],1),t("div",{staticClass:"button-group"},[t("a-button",{staticClass:"nav-style-button",attrs:{type:"default",icon:"download"},on:{click:e.downloadTemplate}},[e._v(" "+e._s(e.$t("repositoryConfig.downloadTemplate"))+" ")]),t("a-upload",{attrs:{"show-upload-list":!1,"custom-request":e.handleUpload,accept:".csv"}},[t("a-button",{staticClass:"nav-style-button",attrs:{type:"default",icon:"upload"}},[e._v(" "+e._s(e.$t("repositoryConfig.uploadTemplate"))+" ")])],1)],1)])])],1)]},proxy:!0}])},[t("div",{staticClass:"config-table"},[t("a-table",{attrs:{columns:e.columns,"data-source":e.repositories,rowKey:e=>e.key,pagination:{current:e.currentPage,pageSize:e.pageSize,total:e.repositories.length,onChange:e.onPageChange},loading:e.loading,"row-selection":{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange,getCheckboxProps:e=>({disabled:e.editable||e.isNew})}},scopedSlots:e._u([e._l(e.editableColumns,(function(s){return{key:s,fn:function(o,i,r){return[t("div",{key:s},[i.editable?t("a-input",{staticStyle:{margin:"-5px 0"},attrs:{value:o,placeholder:"Enter "+e.getColumnTitle(s)},on:{change:t=>e.handleChange(t.target.value,i.key,s)}}):t("span",{staticStyle:{display:"flex","align-items":"center"}},["repository_url"===s&&o?t("a-icon",{staticStyle:{cursor:"pointer","margin-left":"4px",opacity:"0.6","font-size":"12px"},attrs:{type:"copy"},on:{click:function(t){return e.copyText(o)},mouseenter:function(e){e.target.style.opacity="1"},mouseleave:function(e){e.target.style.opacity="0.6"}}}):e._e(),t("span",{style:"repository_url"===s&&o?"cursor: pointer":"",on:{click:function(t){"repository_url"===s&&o&&e.copyText(o)}}},[e._v(e._s(o||"-"))])],1)],1)]}}})),{key:"operation",fn:function(s,o,i){return[t("div",{staticClass:"editable-row-operations"},[o.editable?[t("a-button",{attrs:{type:"link"},on:{click:()=>e.save(o.key)}},[e._v(e._s(e.$t("repositoryConfig.save")))]),t("a-popconfirm",{attrs:{title:"Discard changes?"},on:{confirm:()=>e.cancel(o.key)}},[t("a-button",{attrs:{type:"link",danger:""}},[e._v(e._s(e.$t("repositoryConfig.cancel")))])],1)]:[t("a-button",{attrs:{type:"link"},on:{click:()=>e.edit(o.key)}},[e._v(e._s(e.$t("repositoryConfig.edit")))]),t("a-button",{attrs:{type:"link"},on:{click:()=>e.copyRepository(o)}},[e._v(" "+e._s(e.$t("repositoryConfig.copy"))+" ")]),t("a-popconfirm",{attrs:{title:"Confirm deletion?"},on:{confirm:()=>e.deleteRepository(o)}},[t("a-button",{attrs:{type:"link",danger:""}},[e._v(e._s(e.$t("repositoryConfig.delete")))])],1)]],2)]}}],null,!0)})],1)]),t("repository-download-results")],1)},a=[],n=(s("13d5"),s("0643"),s("2382"),s("fffc"),s("a573"),s("9d4a"),s("0c63")),l=s("fec3"),c=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"download-results-card",attrs:{size:"small",title:e.$t("repositoryDownload.title")},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-space",[e.downloadResults&&"running"===e.downloadResults.status?t("a-tag",{attrs:{color:"blue"}},[e._v(" "+e._s(e.$t("repositoryDownload.downloading"))+" ")]):e.downloadResults&&"success"===e.downloadResults.status?t("a-tag",{attrs:{color:"green"}},[e._v(" "+e._s(e.$t("repositoryDownload.completed"))+" ")]):e.downloadResults&&"partial_success"===e.downloadResults.status?t("a-tag",{attrs:{color:"orange"}},[e._v(" "+e._s(e.$t("repositoryDownload.partialCompleted"))+" ")]):e.downloadResults&&"failed"===e.downloadResults.status?t("a-tag",{attrs:{color:"red"}},[e._v(" "+e._s(e.$t("repositoryDownload.failed"))+" ")]):e._e(),t("a-button",{staticClass:"clear-button",attrs:{type:"link",size:"small"},on:{click:e.clearResults}},[t("a-icon",{attrs:{type:"close"}}),e._v(" "+e._s(e.$t("repositoryDownload.clear"))+" ")],1)],1)]},proxy:!0}])},[t("div",{staticClass:"stats-summary"},[t("a-row",{attrs:{gutter:16}},[t("a-col",{attrs:{span:8}},[t("a-statistic",{attrs:{title:e.$t("repositoryDownload.total"),value:e.totalCount,"value-style":{color:"#1890ff"}}})],1),t("a-col",{attrs:{span:8}},[t("a-statistic",{attrs:{title:e.$t("repositoryDownload.success"),value:e.successCount,"value-style":{color:"#52c41a"}}})],1),t("a-col",{attrs:{span:8}},[t("a-statistic",{attrs:{title:e.$t("repositoryDownload.failed"),value:e.failedCount,"value-style":{color:"#f5222d"}}})],1)],1)],1),t("div",{staticClass:"result-table"},[t("a-table",{attrs:{columns:e.columns,"data-source":e.tableData,pagination:!1,size:"small","row-key":e=>e.key},scopedSlots:e._u([{key:"status",fn:function(s,o){return["success"===o.status?t("a-tag",{attrs:{color:"green"}},[e._v(" "+e._s(e.$t("repositoryDownload.success"))+" ")]):"failed"===o.status?t("a-tag",{attrs:{color:"red"}},[e._v(" "+e._s(e.$t("repositoryDownload.failed"))+" ")]):"downloading"===o.status?t("a-tag",{attrs:{color:"blue"}},[e._v(" "+e._s(e.$t("repositoryDownload.downloading"))+" ")]):t("a-tag",{attrs:{color:"orange"}},[e._v(" "+e._s(e.$t("repositoryDownload.pending"))+" ")])]}},{key:"progress",fn:function(s,o){return["downloading"===o.status?t("div",[t("a-progress",{attrs:{percent:o.progress||0,size:"small",status:"active"}})],1):"success"===o.status?t("span",{staticStyle:{color:"#52c41a","font-weight":"500"}},[e._v(" 100% ")]):"failed"===o.status?t("span",{staticStyle:{color:"#f5222d","font-weight":"500"}},[e._v(" "+e._s(e.$t("repositoryDownload.failed"))+" ")]):t("span",{staticStyle:{color:"#fa8c16","font-weight":"500"}},[e._v(" "+e._s(e.$t("repositoryDownload.pending"))+" ")])]}},{key:"result",fn:function(s,o){return[o.error?t("span",{staticStyle:{color:"#f5222d"}},[t("a-tooltip",{attrs:{title:o.error}},[t("a-icon",{attrs:{type:"exclamation-circle"}}),e._v(" "+e._s(o.error.length>50?o.error.substring(0,50)+"...":o.error)+" ")],1)],1):o.download_path?t("span",{staticStyle:{color:"#52c41a"}},[t("a-tooltip",{attrs:{title:o.download_path}},[t("a-icon",{attrs:{type:"check-circle"}}),e._v(" "+e._s(o.download_path.length>50?"..."+o.download_path.substring(o.download_path.length-50):o.download_path)+" ")],1)],1):"downloading"===o.status?t("span",{staticStyle:{color:"#1890ff"}},[t("a-icon",{attrs:{type:"loading",spin:""}}),e._v(" "+e._s(e.$t("repositoryDownload.downloading"))+"... ")],1):t("span",{staticStyle:{color:"#fa8c16","font-weight":"500"}},[e._v(" "+e._s(e.$t("repositoryDownload.pending"))+" ")])]}}])})],1)])},d=[],p=(s("4e3e"),s("2f62")),u={name:"RepositoryDownloadResults",computed:{...Object(p["e"])(["repositoryDownloadResults"]),downloadResults(){return this.repositoryDownloadResults},totalCount(){return this.downloadResults&&this.downloadResults.repositories?Object.keys(this.downloadResults.repositories).length:0},successCount(){return this.downloadResults&&this.downloadResults.repositories?Object.values(this.downloadResults.repositories).filter(e=>"success"===e.status).length:0},failedCount(){return this.downloadResults&&this.downloadResults.repositories?Object.values(this.downloadResults.repositories).filter(e=>"failed"===e.status).length:0},tableData(){if(!this.downloadResults||!this.downloadResults.repositories)return[];const e=[];return Object.values(this.downloadResults.repositories).forEach((t,s)=>{e.push({key:"repo_"+s,microservice_name:t.microservice_name,repository_url:t.repository_url,branch_name:t.branch_name,status:t.status,progress:t.progress||0,download_path:t.download_path,error:t.error_detail})}),e},columns(){return[{title:"#",width:100,customRender:(e,t,s)=>s+1},{title:this.$t("repositoryConfig.columns.microservice"),dataIndex:"microservice_name",key:"microservice_name",width:150},{title:this.$t("repositoryConfig.columns.repositoryUrl"),dataIndex:"repository_url",key:"repository_url",ellipsis:!0,width:600},{title:this.$t("repositoryConfig.columns.branchName"),dataIndex:"branch_name",key:"branch_name",width:150},{title:this.$t("tool.columns.status"),dataIndex:"status",key:"status",scopedSlots:{customRender:"status"},width:150},{title:this.$t("repositoryDownload.progress"),dataIndex:"progress",key:"progress",scopedSlots:{customRender:"progress"},width:150},{title:this.$t("tool.columns.result"),dataIndex:"result",key:"result",scopedSlots:{customRender:"result"}}]}},methods:{clearResults(){this.$store.dispatch("clearRepositoryDownloadResults")}}},h=u,y=(s("3c90"),s("2877")),g=Object(y["a"])(h,c,d,!1,null,"550fb864",null),m=g.exports,f=s("416a");let w=[];var v={components:{AIcon:n["a"],RepositoryDownloadResults:m},mixins:[f["a"]],computed:{},data(){return{repositories:[],saving:!1,loading:!1,currentPage:1,pageSize:50,editableColumns:["microservice_name","repository_url","branch_name"],selectedRowKeys:[],currentDbFile:localStorage.getItem("currentProject"),downloadTaskId:null,downloadPolling:!1,downloadPollInterval:null,columns:[{title:"#",dataIndex:"index",width:80,customRender:(e,t,s)=>(this.currentPage-1)*this.pageSize+s+1},{title:this.$t("repositoryConfig.columns.microservice"),dataIndex:"microservice_name",scopedSlots:{customRender:"microservice_name"},width:200},{title:this.$t("repositoryConfig.columns.repositoryUrl"),dataIndex:"repository_url",scopedSlots:{customRender:"repository_url"},width:300},{title:this.$t("repositoryConfig.columns.branchName"),dataIndex:"branch_name",scopedSlots:{customRender:"branch_name"},width:150},{title:this.$t("repositoryConfig.actions"),dataIndex:"operation",scopedSlots:{customRender:"operation"},width:150,align:"center"}]}},created(){if(!this.currentDbFile)return this.$message.warning("Please select a project first"),void this.$router.push("/projects");this.fetchRepositoryConfig()},methods:{copyRepository(e){const t="new-"+Date.now(),s={...e,key:t,editable:!0,isNew:!0,microservice_name:e.microservice_name+"_copy"};this.repositories=[s,...this.repositories],this.currentPage=1,w=this.repositories.map(e=>({...e})),this.selectedRowKeys=[]},getColumnTitle(e){const t={microservice_name:this.$t("repositoryConfig.columns.microservice"),repository_url:this.$t("repositoryConfig.columns.repositoryUrl"),branch_name:this.$t("repositoryConfig.columns.branchName")};return t[e]||e},handleChange(e,t,s){const o=[...this.repositories],i=o.find(e=>e.key===t);i&&(i[s]=e,this.repositories=o)},edit(e){const t=[...this.repositories],s=t.find(t=>t.key===e);s&&(s.editable=!0,this.repositories=t)},async save(e){const t=[...this.repositories],s=t.find(t=>t.key===e);if(s){delete s.editable,this.repositories=t,w=t.map(e=>({...e}));try{this.saving=!0;const e={microservice_name:s.microservice_name,repository_url:s.repository_url,branch_name:s.branch_name},t=await l["a"].post("/api/repositories",{repositories:[e]},{params:{dbFile:this.currentDbFile}});if(t.data.success){const{successful:e,failed:s}=t.data.data;if(s.length>0){const e=s[0].error;return void this.$message.error(`${this.$t("repositoryConfig.validation.parseError")}: ${e}`)}await this.fetchRepositoryConfig(),this.$message.success("Repository saved successfully")}else this.$message.error(t.data.error||"Failed to save repository")}catch(i){var o;this.$message.error((null===(o=i.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.error)||"Failed to save repository"),await this.fetchRepositoryConfig()}finally{this.saving=!1}}},cancel(e){const t=this.repositories.findIndex(t=>t.key===e);if(-1===t)return;const s=this.repositories[t];if(s.isNew)this.repositories=this.repositories.filter(t=>t.key!==e);else{const t=[...this.repositories],o=w.find(t=>t.key===e);o&&(Object.assign(s,{...o}),delete s.editable,this.repositories=t)}},addNewRow(){this.repositories=[{key:"new-"+Date.now(),microservice_name:"",repository_url:"",branch_name:"main",editable:!0,isNew:!0},...this.repositories],this.currentPage=1,w=this.repositories.map(e=>({...e})),this.selectedRowKeys=[]},async fetchRepositoryConfig(){try{this.loading=!0;const e=await l["a"].get("/api/repositories",{params:{detail:!0,dbFile:this.currentDbFile}});this.repositories=e.data.data.map(e=>{var t;return{...e,key:(null===(t=e.id)||void 0===t?void 0:t.toString())||"repo_"+e.microservice_name,isNew:!1}}),w=this.repositories.map(e=>({...e}))}catch(t){var e;this.$message.error((null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.error)||"Failed to load repositories")}finally{this.loading=!1}},onPageChange(e){this.currentPage=e},async deleteRepository(e){try{e.id?(await l["a"].delete("/api/repositories/"+e.id,{params:{dbFile:this.currentDbFile}}),this.repositories=this.repositories.filter(t=>t.key!==e.key),this.selectedRowKeys=this.selectedRowKeys.filter(t=>t!==e.key),this.$message.success("Deleted successfully")):(this.repositories=this.repositories.filter(t=>t.key!==e.key),this.selectedRowKeys=this.selectedRowKeys.filter(t=>t!==e.key))}catch(s){var t;this.$message.error((null===(t=s.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"Failed to delete repository"),await this.fetchRepositoryConfig()}},onSelectChange(e){this.selectedRowKeys=e},async deleteSelectedRepositories(){if(0!==this.selectedRowKeys.length)try{const e=this.selectedRowKeys.map(e=>this.repositories.find(t=>t.key===e)).filter(e=>e&&e.id).map(e=>e.id);e.length>0&&await l["a"].post("/api/repositories/batch-delete",{ids:e},{params:{dbFile:this.currentDbFile}}),this.repositories=this.repositories.filter(e=>!this.selectedRowKeys.includes(e.key)),this.selectedRowKeys=[],this.$message.success("Selected repositories deleted successfully")}catch(t){var e;this.$message.error((null===(e=t.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.error)||"Failed to delete repositories"),await this.fetchRepositoryConfig()}else this.$message.warning("Please select repositories to delete")},async exportSelectedRepositories(){if(0!==this.selectedRowKeys.length)try{const e=this.selectedRowKeys.map(e=>this.repositories.find(t=>t.key===e)).filter(e=>e&&e.id).map(e=>e.id),t=await l["a"].post("/api/repositories/export",{ids:e},{params:{dbFile:this.currentDbFile},responseType:"blob"}),s=window.URL.createObjectURL(new Blob([t.data])),o=document.createElement("a");o.href=s,o.setAttribute("download","repositories_export.csv"),document.body.appendChild(o),o.click(),o.remove(),window.URL.revokeObjectURL(s),this.$message.success("Repositories exported successfully")}catch(e){this.$message.error("Failed to export repositories")}else this.$message.warning("Please select repositories to export")},async downloadTemplate(){try{const e=await l["a"].get("/api/repositories/template",{responseType:"blob"}),t=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=t,s.setAttribute("download","repository_template.csv"),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(t),this.$message.success("Template downloaded successfully")}catch(e){this.$message.error("Failed to download template")}},async handleUpload(e){const{file:t}=e;if(t.name.endsWith(".csv"))try{const e=new FormData;e.append("file",t),e.append("dbFile",this.currentDbFile);const s=await l["a"].post("/api/repositories/upload",e,{headers:{"Content-Type":"multipart/form-data"}});if(s.data.success){const{successful:e,failed:t}=s.data.data;if(t.length>0){const e=t.map(e=>`${e.microservice_name||"Unknown"}: ${e.error}`).join("\n");this.$confirm({title:this.$t("repositoryConfig.validation.parseError"),content:e,showCancelButton:!1,confirmButtonText:"OK",type:"warning"})}e.length>0&&(await this.fetchRepositoryConfig(),this.$message.success(`Successfully imported ${e.length} repositories`)),t.length>0&&0===e.length&&this.$message.error("No valid repositories found in the file")}else this.$message.error(s.data.error||"Failed to import repositories")}catch(o){var s;this.$message.error((null===(s=o.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.error)||"Failed to import repositories")}else this.$message.error("Please upload CSV file")},async downloadSelectedRepositories(){if(0===this.selectedRowKeys.length)return void this.$message.warning("Please select repositories to download");const e=this.selectedRowKeys.map(e=>this.repositories.find(t=>t.key===e)).filter(e=>e&&e.id);if(0===e.length)return void this.$message.warning("No valid repositories selected");const t=prompt(this.$t("repositoryConfig.download.selectPath"),"D:\\downloads");if(t&&t.trim()){this.$message.loading(this.$t("repositoryConfig.download.starting"),0);try{const s={repositories:e.map(e=>({id:e.id,microservice_name:e.microservice_name,repository_url:e.repository_url,branch_name:e.branch_name})),download_path:t.trim()},o=await l["a"].post("/api/repositories/download",s,{params:{dbFile:this.currentDbFile}});if(this.$message.destroy(),o.data.success){const t=o.data.data.task_id;this.downloadTaskId=t;const s={task_id:t,status:"running",successful:[],failed:[],repositories:e.reduce((e,t)=>{const s=`${t.microservice_name}_${t.repository_url}`;return e[s]={microservice_name:t.microservice_name,repository_url:t.repository_url,branch_name:t.branch_name,status:"pending",progress:0,error_detail:null,download_path:null},e},{}),timestamp:(new Date).toLocaleString()};this.$store.dispatch("updateRepositoryDownloadResults",s),this.$message.success("Repository download task started successfully"),this.startDownloadPolling(t)}else this.$message.error(o.data.error||this.$t("repositoryConfig.download.failed"))}catch(o){var s;this.$message.destroy(),this.$message.error((null===(s=o.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.error)||o.message||this.$t("repositoryConfig.download.failed"))}}},startDownloadPolling(e){this.downloadPollInterval&&clearInterval(this.downloadPollInterval),this.downloadPolling=!0,this.pollDownloadStatus(e),this.downloadPollInterval=setInterval(()=>{this.pollDownloadStatus(e)},5e3),console.log(`开始轮询代码仓下载任务 ${e}，轮询间隔: 5秒`)},async pollDownloadStatus(e){try{const t=await l["a"].get("/api/repositories/download/status/"+e,{params:{dbFile:this.currentDbFile}});if(t.data.success){const e=t.data.data;this.$store.dispatch("updateRepositoryDownloadResults",{...e,timestamp:(new Date).toLocaleString()}),"success"!==e.status&&"failed"!==e.status&&"partial_success"!==e.status||(this.stopDownloadPolling(),"success"===e.status?this.$message.success(this.$t("repositoryConfig.download.success")):"failed"===e.status?this.$message.error(this.$t("repositoryConfig.download.failed")):this.$message.warning(this.$t("repositoryConfig.download.partialSuccess")),console.log("代码仓下载任务完成，停止轮询"))}else console.error("获取下载状态失败:",t.data.error)}catch(s){var t;console.error("轮询下载状态出错:",s),404===(null===(t=s.response)||void 0===t?void 0:t.status)&&(this.stopDownloadPolling(),this.$message.error("Download task not found"))}},stopDownloadPolling(){this.downloadPollInterval&&(clearInterval(this.downloadPollInterval),this.downloadPollInterval=null),this.downloadPolling=!1,this.downloadTaskId=null},checkActiveDownloadTask(){const e=localStorage.getItem("repositoryDownloadTask_"+this.currentDbFile);if(e)try{const{taskId:t,projectFile:s}=JSON.parse(e);s===this.currentDbFile&&(this.downloadTaskId=t,this.startDownloadPolling(t))}catch(t){console.error("Error parsing repository download task info:",t),localStorage.removeItem("repositoryDownloadTask_"+this.currentDbFile)}}},mounted(){this.fetchRepositoryConfig(),this.checkActiveDownloadTask()},beforeDestroy(){this.stopDownloadPolling()}},_=v,b=(s("9ed9"),Object(y["a"])(_,r,a,!1,null,"db8d5038",null)),R=b.exports,$={name:"Repository",components:{RepositoryConfig:R}},C=$,k=(s("93c3"),Object(y["a"])(C,o,i,!1,null,"02d9aec4",null));t["default"]=k.exports},"416a":function(e,t,s){"use strict";t["a"]={methods:{copyText(e){const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success("Copied to clipboard")}}}},"605d":function(e,t,s){var o=s("c6b6"),i=s("da84");e.exports="process"==o(i.process)},"93c3":function(e,t,s){"use strict";s("c9ea")},"9d4a":function(e,t,s){"use strict";var o=s("23e7"),i=s("2266"),r=s("1c0b"),a=s("825a");o({target:"Iterator",proto:!0,real:!0},{reduce:function(e){a(this),r(e);var t=arguments.length<2,s=t?void 0:arguments[1];if(i(this,(function(o){t?(t=!1,s=o):s=e(s,o)}),{IS_ITERATOR:!0}),t)throw TypeError("Reduce of empty iterator with no initial value");return s}})},"9ed9":function(e,t,s){"use strict";s("a153")},a153:function(e,t,s){},a640:function(e,t,s){"use strict";var o=s("d039");e.exports=function(e,t){var s=[][e];return!!s&&o((function(){s.call(null,t||function(){throw 1},1)}))}},c9ea:function(e,t,s){},d58f:function(e,t,s){var o=s("1c0b"),i=s("7b0b"),r=s("44ad"),a=s("50c4"),n=function(e){return function(t,s,n,l){o(s);var c=i(t),d=r(c),p=a(c.length),u=e?p-1:0,h=e?-1:1;if(n<2)while(1){if(u in d){l=d[u],u+=h;break}if(u+=h,e?u<0:p<=u)throw TypeError("Reduce of empty array with no initial value")}for(;e?u>=0:p>u;u+=h)u in d&&(l=s(l,d[u],u,c));return l}};e.exports={left:n(!1),right:n(!0)}}}]);
//# sourceMappingURL=chunk-49d38e76.b910ab97.js.map