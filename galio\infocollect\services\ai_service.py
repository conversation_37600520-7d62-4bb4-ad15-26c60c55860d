from typing import Dict, List, Any, Optional
import os
import base64
from langchain_ollama import ChatOllama


class AIService:
    def __init__(self, db=None):
        self.db = db
        # self.base_url = "http://**************:9034"
        self.base_url = "http://**************:9034"
        self.username = "ollama"
        self.password = "Huaw"
        self.model = "qwen2.5-coder:7b"
        self.timeout = 360.0

        # 设置环境变量
        os.environ['NO_PROXY'] = 'localhost,127.0.0.1,**************'

        # 初始化ChatOllama实例
        self._llm = None

    @property
    def llm(self):
        if self._llm is None:
            # base64 编码认证信息
            auth_str = f"{self.username}:{self.password}"
            auth_bytes = auth_str.encode("ascii")
            encoded_auth = base64.b64encode(auth_bytes).decode("ascii")

            # 设置 HTTP 头
            headers = {
                "Authorization": f"Basic {encoded_auth}"
            }

            self._llm = ChatOllama(
                model=self.model,
                base_url=self.base_url,
                request_timeout=self.timeout,
                temperature=0,
                client_kwargs={"headers": headers}
            )
        return self._llm

    def _make_request(self, messages: List[Dict[str, str]]) -> Dict:
        # 使用ChatOllama发送请求
        response = self.llm.invoke(messages)

        # 构造与原POST请求相似的返回格式
        return {
            "choices": [
                {
                    "message": {
                        "content": response.content
                    }
                }
            ]
        }

    def analyze_process(self, process_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            prompt = self._build_process_analysis_prompt(process_data)

            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {"role": "user", "content": prompt}
            ]

            response = self._make_request(messages)
            response_content = response['choices'][0]['message']['content']

            return {
                "success": True,
                "analysis": response_content
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _build_process_analysis_prompt(self, process_data: Dict[str, Any]) -> str:
        process_list = process_data.get("process_list", [])

        # 构建进程信息文本
        process_info = ""
        for process in process_list:
            process_info += f"PID: {process.get('pid', 'N/A')}\n"
            process_info += f"PPID: {process.get('ppid', 'N/A')}\n"
            process_info += f"UID: {process.get('uid', 'N/A')}\n"
            process_info += f"GID: {process.get('gid', 'N/A')}\n"
            process_info += f"用户: {process.get('user', 'N/A')}\n"
            process_info += f"命令行: {process.get('cmd', 'N/A')}\n"
            process_info += f"状态: {process.get('state', 'N/A')}\n"

            if process.get('exe'):
                process_info += f"可执行文件: {process.get('exe')}\n"

            if process.get('cwd'):
                process_info += f"工作目录: {process.get('cwd')}\n"

            if process.get('capability'):
                process_info += f"能力信息: {process.get('capability')}\n"

            if process.get('environ'):
                process_info += f"环境变量:\n{process.get('environ')}\n"

            if process.get('memory_maps'):
                process_info += f"内存映射:\n{process.get('memory_maps')}\n"

            process_info += "\n" + "-" * 50 + "\n\n"

        # 构建完整提示词
        prompt = f"""
我正在进行安全测试，需要分析以下进程信息，请帮我提供专业的安全测试建议。

## 进程信息
{process_info}

请从以下几个方面进行分析：

1. 安全风险评估：识别这些进程可能存在的安全风险，如特权提升、敏感文件访问等
2. 漏洞利用可能性：分析是否存在可能被利用的漏洞，如版本过旧、配置不当等
3. 安全测试建议：提供具体的测试步骤和方法，帮助验证潜在风险
4. 最佳实践建议：提供改进安全性的建议

请尽可能详细地分析，并给出具体的测试命令或工具推荐。
"""
        return prompt

    def _get_system_prompt(self) -> str:
        return """
你是一位专业的安全测试专家，擅长分析进程信息并识别潜在的安全风险。
你的回答应该：
1. 专业、准确，基于提供的进程信息进行分析
2. 提供具体的安全测试建议，包括可执行的命令或工具
3. 结构清晰，分类明确
4. 重点关注特权进程、敏感文件访问、网络监听、异常行为等安全相关问题
5. 使用markdown格式组织回答，便于阅读

请确保你的建议具有实用性和可操作性，能够帮助用户验证和解决潜在的安全问题。
"""
