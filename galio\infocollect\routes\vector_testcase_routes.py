from flask import Blueprint, jsonify, request
from services.vector_testcase_service import VectorTestcaseService
import logging

logger = logging.getLogger(__name__)

bp = Blueprint('vector_testcase', __name__)


@bp.route('/sync', methods=['POST'])
def sync_testcases_to_vector():
    """Sync testcases to vector database"""
    try:
        data = request.get_json() or {}
        batch_size = data.get('batch_size', 100)
        clear_before_sync = data.get('clear_before_sync', True)

        vector_service = VectorTestcaseService()
        sync_result = vector_service.sync_testcases_to_vector_db(batch_size, clear_before_sync)
        
        if sync_result['success']:
            return jsonify({
                'status': 'success',
                'message': sync_result['message'],
                'data': sync_result
            })
        else:
            return jsonify({
                'status': 'error',
                'message': sync_result.get('error', 'Sync failed')
            }), 500
        
    except Exception as e:
        logger.error(f"Sync testcases failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Sync failed: {str(e)}'
        }), 500


@bp.route('/search', methods=['POST'])
def search_testcases_by_query():
    """Search testcases with natural language"""
    try:
        data = request.get_json()
        if not data or not data.get('query'):
            return jsonify({
                'status': 'error',
                'message': 'Missing query parameter'
            }), 400
        
        query = data.get('query')
        top_k = data.get('top_k', 10)
        score_threshold = data.get('score_threshold', 0.5)
        
        vector_service = VectorTestcaseService()
        results = vector_service.search_similar_testcases(
            query=query,
            top_k=top_k,
            score_threshold=score_threshold
        )
        
        return jsonify({
            'status': 'success',
            'query': query,
            'total': len(results),
            'results': results
        })
        
    except Exception as e:
        logger.error(f"Search testcases failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Search failed: {str(e)}'
        }), 500


@bp.route('/insert', methods=['POST'])
def insert_testcase_to_vector():
    """Insert testcase to vector database"""
    try:
        data = request.get_json()
        if not data or not data.get('testcase_data'):
            return jsonify({
                'status': 'error',
                'message': 'Missing testcase data'
            }), 400
        
        testcase_data = data.get('testcase_data')
        
        vector_service = VectorTestcaseService()
        result = vector_service.insert_testcase(testcase_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Insert testcase failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Insert failed: {str(e)}'
        }), 500


@bp.route('/batch_insert', methods=['POST'])
def batch_insert_testcases_to_vector():
    """Batch insert testcases to vector database"""
    try:
        data = request.get_json()
        if not data or not data.get('testcases'):
            return jsonify({
                'status': 'error',
                'message': 'Missing testcase data'
            }), 400
        
        testcases = data.get('testcases')
        batch_size = data.get('batch_size', 100)
        
        vector_service = VectorTestcaseService()
        result = vector_service.batch_insert_testcases(testcases, batch_size)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Batch insert testcases failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Batch insert failed: {str(e)}'
        }), 500


@bp.route('/update', methods=['PUT'])
def update_testcase_in_vector():
    """Update testcase in vector database"""
    try:
        data = request.get_json()
        if not data or not data.get('testcase_data'):
            return jsonify({
                'status': 'error',
                'message': 'Missing testcase data'
            }), 400
        
        testcase_data = data.get('testcase_data')
        vector_service = VectorTestcaseService()
        
        success = vector_service.update_testcase(testcase_data)
        
        if success:
            return jsonify({
                'status': 'success',
                'message': 'Testcase updated successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Testcase update failed'
            }), 500
        
    except Exception as e:
        logger.error(f"Update testcase failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Update failed: {str(e)}'
        }), 500


@bp.route('/delete', methods=['DELETE'])
def delete_testcases_from_vector():
    """Delete testcases from vector database"""
    try:
        data = request.get_json()
        if not data or not data.get('testcase_ids'):
            return jsonify({
                'status': 'error',
                'message': 'Missing testcase IDs'
            }), 400
        
        testcase_ids = data.get('testcase_ids')
        if not isinstance(testcase_ids, list):
            testcase_ids = [testcase_ids]
        
        vector_service = VectorTestcaseService()
        success = vector_service.delete_testcases([str(id) for id in testcase_ids])
        
        if success:
            return jsonify({
                'status': 'success',
                'message': f'Successfully deleted {len(testcase_ids)} testcases'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Delete testcases failed'
            }), 500
        
    except Exception as e:
        logger.error(f"Delete testcases failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Delete failed: {str(e)}'
        }), 500


@bp.route('/status', methods=['GET'])
def get_vector_db_status():
    """Get vector database status"""
    try:
        vector_service = VectorTestcaseService()
        status = vector_service.get_vector_db_status()
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Get vector db status failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Get status failed: {str(e)}'
        }), 500


@bp.route('/health', methods=['GET'])
def check_vector_service_health():
    """Check vector service health"""
    try:
        vector_service = VectorTestcaseService()
        is_healthy = vector_service.check_milvus_service_health()
        
        if is_healthy:
            return jsonify({
                'status': 'healthy',
                'message': 'Vector service is running normally'
            })
        else:
            return jsonify({
                'status': 'unhealthy',
                'message': 'Vector service is not available'
            }), 503
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Health check failed: {str(e)}'
        }), 500


@bp.route('/async_sync', methods=['POST'])
def async_sync_testcases():
    """Async sync testcases to vector database"""
    try:
        data = request.get_json() or {}
        batch_size = data.get('batch_size', 100)
        clear_before_sync = data.get('clear_before_sync', True)

        vector_service = VectorTestcaseService()
        vector_service.async_sync_testcases(batch_size, clear_before_sync)
        
        return jsonify({
            'status': 'success',
            'message': 'Async sync task started'
        })
        
    except Exception as e:
        logger.error(f"Start async sync failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Start async sync failed: {str(e)}'
        }), 500


@bp.route('/init', methods=['POST'])
def init_vector_collection():
    """Initialize vector collection (create if not exists)"""
    try:
        vector_service = VectorTestcaseService()
        success = vector_service.ensure_collection_exists()

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Vector collection initialized successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to initialize vector collection'
            }), 500

    except Exception as e:
        logger.error(f"Initialize vector collection failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Initialize failed: {str(e)}'
        }), 500


@bp.route('/clear', methods=['POST'])
def clear_vector_collection():
    """Clear all data in vector collection"""
    try:
        vector_service = VectorTestcaseService()
        success = vector_service.clear_vector_collection()

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Vector collection cleared successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to clear vector collection'
            }), 500

    except Exception as e:
        logger.error(f"Clear vector collection failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Clear failed: {str(e)}'
        }), 500


@bp.route('/recommendations', methods=['POST'])
def get_testcase_recommendations():
    """Get testcase recommendations"""
    try:
        data = request.get_json()
        if not data or not data.get('query'):
            return jsonify({
                'status': 'error',
                'message': 'Missing query parameter'
            }), 400

        query = data.get('query')
        top_k = data.get('top_k', 5)

        vector_service = VectorTestcaseService()
        recommendations = vector_service.get_testcase_recommendations(query, top_k)

        return jsonify({
            'status': 'success',
            'query': query,
            'total': len(recommendations),
            'recommendations': recommendations
        })

    except Exception as e:
        logger.error(f"Get recommendations failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Get recommendations failed: {str(e)}'
        }), 500


@bp.route('/search_with_details', methods=['POST'])
def search_testcases_with_details():
    """Search testcases with natural language and return complete testcase details"""
    try:
        data = request.get_json()
        if not data or not data.get('query'):
            return jsonify({
                'status': 'error',
                'message': 'Missing query parameter'
            }), 400

        query = data.get('query')
        top_k = data.get('top_k', 10)
        score_threshold = data.get('score_threshold', 0.5)

        vector_service = VectorTestcaseService()
        results = vector_service.search_testcases_with_complete_details(
            query=query,
            top_k=top_k,
            score_threshold=score_threshold
        )

        return jsonify({
            'status': 'success',
            'query': query,
            'total': len(results),
            'results': results
        })

    except Exception as e:
        logger.error(f"Search testcases with details failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Search failed: {str(e)}'
        }), 500