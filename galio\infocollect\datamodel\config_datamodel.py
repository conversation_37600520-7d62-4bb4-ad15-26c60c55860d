from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class HostConfig(Base):
    __tablename__ = 'host_config'

    id = Column(Integer, primary_key=True)
    host_name = Column(String(255), unique=False)
    ip = Column(String(15), unique=True)
    ssh_port = Column(Integer, default=22)
    login_user = Column(String(50))
    login_pwd = Column(String(255))
    switch_root_cmd = Column(String(255), default='su - root')
    switch_root_pwd = Column(String(255))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    process_snapshot = relationship("ProcessSnapshot", back_populates="host")
    os_package_snapshot = relationship("OsPackageSnapshot", back_populates="host")
    hardware_snapshot = relationship("HardwareSnapshot", back_populates="host")
    filesystem_snapshot = relationship("FilesystemSnapshot", back_populates="host")

    # Kubernetes Relationships
    k8s_api_server = relationship("K8sAPIServer", back_populates="host")
    k8s_ingress = relationship("K8sIngress", back_populates="host")
    k8s_gateway = relationship("K8sGateway", back_populates="host")
    k8s_virtualservice = relationship("K8sVirtualService", back_populates="host")
    k8s_service = relationship("K8sService", back_populates="host")
    k8s_pod = relationship("K8sPod", back_populates="host")
    k8s_node = relationship("K8sNode", back_populates="host")
    k8s_network_policy = relationship("K8sNetworkPolicy", back_populates="host")
    k8s_secret = relationship("K8sSecret", back_populates="host")
    k8s_configmap = relationship("K8sConfigMap", back_populates="host")
    k8s_role = relationship("K8sRole", back_populates="host")
    k8s_role_binding = relationship("K8sRoleBinding", back_populates="host")
    k8s_cluster_role = relationship("K8sClusterRole", back_populates="host")
    k8s_cluster_role_binding = relationship("K8sClusterRoleBinding", back_populates="host")
    k8s_serviceaccount_permissions = relationship("K8sServiceAccountPermission", back_populates="host")

    # Docker Relationships
    docker_host_config = relationship("DockerHostConfig", back_populates="host", uselist=False)
    docker_containers = relationship("DockerContainer", back_populates="host")
    docker_networks = relationship("DockerNetwork", back_populates="host")

    # Crictl Relationships
    crictl_host_config = relationship("CrictlHostConfig", back_populates="host", cascade="all, delete-orphan")
    crictl_pods = relationship("CrictlPod", back_populates="host", cascade="all, delete-orphan")
    crictl_containers = relationship("CrictlContainer", back_populates="host", cascade="all, delete-orphan")

    tcp_port_snapshot = relationship("TcpPortSnapshot", back_populates="host")
    udp_port_snapshot = relationship("UdpPortSnapshot", back_populates="host")
    unix_socket_snapshot = relationship("UnixSocketSnapshot", back_populates="host")

    # Agent Log Relationship
    agent_logs = relationship("AgentLog", back_populates="host")

    def to_dict(self):
        return {
            'id': self.id,
            "host_name": self.host_name,
            "ip": self.ip,
            "ssh_port": self.ssh_port,
            "login_user": self.login_user,
            "login_pwd": self.login_pwd,
            "switch_root_cmd": self.switch_root_cmd,
            "switch_root_pwd": self.switch_root_pwd
        }
