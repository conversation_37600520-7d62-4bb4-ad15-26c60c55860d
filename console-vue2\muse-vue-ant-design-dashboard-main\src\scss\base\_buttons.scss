// 按钮基础样式
.ant-btn {
  // 基础样式
  display: inline-flex;
  align-items: center;
  justify-content: center;

  svg, .anticon {
    display: inline-flex;
    align-items: center;
    margin-right: 5px;
  }

  &.ant-btn-icon-only {
    svg, .anticon {
      margin-right: 0;
    }
  }

  > .anticon + span {
    margin-left: 0;
  }

  > span + .anticon {
    margin-left: 5px;
  }

  // 圆形按钮
  &.ant-btn-circle, &.ant-btn-circle-outline {
    border-radius: 50%;
  }

  // FAB 按钮
  &.fab {
    width: 50px;
    height: 50px;
    position: fixed;
    bottom: 30px;
    right: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  // 带有bg-*类的按钮样式
  &[class*="bg-"] {
    color: white;
    
    &[disabled], &[disabled]:hover {
      color: rgba(0, 0, 0, 0.5) !important;
      opacity: 0.65;
      box-shadow: none;
    }
  }
  
  // 所有按钮的通用禁用样式
  &[disabled], &[disabled]:hover {
    color: rgba(0, 0, 0, 0.5) !important;
    text-shadow: none;
    box-shadow: none;
  }
}

// 导航风格按钮
.nav-style-button, .ant-btn.nav-style-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  color: #1890ff;
  background: #f5f5f5;
  border: 1px solid transparent;
  border-radius: 20px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  line-height: 1;
  height: 32px;

  .anticon {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    color: #1890ff;
  }

  &:hover {
    background: #e8e8e8;
    color: #1890ff;
    border-color: transparent;
  }

  &:active, &.active {
    background: #d9d9d9;
    color: #1890ff;
  }

  &:disabled {
    background: #f5f5f5;
    color: rgba(0, 0, 0, 0.25);
    opacity: 0.7;
    cursor: not-allowed;

    .anticon {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  svg, .anticon {
    margin-right: 6px;
  }

  &.ant-btn-icon-only {
    padding: 6px;
    svg, .anticon {
      margin-right: 0;
    }
  }
}
