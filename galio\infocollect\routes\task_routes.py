from flask import Blueprint, request, jsonify
from services.task_service import TaskService, active_tasks
from routes.database_wraps import with_database_session

bp = Blueprint('task', __name__)


@bp.route('/collect', methods=['POST'])
@with_database_session
def collect_task(db):
    """
    Starts a new collection task on the selected nodes.
    Expects a JSON body with:
    - targets: list of IP addresses
    - proxy_ip: string
    - dbFile: string (path to database file)
    """
    data = request.get_json()
    if not data or "targets" not in data or "proxy_ip" not in data or "dbFile" not in data:
        return jsonify({"error": "Missing targets, proxy_ip, or dbFile parameter"}), 400

    targets = data.get("targets")
    proxy_ip = data.get("proxy_ip")
    db_file = data.get("dbFile")

    if not isinstance(targets, list) or not targets:
        return jsonify({"error": "Targets must be a non-empty list"}), 400

    task_service = TaskService(db)
    try:
        task_id = task_service.start_collect_task(targets, proxy_ip)
        return jsonify({"task_id": task_id}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/<task_id>', methods=['GET'])
def get_task(task_id):
    """
    Returns the current progress/status of the task.
    """
    task = active_tasks.get(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404
    return jsonify(task), 200
