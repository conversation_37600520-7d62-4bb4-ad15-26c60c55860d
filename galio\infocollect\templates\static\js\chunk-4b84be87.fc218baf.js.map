{"version": 3, "sources": ["webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?cbac", "webpack:///./src/views/SmartOrchestration.vue", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue", "webpack:///src/components/Cards/SmartOrchestrationInfo.vue", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?d925", "webpack:///./src/components/Cards/SmartOrchestrationInfo.vue?3240", "webpack:///src/views/SmartOrchestration.vue", "webpack:///./src/views/SmartOrchestration.vue?499c", "webpack:///./src/views/SmartOrchestration.vue?cc22"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "scopedSlots", "_u", "key", "fn", "_v", "proxy", "analyzing", "hasNodeData", "on", "showAnalysisModal", "availableDataTypes", "length", "join", "analysisResults", "model", "value", "activeKeys", "callback", "$$v", "expression", "_l", "result", "index", "info_type", "toUpperCase", "status", "getStatusColor", "_s", "getStatusText", "query_text", "matched_testcases", "testcaseColumns", "column", "record", "Testcase_Name", "truncateText", "_e", "Testcase_TestSteps", "execution_results", "executionColumns", "expandedRowRender", "testcase_name", "startAnalysis", "analysisModalVisible", "staticStyle", "selectedNodeId", "availableNodes", "node", "id", "name", "ip", "selectedAnalysisTypes", "type", "getTypeName", "components", "BranchesOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "data", "title", "dataIndex", "width", "ellipsis", "computed", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "methods", "response", "$http", "get", "error", "console", "dataTypes", "available", "for<PERSON>ach", "dataKey", "localStorage", "getItem", "push", "message", "warning", "infoType", "collectedData", "getCollectedData", "post", "node_id", "collected_data", "map", "_", "toString", "success", "JSON", "parse", "typeNames", "colors", "texts", "text", "max<PERSON><PERSON><PERSON>", "substring", "outputs", "output", "command", "exit_code", "component", "SmartOrchestrationInfo"], "mappings": "uIAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,2BAA2B,IAAI,IAAI,IAEnNI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,sBAAsBD,MAAM,CAAC,UAAW,GAAOG,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIW,GAAG,gBAAgBC,OAAM,GAAM,CAACH,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,QAAUJ,EAAIa,UAAU,UAAYb,EAAIc,aAAaC,GAAG,CAAC,MAAQf,EAAIgB,mBAAmBT,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACR,EAAG,sBAAsBU,OAAM,MAAS,CAACZ,EAAIW,GAAG,gBAAgBC,OAAM,MAAS,CAACV,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAAGJ,EAAIc,YAA8JZ,EAAG,UAAU,CAACG,YAAY,QAAQD,MAAM,CAAC,QAAU,UAAU,YAAc,QAAQJ,EAAIiB,mBAAmBC,iBAAiBlB,EAAIiB,mBAAmBE,KAAK,OAAO,KAAO,UAAU,YAAY,MAA5UjB,EAAG,UAAU,CAACG,YAAY,QAAQD,MAAM,CAAC,QAAU,WAAW,YAAc,qCAAqC,KAAO,OAAO,YAAY,OAAwM,IAAI,GAAIJ,EAAIoB,gBAAgBF,OAAS,EAAGhB,EAAG,MAAM,CAACA,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,SAAS,CAACJ,EAAIW,GAAG,UAAUT,EAAG,aAAa,CAACG,YAAY,QAAQgB,MAAM,CAACC,MAAOtB,EAAIuB,WAAYC,SAAS,SAAUC,GAAMzB,EAAIuB,WAAWE,GAAKC,WAAW,eAAe1B,EAAI2B,GAAI3B,EAAIoB,iBAAiB,SAASQ,EAAOC,GAAO,OAAO3B,EAAG,mBAAmB,CAACO,IAAIoB,EAAMzB,MAAM,CAAC,OAAS,GAAGwB,EAAOE,UAAUC,wBAA0C,YAAlBH,EAAOI,OAAuB,KAAyB,YAAlBJ,EAAOI,OAAuB,KAAO,QAAQzB,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIiC,eAAeL,EAAOI,UAAU,CAAChC,EAAIW,GAAG,IAAIX,EAAIkC,GAAGlC,EAAImC,cAAcP,EAAOI,SAAS,SAASpB,OAAM,IAAO,MAAK,IAAO,CAACV,EAAG,iBAAiB,CAACG,YAAY,QAAQD,MAAM,CAAC,MAAQ,OAAO,OAAS,EAAE,KAAO,UAAU,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAGX,EAAIkC,GAAGN,EAAOE,cAAc5B,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIW,GAAGX,EAAIkC,GAAGN,EAAOQ,eAAelC,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIW,GAAGX,EAAIkC,GAAGN,EAAOS,kBAAkBnB,YAAY,GAAGhB,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAO,qBAAqB,MAAM,CAACJ,EAAIW,GAAG,aAAaT,EAAG,UAAU,CAACG,YAAY,QAAQD,MAAM,CAAC,WAAawB,EAAOS,kBAAkB,QAAUrC,EAAIsC,gBAAgB,YAAa,EAAM,KAAO,SAAS/B,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAE6B,EAAM,OAAEC,IAAU,MAAO,CAAiB,kBAAfD,EAAO9B,IAAyB,CAACP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQoC,EAAOC,gBAAgB,CAACvC,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIkC,GAAGlC,EAAI0C,aAAaF,EAAOC,cAAe,WAAWzC,EAAI2C,KAAqB,uBAAfJ,EAAO9B,IAA8B,CAACP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQoC,EAAOI,qBAAqB,CAAC1C,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIkC,GAAGlC,EAAI0C,aAAaF,EAAOI,mBAAoB,WAAW5C,EAAI2C,SAAS,MAAK,KAAQzC,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAO,qBAAqB,MAAM,CAACJ,EAAIW,GAAG,UAAUT,EAAG,UAAU,CAACE,MAAM,CAAC,WAAawB,EAAOiB,kBAAkB,QAAU7C,EAAI8C,iBAAiB,YAAa,EAAM,KAAO,QAAQ,WAAa,CAAEC,kBAAmB/C,EAAI+C,oBAAqBxC,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAE6B,EAAM,OAAEC,IAAU,MAAO,CAAiB,WAAfD,EAAO9B,IAAkB,CAACP,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIiC,eAAeO,EAAOR,UAAU,CAAChC,EAAIW,GAAG,IAAIX,EAAIkC,GAAGlC,EAAImC,cAAcK,EAAOR,SAAS,QAAQhC,EAAI2C,KAAqB,kBAAfJ,EAAO9B,IAAyB,CAACP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQoC,EAAOQ,gBAAgB,CAAC9C,EAAG,OAAO,CAACF,EAAIW,GAAGX,EAAIkC,GAAGlC,EAAI0C,aAAaF,EAAOQ,cAAe,WAAWhD,EAAI2C,SAAS,MAAK,MAAS,MAAK,IAAI,GAAG3C,EAAI2C,KAAKzC,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,aAAa,MAAQ,IAAI,eAAiBJ,EAAIa,WAAWE,GAAG,CAAC,GAAKf,EAAIiD,eAAe5B,MAAM,CAACC,MAAOtB,EAAIkD,qBAAsB1B,SAAS,SAAUC,GAAMzB,EAAIkD,qBAAqBzB,GAAKC,WAAW,yBAAyB,CAACxB,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,aAAa,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,SAAW,KAAK,CAACF,EAAG,WAAW,CAACiD,YAAY,CAAC,MAAQ,QAAQ/C,MAAM,CAAC,YAAc,aAAaiB,MAAM,CAACC,MAAOtB,EAAIoD,eAAgB5B,SAAS,SAAUC,GAAMzB,EAAIoD,eAAe3B,GAAKC,WAAW,mBAAmB1B,EAAI2B,GAAI3B,EAAIqD,gBAAgB,SAASC,GAAM,OAAOpD,EAAG,kBAAkB,CAACO,IAAI6C,EAAKC,GAAGnD,MAAM,CAAC,MAAQkD,EAAKC,KAAK,CAACvD,EAAIW,GAAG,IAAIX,EAAIkC,GAAGoB,EAAKE,MAAM,KAAKxD,EAAIkC,GAAGoB,EAAKG,IAAI,WAAU,IAAI,GAAGvD,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,SAAW,KAAK,CAACF,EAAG,mBAAmB,CAACmB,MAAM,CAACC,MAAOtB,EAAI0D,sBAAuBlC,SAAS,SAAUC,GAAMzB,EAAI0D,sBAAsBjC,GAAKC,WAAW,0BAA0B,CAACxB,EAAG,QAAQF,EAAI2B,GAAI3B,EAAIiB,oBAAoB,SAAS0C,GAAM,OAAOzD,EAAG,QAAQ,CAACO,IAAIkD,EAAKvD,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQuD,IAAO,CAAC3D,EAAIW,GAAGX,EAAIkC,GAAGlC,EAAI4D,YAAYD,QAAW,MAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAE/gJrD,EAAkB,G,8CC+JP,GACfkD,KAAA,0BACAK,WAAA,CACAC,uCACAC,6CACAC,yDACAC,8CAEAC,OACA,OACArD,WAAA,EACAqC,sBAAA,EACAE,eAAA,KACAM,sBAAA,GACAtC,gBAAA,GACAG,WAAA,MACA8B,eAAA,GACApC,mBAAA,GAEAqB,gBAAA,CACA,CACA6B,MAAA,OACAC,UAAA,kBACA3D,IAAA,kBACA4D,MAAA,KAEA,CACAF,MAAA,OACAC,UAAA,gBACA3D,IAAA,gBACA6D,UAAA,GAEA,CACAH,MAAA,OACAC,UAAA,iBACA3D,IAAA,iBACA4D,MAAA,IAEA,CACAF,MAAA,OACAC,UAAA,qBACA3D,IAAA,qBACA6D,UAAA,IAIAxB,iBAAA,CACA,CACAqB,MAAA,OACAC,UAAA,kBACA3D,IAAA,kBACA4D,MAAA,KAEA,CACAF,MAAA,OACAC,UAAA,gBACA3D,IAAA,gBACA6D,UAAA,GAEA,CACAH,MAAA,OACAC,UAAA,SACA3D,IAAA,SACA4D,MAAA,KAEA,CACAF,MAAA,OACAC,UAAA,UACA3D,IAAA,UACA6D,UAAA,MAMAC,SAAA,CACAzD,cACA,YAAAuC,eAAAnC,OAAA,QAAAD,mBAAAC,OAAA,IAIAsD,UACA,KAAAC,qBACA,KAAAC,4BAGAC,QAAA,CACA,2BACA,IACA,MAAAC,QAAA,KAAAC,MAAAC,IAAA,mBACAF,EAAAV,MAAAU,EAAAV,KAAAhD,OAAA,IACA,KAAAmC,eAAAuB,EAAAV,MAEA,MAAAa,GACAC,QAAAD,MAAA,YAAAA,KAIAL,2BAEA,MAAAO,EAAA,2EACAC,EAAA,GAEAD,EAAAE,QAAAxB,IACA,MAAAyB,EAAAzB,EAAA,QACA0B,aAAAC,QAAAF,IACAF,EAAAK,KAAA5B,KAIA,KAAA1C,mBAAAiE,GAGAlE,oBACA,KAAAF,aAKA,KAAA4C,sBAAA,SAAAzC,oBACA,KAAAiC,sBAAA,EAEA,SAAAG,eAAAnC,SACA,KAAAkC,eAAA,KAAAC,eAAA,GAAAE,KARAiC,OAAAC,QAAA,aAYA,sBACA,QAAArC,eAKA,YAAAM,sBAAAxC,OAAA,CAKA,KAAAL,WAAA,EACA,KAAAO,gBAAA,GAEA,IAEA,UAAAsE,KAAA,KAAAhC,sBAAA,CACA,MAAAiC,EAAA,KAAAC,iBAAAF,GAEA,GAAAC,EAAA,CACA,MAAAf,QAAA,KAAAC,MAAAgB,KAAA,wCACAC,QAAA,KAAA1C,eACAtB,UAAA4D,EACAK,eAAAJ,IAGA,KAAAvE,gBAAAmE,KAAAX,EAAAV,OAIA,KAAAhB,sBAAA,EACA,KAAA3B,WAAA,KAAAH,gBAAA4E,IAAA,CAAAC,EAAApE,MAAAqE,YAEAV,OAAAW,QAAA,YAAA/E,gBAAAF,qBAEA,MAAA6D,GACAC,QAAAD,MAAA,QAAAA,GACAS,OAAAT,MAAA,aACA,QACA,KAAAlE,WAAA,QAhCA2E,OAAAT,MAAA,gBALAS,OAAAT,MAAA,UAyCAa,iBAAAF,GACA,MAAAN,EAAAM,EAAA,QACAxB,EAAAmB,aAAAC,QAAAF,GACA,OAAAlB,EAAAkC,KAAAC,MAAAnC,GAAA,MAGAN,YAAAD,GACA,MAAA2C,EAAA,CACA,eACA,gBACA,gBACA,oBACA,YACA,kBACA,2BAEA,OAAAA,EAAA3C,OAGA1B,eAAAD,GACA,MAAAuE,EAAA,CACA,gBACA,iBACA,iBACA,aACA,YACA,aAEA,OAAAA,EAAAvE,IAAA,WAGAG,cAAAH,GACA,MAAAwE,EAAA,CACA,aACA,eACA,aACA,YACA,WACA,WAEA,OAAAA,EAAAxE,OAGAU,aAAA+D,EAAAC,GACA,OAAAD,EACAA,EAAAvF,OAAAwF,EAAAD,EAAAE,UAAA,EAAAD,GAAA,MAAAD,EADA,IAIA1D,kBAAAP,GACA,OAAAA,EAAAoE,SAAA,IAAApE,EAAAoE,QAAA1F,OAIA,kFAGAsB,EAAAoE,QAAAZ,IAAA,CAAAa,EAAAhF,IAAA,8IAEAA,EAAA,qBAAAgF,EAAAC,kFACA,IAAAD,EAAAE,UAAA,kBAAAF,EAAAE,uCACAF,SAAA,yHAAAA,qBAAA,qBACAA,EAAA9B,MAAA,qIAAA8B,EAAA9B,kBAAA,sCAEA5D,KAAA,8BAbA,WC7X8W,I,wBCQ1W6F,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCLA,GACfnD,WAAA,CACAoD,2BChB2V,ICOvV,EAAY,eACd,EACAlH,EACAO,GACA,EACA,KACA,KACA,MAIa,e", "file": "static/js/chunk-4b84be87.fc218baf.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=style&index=0&id=430709ea&prod&scoped=true&lang=css\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('SmartOrchestrationInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(\"智能测试用例分析\")])]},proxy:true},{key:\"extra\",fn:function(){return [_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.analyzing,\"disabled\":!_vm.hasNodeData},on:{\"click\":_vm.showAnalysisModal},scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('BranchesOutlined')]},proxy:true}])},[_vm._v(\" 开始智能分析 \")])]},proxy:true}])},[_c('a-row',{staticClass:\"mb-16\",attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":24}},[(!_vm.hasNodeData)?_c('a-alert',{staticClass:\"mb-16\",attrs:{\"message\":\"未检测到节点数据\",\"description\":\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\",\"type\":\"info\",\"show-icon\":\"\"}}):_c('a-alert',{staticClass:\"mb-16\",attrs:{\"message\":\"节点数据已就绪\",\"description\":`已检测到 ${_vm.availableDataTypes.length} 种类型的数据：${_vm.availableDataTypes.join('、')}`,\"type\":\"success\",\"show-icon\":\"\"}})],1)],1),(_vm.analysisResults.length > 0)?_c('div',[_c('a-divider',{attrs:{\"orientation\":\"left\"}},[_vm._v(\"分析结果\")]),_c('a-collapse',{staticClass:\"mb-16\",model:{value:(_vm.activeKeys),callback:function ($$v) {_vm.activeKeys=$$v},expression:\"activeKeys\"}},_vm._l((_vm.analysisResults),function(result,index){return _c('a-collapse-panel',{key:index,attrs:{\"header\":`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(result.status)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(result.status))+\" \")])]},proxy:true}],null,true)},[_c('a-descriptions',{staticClass:\"mb-16\",attrs:{\"title\":\"查询信息\",\"column\":1,\"size\":\"small\"}},[_c('a-descriptions-item',{attrs:{\"label\":\"信息类型\"}},[_vm._v(_vm._s(result.info_type))]),_c('a-descriptions-item',{attrs:{\"label\":\"查询文本\"}},[_vm._v(_vm._s(result.query_text))]),_c('a-descriptions-item',{attrs:{\"label\":\"匹配用例数\"}},[_vm._v(_vm._s(result.matched_testcases.length))])],1),_c('a-divider',{attrs:{\"orientation\":\"left\",\"orientation-margin\":\"0\"}},[_vm._v(\"匹配的测试用例\")]),_c('a-table',{staticClass:\"mb-16\",attrs:{\"dataSource\":result.matched_testcases,\"columns\":_vm.testcaseColumns,\"pagination\":false,\"size\":\"small\"},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'Testcase_Name')?[_c('a-tooltip',{attrs:{\"title\":record.Testcase_Name}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.Testcase_Name, 30)))])])]:_vm._e(),(column.key === 'Testcase_TestSteps')?[_c('a-tooltip',{attrs:{\"title\":record.Testcase_TestSteps}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.Testcase_TestSteps, 50)))])])]:_vm._e()]}}],null,true)}),_c('a-divider',{attrs:{\"orientation\":\"left\",\"orientation-margin\":\"0\"}},[_vm._v(\"执行结果\")]),_c('a-table',{attrs:{\"dataSource\":result.execution_results,\"columns\":_vm.executionColumns,\"pagination\":false,\"size\":\"small\",\"expandable\":{ expandedRowRender: _vm.expandedRowRender }},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'status')?[_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.status)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.status))+\" \")])]:_vm._e(),(column.key === 'testcase_name')?[_c('a-tooltip',{attrs:{\"title\":record.testcase_name}},[_c('span',[_vm._v(_vm._s(_vm.truncateText(record.testcase_name, 30)))])])]:_vm._e()]}}],null,true)})],1)}),1)],1):_vm._e(),_c('a-modal',{attrs:{\"title\":\"智能测试用例分析配置\",\"width\":800,\"confirmLoading\":_vm.analyzing},on:{\"ok\":_vm.startAnalysis},model:{value:(_vm.analysisModalVisible),callback:function ($$v) {_vm.analysisModalVisible=$$v},expression:\"analysisModalVisible\"}},[_c('a-form',{attrs:{\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":\"选择节点\",\"required\":\"\"}},[_c('a-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择要分析的节点\"},model:{value:(_vm.selectedNodeId),callback:function ($$v) {_vm.selectedNodeId=$$v},expression:\"selectedNodeId\"}},_vm._l((_vm.availableNodes),function(node){return _c('a-select-option',{key:node.id,attrs:{\"value\":node.id}},[_vm._v(\" \"+_vm._s(node.name)+\" (\"+_vm._s(node.ip)+\") \")])}),1)],1),_c('a-form-item',{attrs:{\"label\":\"选择分析类型\",\"required\":\"\"}},[_c('a-checkbox-group',{model:{value:(_vm.selectedAnalysisTypes),callback:function ($$v) {_vm.selectedAnalysisTypes=$$v},expression:\"selectedAnalysisTypes\"}},[_c('a-row',_vm._l((_vm.availableDataTypes),function(type){return _c('a-col',{key:type,attrs:{\"span\":8}},[_c('a-checkbox',{attrs:{\"value\":type}},[_vm._v(_vm._s(_vm.getTypeName(type)))])],1)}),1)],1)],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { \r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined \r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n      \r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n  },\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n      \r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n</style> ", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SmartOrchestrationInfo.vue?vue&type=template&id=430709ea&scoped=true\"\nimport script from \"./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"\nexport * from \"./SmartOrchestrationInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./SmartOrchestrationInfo.vue?vue&type=style&index=0&id=430709ea&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"430709ea\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<!-- 智能测试用例分析 -->\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<SmartOrchestrationInfo></SmartOrchestrationInfo>\r\n\t\t\t</a-col>\r\n\t\t</a-row>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport SmartOrchestrationInfo from \"@/components/Cards/SmartOrchestrationInfo\";\r\n\r\nexport default {\r\n    components: {\r\n        SmartOrchestrationInfo,\r\n    },\r\n};\r\n</script> ", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestration.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SmartOrchestration.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SmartOrchestration.vue?vue&type=template&id=57b6b0c4\"\nimport script from \"./SmartOrchestration.vue?vue&type=script&lang=js\"\nexport * from \"./SmartOrchestration.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}