export default {
  common: {
    home: 'Home',
    title: 'SecureTest Copilot',
    selectNode: 'Select Node',
    selectProject: 'Select Project',
    settings: 'Settings',
    notifications: 'Notifications',
    clearAll: 'Clear All',
    noNotifications: 'No Notifications',
    language: 'Language',
    configureNodes: 'Node Configuration',
    configureProxy: 'Proxy Configuration',
    detectReachableIps: 'Detect Reachable IPs',
    taskProgress: 'Task Progress',
    refresh: 'Refresh',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    selectedNodes: 'Selected {count} nodes',
    copiedToClipboard: 'Copied to clipboard',
    copyFailed: 'Copy failed',
    clear: 'Clear'
  },
  configuratorset:{
    sidebarColor: 'Sidebar Color',
    sidenavType: 'Sidenav Type',
    navbarFixed: 'Navbar Fixed',
    configurator: 'Configurator',
  },
  headTopic:{
    process: 'Process Table',
    package: 'Package Table',
    hardware: 'Hardware Table',
    mount: 'Mount Table',
    port: 'Network Ports & Sockets',
    docker: 'Docker Table',
    k8s: 'Kubernetes Table',
    fileUpload: 'File Upload',
    fileDownload: 'File Download',
    aiBash: 'Command Line',
    testcase: 'Test Case Management',
  },
  tool: {
    configureTool: 'Configure Tool',
    spiderTool: 'SSP',
    generalTool: 'GeneralTool',
    uploadToolPackage: 'Upload Tool Package (zip)',
    selectToolPackage: 'upload',
    editScript: 'Edit script',
    runSpider: 'start',
    editShellScript: 'Edit Shell',
    confirmScript: 'confirm',
    scriptReady: 'Script Ready',
    localSaveDirectory: 'Local Save Directory',
    viewResult: 'View Result',
    selectReachableIp: 'Select a reachable IP',
    columns: {
      hostName: 'Host Name',
      ip: 'IP',
      status: 'Status',
      progress: 'Progress',
      result: 'Result',
      errorDetails: 'Error Details',
      speed: 'Speed',
      fileSize: 'File Size'
    },
    status: {
      failed: 'Failed'
    }
  },
  sidebar: {
    taskPanel: 'Task Panel',
    infoCollection: 'Info Collection',
    processInfo: 'Process Info',
    packageInfo: 'Package Info',
    hardwareInfo: 'Hardware Info',
    filesystemInfo: 'Filesys Info',
    portInfo: 'Port Info',
    dockerInfo: 'Docker Info',
    k8sInfo: 'K8S Info',
    fileTransfer: 'File Transfer',
    fileUpload: 'File Upload',
    fileDown: 'File Down',
    aiBash: 'AI Bash',
    testCases: 'Test Cases',
    testCase: 'Test Case',
    smartAnalysis: 'Smart Analysis',
    smartOrchestration: 'Smart Orchestration',
    toolPanel: 'Tool Panel',
    hostConfig: 'Host Config',
    cbhConfig: 'CBH Config',
    repositoryConfig: 'Repo Config',
  },
  fileUpload: {
    selectFile: 'Select File',
    clickToSelect: 'Select File',
    uploadPath: 'Upload Path',
    enterUploadPath: 'Enter Upload Directory',
    startUpload: 'Start Upload',
    uploadProgress: 'Upload Progress',
    uploadResults: 'Upload Results'
  },
  fileDownload: {
    enterDownloadPath: 'Enter remote file path',
    startDownload: 'Start Download',
    downloadProgress: 'Download Progress',
  },
  hostConfig: {
    title: 'Host Configuration',
    addHost: 'Add Host',
    exportSelected: 'Export Selected',
    deleteSelected: 'Delete Selected',
    downloadTemplate: 'Download Tpl',
    uploadTemplate: 'Upload Tpl',
    actions: 'Actions',
    save: 'Save',
    edit: 'Edit',
    copy: 'Copy',
    cancel: 'Cancel',
    delete: 'Delete',
    columns: {
      hostName: 'Host Name',
      ipAddress: 'IP Address',
      sshPort: 'SSH Port',
      loginUser: 'Login User',
      loginPassword: 'Login Password',
      switchRootCmd: 'Switch root cmd',
      switchRootPwd: 'Switch root pwd'
    },
  },
  repositoryConfig: {
    title: 'Repository Configuration',
    addRepository: 'Add Repository',
    exportSelected: 'Export Selected',
    deleteSelected: 'Delete Selected',
    downloadSelected: 'Download Selected',
    downloadTemplate: 'Download Tpl',
    uploadTemplate: 'Upload Tpl',
    selectDownloadPath: 'Select Download Path',
    downloadProgress: 'Download Progress',
    actions: 'Actions',
    save: 'Save',
    edit: 'Edit',
    copy: 'Copy',
    cancel: 'Cancel',
    delete: 'Delete',
    columns: {
      microservice: 'Microservice',
      repositoryUrl: 'Repository URL',
      branchName: 'Branch Name'
    },
    validation: {
      invalidUrl: 'Invalid repository URL',
      unsupportedFormat: 'Unsupported repository format',
      missingBranch: 'Missing branch name',
      parseError: 'Parse failed'
    },
    download: {
      selectPath: 'Please select download path',
      downloading: 'Downloading...',
      starting: 'Starting download...',
      success: 'Download successful',
      failed: 'Download failed',
      partialSuccess: 'Partial download successful',
      cloneError: 'Git clone failed'
    }
  },
  repositoryDownload: {
    title: 'Repository Download Results',
    clear: 'Clear',
    total: 'Total',
    success: 'Success',
    failed: 'Failed',
    downloading: 'Downloading',
    pending: 'Pending',
    completed: 'Completed',
    progress: 'Progress'
  },
  log: {
    title: 'Log Viewer',
    viewLogs: 'View Logs',
    currentNode: 'Node',
    noNodeSelected: 'No node selected',
    selectLevel: 'Select Level',
    refresh: 'Refresh',
    noLogs: 'No logs available',
    fetchError: 'Failed to fetch logs',
  },
  testcase: {
    // Common
    title: 'Test Cases',
    management: 'Test Case Management',
    detail: 'Test Case Detail',
    search: 'Search Cases',
    searchPlaceholder: 'Enter search keywords',
    searchButton: 'Search',
    clearResults: 'Clear Results',
    noResults: 'No search results',

    // Table columns
    columns: {
      number: 'Case Number',
      name: 'Case Name',
      level: 'Case Level',
      similarity: 'Similarity',
      prepareCondition: 'Prepare Condition',
      testSteps: 'Test Steps',
      expectedResult: 'Expected Result',
      actions: 'Actions'
    },

    // Case levels
    levels: {
      level1: 'Level 1',
      level2: 'Level 2',
      level3: 'Level 3',
      level4: 'Level 4',
      high: 'High',
      medium: 'Medium',
      low: 'Low'
    },

    // Smart orchestration
    smartOrchestration: {
      title: 'Smart Orchestration',
      naturalLanguageQuery: 'Natural Language Query',
      queryPlaceholder: 'Enter your testing requirements, e.g.: cluster security testing',
      searchResults: 'Search Results',
      foundResults: 'Found {count} related test cases',
      searchParams: 'Search Parameters',
      topK: 'Top K Results',
      scoreThreshold: 'Score Threshold',
      searching: 'Searching...',
      searchSuccess: 'Search successful',
      searchFailed: 'Search failed',
      searchError: 'Error occurred during search',
      inputRequired: 'Please enter query content',
      resultsCleared: 'Search results cleared'
    },

    // Actions
    actions: {
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      copy: 'Copy',
      export: 'Export',
      import: 'Import',
      sync: 'Sync',
      refresh: 'Refresh'
    },

    // Status
    status: {
      loading: 'Loading...',
      loadFailed: 'Load failed',
      noData: 'No data',
      syncSuccess: 'Sync successful',
      syncFailed: 'Sync failed',
      detailLoadFailed: 'Failed to load test case details'
    }
  }
};