--DROP TABLE IF EXISTS tcp_port_snapshot;

CREATE TABLE IF NOT EXISTS tcp_port_snapshot (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    ip TEXT,
    port TEXT,
    pid TEXT,
    protocols TEXT,
    cipher_suites TEXT,
    certificate TEXT,
    vulnerabilities TEXT,
    http_info TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

-- UDP端口表
CREATE TABLE IF NOT EXISTS udp_port_snapshot (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    proto TEXT,
    recv_q TEXT,
    send_q TEXT,
    ip TEXT,
    port TEXT,
    foreign_address TEXT,
    state TEXT,
    pid_program TEXT,
    raw_data TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

-- UNIX Socket表
CREATE TABLE IF NOT EXISTS unix_socket_snapshot (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    proto TEXT,
    refcnt TEXT,
    flags TEXT,
    type TEXT,
    state TEXT,
    inode TEXT,
    pid_program TEXT,
    path TEXT,
    raw_data TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);
