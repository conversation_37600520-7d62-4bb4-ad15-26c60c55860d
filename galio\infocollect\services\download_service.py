import os
import base64
import json
from pathlib import Path
from log.logger import log_error
from datamodel.config_datamodel import HostConfig
from app.ssh.manager import SSHConnectionManager
import re
import uuid
import threading
import time
from typing import Dict
from db.init_db import get_db


class DownloadService:
    tasks: Dict[str, dict] = {}

    def __init__(self, db, download_dir):
        self.db = db
        self.db_file = None
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self._lock = threading.Lock()

    def _validate_path(self, path):
        if not path.startswith('/'):
            raise ValueError("Path must be absolute")

        dangerous_patterns = ['..', './', '~']
        if any(pattern in path for pattern in dangerous_patterns):
            raise ValueError("Path cannot contain ./, ../ or ~")

        if not re.match(r'^[/a-zA-Z0-9._-]+$', path):
            raise ValueError("Path contains invalid characters")

        return True

    def _get_node_config(self, ip):
        with get_db(self.db_file) as new_db:
            return new_db.query(HostConfig).filter_by(ip=ip).first()

    def _process_node_download(self, node_info, proxy_ip):
        """Process download for a single node using curl to download back"""
        try:
            node = self._get_node_config(node_info['ip'])
            if not node:
                error_msg = f"Node not found for IP: {node_info['ip']}"
                log_error(error_msg)
                raise Exception(error_msg)

            remote_path = node_info['path']
            if not remote_path:
                error_msg = f"Remote path is empty for node {node_info['ip']}"
                log_error(error_msg)
                raise Exception(error_msg)

            with SSHConnectionManager(node).connection() as ssh:
                # Validate path exists
                exit_code, _, stderr = ssh.execute_command(f"test -e '{remote_path}'")
                if exit_code != 0:
                    error_msg = f"Path does not exist on node {node_info['ip']}: {remote_path}"
                    log_error(error_msg)
                    raise Exception(error_msg)

                self.update_node_progress(node_info['task_id'], node.ip, 0, "downloading")

                timestamp = time.strftime("%Y%m%d_%H%M%S")
                temp_tar = f"/tmp/{node.host_name}_{node.ip}_files_{timestamp}.tar"
                local_filename = f"{node.host_name}_{node.ip}_files_{timestamp}.tar.gz"
                
                # 直接压缩指定路径到/tmp目录，不管是文件还是目录
                tar_cmd = f"tar -czf '{temp_tar}' '{remote_path}'"
                
                exit_code, _, error = ssh.execute_command(tar_cmd)
                if exit_code != 0:
                    raise Exception(f"Failed to create tar archive: {error}")

                download_file = temp_tar

                download_info = {
                    'host_ip': node.ip,
                    'host_name': node.host_name,
                    'local_filename': local_filename
                }
                encoded_info = base64.b64encode(json.dumps(download_info).encode()).decode()

                # Create curl command to download file back to Windows server
                curl_cmd = (
                    f"curl -F 'file=@{download_file}' "
                    f"'http://{proxy_ip}:9998/api/file_transfer/download/{encoded_info}'"
                )
                exit_code, output, error = ssh.execute_command(curl_cmd)
                if exit_code != 0:
                    raise Exception(f"download failed: {error}")

                # 清理临时tar文件
                ssh.execute_command(f"rm -f '{temp_tar}'")

                self.update_node_progress(
                    node_info['task_id'],
                    node.ip,
                    100,
                    "completed"
                )

        except Exception as e:
            error_detail = {
                "time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "type": type(e).__name__,
                "message": str(e)
            }
            self.update_node_progress(
                node_info['task_id'],
                node_info['ip'],
                0,
                "failed",
                error_detail
            )
            raise

    def _create_final_archive(self):
        return str(self.download_dir)

    def create_download_task(self, nodes: list, proxy_ip: str) -> str:
        """创建新的下载任务"""
        task_id = str(uuid.uuid4())

        # Get node information from database
        node_ips = [node['ip'] for node in nodes]
        db_nodes = self.db.query(HostConfig).filter(HostConfig.ip.in_(node_ips)).all()
        host_names = {node.ip: node.host_name for node in db_nodes}

        node_status = {}
        for node in nodes:
            node_status[node['ip']] = {
                "status": "pending",
                "progress": 0,
                "speed": "1 MB/s",
                "file_size": 1024 * 1024,
                "host_name": host_names.get(node['ip'], ''),
                "path": node['path'],
                "error_detail": None
            }

        with self._lock:
            self.tasks[task_id] = {
                "nodes": node_status,
                "proxy_ip": proxy_ip,
                "metadata": {
                    "start_time": time.time(),
                    "total_files": len(nodes),
                    "completed_files": 0
                },
                "db_file": self.db_file
            }

        return task_id

    def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                raise Exception("Task not found")
            # Return a copy of the task state to prevent concurrent modification
            return json.loads(json.dumps(task))

    def update_node_progress(self, task_id: str, node_ip: str, progress: int, 
                            status: str, error_detail: str = None):
        with self._lock:
            if task_id in self.tasks and node_ip in self.tasks[task_id]["nodes"]:
                node = self.tasks[task_id]["nodes"][node_ip]
                node["progress"] = progress
                node["speed"] = "1 MB/s"
                node["status"] = status
                node["file_size"] = 1024 * 1024
                node["error_detail"] = error_detail

    def start_download(self, task_id: str):
        """Start the download process for a task"""
        task = self.tasks.get(task_id)
        if not task:
            raise Exception("Task not found")

        def download_from_node(node_ip: str):
            task = self.tasks[task_id]
            db_file = task["db_file"]
            
            with get_db(db_file) as db:
                node_info = task["nodes"][node_ip]
                
                try:
                    node_data = {
                        'ip': node_ip,
                        'path': node_info['path'],
                        'task_id': task_id
                    }

                    self._process_node_download(node_data, task['proxy_ip'])
                    
                except Exception as e:
                    node_info["status"] = "failed"
                    node_info["error_detail"] = {
                        "time": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "type": type(e).__name__,
                        "message": str(e)
                    }
                    log_error(f"Failed to download from {node_ip}: {str(e)}")

        threads = []
        for node_ip in self.tasks[task_id]["nodes"].keys():
            thread = threading.Thread(target=download_from_node, args=(node_ip,))
            thread.start()
            threads.append(thread)

        def update_progress():
            try:
                while True:
                    task = self.tasks.get(task_id)
                    if not task:
                        break

                    all_completed = True
                    for node in task["nodes"].values():
                        if node["status"] in ["pending", "downloading"]:
                            all_completed = False
                            break

                    if all_completed:
                        break

                    time.sleep(1)
            finally:
                for thread in threads:
                    thread.join()

        progress_thread = threading.Thread(target=update_progress)
        progress_thread.start()

    def create_and_start_download(self, nodes: list, proxy_ip: str) -> str:
        """Create and start a new download task"""
        # Create the task first
        task_id = self.create_download_task(nodes, proxy_ip)
        
        # Start the download process
        self.start_download(task_id)
        
        return task_id
