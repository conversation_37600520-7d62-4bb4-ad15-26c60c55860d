from flask import Blueprint, jsonify
from services.filesystem_service import FilesystemService
from routes.database_wraps import with_database_session

bp = Blueprint('filesystem', __name__)


@bp.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_filesystem(node_ip, db):
    filesystem_service = FilesystemService(db)
    node = filesystem_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    result = filesystem_service.get_filesystem(node.id)
    if not result:
        return jsonify({"error": "No filesystem information found"}), 404

    return jsonify(result), 200
