/**
 * 通知混入 - 提供通用的通知处理逻辑
 * 用于任务完成、文件上传/下载完成等场景
 */
export default {
  methods: {
    /**
     * 添加任务完成通知
     * @param {Object} options - 通知选项
     * @param {string} options.taskId - 任务ID
     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)
     * @param {Array} options.nodes - 节点数组
     * @param {string} options.projectId - 项目ID
     * @param {Object} options.titles - 自定义标题 {success, error}
     * @param {Object} options.templates - 自定义消息模板 {success, error}
     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}
     */
    addTaskCompletionNotification({
      taskId,
      taskType,
      nodes,
      projectId,
      titles = {},
      templates = {},
      statusMapping = {}
    }) {
      // 检查是否已经发送过通知
      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;
      if (localStorage.getItem(notificationSentKey)) {
        return; // 已经发送过通知，不再重复发送
      }

      // 设置默认状态映射
      const defaultStatusMapping = {
        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'
        failure: ['failed']                // 失败状态通常是'failed'
      };

      // 合并自定义状态映射
      const finalStatusMapping = {
        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],
        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]
      };

      // 计算成功和失败的节点数量
      const successNodes = nodes.filter(node =>
        finalStatusMapping.success.includes(node.status) && !node.error_detail
      ).length;
      const failedNodes = nodes.filter(node =>
        finalStatusMapping.failure.includes(node.status) || node.error_detail
      ).length;
      const hasFailures = failedNodes > 0;

      // 准备通知标题
      let notificationTitle;
      if (hasFailures) {
        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);
      } else {
        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);
      }

      // 准备通知内容
      let notificationMessage;
      if (hasFailures) {
        notificationMessage = templates.error ||
          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;
      } else {
        notificationMessage = templates.success ||
          `All ${nodes.length} nodes completed successfully.`;
      }

      // 添加到全局通知中心
      this.addNotification({
        title: notificationTitle,
        message: notificationMessage,
        type: hasFailures ? 'error' : 'success',
        taskId: taskId
      });

      // 标记已发送通知
      localStorage.setItem(notificationSentKey, 'true');
    },

    /**
     * 获取默认的成功标题
     * @param {string} taskType - 任务类型
     * @returns {string} 默认标题
     */
    getDefaultSuccessTitle(taskType) {
      const titles = {
        'task': 'Task Completed',
        'upload': 'File Upload Completed',
        'download': 'File Download Completed',
        'tool': 'Tool Execution Completed'
      };
      return titles[taskType] || 'Operation Completed';
    },

    /**
     * 获取默认的错误标题
     * @param {string} taskType - 任务类型
     * @returns {string} 默认标题
     */
    getDefaultErrorTitle(taskType) {
      const titles = {
        'task': 'Task Completed with Errors',
        'upload': 'File Upload Completed with Errors',
        'download': 'File Download Completed with Errors',
        'tool': 'Tool Execution Completed with Errors'
      };
      return titles[taskType] || 'Operation Completed with Errors';
    },

    /**
     * 清除任务通知标记
     * @param {string} taskId - 任务ID
     * @param {string} taskType - 任务类型
     * @param {string} projectId - 项目ID
     */
    clearTaskNotificationMark(taskId, taskType, projectId) {
      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;
      localStorage.removeItem(notificationSentKey);
    }
  }
};
