<template>
  <a-modal
    :visible="localVisible"
    title="Network Listening Details"
    width="800px"
    @cancel="handleClose"
    class="network-listening-modal"
  >
    <template v-slot:footer>
      <a-button @click="handleClose">Close</a-button>
    </template>
    <template v-if="networkData">
      <a-table
        :columns="networkListeningColumns"
        :dataSource="getNetworkListening()"
        :pagination="{ pageSize: 20 }"
        size="middle"
      >
      </a-table>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'NetworkListeningModal',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    networkData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      localVisible: this.visible,
      networkListeningColumns: [
        { title: 'Protocol', dataIndex: 'proto', key: 'proto', width: '80px' },
        { title: 'Local Address', dataIndex: 'local_address', key: 'local_address', width: '150px' },
        { title: 'Foreign Address', dataIndex: 'foreign_address', key: 'foreign_address', width: '150px' },
        { title: 'State', dataIndex: 'state', key: 'state', width: '100px' },
        { title: 'PID/Program', dataIndex: 'pid_program', key: 'pid_program', width: '120px' }
      ]
    };
  },
  watch: {
    visible(newValue) {
      this.localVisible = newValue;
    }
  },
  methods: {
    handleClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    getNetworkListening() {
      if (!this.networkData?.exposures) return [];
      return this.networkData.exposures.listening_ports || [];
    }
  }
}
</script>

<style scoped lang="scss">
.proto-text {
  color: var(--primary-color, #1890ff);
}

.path-arrow {
  color: var(--disabled-color, #999);
}

.program-text {
  color: var(--success-color, #52c41a);
}

.ant-table-cell {
  white-space: pre-line !important;
  vertical-align: top;
  padding: 8px;
}
</style>
