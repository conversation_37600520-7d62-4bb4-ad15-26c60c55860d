from typing import List, Optional, Dict, Set
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.process_datamodel import ProcessSnapshot


class ProcessService:
    def __init__(self, db: Session):
        self.db = db
        self.valid_fields: Set[str] = {
            'pid', 'uid', 'gid', 'cmdline', 'state', 'exe', 'cwd', 'capability', 'environ', 'memory_maps'
        }

    def validate_fields(self, fields: List[str]) -> Set[str]:
        """验证字段是否合法"""
        invalid_fields = set(fields) - self.valid_fields
        if invalid_fields:
            raise ValueError(f"Invalid fields: {invalid_fields}")
        return set(fields)

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_process(self, node_id: int, pid: int, fields: List[str]) -> Dict:
        """获取单个进程信息"""
        process = (self.db.query(ProcessSnapshot)
                   .filter_by(node_id=node_id, pid=pid)
                   .first())
        if not process:
            return {}

        return {field: getattr(process, field) for field in fields}

    def get_processes(self, node_id: int, fields: List[str]) -> List[Dict]:
        """获取进程列表"""
        processes = (self.db.query(ProcessSnapshot)
                     .filter_by(node_id=node_id)
                     .all())

        if not processes:
            return []

        return [{field: getattr(process, field) for field in fields}
                for process in processes]

    @staticmethod
    def get_insert_objects(node_snapshot, node_id: int) -> List[ProcessSnapshot]:
        """获取要插入的进程对象列表"""
        insert_objects = []
        for process in node_snapshot:
            process_snapshot = ProcessSnapshot(
                node_id=node_id,
                pid=process['pid'],
                uid=process['uid'],
                gid=process['gid'],
                cmdline=process['cmdline'],
                state=process['state'],
                exe=process['exe'],
                cwd=process['cwd'],
                capability=process['capability'],
                environ=str(process['environ']),
                memory_maps=str(process['memory_maps'])
            )
            insert_objects.append(process_snapshot)
        return insert_objects
