--DROP TABLE IF EXISTS k8s_api_server;
CREATE TABLE IF NOT EXISTS k8s_api_server (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    address TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_ingress;
CREATE TABLE IF NOT EXISTS k8s_ingress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    ingress_status TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_gateway;
CREATE TABLE IF NOT EXISTS k8s_gateway (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    gateway_status TEXT,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY(node_id) REF<PERSON>ENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_virtualservice;
CREATE TABLE IF NOT EXISTS k8s_virtualservice (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    virtual_service_status TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_service;
CREATE TABLE IF NOT EXISTS k8s_service (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    service_status TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_pod;
CREATE TABLE IF NOT EXISTS k8s_pod (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    pod_status TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);


--DROP TABLE IF EXISTS k8s_node;
CREATE TABLE IF NOT EXISTS k8s_node (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    pod_status TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);


--DROP TABLE IF EXISTS k8s_network_policy;
CREATE TABLE IF NOT EXISTS k8s_network_policy (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    spec TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);


--DROP TABLE IF EXISTS k8s_secret;
CREATE TABLE IF NOT EXISTS k8s_secret (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    data TEXT,
    secret_type TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_configmap;
CREATE TABLE IF NOT EXISTS k8s_configmap (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    data TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_role;
CREATE TABLE IF NOT EXISTS k8s_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    rules TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_role_binding;
CREATE TABLE IF NOT EXISTS k8s_role_binding (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    namespace TEXT,
    name TEXT,
    meta_data TEXT,
    roleRef TEXT,
    subjects TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_cluster_role;
CREATE TABLE IF NOT EXISTS k8s_cluster_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    name TEXT,
    meta_data TEXT,
    aggregationRule TEXT,
    rules TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_cluster_role_binding;
CREATE TABLE IF NOT EXISTS k8s_cluster_role_binding (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    name TEXT,
    meta_data TEXT,
    roleRef TEXT,
    subjects TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTS k8s_serviceaccount_permissions;
CREATE TABLE IF NOT EXISTS k8s_serviceaccount_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    pod_name TEXT,
    namespace TEXT,
    service_account TEXT,
    permissions TEXT,  -- JSON格式存储所有权限信息
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);
