from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class TcpPortSnapshot(Base):
    __tablename__ = 'tcp_port_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    ip = Column(String)
    port = Column(String)
    pid = Column(String)
    protocols = Column(String)
    cipher_suites = Column(String)
    certificate = Column(String)
    vulnerabilities = Column(String)
    http_info = Column(String)

    host = relationship("HostConfig", back_populates="tcp_port_snapshot")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'ip': self.ip,
            'port': self.port,
            'pid': self.pid,
            'port_type': 'tcp',  # 固定为TCP类型
            'protocols': self.protocols,
            'cipher_suites': self.cipher_suites,
            'certificate': self.certificate,
            'vulnerabilities': self.vulnerabilities,
            'http_info': self.http_info
        }


class UdpPortSnapshot(Base):
    __tablename__ = 'udp_port_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    proto = Column(String)
    recv_q = Column(String)
    send_q = Column(String)
    ip = Column(String)
    port = Column(String)
    foreign_address = Column(String)
    state = Column(String)
    pid_program = Column(String)
    raw_data = Column(String)

    host = relationship("HostConfig", back_populates="udp_port_snapshot")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'proto': self.proto,
            'recv_q': self.recv_q,
            'send_q': self.send_q,
            'ip': self.ip,
            'port': self.port,
            'foreign_address': self.foreign_address,
            'state': self.state,
            'pid_program': self.pid_program,
            'port_type': 'udp',  # 固定为UDP类型
            'raw_data': self.raw_data
        }


class UnixSocketSnapshot(Base):
    __tablename__ = 'unix_socket_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    proto = Column(String)
    refcnt = Column(String)
    flags = Column(String)
    type = Column(String)
    state = Column(String)
    inode = Column(String)
    pid_program = Column(String)
    path = Column(String)
    raw_data = Column(String)

    host = relationship("HostConfig", back_populates="unix_socket_snapshot")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'proto': self.proto,
            'refcnt': self.refcnt,
            'flags': self.flags,
            'type': self.type,
            'state': self.state,
            'inode': self.inode,
            'pid_program': self.pid_program,
            'path': self.path,
            'port_type': 'unix_socket',  # 固定为UNIX Socket类型
            'raw_data': self.raw_data
        }
