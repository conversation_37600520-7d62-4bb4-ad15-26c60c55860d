[DEFAULT]
# 确保账号可访问OBS资源，访问秘钥从我的凭证中获取
AK = QTSLZFHARTN2B7Z0WRQ1
SK = TcAJLWGNCCA0tPTux21wFzo59zZ5KFwGP6GZIbUY
SERVER = obs.cn-north-7.ulanqab.huawei.com

# 桶名，全局唯一,桶不存在，会按下面这个命名去创建； 桶不会自动删除，建议项目结束后，手动删除
BUCKET_NAME = safe-test-xian

# 对象存储目录,防止多人同时扫描结果覆盖，这个目录需要单独设置,注意不要带 “/” 结尾
BUCKET_STORE_DIR = yj_test_dir

# 待上传文件或者文件夹的***绝对路径（如示例中需要配置到文件名）***，修改为application_snapshot_agent.zip所在绝对路径即可
UPLOAD_FILE_PATH = D:\_Projects_python\RiskSense\application_snapshot_agent.zip

# 无需修改! 上传对象名，即上传后的文件名
UPLOAD_OBJECT_KEY = application_snapshot_agent.zip

# 无需修改! 下载对象名，会根据不同的主机环境进行拼接
DOWNLOAD_OBJECT_KEY = node_snapshot

# 下载对象的目标路径,修改为cache所在的***绝对路径***即可
DOWNLOAD_FILE_PATH = D:\_Projects_python\RiskSense\cache

####################################堡垒机配置####################################
CBH_HOST = csm-ssh-ulanqab3.inhuawei.com
CBH_USER_Name = y30034075
CBH_USER_PORT = 5555
CBH_PRIVATE_KEY_PATH = C:\Users\<USER>\Desktop\modelArts\y30034075.pem
CBH_PRIVATE_KEY_PASSWD = 8vm5*e)V@KXGFm!*
# 登陆到堡垒机输入对应的节点IP后，需要选择以哪个账号登陆，默认opsadmin
CBH_SWITCH_ACCOUNT = opsadmin