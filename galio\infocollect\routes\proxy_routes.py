from flask import Blueprint, jsonify
from services.proxy_service import ProxyService

bp = Blueprint('proxy', __name__)


@bp.route('/detect', methods=['GET'])
def detect_proxy_ips():
    """
    Endpoint to detect reachable proxy IPs.
    Returns a JSON with a list of reachable IPs.
    """
    reachable_ips = ProxyService.detect_reachable_ips()
    return jsonify({"reachable_ips": reachable_ips}), 200
