<template>
  <a-popover
    trigger="click"
    placement="bottomRight"
    overlayClassName="notification-popover"
    :getPopupContainer="() => wrapper"
    v-model="notificationVisible"
    @visibleChange="onPopoverVisibleChange"
  >
    <template #content>
      <div class="notification-container">
        <div class="notification-header">
          <span>{{ $t('common.notifications') }}</span>
          <a-button type="link" size="small" @click="clearAllNotifications" v-if="notifications.length">
            {{ $t('common.clearAll') }}
          </a-button>
        </div>
        <div class="notification-list">
          <div v-for="notification in notifications" :key="notification.id" :class="['notification-item', `notification-${notification.type}`]">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ notification.time }}</div>
          </div>
          <div v-if="!notifications.length" class="empty-notification">
            <div class="empty-message">{{ $t('common.noNotifications') }}</div>
          </div>
        </div>
      </div>
    </template>
    <a-badge :count="unreadNotifications" :showZero="false" :overflowCount="99" :numberStyle="{ backgroundColor: '#ff4d4f' }">
      <a class="notification-trigger" @click="toggleNotifications">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 2C6.68632 2 4.00003 4.68629 4.00003 8V11.5858L3.29292 12.2929C3.00692 12.5789 2.92137 13.009 3.07615 13.3827C3.23093 13.7564 3.59557 14 4.00003 14H16C16.4045 14 16.7691 13.7564 16.9239 13.3827C17.0787 13.009 16.9931 12.5789 16.7071 12.2929L16 11.5858V8C16 4.68629 13.3137 2 10 2Z" fill="#111827"/>
          <path d="M10 18C8.34315 18 7 16.6569 7 15H13C13 16.6569 11.6569 18 10 18Z" fill="#111827"/>
        </svg>
      </a>
    </a-badge>
  </a-popover>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: 'NotificationButton',
  data() {
    return {
      notificationVisible: false,
      wrapper: document.body,
    };
  },
  computed: {
    ...mapState(['notifications']),
    ...mapGetters(['unreadNotifications']),
  },
  methods: {
    ...mapActions(['markNotificationsAsRead', 'clearNotifications']),

    toggleNotifications(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      // 切换通知弹出框的显示状态
      this.notificationVisible = !this.notificationVisible;

      // 如果显示通知弹出框，并且有未读通知，则标记为已读
      if (this.notificationVisible && this.unreadNotifications > 0) {
        this.markNotificationsAsRead();
      }
    },

    onPopoverVisibleChange(visible) {
      // 如果显示通知弹出框，并且有未读通知，则标记为已读
      if (visible && this.unreadNotifications > 0) {
        this.markNotificationsAsRead();
        // 强制更新组件
        this.$forceUpdate();
      }
    },

    clearAllNotifications(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      // 清除所有通知
      this.clearNotifications();
    },
  },
  mounted() {
    this.wrapper = document.getElementById('layout-dashboard');
  },
};
</script>

<style scoped>
/* 通知样式 */
.notification-trigger {
  display: inline-block;
  cursor: pointer;
  padding: 4px;
}

/* 通知相关样式 */
/* 数字标记 */
::v-deep .ant-badge-count {
  background-color: #ff4d4f;
  box-shadow: 0 0 0 1px #fff;
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  min-width: 20px;
  font-weight: normal;
  top: 0;
  right: 0;
}

/* 弹出框 */
::v-deep .notification-popover {
  width: 320px !important;
  max-width: none !important;
  z-index: 1050 !important;
}

::v-deep .notification-popover .ant-popover-inner-content {
  padding: 0;
}

/* 通知容器 */
.notification-container {
  width: 100%;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  background-color: #fafafa;
}

.notification-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: default;
}

.notification-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 11px;
  color: #999;
  text-align: right;
}

.notification-success {
  border-left: 3px solid #52c41a;
}

.notification-error {
  border-left: 3px solid #ff4d4f;
}

.empty-notification {
  text-align: center;
  padding: 16px;
  color: #999;
}
</style>
