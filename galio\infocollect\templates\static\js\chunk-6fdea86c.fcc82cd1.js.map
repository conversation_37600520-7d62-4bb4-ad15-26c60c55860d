{"version": 3, "sources": ["webpack:///./src/views/Hardware.vue", "webpack:///./src/components/Cards/HardwareInfo.vue", "webpack:///src/components/Cards/HardwareInfo.vue", "webpack:///./src/components/Cards/HardwareInfo.vue?e470", "webpack:///./src/components/Cards/HardwareInfo.vue?8f3d", "webpack:///src/views/Hardware.vue", "webpack:///./src/views/Hardware.vue?db1a", "webpack:///./src/views/Hardware.vue?b0dc", "webpack:///./src/components/Cards/HardwareInfo.vue?2d4f", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "borderBottom", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "fetchHardware", "proxy", "columns", "hardwareItems", "record", "device_info", "pagination", "column", "device_type", "_e", "components", "RefreshButton", "data", "title", "dataIndex", "pageSize", "computed", "mapState", "watch", "selectedNodeIp", "newIp", "mounted", "methods", "console", "log", "response", "axios", "get", "params", "dbFile", "currentProject", "error", "component", "HardwareInfo", "$event", "$emit", "text", "name", "props", "type", "String", "default"], "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,iBAAiB,IAAI,IAAI,IAEzMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,oCAAoCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,GAAI,UAAY,CAAEC,aAAc,sBAAuBC,YAAYT,EAAIU,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACV,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACW,MAAM,QAAQb,EAAIc,aAAeV,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,2BAA2BF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,ySAAySF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIiB,GAAG,4BAA4Bf,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACgB,GAAG,CAAC,QAAUlB,EAAImB,kBAAkB,OAAOC,OAAM,MAAS,CAAClB,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIqB,QAAQ,cAAcrB,EAAIsB,cAAc,OAAUC,GAAWA,EAAOC,YAAY,WAAaxB,EAAIyB,YAAYhB,YAAYT,EAAIU,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAEc,EAAM,OAAEH,IAAU,MAAO,CAAiB,gBAAfG,EAAOf,IAAuB,CAACT,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,OAAO,CAACF,EAAIe,GAAGf,EAAIgB,GAAGO,EAAOC,gBAAgBtB,EAAG,OAAO,CAACF,EAAIe,GAAGf,EAAIgB,GAAGO,EAAOI,mBAAmB3B,EAAI4B,aAAa,IAEnhDtB,EAAkB,G,oCC+CP,GACfuB,WAAA,CACAC,sBAEAC,OACA,OACAT,cAAA,GACAD,QAAA,CACA,CACAW,MAAA,cACAC,UAAA,cACAtB,IAAA,eAEA,CACAqB,MAAA,cACAC,UAAA,cACAtB,IAAA,gBAGAc,WAAA,CACAS,SAAA,OAIAC,SAAA,IACAC,eAAA,qDAEAC,MAAA,CACAC,eAAAC,GACA,KAAApB,kBAGAqB,UACA,KAAArB,iBAEAsB,QAAA,CACA,sBAEA,GADAC,QAAAC,IAAA,yBAAAL,gBACA,KAAAA,eAIA,IACA,MAAAM,QAAAC,OAAAC,IAAA,sBAAAR,eAAA,CACAS,OAAA,CACAC,OAAA,KAAAC,kBAGA,KAAA3B,cAAAsB,EAAAb,KACA,MAAAmB,GACAR,QAAAQ,MAAA,2BAAAA,QAXAR,QAAAQ,MAAA,6BCxFoW,I,wBCQhWC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACftB,WAAA,CACAuB,iBCjBiV,ICO7U,EAAY,eACd,EACArD,EACAO,GACA,EACA,KACA,KACA,MAIa,e,6CClBf,W,kCCAA,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACW,MAAM,CAAC,iBAAkB,QAAQb,EAAIc,cAAgBV,MAAM,CAAC,KAAO,UAAUc,GAAG,CAAC,MAAQ,SAASmC,GAAQ,OAAOrD,EAAIsD,MAAM,cAAc,CAACtD,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIuD,MAAQvD,EAAIiB,GAAG,mBAAmB,QAEhRX,EAAkB,G,YCWP,GACf6B,SAAA,IACAC,eAAA,mBAEAoB,KAAA,gBACAC,MAAA,CACAF,KAAA,CACAG,KAAAC,OACAC,QAAA,MCrBqW,I,YCOjWT,EAAY,eACd,EACApD,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAA6C,E", "file": "static/js/chunk-6fdea86c.fcc82cd1.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('HardwareInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full hardware-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 512 512\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M160 160h192v192H160z\"}}),_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.hardware')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchHardware}})],1)])]},proxy:true}])},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.hardwareItems,\"rowKey\":(record) => record.device_info,\"pagination\":_vm.pagination},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'device_info')?[_c('div',{staticClass:\"table-hardware-info\"},[_c('span',[_vm._v(_vm._s(record.device_info))]),_c('span',[_vm._v(_vm._s(record.device_type))])])]:_vm._e()]}}])})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <!-- Hardware Table Card -->\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full hardware-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M160 160h192v192H160z\"/>\r\n              <path :fill=\"'currentColor'\" d=\"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.hardware') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchHardware\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"hardwareItems\"\r\n      :rowKey=\"(record) => record.device_info\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #bodyCell=\"{ column, record }\">\r\n        <template v-if=\"column.key === 'device_info'\">\r\n          <div class=\"table-hardware-info\">\r\n            <span>{{ record.device_info }}</span>\r\n            <span>{{ record.device_type }}</span>\r\n          </div>\r\n        </template>\r\n      </template>\r\n    </a-table>\r\n  </a-card>\r\n  <!-- / Hardware Table Card -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      hardwareItems: [],\r\n      columns: [\r\n        {\r\n          title: 'Device Info',\r\n          dataIndex: 'device_info',\r\n          key: 'device_info',\r\n        },\r\n        {\r\n          title: 'Device Type',\r\n          dataIndex: 'device_type',\r\n          key: 'device_type',\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchHardware();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchHardware();\r\n  },\r\n  methods: {\r\n    async fetchHardware() {\r\n      console.log('Selected Node IP:', this.selectedNodeIp);\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/hardware/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.hardwareItems = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching hardware:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.hardware-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n.ant-table-thead > tr > th {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  color: #666;\r\n}\r\n\r\n.ant-table-tbody > tr:hover > td {\r\n  background-color: #fafafa !important;\r\n}\r\n\r\n.table-hardware-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HardwareInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HardwareInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./HardwareInfo.vue?vue&type=template&id=26073df1&scoped=true\"\nimport script from \"./HardwareInfo.vue?vue&type=script&lang=js\"\nexport * from \"./HardwareInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./HardwareInfo.vue?vue&type=style&index=0&id=26073df1&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26073df1\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<HardwareInfo></HardwareInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport HardwareInfo from \"@/components/Cards/HardwareInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        HardwareInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Hardware.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Hardware.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Hardware.vue?vue&type=template&id=497d744a\"\nimport script from \"./Hardware.vue?vue&type=script&lang=js\"\nexport * from \"./Hardware.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HardwareInfo.vue?vue&type=style&index=0&id=26073df1&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}