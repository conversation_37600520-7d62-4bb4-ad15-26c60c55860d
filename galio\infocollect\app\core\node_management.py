import os

from db.init_db import get_db
from services.data_loader_service import DataLoaderService
from log.logger import log_error, log_info


class NodeManagement:
    @staticmethod
    def handle_sql_insertion(file_paths: list, host_name: str, ip: str, db_file: str):
        result = {}
        try:
            if not all(os.path.exists(file_path) for file_path in file_paths):
                missing_files = [file_path for file_path in file_paths if not os.path.exists(file_path)]
                raise FileNotFoundError(f"Cache files {missing_files} missing")
            
            with get_db(db_file) as db:
                data_loader_service = DataLoaderService(db)
                try:
                    for file_path in file_paths:
                        try:
                            data_loader_service.insert_data_from_pkl(file_path, host_name, ip)
                            result[file_path] = 'success'
                            log_info(f"Data inserted successfully: {os.path.basename(file_path)} (host: {host_name})")
                        except ValueError as ve:
                            log_error(f"Data inserted error - {file_path}: {str(ve)}")
                            result[file_path] = f'data format error: {str(ve)}'
                            db.rollback()
                            raise
                        except Exception as e:
                            log_error(f"Processing error - {file_path}: {str(e)}")
                            result[file_path] = f'processing error: {str(e)}'
                            db.rollback()
                            raise
                    
                    db.commit()
                except Exception as e:
                    db.rollback()
                    log_error(f"Database operation failed: {str(e)}")
                    if not result:
                        result = {file_path: 'database operation error' for file_path in file_paths}
                    raise
            
        except FileNotFoundError as e:
            log_error(f"Cache files not found: {str(e)}")
            result = {file_path: 'file not found error' for file_path in file_paths}
        except Exception as e:
            log_error(f"Database insertion failed: {str(e)}")
            result = {file_path: 'database insertion error' for file_path in file_paths}
        
        return result
