from flask import Blueprint, jsonify, request
from services.port_service import PortService
from routes.database_wraps import with_database_session

bp = Blueprint('ports', __name__)


@bp.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_ports_or_detail(node_ip, db):
    port = request.args.get('port')
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 50, type=int)
    port_type = request.args.get('port_type', 'tcp')  # 默认为TCP端口

    port_service = PortService(db)

    # 查找节点
    node = port_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    if port:
        # 查询单个端口信息
        result = port_service.get_port(node.id, port, port_type)
        if not result:
            return jsonify({"error": f"{port_type.upper()} port/socket not found"}), 404
        return jsonify(result), 200
    else:
        # 查询所有端口信息
        items, total = port_service.get_ports(node.id, page, page_size, port_type)
        if not items:
            return jsonify([]), 200
        return jsonify(items), 200
