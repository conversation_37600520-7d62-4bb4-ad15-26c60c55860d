{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1751620468733}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCB7IEJyYW5jaGVzT3V0bGluZWQsIENoZWNrQ2lyY2xlT3V0bGluZWQsIEV4Y2xhbWF0aW9uQ2lyY2xlT3V0bGluZWQsIENsb3NlQ2lyY2xlT3V0bGluZWQsIFNlYXJjaE91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMtdnVlJzsKaW1wb3J0IHsgbWVzc2FnZSB9IGZyb20gJ2FudC1kZXNpZ24tdnVlJzsKaW1wb3J0IGF4aW9zIGZyb20gJ0AvYXBpL2F4aW9zSW5zdGFuY2UnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0ludGVsbGlnZW50VGVzdENhc2VJbmZvJywKICBjb21wb25lbnRzOiB7CiAgICBCcmFuY2hlc091dGxpbmVkLAogICAgQ2hlY2tDaXJjbGVPdXRsaW5lZCwKICAgIEV4Y2xhbWF0aW9uQ2lyY2xlT3V0bGluZWQsCiAgICBDbG9zZUNpcmNsZU91dGxpbmVkLAogICAgU2VhcmNoT3V0bGluZWQKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhbmFseXppbmc6IGZhbHNlLAogICAgICBhbmFseXNpc01vZGFsVmlzaWJsZTogZmFsc2UsCiAgICAgIHNlbGVjdGVkTm9kZUlkOiBudWxsLAogICAgICBzZWxlY3RlZEFuYWx5c2lzVHlwZXM6IFtdLAogICAgICBhbmFseXNpc1Jlc3VsdHM6IFtdLAogICAgICBhY3RpdmVLZXlzOiBbJzAnXSwKICAgICAgYXZhaWxhYmxlTm9kZXM6IFtdLAogICAgICBhdmFpbGFibGVEYXRhVHlwZXM6IFtdLAogICAgICAvLyDmn6Xor6Lnm7jlhbPmlbDmja4KICAgICAgcXVlcnlUZXh0OiAnJywKICAgICAgc2VhcmNoaW5nOiBmYWxzZSwKICAgICAgc2VhcmNoUmVzdWx0czogW10sCiAgICAgIHNlYXJjaFBhcmFtczogewogICAgICAgIHRvcF9rOiAxMCwKICAgICAgICBzY29yZV90aHJlc2hvbGQ6IDAuNQogICAgICB9LAogICAgICB0ZXN0Y2FzZURldGFpbFZpc2libGU6IGZhbHNlLAogICAgICBzZWxlY3RlZFRlc3RjYXNlOiBudWxsLAogICAgICBzZWFyY2hSZXN1bHRDb2x1bW5zOiBbewogICAgICAgIHRpdGxlOiAn55So5L6L57yW5Y+3JywKICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9OdW1iZXInLAogICAgICAgIGtleTogJ1Rlc3RjYXNlX051bWJlcicsCiAgICAgICAgd2lkdGg6IDEyMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjkvovlkI3np7AnLAogICAgICAgIGRhdGFJbmRleDogJ1Rlc3RjYXNlX05hbWUnLAogICAgICAgIGtleTogJ1Rlc3RjYXNlX05hbWUnLAogICAgICAgIGVsbGlwc2lzOiB0cnVlLAogICAgICAgIHdpZHRoOiAyMDAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn55So5L6L57qn5YirJywKICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTGV2ZWwnLAogICAgICAgIHdpZHRoOiAxMDAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn55u45Ly85bqmJywKICAgICAgICBkYXRhSW5kZXg6ICdzaW1pbGFyaXR5JywKICAgICAgICBrZXk6ICdzaW1pbGFyaXR5JywKICAgICAgICB3aWR0aDogMTIwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+WHhuWkh+adoeS7ticsCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfUHJlcGFyZUNvbmRpdGlvbicsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfUHJlcGFyZUNvbmRpdGlvbicsCiAgICAgICAgZWxsaXBzaXM6IHRydWUsCiAgICAgICAgd2lkdGg6IDIwMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmk43kvZwnLAogICAgICAgIGtleTogJ2FjdGlvbicsCiAgICAgICAgd2lkdGg6IDgwLAogICAgICAgIGZpeGVkOiAncmlnaHQnCiAgICAgIH1dLAogICAgICB0ZXN0Y2FzZUNvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICfnlKjkvovnvJblj7cnLAogICAgICAgIGRhdGFJbmRleDogJ1Rlc3RjYXNlX051bWJlcicsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTnVtYmVyJywKICAgICAgICB3aWR0aDogMTIwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+eUqOS+i+WQjeensCcsCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTmFtZScsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTmFtZScsCiAgICAgICAgZWxsaXBzaXM6IHRydWUKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn55So5L6L57qn5YirJywKICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTGV2ZWwnLAogICAgICAgIHdpZHRoOiA4MAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmtYvor5XmraXpqqQnLAogICAgICAgIGRhdGFJbmRleDogJ1Rlc3RjYXNlX1Rlc3RTdGVwcycsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfVGVzdFN0ZXBzJywKICAgICAgICBlbGxpcHNpczogdHJ1ZQogICAgICB9XSwKICAgICAgZXhlY3V0aW9uQ29sdW1uczogW3sKICAgICAgICB0aXRsZTogJ+eUqOS+i+e8luWPtycsCiAgICAgICAgZGF0YUluZGV4OiAndGVzdGNhc2VfbnVtYmVyJywKICAgICAgICBrZXk6ICd0ZXN0Y2FzZV9udW1iZXInLAogICAgICAgIHdpZHRoOiAxMjAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn55So5L6L5ZCN56ewJywKICAgICAgICBkYXRhSW5kZXg6ICd0ZXN0Y2FzZV9uYW1lJywKICAgICAgICBrZXk6ICd0ZXN0Y2FzZV9uYW1lJywKICAgICAgICBlbGxpcHNpczogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmiafooYznirbmgIEnLAogICAgICAgIGRhdGFJbmRleDogJ3N0YXR1cycsCiAgICAgICAga2V5OiAnc3RhdHVzJywKICAgICAgICB3aWR0aDogMTAwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+aJp+ihjOa2iOaBrycsCiAgICAgICAgZGF0YUluZGV4OiAnbWVzc2FnZScsCiAgICAgICAga2V5OiAnbWVzc2FnZScsCiAgICAgICAgZWxsaXBzaXM6IHRydWUKICAgICAgfV0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgaGFzTm9kZURhdGEoKSB7CiAgICAgIHJldHVybiB0aGlzLmF2YWlsYWJsZU5vZGVzLmxlbmd0aCA+IDAgJiYgdGhpcy5hdmFpbGFibGVEYXRhVHlwZXMubGVuZ3RoID4gMDsKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmxvYWRBdmFpbGFibGVOb2RlcygpOwogICAgdGhpcy5kZXRlY3RBdmFpbGFibGVEYXRhVHlwZXMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGxvYWRBdmFpbGFibGVOb2RlcygpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuJGh0dHAuZ2V0KCcvYXBpL25vZGUvbm9kZXMnKTsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuYXZhaWxhYmxlTm9kZXMgPSByZXNwb25zZS5kYXRhOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3oioLngrnliJfooajlpLHotKU6JywgZXJyb3IpOwogICAgICB9CiAgICB9LAogICAgZGV0ZWN0QXZhaWxhYmxlRGF0YVR5cGVzKCkgewogICAgICAvLyDmo4DmtYtsb2NhbFN0b3JhZ2XkuK3mmK/lkKbmnInlkITnp43nsbvlnovnmoTmlbDmja4KICAgICAgY29uc3QgZGF0YVR5cGVzID0gWydwcm9jZXNzJywgJ3BhY2thZ2UnLCAnaGFyZHdhcmUnLCAnZmlsZXN5c3RlbScsICdwb3J0JywgJ2RvY2tlcicsICdrdWJlcm5ldGVzJ107CiAgICAgIGNvbnN0IGF2YWlsYWJsZSA9IFtdOwogICAgICBkYXRhVHlwZXMuZm9yRWFjaCh0eXBlID0+IHsKICAgICAgICBjb25zdCBkYXRhS2V5ID0gYCR7dHlwZX1fZGF0YWA7CiAgICAgICAgaWYgKGxvY2FsU3RvcmFnZS5nZXRJdGVtKGRhdGFLZXkpKSB7CiAgICAgICAgICBhdmFpbGFibGUucHVzaCh0eXBlKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICB0aGlzLmF2YWlsYWJsZURhdGFUeXBlcyA9IGF2YWlsYWJsZTsKICAgIH0sCiAgICBzaG93QW5hbHlzaXNNb2RhbCgpIHsKICAgICAgaWYgKCF0aGlzLmhhc05vZGVEYXRhKSB7CiAgICAgICAgbWVzc2FnZS53YXJuaW5nKCfor7flhYjmlLbpm4boioLngrnmlbDmja4nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zZWxlY3RlZEFuYWx5c2lzVHlwZXMgPSBbLi4udGhpcy5hdmFpbGFibGVEYXRhVHlwZXNdOwogICAgICB0aGlzLmFuYWx5c2lzTW9kYWxWaXNpYmxlID0gdHJ1ZTsKICAgICAgaWYgKHRoaXMuYXZhaWxhYmxlTm9kZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZE5vZGVJZCA9IHRoaXMuYXZhaWxhYmxlTm9kZXNbMF0uaWQ7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBzdGFydEFuYWx5c2lzKCkgewogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWROb2RlSWQpIHsKICAgICAgICBtZXNzYWdlLmVycm9yKCfor7fpgInmi6noioLngrknKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRBbmFseXNpc1R5cGVzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeWIhuaekOexu+WeiycpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmFuYWx5emluZyA9IHRydWU7CiAgICAgIHRoaXMuYW5hbHlzaXNSZXN1bHRzID0gW107CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5a+55q+P56eN5pWw5o2u57G75Z6L6L+b6KGM5YiG5p6QCiAgICAgICAgZm9yIChjb25zdCBpbmZvVHlwZSBvZiB0aGlzLnNlbGVjdGVkQW5hbHlzaXNUeXBlcykgewogICAgICAgICAgY29uc3QgY29sbGVjdGVkRGF0YSA9IHRoaXMuZ2V0Q29sbGVjdGVkRGF0YShpbmZvVHlwZSk7CiAgICAgICAgICBpZiAoY29sbGVjdGVkRGF0YSkgewogICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuJGh0dHAucG9zdCgnL2FwaS9pbnRlbGxpZ2VudC9hbmFseXplLWFuZC1leGVjdXRlJywgewogICAgICAgICAgICAgIG5vZGVfaWQ6IHRoaXMuc2VsZWN0ZWROb2RlSWQsCiAgICAgICAgICAgICAgaW5mb190eXBlOiBpbmZvVHlwZSwKICAgICAgICAgICAgICBjb2xsZWN0ZWRfZGF0YTogY29sbGVjdGVkRGF0YQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5hbmFseXNpc1Jlc3VsdHMucHVzaChyZXNwb25zZS5kYXRhKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdGhpcy5hbmFseXNpc01vZGFsVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIHRoaXMuYWN0aXZlS2V5cyA9IHRoaXMuYW5hbHlzaXNSZXN1bHRzLm1hcCgoXywgaW5kZXgpID0+IGluZGV4LnRvU3RyaW5nKCkpOwogICAgICAgIG1lc3NhZ2Uuc3VjY2Vzcyhg5a6M5oiQ5LqGICR7dGhpcy5hbmFseXNpc1Jlc3VsdHMubGVuZ3RofSDnp43mlbDmja7nsbvlnovnmoTmmbrog73liIbmnpBgKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliIbmnpDlpLHotKU6JywgZXJyb3IpOwogICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+WIhuaekOi/h+eoi+S4reWHuueOsOmUmeivrycpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMuYW5hbHl6aW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBnZXRDb2xsZWN0ZWREYXRhKGluZm9UeXBlKSB7CiAgICAgIGNvbnN0IGRhdGFLZXkgPSBgJHtpbmZvVHlwZX1fZGF0YWA7CiAgICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShkYXRhS2V5KTsKICAgICAgcmV0dXJuIGRhdGEgPyBKU09OLnBhcnNlKGRhdGEpIDogbnVsbDsKICAgIH0sCiAgICBnZXRUeXBlTmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVOYW1lcyA9IHsKICAgICAgICAncHJvY2Vzcyc6ICfov5vnqIvkv6Hmga8nLAogICAgICAgICdwYWNrYWdlJzogJ+i9r+S7tuWMheS/oeaBrycsCiAgICAgICAgJ2hhcmR3YXJlJzogJ+ehrOS7tuS/oeaBrycsCiAgICAgICAgJ2ZpbGVzeXN0ZW0nOiAn5paH5Lu257O757uf5L+h5oGvJywKICAgICAgICAncG9ydCc6ICfnq6/lj6Pkv6Hmga8nLAogICAgICAgICdkb2NrZXInOiAnRG9ja2Vy5L+h5oGvJywKICAgICAgICAna3ViZXJuZXRlcyc6ICdLdWJlcm5ldGVz5L+h5oGvJwogICAgICB9OwogICAgICByZXR1cm4gdHlwZU5hbWVzW3R5cGVdIHx8IHR5cGU7CiAgICB9LAogICAgZ2V0U3RhdHVzQ29sb3Ioc3RhdHVzKSB7CiAgICAgIGNvbnN0IGNvbG9ycyA9IHsKICAgICAgICAnc3VjY2Vzcyc6ICdncmVlbicsCiAgICAgICAgJ3BhcnRpYWwnOiAnb3JhbmdlJywKICAgICAgICAnd2FybmluZyc6ICdvcmFuZ2UnLAogICAgICAgICdmYWlsZWQnOiAncmVkJywKICAgICAgICAnZXJyb3InOiAncmVkJywKICAgICAgICAnaW5mbyc6ICdibHVlJwogICAgICB9OwogICAgICByZXR1cm4gY29sb3JzW3N0YXR1c10gfHwgJ2RlZmF1bHQnOwogICAgfSwKICAgIGdldFN0YXR1c1RleHQoc3RhdHVzKSB7CiAgICAgIGNvbnN0IHRleHRzID0gewogICAgICAgICdzdWNjZXNzJzogJ+aIkOWKnycsCiAgICAgICAgJ3BhcnRpYWwnOiAn6YOo5YiG5oiQ5YqfJywKICAgICAgICAnd2FybmluZyc6ICforablkYonLAogICAgICAgICdmYWlsZWQnOiAn5aSx6LSlJywKICAgICAgICAnZXJyb3InOiAn6ZSZ6K+vJywKICAgICAgICAnaW5mbyc6ICfkv6Hmga8nCiAgICAgIH07CiAgICAgIHJldHVybiB0ZXh0c1tzdGF0dXNdIHx8IHN0YXR1czsKICAgIH0sCiAgICB0cnVuY2F0ZVRleHQodGV4dCwgbWF4TGVuZ3RoKSB7CiAgICAgIGlmICghdGV4dCkgcmV0dXJuICcnOwogICAgICByZXR1cm4gdGV4dC5sZW5ndGggPiBtYXhMZW5ndGggPyB0ZXh0LnN1YnN0cmluZygwLCBtYXhMZW5ndGgpICsgJy4uLicgOiB0ZXh0OwogICAgfSwKICAgIGV4cGFuZGVkUm93UmVuZGVyKHJlY29yZCkgewogICAgICBpZiAoIXJlY29yZC5vdXRwdXRzIHx8IHJlY29yZC5vdXRwdXRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHJldHVybiAn5peg5omn6KGM6K+m5oOFJzsKICAgICAgfQogICAgICByZXR1cm4gYAogICAgICAgIDxkaXYgc3R5bGU9Im1hcmdpbjogMTZweCAwOyI+CiAgICAgICAgICA8aDQ+5ZG95Luk5omn6KGM6K+m5oOFOjwvaDQ+CiAgICAgICAgICAke3JlY29yZC5vdXRwdXRzLm1hcCgob3V0cHV0LCBpbmRleCkgPT4gYAogICAgICAgICAgICA8ZGl2IHN0eWxlPSJtYXJnaW4tYm90dG9tOiAxMnB4OyBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OyBib3JkZXItcmFkaXVzOiA0cHg7IHBhZGRpbmc6IDhweDsiPgogICAgICAgICAgICAgIDxwPjxzdHJvbmc+5ZG95LukICR7aW5kZXggKyAxfTo8L3N0cm9uZz4gPGNvZGU+JHtvdXRwdXQuY29tbWFuZH08L2NvZGU+PC9wPgogICAgICAgICAgICAgIDxwPjxzdHJvbmc+6YCA5Ye656CBOjwvc3Ryb25nPiA8c3BhbiBzdHlsZT0iY29sb3I6ICR7b3V0cHV0LmV4aXRfY29kZSA9PT0gMCA/ICdncmVlbicgOiAncmVkJ30iPiR7b3V0cHV0LmV4aXRfY29kZX08L3NwYW4+PC9wPgogICAgICAgICAgICAgICR7b3V0cHV0Lm91dHB1dCA/IGA8cD48c3Ryb25nPui+k+WHujo8L3N0cm9uZz48YnI+PHByZSBzdHlsZT0iYmFja2dyb3VuZDogI2Y1ZjVmNTsgcGFkZGluZzogOHB4OyBib3JkZXItcmFkaXVzOiA0cHg7IHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsiPiR7b3V0cHV0Lm91dHB1dH08L3ByZT48L3A+YCA6ICcnfQogICAgICAgICAgICAgICR7b3V0cHV0LmVycm9yID8gYDxwPjxzdHJvbmc+6ZSZ6K+vOjwvc3Ryb25nPjxicj48cHJlIHN0eWxlPSJiYWNrZ3JvdW5kOiAjZmZmMmYwOyBwYWRkaW5nOiA4cHg7IGJvcmRlci1yYWRpdXM6IDRweDsgY29sb3I6IHJlZDsgd2hpdGUtc3BhY2U6IHByZS13cmFwOyI+JHtvdXRwdXQuZXJyb3J9PC9wcmU+PC9wPmAgOiAnJ30KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICBgKS5qb2luKCcnKX0KICAgICAgICA8L2Rpdj4KICAgICAgYDsKICAgIH0sCiAgICAvLyDmkJzntKLmtYvor5XnlKjkvosKICAgIGFzeW5jIHNlYXJjaFRlc3RjYXNlcygpIHsKICAgICAgaWYgKCF0aGlzLnF1ZXJ5VGV4dC50cmltKCkpIHsKICAgICAgICBtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeafpeivouWGheWuuScpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnNlYXJjaGluZyA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KCcvYXBpL3ZlY3Rvcl90ZXN0Y2FzZS9zZWFyY2hfd2l0aF9kZXRhaWxzJywgewogICAgICAgICAgcXVlcnk6IHRoaXMucXVlcnlUZXh0LAogICAgICAgICAgdG9wX2s6IHRoaXMuc2VhcmNoUGFyYW1zLnRvcF9rLAogICAgICAgICAgc2NvcmVfdGhyZXNob2xkOiB0aGlzLnNlYXJjaFBhcmFtcy5zY29yZV90aHJlc2hvbGQKICAgICAgICB9KTsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdGF0dXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRzID0gcmVzcG9uc2UuZGF0YS5yZXN1bHRzOwogICAgICAgICAgbWVzc2FnZS5zdWNjZXNzKGDmib7liLAgJHt0aGlzLnNlYXJjaFJlc3VsdHMubGVuZ3RofSDkuKrnm7jlhbPmtYvor5XnlKjkvotgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgbWVzc2FnZS5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+aQnOe0ouWksei0pScpOwogICAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRzID0gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aQnOe0oua1i+ivleeUqOS+i+Wksei0pTonLCBlcnJvcik7CiAgICAgICAgbWVzc2FnZS5lcnJvcign5pCc57Si6L+H56iL5Lit5Ye6546w6ZSZ6K+vJyk7CiAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRzID0gW107CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5zZWFyY2hpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8vIOafpeeci+a1i+ivleeUqOS+i+ivpuaDhQogICAgdmlld1Rlc3RjYXNlRGV0YWlsKHJlY29yZCkgewogICAgICB0aGlzLnNlbGVjdGVkVGVzdGNhc2UgPSByZWNvcmQ7CiAgICAgIHRoaXMudGVzdGNhc2VEZXRhaWxWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDojrflj5bnm7jkvLzluqbpopzoibIKICAgIGdldFNpbWlsYXJpdHlDb2xvcihzaW1pbGFyaXR5KSB7CiAgICAgIGlmIChzaW1pbGFyaXR5ID49IDAuOCkgcmV0dXJuICcjNTJjNDFhJzsgLy8g57u/6ImyCiAgICAgIGlmIChzaW1pbGFyaXR5ID49IDAuNikgcmV0dXJuICcjZmFhZDE0JzsgLy8g5qmZ6ImyCiAgICAgIHJldHVybiAnI2Y1MjIyZCc7IC8vIOe6ouiJsgogICAgfSwKICAgIC8vIOiOt+WPlue6p+WIq+minOiJsgogICAgZ2V0TGV2ZWxDb2xvcihsZXZlbCkgewogICAgICBjb25zdCBjb2xvcnMgPSB7CiAgICAgICAgJ1AwJzogJ3JlZCcsCiAgICAgICAgJ1AxJzogJ29yYW5nZScsCiAgICAgICAgJ1AyJzogJ2JsdWUnLAogICAgICAgICdQMyc6ICdncmVlbicsCiAgICAgICAgJ1A0JzogJ2dyYXknCiAgICAgIH07CiAgICAgIHJldHVybiBjb2xvcnNbbGV2ZWxdIHx8ICdkZWZhdWx0JzsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["BranchesOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "SearchOutlined", "message", "axios", "name", "components", "data", "analyzing", "analysisModalVisible", "selectedNodeId", "selectedAnalysisTypes", "analysisResults", "activeKeys", "availableNodes", "availableDataTypes", "queryText", "searching", "searchResults", "searchParams", "top_k", "score_threshold", "testcaseDetailVisible", "selectedTestcase", "searchResultColumns", "title", "dataIndex", "key", "width", "ellipsis", "fixed", "testcaseColumns", "executionColumns", "computed", "hasNodeData", "length", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "methods", "response", "$http", "get", "error", "console", "dataTypes", "available", "for<PERSON>ach", "type", "dataKey", "localStorage", "getItem", "push", "showAnalysisModal", "warning", "id", "startAnalysis", "infoType", "collectedData", "getCollectedData", "post", "node_id", "info_type", "collected_data", "map", "_", "index", "toString", "success", "JSON", "parse", "getTypeName", "typeNames", "getStatusColor", "status", "colors", "getStatusText", "texts", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "expandedRowRender", "record", "outputs", "output", "command", "exit_code", "join", "searchTestcases", "trim", "query", "results", "viewTestcaseDetail", "getSimilarityColor", "similarity", "getLevelColor", "level"], "sources": ["src/components/Cards/SmartOrchestrationInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card title=\"智能测试用例查询\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item label=\"自然语言查询\">\r\n              <a-input-search\r\n                v-model:value=\"queryText\"\r\n                placeholder=\"请输入自然语言描述，例如：查找与网络安全相关的测试用例\"\r\n                enter-button=\"搜索\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item v-if=\"queryText\">\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.top_k\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    placeholder=\"返回数量\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">返回数量</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.score_threshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    placeholder=\"相似度阈值\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">相似度阈值</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block>\r\n                    <template #icon>\r\n                      <SearchOutlined />\r\n                    </template>\r\n                    搜索\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\" v-if=\"searchResults.length > 0\">\r\n      <a-col :span=\"24\">\r\n        <a-card title=\"搜索结果\" size=\"small\">\r\n          <template #extra>\r\n            <a-tag color=\"blue\">找到 {{ searchResults.length }} 个相关测试用例</a-tag>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"searchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 1200 }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'similarity'\">\r\n                <a-progress\r\n                  :percent=\"Math.round(record.similarity * 100)\"\r\n                  size=\"small\"\r\n                  :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n                />\r\n              </template>\r\n              <template v-else-if=\"column.key === 'action'\">\r\n                <a-button type=\"link\" size=\"small\" @click=\"viewTestcaseDetail(record)\">\r\n                  查看详情\r\n                </a-button>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"testcaseDetailVisible\"\r\n      title=\"测试用例详情\"\r\n      :width=\"800\"\r\n      :footer=\"null\"\r\n    >\r\n      <a-descriptions v-if=\"selectedTestcase\" :column=\"1\" bordered size=\"small\">\r\n        <a-descriptions-item label=\"用例编号\">\r\n          {{ selectedTestcase.Testcase_Number }}\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"用例名称\">\r\n          {{ selectedTestcase.Testcase_Name }}\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"用例级别\">\r\n          <a-tag :color=\"getLevelColor(selectedTestcase.Testcase_Level)\">\r\n            {{ selectedTestcase.Testcase_Level }}\r\n          </a-tag>\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"相似度\" v-if=\"selectedTestcase.similarity\">\r\n          <a-progress\r\n            :percent=\"Math.round(selectedTestcase.similarity * 100)\"\r\n            size=\"small\"\r\n            :stroke-color=\"getSimilarityColor(selectedTestcase.similarity)\"\r\n          />\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"准备条件\">\r\n          <div class=\"testcase-content\">\r\n            {{ selectedTestcase.Testcase_PrepareCondition }}\r\n          </div>\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"测试步骤\">\r\n          <div class=\"testcase-content\">\r\n            {{ selectedTestcase.Testcase_TestSteps }}\r\n          </div>\r\n        </a-descriptions-item>\r\n        <a-descriptions-item label=\"预期结果\">\r\n          <div class=\"testcase-content\">\r\n            {{ selectedTestcase.Testcase_ExpectedResult }}\r\n          </div>\r\n        </a-descriptions-item>\r\n      </a-descriptions>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  SearchOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined,\r\n    SearchOutlined\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      queryText: '',\r\n      searching: false,\r\n      searchResults: [],\r\n      searchParams: {\r\n        top_k: 10,\r\n        score_threshold: 0.5\r\n      },\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      searchResultColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 200\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '相似度',\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '准备条件',\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          ellipsis: true,\r\n          width: 200\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: 'action',\r\n          width: 80,\r\n          fixed: 'right'\r\n        }\r\n      ],\r\n\r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n  },\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.queryText.trim()) {\r\n        message.warning('请输入查询内容');\r\n        return;\r\n      }\r\n\r\n      this.searching = true;\r\n      try {\r\n        const response = await axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.queryText,\r\n          top_k: this.searchParams.top_k,\r\n          score_threshold: this.searchParams.score_threshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.searchResults = response.data.results;\r\n          message.success(`找到 ${this.searchResults.length} 个相关测试用例`);\r\n        } else {\r\n          message.error(response.data.message || '搜索失败');\r\n          this.searchResults = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error('搜索过程中出现错误');\r\n        this.searchResults = [];\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n.testcase-content {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n</style> "], "mappings": ";;AA4RA,SACAA,gBAAA,EACAC,mBAAA,EACAC,yBAAA,EACAC,mBAAA,EACAC,cAAA,QACA;AACA,SAAAC,OAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAR,gBAAA;IACAC,mBAAA;IACAC,yBAAA;IACAC,mBAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,SAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,SAAA;MACAC,aAAA;MACAC,YAAA;QACAC,KAAA;QACAC,eAAA;MACA;MACAC,qBAAA;MACAC,gBAAA;MAEAC,mBAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;QACAD,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;QACAD,KAAA;MACA,GACA;QACAH,KAAA;QACAE,GAAA;QACAC,KAAA;QACAE,KAAA;MACA,EACA;MAEAC,eAAA,GACA;QACAN,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;MACA,EACA;MAEAG,gBAAA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,QAAA;MACA;IAEA;EACA;EAEAI,QAAA;IACAC,YAAA;MACA,YAAApB,cAAA,CAAAqB,MAAA,aAAApB,kBAAA,CAAAoB,MAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;EACA;EAEAC,OAAA;IACA,MAAAF,mBAAA;MACA;QACA,MAAAG,QAAA,cAAAC,KAAA,CAAAC,GAAA;QACA,IAAAF,QAAA,CAAAjC,IAAA,IAAAiC,QAAA,CAAAjC,IAAA,CAAA4B,MAAA;UACA,KAAArB,cAAA,GAAA0B,QAAA,CAAAjC,IAAA;QACA;MACA,SAAAoC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEAL,yBAAA;MACA;MACA,MAAAO,SAAA;MACA,MAAAC,SAAA;MAEAD,SAAA,CAAAE,OAAA,CAAAC,IAAA;QACA,MAAAC,OAAA,MAAAD,IAAA;QACA,IAAAE,YAAA,CAAAC,OAAA,CAAAF,OAAA;UACAH,SAAA,CAAAM,IAAA,CAAAJ,IAAA;QACA;MACA;MAEA,KAAAjC,kBAAA,GAAA+B,SAAA;IACA;IAEAO,kBAAA;MACA,UAAAnB,WAAA;QACA/B,OAAA,CAAAmD,OAAA;QACA;MACA;MAEA,KAAA3C,qBAAA,YAAAI,kBAAA;MACA,KAAAN,oBAAA;MAEA,SAAAK,cAAA,CAAAqB,MAAA;QACA,KAAAzB,cAAA,QAAAI,cAAA,IAAAyC,EAAA;MACA;IACA;IAEA,MAAAC,cAAA;MACA,UAAA9C,cAAA;QACAP,OAAA,CAAAwC,KAAA;QACA;MACA;MAEA,SAAAhC,qBAAA,CAAAwB,MAAA;QACAhC,OAAA,CAAAwC,KAAA;QACA;MACA;MAEA,KAAAnC,SAAA;MACA,KAAAI,eAAA;MAEA;QACA;QACA,WAAA6C,QAAA,SAAA9C,qBAAA;UACA,MAAA+C,aAAA,QAAAC,gBAAA,CAAAF,QAAA;UAEA,IAAAC,aAAA;YACA,MAAAlB,QAAA,cAAAC,KAAA,CAAAmB,IAAA;cACAC,OAAA,OAAAnD,cAAA;cACAoD,SAAA,EAAAL,QAAA;cACAM,cAAA,EAAAL;YACA;YAEA,KAAA9C,eAAA,CAAAwC,IAAA,CAAAZ,QAAA,CAAAjC,IAAA;UACA;QACA;QAEA,KAAAE,oBAAA;QACA,KAAAI,UAAA,QAAAD,eAAA,CAAAoD,GAAA,EAAAC,CAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,QAAA;QAEAhE,OAAA,CAAAiE,OAAA,aAAAxD,eAAA,CAAAuB,MAAA;MAEA,SAAAQ,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACAxC,OAAA,CAAAwC,KAAA;MACA;QACA,KAAAnC,SAAA;MACA;IACA;IAEAmD,iBAAAF,QAAA;MACA,MAAAR,OAAA,MAAAQ,QAAA;MACA,MAAAlD,IAAA,GAAA2C,YAAA,CAAAC,OAAA,CAAAF,OAAA;MACA,OAAA1C,IAAA,GAAA8D,IAAA,CAAAC,KAAA,CAAA/D,IAAA;IACA;IAEAgE,YAAAvB,IAAA;MACA,MAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,IAAA,KAAAA,IAAA;IACA;IAEAyB,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IAEAE,cAAAF,MAAA;MACA,MAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEAI,aAAAC,IAAA,EAAAC,SAAA;MACA,KAAAD,IAAA;MACA,OAAAA,IAAA,CAAA5C,MAAA,GAAA6C,SAAA,GAAAD,IAAA,CAAAE,SAAA,IAAAD,SAAA,YAAAD,IAAA;IACA;IAEAG,kBAAAC,MAAA;MACA,KAAAA,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAAC,OAAA,CAAAjD,MAAA;QACA;MACA;MAEA;AACA;AACA;AACA,YAAAgD,MAAA,CAAAC,OAAA,CAAApB,GAAA,EAAAqB,MAAA,EAAAnB,KAAA;AACA;AACA,8BAAAA,KAAA,wBAAAmB,MAAA,CAAAC,OAAA;AACA,6DAAAD,MAAA,CAAAE,SAAA,6BAAAF,MAAA,CAAAE,SAAA;AACA,gBAAAF,MAAA,CAAAA,MAAA,4HAAAA,MAAA,CAAAA,MAAA;AACA,gBAAAA,MAAA,CAAA1C,KAAA,wIAAA0C,MAAA,CAAA1C,KAAA;AACA;AACA,aAAA6C,IAAA;AACA;AACA;IACA;IAEA;IACA,MAAAC,gBAAA;MACA,UAAAzE,SAAA,CAAA0E,IAAA;QACAvF,OAAA,CAAAmD,OAAA;QACA;MACA;MAEA,KAAArC,SAAA;MACA;QACA,MAAAuB,QAAA,SAAApC,KAAA,CAAAwD,IAAA;UACA+B,KAAA,OAAA3E,SAAA;UACAI,KAAA,OAAAD,YAAA,CAAAC,KAAA;UACAC,eAAA,OAAAF,YAAA,CAAAE;QACA;QAEA,IAAAmB,QAAA,CAAAjC,IAAA,CAAAmE,MAAA;UACA,KAAAxD,aAAA,GAAAsB,QAAA,CAAAjC,IAAA,CAAAqF,OAAA;UACAzF,OAAA,CAAAiE,OAAA,YAAAlD,aAAA,CAAAiB,MAAA;QACA;UACAhC,OAAA,CAAAwC,KAAA,CAAAH,QAAA,CAAAjC,IAAA,CAAAJ,OAAA;UACA,KAAAe,aAAA;QACA;MACA,SAAAyB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAxC,OAAA,CAAAwC,KAAA;QACA,KAAAzB,aAAA;MACA;QACA,KAAAD,SAAA;MACA;IACA;IAEA;IACA4E,mBAAAV,MAAA;MACA,KAAA5D,gBAAA,GAAA4D,MAAA;MACA,KAAA7D,qBAAA;IACA;IAEA;IACAwE,mBAAAC,UAAA;MACA,IAAAA,UAAA;MACA,IAAAA,UAAA;MACA;IACA;IAEA;IACAC,cAAAC,KAAA;MACA,MAAAtB,MAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAsB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}