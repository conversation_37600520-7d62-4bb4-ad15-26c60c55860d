{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1751878419786}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCB7IEJyYW5jaGVzT3V0bGluZWQsIENoZWNrQ2lyY2xlT3V0bGluZWQsIEV4Y2xhbWF0aW9uQ2lyY2xlT3V0bGluZWQsIENsb3NlQ2lyY2xlT3V0bGluZWQsIFNlYXJjaE91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMtdnVlJzsKaW1wb3J0IHsgbWVzc2FnZSB9IGZyb20gJ2FudC1kZXNpZ24tdnVlJzsKaW1wb3J0IHsgbWFwU3RhdGUsIG1hcEFjdGlvbnMsIG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IGF4aW9zIGZyb20gJ0AvYXBpL2F4aW9zSW5zdGFuY2UnOwppbXBvcnQgVGVzdENhc2VEZXRhaWxNb2RhbCBmcm9tICcuLi9XaWRnZXRzL1Rlc3RDYXNlRGV0YWlsTW9kYWwudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdJbnRlbGxpZ2VudFRlc3RDYXNlSW5mbycsCiAgY29tcG9uZW50czogewogICAgQnJhbmNoZXNPdXRsaW5lZCwKICAgIENoZWNrQ2lyY2xlT3V0bGluZWQsCiAgICBFeGNsYW1hdGlvbkNpcmNsZU91dGxpbmVkLAogICAgQ2xvc2VDaXJjbGVPdXRsaW5lZCwKICAgIFNlYXJjaE91dGxpbmVkLAogICAgVGVzdENhc2VEZXRhaWxNb2RhbAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFuYWx5emluZzogZmFsc2UsCiAgICAgIGFuYWx5c2lzTW9kYWxWaXNpYmxlOiBmYWxzZSwKICAgICAgc2VsZWN0ZWROb2RlSWQ6IG51bGwsCiAgICAgIHNlbGVjdGVkQW5hbHlzaXNUeXBlczogW10sCiAgICAgIGFuYWx5c2lzUmVzdWx0czogW10sCiAgICAgIGFjdGl2ZUtleXM6IFsnMCddLAogICAgICBhdmFpbGFibGVOb2RlczogW10sCiAgICAgIGF2YWlsYWJsZURhdGFUeXBlczogW10sCiAgICAgIC8vIOafpeivouebuOWFs+aVsOaNrgogICAgICBxdWVyeVRleHQ6ICcnLAogICAgICBzZWFyY2hpbmc6IGZhbHNlLAogICAgICBzZWFyY2hSZXN1bHRzOiBbXSwKICAgICAgc2VhcmNoUGFyYW1zOiB7CiAgICAgICAgdG9wX2s6IDEwLAogICAgICAgIHNjb3JlX3RocmVzaG9sZDogMC41CiAgICAgIH0sCiAgICAgIHRlc3RjYXNlRGV0YWlsVmlzaWJsZTogZmFsc2UsCiAgICAgIHNlbGVjdGVkVGVzdGNhc2U6IG51bGwsCiAgICAgIHNlYXJjaFJlc3VsdENvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6IHRoaXMuJHQoJ3Rlc3RjYXNlLmNvbHVtbnMubnVtYmVyJyksCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTnVtYmVyJywKICAgICAgICBrZXk6ICdUZXN0Y2FzZV9OdW1iZXInLAogICAgICAgIHdpZHRoOiAxMjAsCiAgICAgICAgc2NvcGVkU2xvdHM6IHsKICAgICAgICAgIGN1c3RvbVJlbmRlcjogJ1Rlc3RjYXNlX051bWJlcicKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogdGhpcy4kdCgndGVzdGNhc2UuY29sdW1ucy5uYW1lJyksCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTmFtZScsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTmFtZScsCiAgICAgICAgZWxsaXBzaXM6IHRydWUsCiAgICAgICAgd2lkdGg6IDMwMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6IHRoaXMuJHQoJ3Rlc3RjYXNlLmNvbHVtbnMubGV2ZWwnKSwKICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAga2V5OiAnVGVzdGNhc2VfTGV2ZWwnLAogICAgICAgIHdpZHRoOiAxMDAsCiAgICAgICAgc2NvcGVkU2xvdHM6IHsKICAgICAgICAgIGN1c3RvbVJlbmRlcjogJ1Rlc3RjYXNlX0xldmVsJwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiB0aGlzLiR0KCd0ZXN0Y2FzZS5jb2x1bW5zLnNpbWlsYXJpdHknKSwKICAgICAgICBkYXRhSW5kZXg6ICdzaW1pbGFyaXR5JywKICAgICAgICBrZXk6ICdzaW1pbGFyaXR5JywKICAgICAgICB3aWR0aDogMTUwLAogICAgICAgIHNjb3BlZFNsb3RzOiB7CiAgICAgICAgICBjdXN0b21SZW5kZXI6ICdzaW1pbGFyaXR5JwogICAgICAgIH0KICAgICAgfV0sCiAgICAgIHRlc3RjYXNlQ29sdW1uczogW3sKICAgICAgICB0aXRsZTogJ+eUqOS+i+e8luWPtycsCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfTnVtYmVyJywKICAgICAgICBrZXk6ICdUZXN0Y2FzZV9OdW1iZXInLAogICAgICAgIHdpZHRoOiAxMjAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn55So5L6L5ZCN56ewJywKICAgICAgICBkYXRhSW5kZXg6ICdUZXN0Y2FzZV9OYW1lJywKICAgICAgICBrZXk6ICdUZXN0Y2FzZV9OYW1lJywKICAgICAgICBlbGxpcHNpczogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjkvovnuqfliKsnLAogICAgICAgIGRhdGFJbmRleDogJ1Rlc3RjYXNlX0xldmVsJywKICAgICAgICBrZXk6ICdUZXN0Y2FzZV9MZXZlbCcsCiAgICAgICAgd2lkdGg6IDgwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+a1i+ivleatpemqpCcsCiAgICAgICAgZGF0YUluZGV4OiAnVGVzdGNhc2VfVGVzdFN0ZXBzJywKICAgICAgICBrZXk6ICdUZXN0Y2FzZV9UZXN0U3RlcHMnLAogICAgICAgIGVsbGlwc2lzOiB0cnVlCiAgICAgIH1dLAogICAgICBleGVjdXRpb25Db2x1bW5zOiBbewogICAgICAgIHRpdGxlOiAn55So5L6L57yW5Y+3JywKICAgICAgICBkYXRhSW5kZXg6ICd0ZXN0Y2FzZV9udW1iZXInLAogICAgICAgIGtleTogJ3Rlc3RjYXNlX251bWJlcicsCiAgICAgICAgd2lkdGg6IDEyMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjkvovlkI3np7AnLAogICAgICAgIGRhdGFJbmRleDogJ3Rlc3RjYXNlX25hbWUnLAogICAgICAgIGtleTogJ3Rlc3RjYXNlX25hbWUnLAogICAgICAgIGVsbGlwc2lzOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+aJp+ihjOeKtuaAgScsCiAgICAgICAgZGF0YUluZGV4OiAnc3RhdHVzJywKICAgICAgICBrZXk6ICdzdGF0dXMnLAogICAgICAgIHdpZHRoOiAxMDAKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5omn6KGM5raI5oGvJywKICAgICAgICBkYXRhSW5kZXg6ICdtZXNzYWdlJywKICAgICAgICBrZXk6ICdtZXNzYWdlJywKICAgICAgICBlbGxpcHNpczogdHJ1ZQogICAgICB9XQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDmmoLml7bkvb/nlKjmnKzlnLDnirbmgIHvvIzpgb/lhY0gVnVleCDlpI3mnYLmgKcKICB9LAogIGNvbXB1dGVkOiB7CiAgICBoYXNOb2RlRGF0YSgpIHsKICAgICAgcmV0dXJuIHRoaXMuYXZhaWxhYmxlTm9kZXMubGVuZ3RoID4gMCAmJiB0aGlzLmF2YWlsYWJsZURhdGFUeXBlcy5sZW5ndGggPiAwOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMubG9hZEF2YWlsYWJsZU5vZGVzKCk7CiAgICB0aGlzLmRldGVjdEF2YWlsYWJsZURhdGFUeXBlcygpOwogICAgdGhpcy5sb2FkU2VhcmNoUmVzdWx0cygpOwogIH0sCiAgbWV0aG9kczogewogICAgYXN5bmMgbG9hZEF2YWlsYWJsZU5vZGVzKCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5nZXQoJy9hcGkvbm9kZS9ub2RlcycpOwogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5hdmFpbGFibGVOb2RlcyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veiKgueCueWIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCiAgICBkZXRlY3RBdmFpbGFibGVEYXRhVHlwZXMoKSB7CiAgICAgIC8vIOajgOa1i2xvY2FsU3RvcmFnZeS4reaYr+WQpuacieWQhOenjeexu+Wei+eahOaVsOaNrgogICAgICBjb25zdCBkYXRhVHlwZXMgPSBbJ3Byb2Nlc3MnLCAncGFja2FnZScsICdoYXJkd2FyZScsICdmaWxlc3lzdGVtJywgJ3BvcnQnLCAnZG9ja2VyJywgJ2t1YmVybmV0ZXMnXTsKICAgICAgY29uc3QgYXZhaWxhYmxlID0gW107CiAgICAgIGRhdGFUeXBlcy5mb3JFYWNoKHR5cGUgPT4gewogICAgICAgIGNvbnN0IGRhdGFLZXkgPSBgJHt0eXBlfV9kYXRhYDsKICAgICAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oZGF0YUtleSkpIHsKICAgICAgICAgIGF2YWlsYWJsZS5wdXNoKHR5cGUpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuYXZhaWxhYmxlRGF0YVR5cGVzID0gYXZhaWxhYmxlOwogICAgfSwKICAgIHNob3dBbmFseXNpc01vZGFsKCkgewogICAgICBpZiAoIXRoaXMuaGFzTm9kZURhdGEpIHsKICAgICAgICBtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOaUtumbhuiKgueCueaVsOaNricpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnNlbGVjdGVkQW5hbHlzaXNUeXBlcyA9IFsuLi50aGlzLmF2YWlsYWJsZURhdGFUeXBlc107CiAgICAgIHRoaXMuYW5hbHlzaXNNb2RhbFZpc2libGUgPSB0cnVlOwogICAgICBpZiAodGhpcy5hdmFpbGFibGVOb2Rlcy5sZW5ndGggPT09IDEpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkTm9kZUlkID0gdGhpcy5hdmFpbGFibGVOb2Rlc1swXS5pZDsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIHN0YXJ0QW5hbHlzaXMoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3RlZE5vZGVJZCkgewogICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeiKgueCuScpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5zZWxlY3RlZEFuYWx5c2lzVHlwZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgbWVzc2FnZS5lcnJvcign6K+36YCJ5oup5YiG5p6Q57G75Z6LJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuYW5hbHl6aW5nID0gdHJ1ZTsKICAgICAgdGhpcy5hbmFseXNpc1Jlc3VsdHMgPSBbXTsKICAgICAgdHJ5IHsKICAgICAgICAvLyDlr7nmr4/np43mlbDmja7nsbvlnovov5vooYzliIbmnpAKICAgICAgICBmb3IgKGNvbnN0IGluZm9UeXBlIG9mIHRoaXMuc2VsZWN0ZWRBbmFseXNpc1R5cGVzKSB7CiAgICAgICAgICBjb25zdCBjb2xsZWN0ZWREYXRhID0gdGhpcy5nZXRDb2xsZWN0ZWREYXRhKGluZm9UeXBlKTsKICAgICAgICAgIGlmIChjb2xsZWN0ZWREYXRhKSB7CiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcvYXBpL2ludGVsbGlnZW50L2FuYWx5emUtYW5kLWV4ZWN1dGUnLCB7CiAgICAgICAgICAgICAgbm9kZV9pZDogdGhpcy5zZWxlY3RlZE5vZGVJZCwKICAgICAgICAgICAgICBpbmZvX3R5cGU6IGluZm9UeXBlLAogICAgICAgICAgICAgIGNvbGxlY3RlZF9kYXRhOiBjb2xsZWN0ZWREYXRhCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmFuYWx5c2lzUmVzdWx0cy5wdXNoKHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICB0aGlzLmFuYWx5c2lzTW9kYWxWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgdGhpcy5hY3RpdmVLZXlzID0gdGhpcy5hbmFseXNpc1Jlc3VsdHMubWFwKChfLCBpbmRleCkgPT4gaW5kZXgudG9TdHJpbmcoKSk7CiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKGDlrozmiJDkuoYgJHt0aGlzLmFuYWx5c2lzUmVzdWx0cy5sZW5ndGh9IOenjeaVsOaNruexu+Wei+eahOaZuuiDveWIhuaekGApOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIhuaekOWksei0pTonLCBlcnJvcik7CiAgICAgICAgbWVzc2FnZS5lcnJvcign5YiG5p6Q6L+H56iL5Lit5Ye6546w6ZSZ6K+vJyk7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5hbmFseXppbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGdldENvbGxlY3RlZERhdGEoaW5mb1R5cGUpIHsKICAgICAgY29uc3QgZGF0YUtleSA9IGAke2luZm9UeXBlfV9kYXRhYDsKICAgICAgY29uc3QgZGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGRhdGFLZXkpOwogICAgICByZXR1cm4gZGF0YSA/IEpTT04ucGFyc2UoZGF0YSkgOiBudWxsOwogICAgfSwKICAgIGdldFR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU5hbWVzID0gewogICAgICAgICdwcm9jZXNzJzogJ+i/m+eoi+S/oeaBrycsCiAgICAgICAgJ3BhY2thZ2UnOiAn6L2v5Lu25YyF5L+h5oGvJywKICAgICAgICAnaGFyZHdhcmUnOiAn56Gs5Lu25L+h5oGvJywKICAgICAgICAnZmlsZXN5c3RlbSc6ICfmlofku7bns7vnu5/kv6Hmga8nLAogICAgICAgICdwb3J0JzogJ+err+WPo+S/oeaBrycsCiAgICAgICAgJ2RvY2tlcic6ICdEb2NrZXLkv6Hmga8nLAogICAgICAgICdrdWJlcm5ldGVzJzogJ0t1YmVybmV0ZXPkv6Hmga8nCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTmFtZXNbdHlwZV0gfHwgdHlwZTsKICAgIH0sCiAgICBnZXRTdGF0dXNDb2xvcihzdGF0dXMpIHsKICAgICAgY29uc3QgY29sb3JzID0gewogICAgICAgICdzdWNjZXNzJzogJ2dyZWVuJywKICAgICAgICAncGFydGlhbCc6ICdvcmFuZ2UnLAogICAgICAgICd3YXJuaW5nJzogJ29yYW5nZScsCiAgICAgICAgJ2ZhaWxlZCc6ICdyZWQnLAogICAgICAgICdlcnJvcic6ICdyZWQnLAogICAgICAgICdpbmZvJzogJ2JsdWUnCiAgICAgIH07CiAgICAgIHJldHVybiBjb2xvcnNbc3RhdHVzXSB8fCAnZGVmYXVsdCc7CiAgICB9LAogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3QgdGV4dHMgPSB7CiAgICAgICAgJ3N1Y2Nlc3MnOiAn5oiQ5YqfJywKICAgICAgICAncGFydGlhbCc6ICfpg6jliIbmiJDlip8nLAogICAgICAgICd3YXJuaW5nJzogJ+itpuWRiicsCiAgICAgICAgJ2ZhaWxlZCc6ICflpLHotKUnLAogICAgICAgICdlcnJvcic6ICfplJnor68nLAogICAgICAgICdpbmZvJzogJ+S/oeaBrycKICAgICAgfTsKICAgICAgcmV0dXJuIHRleHRzW3N0YXR1c10gfHwgc3RhdHVzOwogICAgfSwKICAgIHRydW5jYXRlVGV4dCh0ZXh0LCBtYXhMZW5ndGgpIHsKICAgICAgaWYgKCF0ZXh0KSByZXR1cm4gJyc7CiAgICAgIHJldHVybiB0ZXh0Lmxlbmd0aCA+IG1heExlbmd0aCA/IHRleHQuc3Vic3RyaW5nKDAsIG1heExlbmd0aCkgKyAnLi4uJyA6IHRleHQ7CiAgICB9LAogICAgZXhwYW5kZWRSb3dSZW5kZXIocmVjb3JkKSB7CiAgICAgIGlmICghcmVjb3JkLm91dHB1dHMgfHwgcmVjb3JkLm91dHB1dHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuICfml6DmiafooYzor6bmg4UnOwogICAgICB9CiAgICAgIHJldHVybiBgCiAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luOiAxNnB4IDA7Ij4KICAgICAgICAgIDxoND7lkb3ku6TmiafooYzor6bmg4U6PC9oND4KICAgICAgICAgICR7cmVjb3JkLm91dHB1dHMubWFwKChvdXRwdXQsIGluZGV4KSA9PiBgCiAgICAgICAgICAgIDxkaXYgc3R5bGU9Im1hcmdpbi1ib3R0b206IDEycHg7IGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7IGJvcmRlci1yYWRpdXM6IDRweDsgcGFkZGluZzogOHB4OyI+CiAgICAgICAgICAgICAgPHA+PHN0cm9uZz7lkb3ku6QgJHtpbmRleCArIDF9Ojwvc3Ryb25nPiA8Y29kZT4ke291dHB1dC5jb21tYW5kfTwvY29kZT48L3A+CiAgICAgICAgICAgICAgPHA+PHN0cm9uZz7pgIDlh7rnoIE6PC9zdHJvbmc+IDxzcGFuIHN0eWxlPSJjb2xvcjogJHtvdXRwdXQuZXhpdF9jb2RlID09PSAwID8gJ2dyZWVuJyA6ICdyZWQnfSI+JHtvdXRwdXQuZXhpdF9jb2RlfTwvc3Bhbj48L3A+CiAgICAgICAgICAgICAgJHtvdXRwdXQub3V0cHV0ID8gYDxwPjxzdHJvbmc+6L6T5Ye6Ojwvc3Ryb25nPjxicj48cHJlIHN0eWxlPSJiYWNrZ3JvdW5kOiAjZjVmNWY1OyBwYWRkaW5nOiA4cHg7IGJvcmRlci1yYWRpdXM6IDRweDsgd2hpdGUtc3BhY2U6IHByZS13cmFwOyI+JHtvdXRwdXQub3V0cHV0fTwvcHJlPjwvcD5gIDogJyd9CiAgICAgICAgICAgICAgJHtvdXRwdXQuZXJyb3IgPyBgPHA+PHN0cm9uZz7plJnor686PC9zdHJvbmc+PGJyPjxwcmUgc3R5bGU9ImJhY2tncm91bmQ6ICNmZmYyZjA7IHBhZGRpbmc6IDhweDsgYm9yZGVyLXJhZGl1czogNHB4OyBjb2xvcjogcmVkOyB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7Ij4ke291dHB1dC5lcnJvcn08L3ByZT48L3A+YCA6ICcnfQogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIGApLmpvaW4oJycpfQogICAgICAgIDwvZGl2PgogICAgICBgOwogICAgfSwKICAgIC8vIOaQnOe0oua1i+ivleeUqOS+iwogICAgYXN5bmMgc2VhcmNoVGVzdGNhc2VzKCkgewogICAgICBpZiAoIXRoaXMucXVlcnlUZXh0LnRyaW0oKSkgewogICAgICAgIG1lc3NhZ2Uud2FybmluZyh0aGlzLiR0KCd0ZXN0Y2FzZS5zbWFydE9yY2hlc3RyYXRpb24uaW5wdXRSZXF1aXJlZCcpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2hpbmcgPSB0cnVlOwogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcvYXBpL3ZlY3Rvcl90ZXN0Y2FzZS9zZWFyY2hfd2l0aF9kZXRhaWxzJywgewogICAgICAgICAgcXVlcnk6IHRoaXMucXVlcnlUZXh0LAogICAgICAgICAgdG9wX2s6IHRoaXMuc2VhcmNoUGFyYW1zLnRvcF9rLAogICAgICAgICAgc2NvcmVfdGhyZXNob2xkOiB0aGlzLnNlYXJjaFBhcmFtcy5zY29yZV90aHJlc2hvbGQKICAgICAgICB9KTsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdGF0dXMgPT09ICdzdWNjZXNzJykgewogICAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRzID0gcmVzcG9uc2UuZGF0YS5yZXN1bHRzOwogICAgICAgICAgdGhpcy5zYXZlU2VhcmNoUmVzdWx0cygpOwogICAgICAgICAgbWVzc2FnZS5zdWNjZXNzKHRoaXMuJHQoJ3Rlc3RjYXNlLnNtYXJ0T3JjaGVzdHJhdGlvbi5mb3VuZFJlc3VsdHMnLCB7CiAgICAgICAgICAgIGNvdW50OiByZXNwb25zZS5kYXRhLnJlc3VsdHMubGVuZ3RoCiAgICAgICAgICB9KSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8IHRoaXMuJHQoJ3Rlc3RjYXNlLnNtYXJ0T3JjaGVzdHJhdGlvbi5zZWFyY2hGYWlsZWQnKSk7CiAgICAgICAgICB0aGlzLnNlYXJjaFJlc3VsdHMgPSBbXTsKICAgICAgICAgIHRoaXMuY2xlYXJTZWFyY2hSZXN1bHRzKCk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aQnOe0oua1i+ivleeUqOS+i+Wksei0pTonLCBlcnJvcik7CiAgICAgICAgbWVzc2FnZS5lcnJvcih0aGlzLiR0KCd0ZXN0Y2FzZS5zbWFydE9yY2hlc3RyYXRpb24uc2VhcmNoRXJyb3InKSk7CiAgICAgICAgdGhpcy5zZWFyY2hSZXN1bHRzID0gW107CiAgICAgICAgdGhpcy5jbGVhclNlYXJjaFJlc3VsdHMoKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLnNlYXJjaGluZyA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgLy8g5p+l55yL5rWL6K+V55So5L6L6K+m5oOFCiAgICB2aWV3VGVzdGNhc2VEZXRhaWwocmVjb3JkKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUZXN0Y2FzZSA9IHJlY29yZDsKICAgICAgdGhpcy50ZXN0Y2FzZURldGFpbFZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOS/neWtmOaQnOe0oue7k+aenOWIsGxvY2FsU3RvcmFnZQogICAgc2F2ZVNlYXJjaFJlc3VsdHMoKSB7CiAgICAgIGNvbnN0IHNlYXJjaERhdGEgPSB7CiAgICAgICAgcXVlcnlUZXh0OiB0aGlzLnF1ZXJ5VGV4dCwKICAgICAgICBzZWFyY2hQYXJhbXM6IHRoaXMuc2VhcmNoUGFyYW1zLAogICAgICAgIHNlYXJjaFJlc3VsdHM6IHRoaXMuc2VhcmNoUmVzdWx0cywKICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCkKICAgICAgfTsKICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3NtYXJ0X29yY2hlc3RyYXRpb25fc2VhcmNoX3Jlc3VsdHMnLCBKU09OLnN0cmluZ2lmeShzZWFyY2hEYXRhKSk7CiAgICB9LAogICAgLy8g5LuObG9jYWxTdG9yYWdl5oGi5aSN5pCc57Si57uT5p6cCiAgICBsb2FkU2VhcmNoUmVzdWx0cygpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBzYXZlZERhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnc21hcnRfb3JjaGVzdHJhdGlvbl9zZWFyY2hfcmVzdWx0cycpOwogICAgICAgIGlmIChzYXZlZERhdGEpIHsKICAgICAgICAgIGNvbnN0IHNlYXJjaERhdGEgPSBKU09OLnBhcnNlKHNhdmVkRGF0YSk7CiAgICAgICAgICAvLyDmo4Dmn6XmlbDmja7mmK/lkKbov4fmnJ/vvIgyNOWwj+aXtu+8iQogICAgICAgICAgY29uc3QgaXNFeHBpcmVkID0gRGF0ZS5ub3coKSAtIHNlYXJjaERhdGEudGltZXN0YW1wID4gMjQgKiA2MCAqIDYwICogMTAwMDsKICAgICAgICAgIGlmICghaXNFeHBpcmVkICYmIHNlYXJjaERhdGEuc2VhcmNoUmVzdWx0cyAmJiBzZWFyY2hEYXRhLnNlYXJjaFJlc3VsdHMubGVuZ3RoID4gMCkgewogICAgICAgICAgICB0aGlzLnF1ZXJ5VGV4dCA9IHNlYXJjaERhdGEucXVlcnlUZXh0IHx8ICcnOwogICAgICAgICAgICB0aGlzLnNlYXJjaFBhcmFtcyA9IHsKICAgICAgICAgICAgICAuLi50aGlzLnNlYXJjaFBhcmFtcywKICAgICAgICAgICAgICAuLi5zZWFyY2hEYXRhLnNlYXJjaFBhcmFtcwogICAgICAgICAgICB9OwogICAgICAgICAgICB0aGlzLnNlYXJjaFJlc3VsdHMgPSBzZWFyY2hEYXRhLnNlYXJjaFJlc3VsdHMgfHwgW107CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aBouWkjeaQnOe0oue7k+aenOWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy5jbGVhclNlYXJjaFJlc3VsdHMoKTsKICAgICAgfQogICAgfSwKICAgIC8vIOa4heeQhuaQnOe0oue7k+aenOWtmOWCqAogICAgY2xlYXJTZWFyY2hSZXN1bHRzKCkgewogICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnc21hcnRfb3JjaGVzdHJhdGlvbl9zZWFyY2hfcmVzdWx0cycpOwogICAgfSwKICAgIC8vIOa4hemZpOaQnOe0ouWOhuWPsu+8iOeUqOaIt+aJi+WKqOaTjeS9nO+8iQogICAgY2xlYXJTZWFyY2hIaXN0b3J5KCkgewogICAgICB0aGlzLnF1ZXJ5VGV4dCA9ICcnOwogICAgICB0aGlzLnNlYXJjaFJlc3VsdHMgPSBbXTsKICAgICAgdGhpcy5zZWFyY2hQYXJhbXMgPSB7CiAgICAgICAgdG9wX2s6IDEwLAogICAgICAgIHNjb3JlX3RocmVzaG9sZDogMC41CiAgICAgIH07CiAgICAgIHRoaXMuY2xlYXJTZWFyY2hSZXN1bHRzKCk7CiAgICAgIG1lc3NhZ2Uuc3VjY2Vzcyh0aGlzLiR0KCd0ZXN0Y2FzZS5zbWFydE9yY2hlc3RyYXRpb24ucmVzdWx0c0NsZWFyZWQnKSk7CiAgICB9LAogICAgLy8g6I635Y+W55u45Ly85bqm6aKc6ImyCiAgICBnZXRTaW1pbGFyaXR5Q29sb3Ioc2ltaWxhcml0eSkgewogICAgICBpZiAoc2ltaWxhcml0eSA+PSAwLjgpIHJldHVybiAnIzUyYzQxYSc7IC8vIOe7v+iJsgogICAgICBpZiAoc2ltaWxhcml0eSA+PSAwLjYpIHJldHVybiAnI2ZhYWQxNCc7IC8vIOapmeiJsgogICAgICByZXR1cm4gJyNmNTIyMmQnOyAvLyDnuqLoibIKICAgIH0sCiAgICAvLyDojrflj5bnuqfliKvpopzoibIKICAgIGdldExldmVsQ29sb3IobGV2ZWwpIHsKICAgICAgY29uc3QgY29sb3JzID0gewogICAgICAgICdsZXZlbCAwJzogJ3JlZCcsCiAgICAgICAgJ2xldmVsIDEnOiAnb3JhbmdlJywKICAgICAgICAnbGV2ZWwgMic6ICdncmVlbicsCiAgICAgICAgJ2xldmVsIDMnOiAnYmx1ZScsCiAgICAgICAgJ2xldmVsIDQnOiAncHVycGxlJywKICAgICAgICAnUDAnOiAncmVkJywKICAgICAgICAnUDEnOiAnb3JhbmdlJywKICAgICAgICAnUDInOiAnYmx1ZScsCiAgICAgICAgJ1AzJzogJ2dyZWVuJywKICAgICAgICAnUDQnOiAnZ3JheScKICAgICAgfTsKICAgICAgcmV0dXJuIGNvbG9yc1tsZXZlbF0gfHwgJ2RlZmF1bHQnOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["BranchesOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "SearchOutlined", "message", "mapState", "mapActions", "mapGetters", "axios", "TestCaseDetailModal", "name", "components", "data", "analyzing", "analysisModalVisible", "selectedNodeId", "selectedAnalysisTypes", "analysisResults", "activeKeys", "availableNodes", "availableDataTypes", "queryText", "searching", "searchResults", "searchParams", "top_k", "score_threshold", "testcaseDetailVisible", "selectedTestcase", "searchResultColumns", "title", "$t", "dataIndex", "key", "width", "scopedSlots", "customRender", "ellipsis", "testcaseColumns", "executionColumns", "computed", "hasNodeData", "length", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "loadSearchResults", "methods", "response", "$http", "get", "error", "console", "dataTypes", "available", "for<PERSON>ach", "type", "dataKey", "localStorage", "getItem", "push", "showAnalysisModal", "warning", "id", "startAnalysis", "infoType", "collectedData", "getCollectedData", "post", "node_id", "info_type", "collected_data", "map", "_", "index", "toString", "success", "JSON", "parse", "getTypeName", "typeNames", "getStatusColor", "status", "colors", "getStatusText", "texts", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "expandedRowRender", "record", "outputs", "output", "command", "exit_code", "join", "searchTestcases", "trim", "query", "results", "saveSearchResults", "count", "clearSearchResults", "viewTestcaseDetail", "searchData", "timestamp", "Date", "now", "setItem", "stringify", "savedData", "isExpired", "removeItem", "clearSearchHistory", "getSimilarityColor", "similarity", "getLevelColor", "level"], "sources": ["src/components/Cards/SmartOrchestrationInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.title')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('testcase.smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model=\"queryText\"\r\n                :placeholder=\"$t('testcase.smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.smartOrchestration.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item v-if=\"queryText\">\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"searchParams.top_k\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model=\"searchParams.score_threshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block>\r\n                    <template #icon>\r\n                      <SearchOutlined />\r\n                    </template>\r\n                    {{ $t('testcase.smartOrchestration.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\" v-if=\"searchResults.length > 0\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('testcase.smartOrchestration.foundResults', { count: searchResults.length }) }}</a-tag>\r\n              <a-button size=\"small\" @click=\"clearSearchHistory\">\r\n                <template #icon>\r\n                  <a-icon type=\"delete\" />\r\n                </template>\r\n                {{ $t('testcase.smartOrchestration.clearResults') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"searchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  SearchOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined,\r\n    SearchOutlined,\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      queryText: '',\r\n      searching: false,\r\n      searchResults: [],\r\n      searchParams: {\r\n        top_k: 10,\r\n        score_threshold: 0.5\r\n      },\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      searchResultColumns: [\r\n        {\r\n          title: this.$t('testcase.columns.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ],\r\n\r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 暂时使用本地状态，避免 Vuex 复杂性\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    this.loadSearchResults();\r\n  },\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.queryText.trim()) {\r\n        message.warning(this.$t('testcase.smartOrchestration.inputRequired'));\r\n        return;\r\n      }\r\n\r\n      this.searching = true;\r\n      try {\r\n        const response = await this.$http.post('/api/vector_testcase/search_with_details', {\r\n          query: this.queryText,\r\n          top_k: this.searchParams.top_k,\r\n          score_threshold: this.searchParams.score_threshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.searchResults = response.data.results;\r\n          this.saveSearchResults();\r\n          message.success(this.$t('testcase.smartOrchestration.foundResults', { count: response.data.results.length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('testcase.smartOrchestration.searchFailed'));\r\n          this.searchResults = [];\r\n          this.clearSearchResults();\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('testcase.smartOrchestration.searchError'));\r\n        this.searchResults = [];\r\n        this.clearSearchResults();\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 保存搜索结果到localStorage\r\n    saveSearchResults() {\r\n      const searchData = {\r\n        queryText: this.queryText,\r\n        searchParams: this.searchParams,\r\n        searchResults: this.searchResults,\r\n        timestamp: Date.now()\r\n      };\r\n      localStorage.setItem('smart_orchestration_search_results', JSON.stringify(searchData));\r\n    },\r\n\r\n    // 从localStorage恢复搜索结果\r\n    loadSearchResults() {\r\n      try {\r\n        const savedData = localStorage.getItem('smart_orchestration_search_results');\r\n        if (savedData) {\r\n          const searchData = JSON.parse(savedData);\r\n          // 检查数据是否过期（24小时）\r\n          const isExpired = Date.now() - searchData.timestamp > 24 * 60 * 60 * 1000;\r\n\r\n          if (!isExpired && searchData.searchResults && searchData.searchResults.length > 0) {\r\n            this.queryText = searchData.queryText || '';\r\n            this.searchParams = { ...this.searchParams, ...searchData.searchParams };\r\n            this.searchResults = searchData.searchResults || [];\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('恢复搜索结果失败:', error);\r\n        this.clearSearchResults();\r\n      }\r\n    },\r\n\r\n    // 清理搜索结果存储\r\n    clearSearchResults() {\r\n      localStorage.removeItem('smart_orchestration_search_results');\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.queryText = '';\r\n      this.searchResults = [];\r\n      this.searchParams = {\r\n        top_k: 10,\r\n        score_threshold: 0.5\r\n      };\r\n      this.clearSearchResults();\r\n      message.success(this.$t('testcase.smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "], "mappings": ";;AAuQA,SACAA,gBAAA,EACAC,mBAAA,EACAC,yBAAA,EACAC,mBAAA,EACAC,cAAA,QACA;AACA,SAAAC,OAAA;AACA,SAAAC,QAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AACA,OAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAZ,gBAAA;IACAC,mBAAA;IACAC,yBAAA;IACAC,mBAAA;IACAC,cAAA;IACAM;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,SAAA;MACAC,aAAA;MACAC,YAAA;QACAC,KAAA;QACAC,eAAA;MACA;MACAC,qBAAA;MACAC,gBAAA;MAEAC,mBAAA,GACA;QACAC,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAN,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAI,QAAA;QACAH,KAAA;MACA,GACA;QACAJ,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAN,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MAEAE,eAAA,GACA;QACAR,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,GACA;QACAP,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,EACA;MAEAE,gBAAA,GACA;QACAT,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,GACA;QACAP,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA;IAEA;EACA;EACAG,QAAA;IACA;EAAA,CACA;EAEAA,QAAA;IACAC,YAAA;MACA,YAAAtB,cAAA,CAAAuB,MAAA,aAAAtB,kBAAA,CAAAsB,MAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,iBAAA;EACA;EAEAC,OAAA;IACA,MAAAH,mBAAA;MACA;QACA,MAAAI,QAAA,cAAAC,KAAA,CAAAC,GAAA;QACA,IAAAF,QAAA,CAAApC,IAAA,IAAAoC,QAAA,CAAApC,IAAA,CAAA8B,MAAA;UACA,KAAAvB,cAAA,GAAA6B,QAAA,CAAApC,IAAA;QACA;MACA,SAAAuC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEAN,yBAAA;MACA;MACA,MAAAQ,SAAA;MACA,MAAAC,SAAA;MAEAD,SAAA,CAAAE,OAAA,CAAAC,IAAA;QACA,MAAAC,OAAA,MAAAD,IAAA;QACA,IAAAE,YAAA,CAAAC,OAAA,CAAAF,OAAA;UACAH,SAAA,CAAAM,IAAA,CAAAJ,IAAA;QACA;MACA;MAEA,KAAApC,kBAAA,GAAAkC,SAAA;IACA;IAEAO,kBAAA;MACA,UAAApB,WAAA;QACArC,OAAA,CAAA0D,OAAA;QACA;MACA;MAEA,KAAA9C,qBAAA,YAAAI,kBAAA;MACA,KAAAN,oBAAA;MAEA,SAAAK,cAAA,CAAAuB,MAAA;QACA,KAAA3B,cAAA,QAAAI,cAAA,IAAA4C,EAAA;MACA;IACA;IAEA,MAAAC,cAAA;MACA,UAAAjD,cAAA;QACAX,OAAA,CAAA+C,KAAA;QACA;MACA;MAEA,SAAAnC,qBAAA,CAAA0B,MAAA;QACAtC,OAAA,CAAA+C,KAAA;QACA;MACA;MAEA,KAAAtC,SAAA;MACA,KAAAI,eAAA;MAEA;QACA;QACA,WAAAgD,QAAA,SAAAjD,qBAAA;UACA,MAAAkD,aAAA,QAAAC,gBAAA,CAAAF,QAAA;UAEA,IAAAC,aAAA;YACA,MAAAlB,QAAA,cAAAC,KAAA,CAAAmB,IAAA;cACAC,OAAA,OAAAtD,cAAA;cACAuD,SAAA,EAAAL,QAAA;cACAM,cAAA,EAAAL;YACA;YAEA,KAAAjD,eAAA,CAAA2C,IAAA,CAAAZ,QAAA,CAAApC,IAAA;UACA;QACA;QAEA,KAAAE,oBAAA;QACA,KAAAI,UAAA,QAAAD,eAAA,CAAAuD,GAAA,EAAAC,CAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,QAAA;QAEAvE,OAAA,CAAAwE,OAAA,aAAA3D,eAAA,CAAAyB,MAAA;MAEA,SAAAS,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA/C,OAAA,CAAA+C,KAAA;MACA;QACA,KAAAtC,SAAA;MACA;IACA;IAEAsD,iBAAAF,QAAA;MACA,MAAAR,OAAA,MAAAQ,QAAA;MACA,MAAArD,IAAA,GAAA8C,YAAA,CAAAC,OAAA,CAAAF,OAAA;MACA,OAAA7C,IAAA,GAAAiE,IAAA,CAAAC,KAAA,CAAAlE,IAAA;IACA;IAEAmE,YAAAvB,IAAA;MACA,MAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,IAAA,KAAAA,IAAA;IACA;IAEAyB,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IAEAE,cAAAF,MAAA;MACA,MAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEAI,aAAAC,IAAA,EAAAC,SAAA;MACA,KAAAD,IAAA;MACA,OAAAA,IAAA,CAAA7C,MAAA,GAAA8C,SAAA,GAAAD,IAAA,CAAAE,SAAA,IAAAD,SAAA,YAAAD,IAAA;IACA;IAEAG,kBAAAC,MAAA;MACA,KAAAA,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAAC,OAAA,CAAAlD,MAAA;QACA;MACA;MAEA;AACA;AACA;AACA,YAAAiD,MAAA,CAAAC,OAAA,CAAApB,GAAA,EAAAqB,MAAA,EAAAnB,KAAA;AACA;AACA,8BAAAA,KAAA,wBAAAmB,MAAA,CAAAC,OAAA;AACA,6DAAAD,MAAA,CAAAE,SAAA,6BAAAF,MAAA,CAAAE,SAAA;AACA,gBAAAF,MAAA,CAAAA,MAAA,4HAAAA,MAAA,CAAAA,MAAA;AACA,gBAAAA,MAAA,CAAA1C,KAAA,wIAAA0C,MAAA,CAAA1C,KAAA;AACA;AACA,aAAA6C,IAAA;AACA;AACA;IACA;IAEA;IACA,MAAAC,gBAAA;MACA,UAAA5E,SAAA,CAAA6E,IAAA;QACA9F,OAAA,CAAA0D,OAAA,MAAA/B,EAAA;QACA;MACA;MAEA,KAAAT,SAAA;MACA;QACA,MAAA0B,QAAA,cAAAC,KAAA,CAAAmB,IAAA;UACA+B,KAAA,OAAA9E,SAAA;UACAI,KAAA,OAAAD,YAAA,CAAAC,KAAA;UACAC,eAAA,OAAAF,YAAA,CAAAE;QACA;QAEA,IAAAsB,QAAA,CAAApC,IAAA,CAAAsE,MAAA;UACA,KAAA3D,aAAA,GAAAyB,QAAA,CAAApC,IAAA,CAAAwF,OAAA;UACA,KAAAC,iBAAA;UACAjG,OAAA,CAAAwE,OAAA,MAAA7C,EAAA;YAAAuE,KAAA,EAAAtD,QAAA,CAAApC,IAAA,CAAAwF,OAAA,CAAA1D;UAAA;QACA;UACAtC,OAAA,CAAA+C,KAAA,CAAAH,QAAA,CAAApC,IAAA,CAAAR,OAAA,SAAA2B,EAAA;UACA,KAAAR,aAAA;UACA,KAAAgF,kBAAA;QACA;MACA,SAAApD,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA/C,OAAA,CAAA+C,KAAA,MAAApB,EAAA;QACA,KAAAR,aAAA;QACA,KAAAgF,kBAAA;MACA;QACA,KAAAjF,SAAA;MACA;IACA;IAEA;IACAkF,mBAAAb,MAAA;MACA,KAAA/D,gBAAA,GAAA+D,MAAA;MACA,KAAAhE,qBAAA;IACA;IAEA;IACA0E,kBAAA;MACA,MAAAI,UAAA;QACApF,SAAA,OAAAA,SAAA;QACAG,YAAA,OAAAA,YAAA;QACAD,aAAA,OAAAA,aAAA;QACAmF,SAAA,EAAAC,IAAA,CAAAC,GAAA;MACA;MACAlD,YAAA,CAAAmD,OAAA,uCAAAhC,IAAA,CAAAiC,SAAA,CAAAL,UAAA;IACA;IAEA;IACA3D,kBAAA;MACA;QACA,MAAAiE,SAAA,GAAArD,YAAA,CAAAC,OAAA;QACA,IAAAoD,SAAA;UACA,MAAAN,UAAA,GAAA5B,IAAA,CAAAC,KAAA,CAAAiC,SAAA;UACA;UACA,MAAAC,SAAA,GAAAL,IAAA,CAAAC,GAAA,KAAAH,UAAA,CAAAC,SAAA;UAEA,KAAAM,SAAA,IAAAP,UAAA,CAAAlF,aAAA,IAAAkF,UAAA,CAAAlF,aAAA,CAAAmB,MAAA;YACA,KAAArB,SAAA,GAAAoF,UAAA,CAAApF,SAAA;YACA,KAAAG,YAAA;cAAA,QAAAA,YAAA;cAAA,GAAAiF,UAAA,CAAAjF;YAAA;YACA,KAAAD,aAAA,GAAAkF,UAAA,CAAAlF,aAAA;UACA;QACA;MACA,SAAA4B,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAoD,kBAAA;MACA;IACA;IAEA;IACAA,mBAAA;MACA7C,YAAA,CAAAuD,UAAA;IACA;IAEA;IACAC,mBAAA;MACA,KAAA7F,SAAA;MACA,KAAAE,aAAA;MACA,KAAAC,YAAA;QACAC,KAAA;QACAC,eAAA;MACA;MACA,KAAA6E,kBAAA;MACAnG,OAAA,CAAAwE,OAAA,MAAA7C,EAAA;IACA;IAEA;IACAoF,mBAAAC,UAAA;MACA,IAAAA,UAAA;MACA,IAAAA,UAAA;MACA;IACA;IAEA;IACAC,cAAAC,KAAA;MACA,MAAAnC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAmC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}