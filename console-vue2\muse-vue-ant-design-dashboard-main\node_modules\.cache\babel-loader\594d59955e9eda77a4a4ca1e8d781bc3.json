{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1751877662954}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["BranchesOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "SearchOutlined", "message", "mapState", "mapActions", "mapGetters", "axios", "TestCaseDetailModal", "name", "components", "data", "analyzing", "analysisModalVisible", "selectedNodeId", "selectedAnalysisTypes", "analysisResults", "activeKeys", "availableNodes", "availableDataTypes", "searching", "testcaseDetailVisible", "selectedTestcase", "searchResultColumns", "title", "$t", "dataIndex", "key", "width", "scopedSlots", "customRender", "ellipsis", "testcaseColumns", "executionColumns", "computed", "queryText", "get", "$store", "state", "smartOrchestration", "set", "value", "dispatch", "searchParams", "searchResults", "hasNodeData", "length", "mounted", "loadAvailableNodes", "detectAvailableDataTypes", "isSearchExpired", "clearSearchResults", "methods", "response", "$http", "error", "console", "dataTypes", "available", "for<PERSON>ach", "type", "dataKey", "localStorage", "getItem", "push", "showAnalysisModal", "warning", "id", "startAnalysis", "infoType", "collectedData", "getCollectedData", "post", "node_id", "info_type", "collected_data", "map", "_", "index", "toString", "success", "JSON", "parse", "getTypeName", "typeNames", "getStatusColor", "status", "colors", "getStatusText", "texts", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "expandedRowRender", "record", "outputs", "output", "command", "exit_code", "join", "searchTestcases", "trim", "query", "top_k", "score_threshold", "updateSearchResults", "results", "count", "viewTestcaseDetail", "clearSearchHistory", "getSimilarityColor", "similarity", "getLevelColor", "level"], "sources": ["src/components/Cards/SmartOrchestrationInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.title')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('testcase.smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model:value=\"queryText\"\r\n                :placeholder=\"$t('testcase.smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.smartOrchestration.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item v-if=\"queryText\">\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.top_k\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.score_threshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block>\r\n                    <template #icon>\r\n                      <SearchOutlined />\r\n                    </template>\r\n                    {{ $t('testcase.smartOrchestration.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\" v-if=\"searchResults.length > 0\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('testcase.smartOrchestration.foundResults', { count: searchResults.length }) }}</a-tag>\r\n              <a-button size=\"small\" @click=\"clearSearchHistory\">\r\n                <template #icon>\r\n                  <a-icon type=\"delete\" />\r\n                </template>\r\n                {{ $t('testcase.smartOrchestration.clearResults') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"searchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  SearchOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined,\r\n    SearchOutlined,\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      searching: false,\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      searchResultColumns: [\r\n        {\r\n          title: this.$t('testcase.columns.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ],\r\n\r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters('smartOrchestration', ['hasSearchResults', 'isSearchExpired']),\r\n\r\n    // 双向绑定的计算属性\r\n    queryText: {\r\n      get() {\r\n        return this.$store.state.smartOrchestration.queryText;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('smartOrchestration/updateQueryText', value);\r\n      }\r\n    },\r\n\r\n    searchParams: {\r\n      get() {\r\n        return this.$store.state.smartOrchestration.searchParams;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('smartOrchestration/updateSearchParams', value);\r\n      }\r\n    },\r\n\r\n    searchResults() {\r\n      return this.$store.state.smartOrchestration.searchResults;\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    // 检查是否有过期的搜索结果需要清理\r\n    if (this.isSearchExpired) {\r\n      this.clearSearchResults();\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    ...mapActions('smartOrchestration', [\r\n      'updateQueryText',\r\n      'updateSearchParams',\r\n      'updateSearchResults',\r\n      'clearSearchResults'\r\n    ]),\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.queryText.trim()) {\r\n        message.warning(this.$t('testcase.smartOrchestration.inputRequired'));\r\n        return;\r\n      }\r\n\r\n      this.searching = true;\r\n      try {\r\n        const response = await axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.queryText,\r\n          top_k: this.searchParams.top_k,\r\n          score_threshold: this.searchParams.score_threshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          // 使用 Vuex action 更新搜索结果\r\n          this.updateSearchResults(response.data.results);\r\n          message.success(this.$t('testcase.smartOrchestration.foundResults', { count: response.data.results.length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('testcase.smartOrchestration.searchFailed'));\r\n          this.clearSearchResults();\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('testcase.smartOrchestration.searchError'));\r\n        this.clearSearchResults();\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.clearSearchResults();\r\n      message.success(this.$t('testcase.smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "], "mappings": ";;AAuQA,SACAA,gBAAA,EACAC,mBAAA,EACAC,yBAAA,EACAC,mBAAA,EACAC,cAAA,QACA;AACA,SAAAC,OAAA;AACA,SAAAC,QAAA,EAAAC,UAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AACA,OAAAC,mBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAZ,gBAAA;IACAC,mBAAA;IACAC,yBAAA;IACAC,mBAAA;IACAC,cAAA;IACAM;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,cAAA;MACAC,kBAAA;MAEA;MACAC,SAAA;MACAC,qBAAA;MACAC,gBAAA;MAEAC,mBAAA,GACA;QACAC,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAN,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAI,QAAA;QACAH,KAAA;MACA,GACA;QACAJ,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,GACA;QACAN,KAAA,OAAAC,EAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,WAAA;UAAAC,YAAA;QAAA;MACA,EACA;MAEAE,eAAA,GACA;QACAR,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,GACA;QACAP,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,EACA;MAEAE,gBAAA,GACA;QACAT,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA,GACA;QACAP,KAAA;QACAE,SAAA;QACAC,GAAA;QACAC,KAAA;MACA,GACA;QACAJ,KAAA;QACAE,SAAA;QACAC,GAAA;QACAI,QAAA;MACA;IAEA;EACA;EACAG,QAAA;IACA,GAAA5B,UAAA;IAEA;IACA6B,SAAA;MACAC,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,SAAA;MACA;MACAK,IAAAC,KAAA;QACA,KAAAJ,MAAA,CAAAK,QAAA,uCAAAD,KAAA;MACA;IACA;IAEAE,YAAA;MACAP,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAI,YAAA;MACA;MACAH,IAAAC,KAAA;QACA,KAAAJ,MAAA,CAAAK,QAAA,0CAAAD,KAAA;MACA;IACA;IAEAG,cAAA;MACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAK,aAAA;IACA;EACA;EAEAV,QAAA;IACAW,YAAA;MACA,YAAA3B,cAAA,CAAA4B,MAAA,aAAA3B,kBAAA,CAAA2B,MAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;IACA;IACA,SAAAC,eAAA;MACA,KAAAC,kBAAA;IACA;EACA;EAEAC,OAAA;IACA,GAAA/C,UAAA,wBACA,mBACA,sBACA,uBACA,qBACA;IACA,MAAA2C,mBAAA;MACA;QACA,MAAAK,QAAA,cAAAC,KAAA,CAAAlB,GAAA;QACA,IAAAiB,QAAA,CAAA1C,IAAA,IAAA0C,QAAA,CAAA1C,IAAA,CAAAmC,MAAA;UACA,KAAA5B,cAAA,GAAAmC,QAAA,CAAA1C,IAAA;QACA;MACA,SAAA4C,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEAN,yBAAA;MACA;MACA,MAAAQ,SAAA;MACA,MAAAC,SAAA;MAEAD,SAAA,CAAAE,OAAA,CAAAC,IAAA;QACA,MAAAC,OAAA,MAAAD,IAAA;QACA,IAAAE,YAAA,CAAAC,OAAA,CAAAF,OAAA;UACAH,SAAA,CAAAM,IAAA,CAAAJ,IAAA;QACA;MACA;MAEA,KAAAzC,kBAAA,GAAAuC,SAAA;IACA;IAEAO,kBAAA;MACA,UAAApB,WAAA;QACA1C,OAAA,CAAA+D,OAAA;QACA;MACA;MAEA,KAAAnD,qBAAA,YAAAI,kBAAA;MACA,KAAAN,oBAAA;MAEA,SAAAK,cAAA,CAAA4B,MAAA;QACA,KAAAhC,cAAA,QAAAI,cAAA,IAAAiD,EAAA;MACA;IACA;IAEA,MAAAC,cAAA;MACA,UAAAtD,cAAA;QACAX,OAAA,CAAAoD,KAAA;QACA;MACA;MAEA,SAAAxC,qBAAA,CAAA+B,MAAA;QACA3C,OAAA,CAAAoD,KAAA;QACA;MACA;MAEA,KAAA3C,SAAA;MACA,KAAAI,eAAA;MAEA;QACA;QACA,WAAAqD,QAAA,SAAAtD,qBAAA;UACA,MAAAuD,aAAA,QAAAC,gBAAA,CAAAF,QAAA;UAEA,IAAAC,aAAA;YACA,MAAAjB,QAAA,cAAAC,KAAA,CAAAkB,IAAA;cACAC,OAAA,OAAA3D,cAAA;cACA4D,SAAA,EAAAL,QAAA;cACAM,cAAA,EAAAL;YACA;YAEA,KAAAtD,eAAA,CAAAgD,IAAA,CAAAX,QAAA,CAAA1C,IAAA;UACA;QACA;QAEA,KAAAE,oBAAA;QACA,KAAAI,UAAA,QAAAD,eAAA,CAAA4D,GAAA,EAAAC,CAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,QAAA;QAEA5E,OAAA,CAAA6E,OAAA,aAAAhE,eAAA,CAAA8B,MAAA;MAEA,SAAAS,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACApD,OAAA,CAAAoD,KAAA;MACA;QACA,KAAA3C,SAAA;MACA;IACA;IAEA2D,iBAAAF,QAAA;MACA,MAAAR,OAAA,MAAAQ,QAAA;MACA,MAAA1D,IAAA,GAAAmD,YAAA,CAAAC,OAAA,CAAAF,OAAA;MACA,OAAAlD,IAAA,GAAAsE,IAAA,CAAAC,KAAA,CAAAvE,IAAA;IACA;IAEAwE,YAAAvB,IAAA;MACA,MAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,IAAA,KAAAA,IAAA;IACA;IAEAyB,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IAEAE,cAAAF,MAAA;MACA,MAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEAI,aAAAC,IAAA,EAAAC,SAAA;MACA,KAAAD,IAAA;MACA,OAAAA,IAAA,CAAA7C,MAAA,GAAA8C,SAAA,GAAAD,IAAA,CAAAE,SAAA,IAAAD,SAAA,YAAAD,IAAA;IACA;IAEAG,kBAAAC,MAAA;MACA,KAAAA,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAAC,OAAA,CAAAlD,MAAA;QACA;MACA;MAEA;AACA;AACA;AACA,YAAAiD,MAAA,CAAAC,OAAA,CAAApB,GAAA,EAAAqB,MAAA,EAAAnB,KAAA;AACA;AACA,8BAAAA,KAAA,wBAAAmB,MAAA,CAAAC,OAAA;AACA,6DAAAD,MAAA,CAAAE,SAAA,6BAAAF,MAAA,CAAAE,SAAA;AACA,gBAAAF,MAAA,CAAAA,MAAA,4HAAAA,MAAA,CAAAA,MAAA;AACA,gBAAAA,MAAA,CAAA1C,KAAA,wIAAA0C,MAAA,CAAA1C,KAAA;AACA;AACA,aAAA6C,IAAA;AACA;AACA;IACA;IAEA;IACA,MAAAC,gBAAA;MACA,UAAAlE,SAAA,CAAAmE,IAAA;QACAnG,OAAA,CAAA+D,OAAA,MAAAzC,EAAA;QACA;MACA;MAEA,KAAAL,SAAA;MACA;QACA,MAAAiC,QAAA,SAAA9C,KAAA,CAAAiE,IAAA;UACA+B,KAAA,OAAApE,SAAA;UACAqE,KAAA,OAAA7D,YAAA,CAAA6D,KAAA;UACAC,eAAA,OAAA9D,YAAA,CAAA8D;QACA;QAEA,IAAApD,QAAA,CAAA1C,IAAA,CAAA2E,MAAA;UACA;UACA,KAAAoB,mBAAA,CAAArD,QAAA,CAAA1C,IAAA,CAAAgG,OAAA;UACAxG,OAAA,CAAA6E,OAAA,MAAAvD,EAAA;YAAAmF,KAAA,EAAAvD,QAAA,CAAA1C,IAAA,CAAAgG,OAAA,CAAA7D;UAAA;QACA;UACA3C,OAAA,CAAAoD,KAAA,CAAAF,QAAA,CAAA1C,IAAA,CAAAR,OAAA,SAAAsB,EAAA;UACA,KAAA0B,kBAAA;QACA;MACA,SAAAI,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACApD,OAAA,CAAAoD,KAAA,MAAA9B,EAAA;QACA,KAAA0B,kBAAA;MACA;QACA,KAAA/B,SAAA;MACA;IACA;IAEA;IACAyF,mBAAAd,MAAA;MACA,KAAAzE,gBAAA,GAAAyE,MAAA;MACA,KAAA1E,qBAAA;IACA;IAEA;IACAyF,mBAAA;MACA,KAAA3D,kBAAA;MACAhD,OAAA,CAAA6E,OAAA,MAAAvD,EAAA;IACA;IAEA;IACAsF,mBAAAC,UAAA;MACA,IAAAA,UAAA;MACA,IAAAA,UAAA;MACA;IACA;IAEA;IACAC,cAAAC,KAAA;MACA,MAAA3B,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAA2B,KAAA;IACA;EACA;AACA", "ignoreList": []}]}