<template>
  <a-modal
    :visible="visible"
    title="测试用例详情"
    width="800px"
    :footer="null"
    @cancel="handleClose"
  >
    <a-descriptions v-if="testcase" bordered>
      <a-descriptions-item label="用例编号" :span="3">
        <div class="testcase-content">
          {{ testcase.Testcase_Number }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="用例名称" :span="3">
        <div class="testcase-content">
          {{ testcase.Testcase_Name }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="用例级别" :span="3">
        <a-tag :color="getLevelColor(testcase.Testcase_Level)">
          {{ testcase.Testcase_Level }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="相似度" :span="3" v-if="testcase.similarity !== undefined">
        <a-progress
          :percent="Math.round(testcase.similarity * 100)"
          size="small"
          :stroke-color="getSimilarityColor(testcase.similarity)"
        />
        <span style="margin-left: 8px;">{{ (testcase.similarity * 100).toFixed(1) }}%</span>
      </a-descriptions-item>
      <a-descriptions-item label="准备条件" :span="3">
        <div class="testcase-content">
          {{ testcase.Testcase_PrepareCondition }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="测试步骤" :span="3">
        <div class="testcase-content">
          {{ testcase.Testcase_TestSteps }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="预期结果" :span="3">
        <div class="testcase-content">
          {{ testcase.Testcase_ExpectedResult }}
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import axios from '@/api/axiosInstance';

export default {
  name: 'TestCaseDetailModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    testcase: {
      type: Object,
      default: null
    },
    // 是否需要通过API获取详细信息（当传入的testcase只有基本信息时）
    fetchDetails: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      detailedTestcase: null
    };
  },
  watch: {
    visible(newVal) {
      if (newVal && this.fetchDetails && this.testcase && !this.detailedTestcase) {
        this.fetchTestcaseDetails();
      }
    },
    testcase() {
      this.detailedTestcase = null;
    }
  },
  computed: {
    currentTestcase() {
      return this.detailedTestcase || this.testcase;
    }
  },
  methods: {
    async fetchTestcaseDetails() {
      if (!this.testcase || !this.testcase.Testcase_Number) return;
      
      this.loading = true;
      try {
        const response = await axios.get(`/api/testcase/${this.testcase.Testcase_Number}`);
        this.detailedTestcase = {
          ...response.data,
          // 保留原有的相似度信息（如果有的话）
          similarity: this.testcase.similarity
        };
      } catch (error) {
        console.error('获取测试用例详情失败:', error);
        this.$message.error('获取测试用例详情失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleClose() {
      this.$emit('close');
      this.detailedTestcase = null;
    },
    
    getLevelColor(level) {
      const colors = {
        'level 0': 'red',
        'level 1': 'orange', 
        'level 2': 'green',
        'level 3': 'blue',
        'level 4': 'purple',
        'P0': 'red',
        'P1': 'orange',
        'P2': 'blue', 
        'P3': 'green',
        'P4': 'gray'
      };
      return colors[level] || 'default';
    },
    
    getSimilarityColor(similarity) {
      if (similarity >= 0.8) return '#52c41a'; // 绿色
      if (similarity >= 0.6) return '#faad14'; // 橙色
      return '#f5222d'; // 红色
    }
  }
};
</script>

<style lang="scss" scoped>
.testcase-content {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.75);
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}
</style>
