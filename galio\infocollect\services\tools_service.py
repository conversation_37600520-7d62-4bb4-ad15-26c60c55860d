import uuid
import time
import threading
import queue
import os
import base64
import json
import shutil
from app.ssh.manager import SSHConnectionManager
from app.models.node import Node
from sqlalchemy.sql.expression import or_
from log.logger import log_warning

# 全局任务字典和锁
active_script_tasks = {}
task_lock = threading.RLock()


class ScriptService:
    def __init__(self, db):
        self.db = db
        self.task_queue = queue.Queue()

    def get_all_nodes(self):
        from datamodel.config_datamodel import HostConfig
        nodes = self.db.query(HostConfig).all()
        result = []
        for node in nodes:
            result.append({
                "host_name": node.host_name,
                "ip": node.ip,
                "ssh_port": node.ssh_port,
                "login_user": node.login_user,
                "login_pwd": node.login_pwd,
                "switch_root_cmd": node.switch_root_cmd,
                "switch_root_pwd": node.switch_root_pwd
            })
        return result

    def start_script_task(self, targets, proxy_ip, script_path, result_path, local_save_path, script_content):
        """
        启动脚本执行任务
        :param targets: 目标节点IP列表
        :param proxy_ip: 代理IP
        :param script_path: 工具包路径
        :param result_path: 结果文件路径
        :param local_save_path: 本地保存路径
        :param script_content: shell脚本内容，定义如何运行工具
        """
        from datamodel.config_datamodel import HostConfig

        if not os.path.exists(script_path):
            raise ValueError(f"Script file or tool package not found: {script_path}")

        # 确保本地保存目录存在
        if local_save_path and not os.path.exists(local_save_path):
            try:
                os.makedirs(local_save_path, exist_ok=True)
            except Exception as e:
                raise ValueError(f"Failed to create local save directory: {str(e)}")

        selected_nodes = self.db.query(HostConfig).filter(
            or_(*[HostConfig.ip == ip for ip in targets])
        ).all()

        if not selected_nodes:
            raise ValueError("No valid nodes found for the provided targets.")

        task_id = str(uuid.uuid4())
        task = {
            "task_id": task_id,
            "nodes": {},
            "metadata": {
                "overall_status": "running",
                "start_time": time.time(),
                "duration_sec": 0,
                "total_nodes": len(selected_nodes),
                "completed_nodes": 0,
                "script_path": script_path,
                "result_path": result_path,
                "local_save_path": local_save_path,
                "script_content": script_content
            }
        }

        for node in selected_nodes:
            task["nodes"][node.ip] = {
                "status": "pending",
                "host_name": node.host_name,
                "progress": 0,
                "error_detail": None,
                "result_file": None
            }

        with task_lock:
            active_script_tasks[task_id] = task

        def process_tasks():
            thread_count = min(max(len(selected_nodes) // 5, 10), 40)
            threads = []

            for _ in range(thread_count):
                thread = threading.Thread(
                    target=self.worker,
                    args=(task_id, proxy_ip, script_path, result_path, local_save_path, script_content)
                )
                thread.daemon = True
                thread.start()
                threads.append(thread)

            for db_node in selected_nodes:
                node = Node(
                    host_name=db_node.host_name,
                    ip=db_node.ip,
                    ssh_port=db_node.ssh_port,
                    login_user=db_node.login_user,
                    login_pwd=db_node.login_pwd,
                    switch_root_cmd=db_node.switch_root_cmd,
                    switch_root_pwd=db_node.switch_root_pwd
                )
                self.task_queue.put((task_id, node))

            for thread in threads:
                thread.join()

        background_thread = threading.Thread(target=process_tasks)
        background_thread.daemon = True
        background_thread.start()

        return task_id

    def worker(self, task_id, proxy_ip, script_path, result_path, local_save_path, script_content=None):
        while True:
            try:
                task_q_item = self.task_queue.get(timeout=60)
                sub_task_id, node = task_q_item
                if sub_task_id != task_id:
                    self.task_queue.put(task_q_item)
                    continue

                with task_lock:
                    task = active_script_tasks[task_id]
                    task["nodes"][node.ip]["status"] = "processing"
                    task["nodes"][node.ip]["progress"] = "20"

                try:
                    with SSHConnectionManager(node).connection() as ssh_client:
                        work_dir = f"/root/.test/script_task_{task_id}"
                        cmd = f"mkdir -p {work_dir}"
                        ssh_client.execute_command(cmd)

                        with task_lock:
                            task["nodes"][node.ip]["progress"] = 30

                        # Build encoded agent info for download and upload operations.
                        tool_filename = os.path.basename(script_path)
                        download_script_info = base64.b64encode(
                            json.dumps({"host_ip": node.ip, "file_name": tool_filename}).encode()
                        ).decode()
                        upload_script_info = base64.b64encode(
                            json.dumps({"host_ip": node.host_name}).encode()
                        ).decode()
                        # 替换脚本中的变量
                        script_content_replaced = script_content
                        # 替换基本变量
                        script_content_replaced = script_content_replaced.replace("{work_dir}", work_dir)
                        script_content_replaced = script_content_replaced.replace("{node_ip}", node.ip)
                        script_content_replaced = script_content_replaced.replace("{node_name}", node.host_name)
                        script_content_replaced = script_content_replaced.replace("{proxy_ip}", proxy_ip)
                        script_content_replaced = script_content_replaced.replace("{server_port}", "9998")
                        script_content_replaced = script_content_replaced.replace("{output_file_name}", tool_filename)
                        # 强制要求所有工具运行结果都打包为{node_name}_{node_ip}.tar格式
                        result_file_value = f"{node.host_name}_{node.ip}.tar"
                        script_content_replaced = script_content_replaced.replace("{result_file}", result_file_value)
                        script_content_replaced = script_content_replaced.replace("{download_script_info}",
                                                                                  download_script_info)
                        script_content_replaced = script_content_replaced.replace("{upload_script_info}",
                                                                                  upload_script_info)

                        remote_script_path = f"{work_dir}/script.sh"
                        ssh_client.execute_command(f"echo '{script_content_replaced}' > {remote_script_path} && "
                                                   f"chmod -R +x {work_dir}")

                        with task_lock:
                            task["nodes"][node.ip]["progress"] = 50

                        script_exec_cmd = f"cd {work_dir} && nohup sh script.sh > output.txt 2>&1 && echo exec_success"
                        exit_code, output, error = ssh_client.execute_command(script_exec_cmd)
                        if "exec_success" in output:
                            with task_lock:
                                task["nodes"][node.ip]["progress"] = 80

                        # 使用前面定义的result_file_value
                        if result_file_value:
                            # 检查cache目录中是否存在上传的文件
                            cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'cache')

                            uploads_path = os.path.join(cache_dir, 'uploads', result_file_value)

                            # 只有当文件存在时才处理
                            if os.path.exists(uploads_path):
                                # 标准化路径，处理Windows路径中的反斜杠
                                normalized_path = os.path.normpath(local_save_path)
                                if not os.path.exists(normalized_path):
                                    os.makedirs(normalized_path, exist_ok=True)
                                target_path = os.path.join(normalized_path, os.path.basename(uploads_path))
                                try:
                                    shutil.move(uploads_path, target_path)
                                    with task_lock:
                                        task["nodes"][node.ip]["result_file"] = target_path
                                except Exception as move_error:
                                    error_msg = f"Failed to move result file: {str(move_error)}"
                                    log_warning(error_msg)
                                    with task_lock:
                                        task["nodes"][node.ip]["error_detail"] = error_msg
                            else:
                                error_msg = f"Result file '{result_file_value}' not found in cache/uploads directory"
                                log_warning(error_msg)
                                with task_lock:
                                    task["nodes"][node.ip]["error_detail"] = error_msg

                        if result_path:
                            try:
                                if not task["nodes"][node.ip].get("result_file"):
                                    with task_lock:
                                        task["nodes"][node.ip]["result_file"] = result_path
                            except Exception as download_error:
                                log_warning(f"Failed to download result file: {str(download_error)}")
                        # 清理远程工作目录
                        ssh_client.execute_command("cd /root/.test/ && rm -rf script_task_*")

                        with task_lock:
                            task = active_script_tasks[task_id]
                            task["nodes"][node.ip]["status"] = "success"
                            task["nodes"][node.ip]["progress"] = 100

                except Exception as e:
                    with task_lock:
                        task = active_script_tasks[task_id]
                        task["nodes"][node.ip]["status"] = "failed"
                        task["nodes"][node.ip]["error_detail"] = str(e)

                finally:
                    with task_lock:
                        task = active_script_tasks[task_id]
                        task["metadata"]["completed_nodes"] += 1

                        if task["metadata"]["completed_nodes"] == task["metadata"]["total_nodes"]:
                            task["metadata"]["duration_sec"] = time.time() - task["metadata"]["start_time"]
                            task["metadata"]["overall_status"] = "success" if all(
                                info["status"] == "success" for info in task["nodes"].values()
                            ) else "failed"

                    self.task_queue.task_done()

            except queue.Empty:
                with task_lock:
                    task = active_script_tasks[task_id]
                    if task["metadata"]["completed_nodes"] == task["metadata"]["total_nodes"]:
                        break
                continue
