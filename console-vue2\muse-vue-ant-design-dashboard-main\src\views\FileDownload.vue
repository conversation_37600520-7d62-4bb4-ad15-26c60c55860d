<template>
  <div style="padding: 2px;">
    <div class="card-header-wrapper">
      <div class="header-wrapper">
        <div class="logo-wrapper">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" height="20" width="20" :class="`text-${sidebarColor}`">
            <path :fill="'currentColor'" d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>
          </svg>
        </div>
        <h6 class="font-semibold m-0">{{ $t('headTopic.fileDownload') }}</h6>
      </div>
      <a-button @click="handleDownload" :loading="downloading" class="nav-style-button">
        {{ $t('fileDownload.startDownload') }}
      </a-button>
    </div>

    <div class="main-content">
      <div class="left-section">
        <a-card size="small" class="compact-card" :title="$t('common.configureProxy')">
          <proxy-selector
            v-model="selectedProxyIp"
            :disabled="downloading"
            @change="handleProxyChange"
          />
          <p style="margin-top: 16px; color: gray; font-size: 12px;">
            Default download path: \infocollect\cache\download\
          </p>
        </a-card>
      </div>

      <div class="right-section config-table">
        <a-card size="small" class="compact-card" :title="$t('common.configureNodes')">
          <a-table
            :dataSource="nodesWithPath"
            :columns="columns"
            rowKey="ip"
            :pagination="{
              pageSize: 10,
              total: nodes.length,
              showSizeChanger: false
            }"
            :rowSelection="rowSelection"
            class="bordered-nodes-table"
          >
            <template slot="remotePath" slot-scope="text, record">
              <a-input
                v-model="record.remotePath"
                :placeholder="$t('fileDownload.enterDownloadPath')"
                @change="(e) => updateRemotePath(record.ip, e.target.value)"
              />
            </template>
          </a-table>
        </a-card>
      </div>
    </div>



    <!-- Add progress card after the alert -->
    <a-card
      :title="$t('fileDownload.downloadProgress')"
      style="margin-top: 16px;"
      class="compact-card"
    >
      <template slot="extra">
        <span>Overall Progress: {{ downloadProgress }}%</span>
      </template>

      <a-progress
        :percent="downloadProgress"
        :status="progressBarStatus"
        style="margin-bottom: 16px;"
      />

      <a-table
        :dataSource="downloadProgressTableData"
        :columns="downloadProgressColumns"
        rowKey="ip"
        :pagination="false"
      >
        <template slot="errorDetail" slot-scope="text, record">
          <a-popover v-if="record && record.error_detail" placement="topLeft">
            <template slot="content">
              <p>Time: {{ record.error_detail.time }}</p>
              <p>Type: {{ record.error_detail.type }}</p>
              <p>Message: {{ record.error_detail.message }}</p>
            </template>
            <a-icon type="info-circle" style="color: #ff4d4f" />
          </a-popover>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import axios from '@/api/axiosInstance';
import NotificationMixin from '@/mixins/NotificationMixin';
import ProxySelector from '@/components/common/ProxySelector.vue';

export default {
  name: 'FileDownload',
  mixins: [NotificationMixin],
  components: {
    ProxySelector
  },
  data() {
    return {
      selectedNodes: [],
      nodeRemotePaths: {},
      downloading: false,
      selectedProxyIp: null,
      pollInterval: null,
    };
  },
  computed: {
    ...mapState(['nodes', 'activeDownloadTask', 'currentProject', 'sidebarColor']),
    activeTask: {
      get() {
        return this.activeDownloadTask;
      },
      set(value) {
        this.$store.dispatch('updateDownloadTask', value);
      }
    },
    columns() {
      return [
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name'
        },
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          key: 'ip'
        },
        {
          title: 'Remote Path',
          dataIndex: 'remotePath',
          scopedSlots: { customRender: 'remotePath' }
        }
      ];
    },
    nodesWithPath() {
      return this.nodes.map(node => ({
        ...node,
        remotePath: this.nodeRemotePaths[node.ip] || ''
      }));
    },
    rowSelection() {
      return {
        selectedRowKeys: this.selectedNodes,
        onChange: (selectedRowKeys) => {
          this.selectedNodes = selectedRowKeys;
        },
      };
    },
    downloadProgressColumns() {
      return [
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          key: 'ip',
          width: '120px'
        },
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name',
          width: '150px',
          ellipsis: true
        },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          key: 'status',
          width: '100px',
          customRender: (text) => {
            const color = {
              'pending': '#1890ff',
              'downloading': '#1890ff',
              'completed': '#52c41a',
              'failed': '#f5222d'
            }[text] || '#000';
            return <span style={{ color }}>{text}</span>;
          }
        },
        {
          title: this.$t('tool.columns.progress'),
          dataIndex: 'progress',
          key: 'progress',
          width: '200px',
          customRender: (text) => (
            <a-progress percent={text || 0} size="small" />
          )
        },
        {
          title: this.$t('tool.columns.speed'),
          dataIndex: 'speed',
          key: 'speed',
          width: '100px'
        },
        {
          title: this.$t('tool.columns.fileSize'),
          dataIndex: 'file_size',
          key: 'file_size',
          width: '100px',
          customRender: (text) => this.formatBytes(text)
        },
        {
          title: this.$t('tool.columns.errorDetails'),
          dataIndex: 'error_detail',
          key: 'error_detail',
          width: '60px',
          scopedSlots: { customRender: 'errorDetail' }
        }
      ];
    },
    downloadProgressTableData() {
      if (!this.activeDownloadTask || !this.activeDownloadTask.nodes) return [];
      return Object.keys(this.activeDownloadTask.nodes).map(ip => {
        const node = this.activeDownloadTask.nodes[ip];
        return {
          ip,
          host_name: node.host_name,
          status: node.status,
          progress: node.progress || 0,
          speed: node.speed || '-',
          error_detail: node.error_detail
        };
      });
    },
    downloadProgress() {
      if (!this.activeDownloadTask || !this.activeDownloadTask.nodes) return 0;
      const nodes = Object.values(this.activeDownloadTask.nodes);
      if (nodes.length === 0) return 0;

      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);
      return Math.round(totalProgress / nodes.length);
    },
    progressBarStatus() {
      if (!this.activeDownloadTask) return 'normal';
      const nodes = Object.values(this.activeDownloadTask.nodes);
      if (nodes.some(node => node.status === 'failed')) return 'exception';
      if (nodes.every(node => node.status === 'completed')) return 'success';
      return 'active';
    }
  },
  created() {
    if (!this.checkDatabaseStatus()) {
      return;
    }
    this.$store.dispatch('fetchNodes');
    // Only check active download task if we're in the same project
    const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);
    if (taskInfo) {
      const { projectFile } = JSON.parse(taskInfo);
      if (projectFile === this.currentProject) {
        this.checkActiveDownloadTask();
      } else {
        // Clear task info if it belongs to a different project
        localStorage.removeItem(`downloadTask_${this.currentProject}`);
        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);
        this.$store.dispatch('updateDownloadTask', null);
      }
    }
  },
  methods: {
    ...mapActions(['addNotification']),
    checkDatabaseStatus() {
      if (!this.currentProject) {
        this.$notify.error({
          title: 'Database Error',
          message: 'No project database selected. Please select a project first.'
        });
        this.$router.push('/projects');
        return false;
      }
      return true;
    },
    // 处理代理IP变化
    handleProxyChange(ip) {
      console.log('Proxy IP changed:', ip);
      this.selectedProxyIp = ip;
    },
    validatePath(path) {
      // Check for absolute path and disallowed characters
      if (!path.startsWith('/')) {
        return 'Path must be absolute (start with /)';
      }

      if (path.includes('..') || path.includes('./') || path.includes('~')) {
        return 'Path cannot contain ./, ../ or ~';
      }

      return null; // Path is valid
    },
    updateRemotePath(ip, path) {
      const error = this.validatePath(path);
      if (error) {
        this.$message.error(error);
        // Clear invalid path
        this.$set(this.nodeRemotePaths, ip, '');
        return;
      }
      this.$set(this.nodeRemotePaths, ip, path);
    },
    validatePaths() {
      const selectedPaths = this.selectedNodes
        .map(ip => ({
          ip,
          path: this.nodeRemotePaths[ip] || ''
        }))
        .filter(item => item.path);

      if (!selectedPaths.length) {
        this.$message.error('Please enter remote paths for selected nodes');
        return null;
      }

      // Validate all paths
      for (const {ip, path} of selectedPaths) {
        const error = this.validatePath(path);
        if (error) {
          this.$message.error(`Invalid path for ${ip}: ${error}`);
          return null;
        }
      }

      return selectedPaths;
    },
    formatBytes(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    },
    async handleDownload() {
      if (!this.selectedNodes.length) {
        this.$message.warning('Please select at least one node');
        return;
      }

      if (!this.selectedProxyIp) {
        this.$message.warning('Please select a proxy IP');
        return;
      }

      const nodes = this.selectedNodes.map(ip => ({
        ip,
        path: this.nodeRemotePaths[ip] || ''
      }));

      try {
        this.downloading = true;

        // 清除之前的下载任务通知记录
        const previousTaskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);
        if (previousTaskInfo) {
          try {
            const { taskId } = JSON.parse(previousTaskInfo);
            if (taskId) {
              this.clearTaskNotificationMark(taskId, 'download', this.currentProject);
            }
          } catch (e) {
            console.error('Error clearing previous download notification:', e);
          }
        }
        const response = await axios.post(
          `/api/file_transfer/download/start?dbFile=${encodeURIComponent(this.currentProject)}`,
          {
            nodes,
            proxyIp: this.selectedProxyIp
          }
        );

        const taskId = response.data.task_id;
        localStorage.setItem(`downloadTask_${this.currentProject}`, JSON.stringify({
          taskId,
          projectFile: this.currentProject
        }));
        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);

        this.startPolling(taskId);

      } catch (error) {
        console.error('Download error:', error);
        this.downloading = false;
        this.$message.error(error.response?.data?.error || 'Failed to start download');
      }
    },
    async startPolling(taskId) {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
      }

      const pollStatus = async () => {
        try {
          if (!this.currentProject) {
            throw new Error('No project database selected');
          }

          const response = await axios.get(
            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`
          );

          this.$store.dispatch('updateDownloadTask', response.data);

          const allCompleted = Object.values(response.data.nodes).every(
            node => ['completed', 'failed'].includes(node.status)
          );

          if (allCompleted) {
            clearInterval(this.pollInterval);
            this.downloading = false;
            localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');

            const nodes = Object.values(response.data.nodes);

            // 使用混入中的方法添加下载完成通知
            this.addTaskCompletionNotification({
              taskId,
              taskType: 'download',
              nodes,
              projectId: this.currentProject,
              templates: {
                success: `File downloaded successfully to all ${nodes.length} nodes.`,
                error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`
              },
              // 指定下载任务的状态映射
              statusMapping: {
                success: ['completed'],
                failure: ['failed']
              }
            });
          }
        } catch (error) {
          console.error('Error polling status:', error);
          if (error.response?.status === 404) {
            clearInterval(this.pollInterval);
            this.downloading = false;
            localStorage.removeItem(`downloadTask_${this.currentProject}`);
            localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);
          }
        }
      };

      await pollStatus();
      this.pollInterval = setInterval(pollStatus, 10000); // 降低轮询频率到10秒
    },
    async checkActiveDownloadTask() {
      try {
        const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);
        const taskCompleted = localStorage.getItem(`downloadTaskCompleted_${this.currentProject}`);

        if (taskInfo) {
          const { taskId, projectFile } = JSON.parse(taskInfo);

          if (projectFile !== this.currentProject) {
            throw new Error('Task belongs to different project');
          }

          if (!this.currentProject) {
            throw new Error('No project database selected');
          }

          const response = await axios.get(
            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`
          );

          if (response.data) {
            this.$store.dispatch('updateDownloadTask', response.data);

            const allCompleted = Object.values(response.data.nodes).every(
              node => ['completed', 'failed'].includes(node.status)
            );

            if (!allCompleted && !taskCompleted) {
              this.downloading = true;
              this.startPolling(taskId);
            } else if (allCompleted) {
              this.downloading = false;
              localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');

              const nodes = Object.values(response.data.nodes);

              // 使用混入中的方法添加下载完成通知
              this.addTaskCompletionNotification({
                taskId,
                taskType: 'download',
                nodes,
                projectId: this.currentProject,
                templates: {
                  success: `File downloaded successfully to all ${nodes.length} nodes.`,
                  error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`
                },
                // 指定下载任务的状态映射
                statusMapping: {
                  success: ['completed'],
                  failure: ['failed']
                }
              });
            }
          }
        }
      } catch (error) {
        console.error('Error checking active download task:', error);
        localStorage.removeItem(`downloadTask_${this.currentProject}`);
        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);
      }
    },
    beforeDestroy() {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
      }
      // Clear download task when component is destroyed
      this.$store.dispatch('updateDownloadTask', null);
    },
    watch: {
      // Add watcher for currentProject changes
      currentProject: {
        handler(newProject, oldProject) {
          if (newProject !== oldProject) {
            // Clear previous project's task status
            this.$store.dispatch('updateDownloadTask', null);
            if (this.pollInterval) {
              clearInterval(this.pollInterval);
            }
            // Check for active tasks in new project
            this.checkActiveDownloadTask();
          }
        },
        immediate: true
      }
    }
  }
};
</script>

<style scoped>
/* 卡片头部样式 */
.card-header-wrapper {
  margin-bottom: 16px;
}

.header-wrapper {
  display: flex;
  align-items: center;
}

.logo-wrapper {
  margin-right: 8px;
}

.card-title {
  margin: 0;
}

/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 16px;
}

/* 左侧区域的Flexbox布局 */
.left-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 左侧卡片样式 */
.left-section .compact-card:first-child {
  margin-bottom: 8px;
  flex: 2; /* Takes up 2/3 of the available space */
}

.left-section .compact-card:last-child {
  flex: 1; /* Takes up 1/3 of the available space */
}

/* 响应式布局 - 在移动设备上切换为单列布局 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

/* 调整输入框样式，使其更紧凑 */
.bordered-nodes-table .ant-input {
  height: 28px;
  padding: 0 8px;
}
</style>
