{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/mixins/NotificationMixin.js", "webpack:///./src/components/common/ProxySelector.vue", "webpack:///src/components/common/ProxySelector.vue", "webpack:///./src/components/common/ProxySelector.vue?a770", "webpack:///./src/components/common/ProxySelector.vue?1de0", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.every.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/core-js/internals/array-reduce.js", "webpack:///./src/views/FileDownload.vue", "webpack:///src/views/FileDownload.vue", "webpack:///./src/views/FileDownload.vue?eef0", "webpack:///./src/views/FileDownload.vue?dbcb", "webpack:///./src/views/FileDownload.vue?dae6"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "methods", "addTaskCompletionNotification", "taskId", "taskType", "nodes", "projectId", "titles", "templates", "statusMapping", "notificationSent<PERSON>ey", "localStorage", "getItem", "defaultStatusMapping", "success", "failure", "finalStatusMapping", "successNodes", "filter", "node", "includes", "status", "error_detail", "failedNodes", "hasFailures", "notificationTitle", "notificationMessage", "error", "getDefaultErrorTitle", "getDefaultSuccessTitle", "addNotification", "title", "message", "type", "setItem", "clearTaskNotificationMark", "removeItem", "render", "_vm", "_c", "_self", "staticClass", "attrs", "isDetecting", "disabled", "on", "fetchReachableIps", "_v", "_s", "$t", "reachableIps", "staticStyle", "model", "value", "selectedIpValue", "callback", "$$v", "expression", "_l", "ip", "key", "_e", "staticRenderFns", "name", "props", "Boolean", "default", "String", "data", "computed", "watch", "newValue", "$emit", "response", "axios", "get", "reachable_ips", "console", "$notify", "component", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "every", "fn", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped", "some", "reducer", "noInitial", "accumulator", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "i", "right", "class", "sidebarColor", "downloading", "handleDownload", "handleProxyChange", "selectedProxyIp", "nodesWithPath", "columns", "pageSize", "total", "showSizeChanger", "rowSelection", "scopedSlots", "_u", "text", "record", "e", "updateRemotePath", "remotePath", "$set", "slot", "downloadProgress", "progressBarStatus", "downloadProgressTableData", "downloadProgressColumns", "time", "mixins", "NotificationMixin", "components", "ProxySelector", "selectedNodes", "nodeRemotePaths", "pollInterval", "mapState", "activeTask", "activeDownloadTask", "set", "$store", "dispatch", "dataIndex", "customRender", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "h", "$createElement", "width", "ellipsis", "color", "formatBytes", "Object", "keys", "host_name", "progress", "speed", "values", "totalProgress", "sum", "Math", "round", "created", "checkDatabaseStatus", "taskInfo", "currentProject", "projectFile", "JSON", "parse", "checkActiveDownloadTask", "mapActions", "$router", "push", "log", "validatePath", "path", "startsWith", "$message", "validatePaths", "selectedPaths", "item", "bytes", "k", "sizes", "floor", "parseFloat", "pow", "toFixed", "warning", "previousTaskInfo", "post", "encodeURIComponent", "proxyIp", "task_id", "stringify", "startPolling", "_error$response", "clearInterval", "pollStatus", "Error", "allCompleted", "_error$response2", "setInterval", "taskCompleted", "<PERSON><PERSON><PERSON><PERSON>", "handler", "newProject", "oldProject", "immediate"], "mappings": "2IACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,wDCZ9E,QACbC,QAAS,CAYPC,+BAA8B,OAC5BC,EAAM,SACNC,EAAQ,MACRC,EAAK,UACLC,EAAS,OACTC,EAAS,GAAE,UACXC,EAAY,GAAE,cACdC,EAAgB,KAGhB,MAAMC,EAAsB,GAAGN,aAAoBE,KAAaH,IAChE,GAAIQ,aAAaC,QAAQF,GACvB,OAIF,MAAMG,EAAuB,CAC3BC,QAAS,CAAC,UAAW,aACrBC,QAAS,CAAC,WAINC,EAAqB,CACzBF,QAAS,IAAKL,EAAcK,SAAW,MAAQD,EAAqBC,SACpEC,QAAS,IAAKN,EAAcM,SAAW,MAAQF,EAAqBE,UAIhEE,EAAeZ,EAAMa,OAAOC,GAChCH,EAAmBF,QAAQM,SAASD,EAAKE,UAAYF,EAAKG,cAC1DvB,OACIwB,EAAclB,EAAMa,OAAOC,GAC/BH,EAAmBD,QAAQK,SAASD,EAAKE,SAAWF,EAAKG,cACzDvB,OACIyB,EAAcD,EAAc,EAGlC,IAAIE,EAQAC,EANFD,EADED,EACkBjB,EAAOoB,OAAS9B,KAAK+B,qBAAqBxB,GAE1CG,EAAOO,SAAWjB,KAAKgC,uBAAuBzB,GAMlEsB,EADEF,EACoBhB,EAAUmB,OAC9B,GAAGV,mCAA8CM,kBAE7Bf,EAAUM,SAC9B,OAAOT,EAAMN,uCAIjBF,KAAKiC,gBAAgB,CACnBC,MAAON,EACPO,QAASN,EACTO,KAAMT,EAAc,QAAU,UAC9BrB,OAAQA,IAIVQ,aAAauB,QAAQxB,EAAqB,SAQ5CmB,uBAAuBzB,GACrB,MAAMG,EAAS,CACb,KAAQ,iBACR,OAAU,wBACV,SAAY,0BACZ,KAAQ,4BAEV,OAAOA,EAAOH,IAAa,uBAQ7BwB,qBAAqBxB,GACnB,MAAMG,EAAS,CACb,KAAQ,6BACR,OAAU,oCACV,SAAY,sCACZ,KAAQ,wCAEV,OAAOA,EAAOH,IAAa,mCAS7B+B,0BAA0BhC,EAAQC,EAAUE,GAC1C,MAAMI,EAAsB,GAAGN,aAAoBE,KAAaH,IAChEQ,aAAayB,WAAW1B,O,oCCzH9B,IAAI2B,EAAS,WAAkB,IAAIC,EAAIzC,KAAK0C,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBC,MAAM,CAAC,QAAUJ,EAAIK,YAAY,SAAWL,EAAIM,UAAUC,GAAG,CAAC,MAAQP,EAAIQ,oBAAoB,CAACR,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIW,GAAG,8BAAgC,UAAU,OAAQX,EAAIY,aAAanD,OAAQwC,EAAG,WAAW,CAACY,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQT,MAAM,CAAC,YAAcJ,EAAIW,GAAG,2BAA6B,SAAS,SAAWX,EAAIM,UAAUQ,MAAM,CAACC,MAAOf,EAAIgB,gBAAiBC,SAAS,SAAUC,GAAMlB,EAAIgB,gBAAgBE,GAAKC,WAAW,oBAAoBnB,EAAIoB,GAAIpB,EAAIY,cAAc,SAASS,GAAI,OAAOpB,EAAG,kBAAkB,CAACqB,IAAID,EAAGjB,MAAM,CAAC,MAAQiB,IAAK,CAACrB,EAAIS,GAAG,IAAIT,EAAIU,GAAGW,GAAI,UAAS,GAAGrB,EAAIuB,MAAM,IAEpvBC,EAAkB,G,YC6BP,GACfC,KAAA,gBACAC,MAAA,CAEApB,SAAA,CACAX,KAAAgC,QACAC,SAAA,GAGAb,MAAA,CACApB,KAAAkC,OACAD,QAAA,OAGAE,OACA,OACAzB,aAAA,EACAO,aAAA,GACAI,gBAAA,KAAAD,QAGAgB,SAAA,GAEAC,MAAA,CAEAjB,MAAAkB,GACA,KAAAjB,gBAAAiB,GAGAjB,gBAAAiB,GACA,KAAAC,MAAA,QAAAD,GACA,KAAAC,MAAA,SAAAD,KAGAtE,QAAA,CACA,0BACA,KAAA0C,aAAA,EACA,IACA,MAAA8B,QAAAC,OAAAC,IAAA,qBACA,KAAAzB,aAAAuB,EAAAL,KAAAQ,cACA,KAAA1B,aAAAnD,SACA,KAAAuD,gBAAA,KAAAJ,aAAA,IAEA,MAAAvB,GACAkD,QAAAlD,MAAA,iCAAAA,GACA,KAAAmD,QAAAnD,MAAA,CACAI,MAAA,QACAC,QAAA,mCAEA,QACA,KAAAW,aAAA,MCjFqW,I,YCOjWoC,EAAY,eACd,EACA1C,EACAyB,GACA,EACA,KACA,WACA,MAIa,OAAAiB,E,gCClBf,IAAIC,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCDhC,IAAIpG,EAAI,EAAQ,QACZqG,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvBvG,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAM+F,MAAM,GAAQ,CACjDC,MAAO,SAAeC,GAGpB,OAFAH,EAAS1F,MACTyF,EAAUI,IACFL,EAAQxF,MAAM,SAAUwD,EAAOsC,GACrC,IAAKD,EAAGrC,GAAQ,OAAOsC,MACtB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAI9G,EAAI,EAAQ,QACZqG,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvBvG,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAM+F,MAAM,GAAQ,CACjDO,KAAM,SAAcL,GAGlB,OAFAH,EAAS1F,MACTyF,EAAUI,GACHL,EAAQxF,MAAM,SAAUwD,EAAOsC,GACpC,GAAID,EAAGrC,GAAQ,OAAOsC,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAI9G,EAAI,EAAQ,QACZqG,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvBvG,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAM+F,MAAM,GAAQ,CACjD7F,OAAQ,SAAgBqG,GACtBT,EAAS1F,MACTyF,EAAUU,GACV,IAAIC,EAAYnG,UAAUC,OAAS,EAC/BmG,EAAcD,OAAYjG,EAAYF,UAAU,GASpD,GARAuF,EAAQxF,MAAM,SAAUwD,GAClB4C,GACFA,GAAY,EACZC,EAAc7C,GAEd6C,EAAcF,EAAQE,EAAa7C,KAEpC,CAAEuC,aAAa,IACdK,EAAW,MAAME,UAAU,kDAC/B,OAAOD,M,kCCrBX,IAAIE,EAAQ,EAAQ,QAEpBlB,EAAOC,QAAU,SAAUkB,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,qBCP5D,IAAIhB,EAAY,EAAQ,QACpBmB,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMlH,EAAYmH,EAAiBC,GAClD1B,EAAU1F,GACV,IAAIqH,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrBlH,EAAS4G,EAASM,EAAElH,QACpBoH,EAAQN,EAAW9G,EAAS,EAAI,EAChCqH,EAAIP,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASC,EACT,MAGF,GADAD,GAASC,EACLP,EAAWM,EAAQ,EAAIpH,GAAUoH,EACnC,MAAMhB,UAAU,+CAGpB,KAAMU,EAAWM,GAAS,EAAIpH,EAASoH,EAAOA,GAASC,EAAOD,KAASD,IACrEF,EAAOpH,EAAWoH,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIX9B,EAAOC,QAAU,CAGfjG,KAAM0H,GAAa,GAGnBS,MAAOT,GAAa,K,yCCtCtB,IAAIvE,EAAS,WAAkB,IAAIC,EAAIzC,KAAK0C,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,CAAC,QAAU,QAAQ,CAACZ,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAAC+E,MAAM,QAAQhF,EAAIiF,aAAe7E,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACH,EAAG,OAAO,CAACG,MAAM,CAAC,KAAO,eAAe,EAAI,+cAA+cH,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACH,EAAIS,GAAGT,EAAIU,GAAGV,EAAIW,GAAG,gCAAgCV,EAAG,WAAW,CAACE,YAAY,mBAAmBC,MAAM,CAAC,QAAUJ,EAAIkF,aAAa3E,GAAG,CAAC,MAAQP,EAAImF,iBAAiB,CAACnF,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIW,GAAG,+BAA+B,QAAQ,GAAGV,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeC,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIW,GAAG,2BAA2B,CAACV,EAAG,iBAAiB,CAACG,MAAM,CAAC,SAAWJ,EAAIkF,aAAa3E,GAAG,CAAC,OAASP,EAAIoF,mBAAmBtE,MAAM,CAACC,MAAOf,EAAIqF,gBAAiBpE,SAAS,SAAUC,GAAMlB,EAAIqF,gBAAgBnE,GAAKC,WAAW,qBAAqBlB,EAAG,IAAI,CAACY,YAAY,CAAC,aAAa,OAAO,MAAQ,OAAO,YAAY,SAAS,CAACb,EAAIS,GAAG,gEAAgE,IAAI,GAAGR,EAAG,MAAM,CAACE,YAAY,8BAA8B,CAACF,EAAG,SAAS,CAACE,YAAY,eAAeC,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIW,GAAG,2BAA2B,CAACV,EAAG,UAAU,CAACE,YAAY,uBAAuBC,MAAM,CAAC,WAAaJ,EAAIsF,cAAc,QAAUtF,EAAIuF,QAAQ,OAAS,KAAK,WAAa,CACt8DC,SAAU,GACVC,MAAOzF,EAAIjC,MAAMN,OACjBiI,iBAAiB,GACjB,aAAe1F,EAAI2F,cAAcC,YAAY5F,EAAI6F,GAAG,CAAC,CAACvE,IAAI,aAAa8B,GAAG,SAAS0C,EAAMC,GAAQ,MAAO,CAAC9F,EAAG,UAAU,CAACG,MAAM,CAAC,YAAcJ,EAAIW,GAAG,mCAAmCJ,GAAG,CAAC,OAAUyF,GAAMhG,EAAIiG,iBAAiBF,EAAO1E,GAAI2E,EAAE9I,OAAO6D,QAAQD,MAAM,CAACC,MAAOgF,EAAOG,WAAYjF,SAAS,SAAUC,GAAMlB,EAAImG,KAAKJ,EAAQ,aAAc7E,IAAMC,WAAW,+BAA+B,IAAI,KAAKlB,EAAG,SAAS,CAACE,YAAY,eAAeU,YAAY,CAAC,aAAa,QAAQT,MAAM,CAAC,MAAQJ,EAAIW,GAAG,mCAAmC,CAACV,EAAG,WAAW,CAACmG,KAAK,SAAS,CAACnG,EAAG,OAAO,CAACD,EAAIS,GAAG,qBAAqBT,EAAIU,GAAGV,EAAIqG,kBAAkB,SAASpG,EAAG,aAAa,CAACY,YAAY,CAAC,gBAAgB,QAAQT,MAAM,CAAC,QAAUJ,EAAIqG,iBAAiB,OAASrG,EAAIsG,qBAAqBrG,EAAG,UAAU,CAACG,MAAM,CAAC,WAAaJ,EAAIuG,0BAA0B,QAAUvG,EAAIwG,wBAAwB,OAAS,KAAK,YAAa,GAAOZ,YAAY5F,EAAI6F,GAAG,CAAC,CAACvE,IAAI,cAAc8B,GAAG,SAAS0C,EAAMC,GAAQ,MAAO,CAAEA,GAAUA,EAAO/G,aAAciB,EAAG,YAAY,CAACG,MAAM,CAAC,UAAY,YAAY,CAACH,EAAG,WAAW,CAACmG,KAAK,WAAW,CAACnG,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGqF,EAAO/G,aAAayH,SAASxG,EAAG,IAAI,CAACD,EAAIS,GAAG,SAAST,EAAIU,GAAGqF,EAAO/G,aAAaW,SAASM,EAAG,IAAI,CAACD,EAAIS,GAAG,YAAYT,EAAIU,GAAGqF,EAAO/G,aAAaU,cAAcO,EAAG,SAAS,CAACY,YAAY,CAAC,MAAQ,WAAWT,MAAM,CAAC,KAAO,kBAAkB,GAAGJ,EAAIuB,aAAa,IAAI,IAEp2CC,EAAkB,G,wHC+FP,GACfC,KAAA,eACAiF,OAAA,CAAAC,QACAC,WAAA,CACAC,sBAEA/E,OACA,OACAgF,cAAA,GACAC,gBAAA,GACA7B,aAAA,EACAG,gBAAA,KACA2B,aAAA,OAGAjF,SAAA,IACAkF,eAAA,gEACAC,WAAA,CACA7E,MACA,YAAA8E,oBAEAC,IAAArG,GACA,KAAAsG,OAAAC,SAAA,qBAAAvG,KAGAwE,UACA,OACA,CACA9F,MAAA,KAAAkB,GAAA,+BACA4G,UAAA,YACAjG,IAAA,aAEA,CACA7B,MAAA,KAAAkB,GAAA,gCACA4G,UAAA,KACAjG,IAAA,MAEA,CACA7B,MAAA,cACA8H,UAAA,aACA3B,YAAA,CAAA4B,aAAA,iBAIAlC,gBACA,YAAAvH,MAAA0J,IAAA5I,IAAA,IACAA,EACAqH,WAAA,KAAAa,gBAAAlI,EAAAwC,KAAA,OAGAsE,eACA,OACA+B,gBAAA,KAAAZ,cACAa,SAAAD,IACA,KAAAZ,cAAAY,KAIAlB,0BAAA,MAAAoB,EAAA,KAAAC,eACA,OACA,CACApI,MAAA,KAAAkB,GAAA,gCACA4G,UAAA,KACAjG,IAAA,KACAwG,MAAA,SAEA,CACArI,MAAA,KAAAkB,GAAA,+BACA4G,UAAA,YACAjG,IAAA,YACAwG,MAAA,QACAC,UAAA,GAEA,CACAtI,MAAA,KAAAkB,GAAA,uBACA4G,UAAA,SACAjG,IAAA,SACAwG,MAAA,QACAN,aAAA1B,IACA,MAAAkC,EAAA,CACA,kBACA,sBACA,oBACA,kBACAlC,IAAA,OACA,OAAA8B,EAAA,eAAAI,UAAA,CAAAlC,MAGA,CACArG,MAAA,KAAAkB,GAAA,yBACA4G,UAAA,WACAjG,IAAA,WACAwG,MAAA,QACAN,aAAA1B,GAAA8B,EAAA,6BACA9B,GAAA,mBAGA,CACArG,MAAA,KAAAkB,GAAA,sBACA4G,UAAA,QACAjG,IAAA,QACAwG,MAAA,SAEA,CACArI,MAAA,KAAAkB,GAAA,yBACA4G,UAAA,YACAjG,IAAA,YACAwG,MAAA,QACAN,aAAA1B,GAAA,KAAAmC,YAAAnC,IAEA,CACArG,MAAA,KAAAkB,GAAA,6BACA4G,UAAA,eACAjG,IAAA,eACAwG,MAAA,OACAlC,YAAA,CAAA4B,aAAA,kBAIAjB,4BACA,YAAAY,oBAAA,KAAAA,mBAAApJ,MACAmK,OAAAC,KAAA,KAAAhB,mBAAApJ,OAAA0J,IAAApG,IACA,MAAAxC,EAAA,KAAAsI,mBAAApJ,MAAAsD,GACA,OACAA,KACA+G,UAAAvJ,EAAAuJ,UACArJ,OAAAF,EAAAE,OACAsJ,SAAAxJ,EAAAwJ,UAAA,EACAC,MAAAzJ,EAAAyJ,OAAA,IACAtJ,aAAAH,EAAAG,gBATA,IAaAqH,mBACA,SAAAc,qBAAA,KAAAA,mBAAApJ,MAAA,SACA,MAAAA,EAAAmK,OAAAK,OAAA,KAAApB,mBAAApJ,OACA,OAAAA,EAAAN,OAAA,SAEA,MAAA+K,EAAAzK,EAAAV,OAAA,CAAAoL,EAAA5J,IAAA4J,GAAA5J,EAAAwJ,UAAA,MACA,OAAAK,KAAAC,MAAAH,EAAAzK,EAAAN,SAEA6I,oBACA,SAAAa,mBAAA,eACA,MAAApJ,EAAAmK,OAAAK,OAAA,KAAApB,mBAAApJ,OACA,OAAAA,EAAA0F,KAAA5E,GAAA,WAAAA,EAAAE,QAAA,YACAhB,EAAAoF,MAAAtE,GAAA,cAAAA,EAAAE,QAAA,UACA,WAGA6J,UACA,SAAAC,sBACA,OAEA,KAAAxB,OAAAC,SAAA,cAEA,MAAAwB,EAAAzK,aAAAC,QAAA,qBAAAyK,gBACA,GAAAD,EAAA,CACA,kBAAAE,GAAAC,KAAAC,MAAAJ,GACAE,IAAA,KAAAD,eACA,KAAAI,2BAGA9K,aAAAyB,WAAA,qBAAAiJ,gBACA1K,aAAAyB,WAAA,8BAAAiJ,gBACA,KAAA1B,OAAAC,SAAA,8BAIA3J,QAAA,IACAyL,eAAA,qBACAP,sBACA,aAAAE,iBACA,KAAAvG,QAAAnD,MAAA,CACAI,MAAA,iBACAC,QAAA,iEAEA,KAAA2J,QAAAC,KAAA,cACA,IAKAlE,kBAAA/D,GACAkB,QAAAgH,IAAA,oBAAAlI,GACA,KAAAgE,gBAAAhE,GAEAmI,aAAAC,GAEA,OAAAA,EAAAC,WAAA,KAIAD,EAAA3K,SAAA,OAAA2K,EAAA3K,SAAA,OAAA2K,EAAA3K,SAAA,KACA,mCAGA,KAPA,wCASAmH,iBAAA5E,EAAAoI,GACA,MAAApK,EAAA,KAAAmK,aAAAC,GACA,GAAApK,EAIA,OAHA,KAAAsK,SAAAtK,cAEA,KAAA8G,KAAA,KAAAY,gBAAA1F,EAAA,IAGA,KAAA8E,KAAA,KAAAY,gBAAA1F,EAAAoI,IAEAG,gBACA,MAAAC,EAAA,KAAA/C,cACAW,IAAApG,IAAA,CACAA,KACAoI,KAAA,KAAA1C,gBAAA1F,IAAA,MAEAzC,OAAAkL,KAAAL,MAEA,IAAAI,EAAApM,OAEA,OADA,KAAAkM,SAAAtK,MAAA,gDACA,KAIA,aAAAgC,EAAA,KAAAoI,KAAAI,EAAA,CACA,MAAAxK,EAAA,KAAAmK,aAAAC,GACA,GAAApK,EAEA,OADA,KAAAsK,SAAAtK,MAAA,oBAAAgC,MAAAhC,KACA,KAIA,OAAAwK,GAEA5B,YAAA8B,GACA,IAAAA,EAAA,YACA,MAAAC,EAAA,KACAC,EAAA,qBACAnF,EAAA4D,KAAAwB,MAAAxB,KAAAa,IAAAQ,GAAArB,KAAAa,IAAAS,IACA,SAAAG,YAAAJ,EAAArB,KAAA0B,IAAAJ,EAAAlF,IAAAuF,QAAA,OAAAJ,EAAAnF,MAEA,uBACA,SAAAgC,cAAArJ,OAEA,YADA,KAAAkM,SAAAW,QAAA,mCAIA,SAAAjF,gBAEA,YADA,KAAAsE,SAAAW,QAAA,4BAIA,MAAAvM,EAAA,KAAA+I,cAAAW,IAAApG,IAAA,CACAA,KACAoI,KAAA,KAAA1C,gBAAA1F,IAAA,MAGA,IACA,KAAA6D,aAAA,EAGA,MAAAqF,EAAAlM,aAAAC,QAAA,qBAAAyK,gBACA,GAAAwB,EACA,IACA,aAAA1M,GAAAoL,KAAAC,MAAAqB,GACA1M,GACA,KAAAgC,0BAAAhC,EAAA,gBAAAkL,gBAEA,MAAA/C,GACAzD,QAAAlD,MAAA,iDAAA2G,GAGA,MAAA7D,QAAAC,OAAAoI,KACA,4CAAAC,mBAAA,KAAA1B,gBACA,CACAhL,QACA2M,QAAA,KAAArF,kBAIAxH,EAAAsE,EAAAL,KAAA6I,QACAtM,aAAAuB,QAAA,qBAAAmJ,eAAAE,KAAA2B,UAAA,CACA/M,SACAmL,YAAA,KAAAD,kBAEA1K,aAAAyB,WAAA,8BAAAiJ,gBAEA,KAAA8B,aAAAhN,GAEA,MAAAwB,GAAA,IAAAyL,EACAvI,QAAAlD,MAAA,kBAAAA,GACA,KAAA6F,aAAA,EACA,KAAAyE,SAAAtK,OAAA,QAAAyL,EAAAzL,EAAA8C,gBAAA,IAAA2I,GAAA,QAAAA,IAAAhJ,YAAA,IAAAgJ,OAAA,EAAAA,EAAAzL,QAAA,8BAGA,mBAAAxB,GACA,KAAAmJ,cACA+D,cAAA,KAAA/D,cAGA,MAAAgE,EAAA,UACA,IACA,SAAAjC,eACA,UAAAkC,MAAA,gCAGA,MAAA9I,QAAAC,OAAAC,IACA,sCAAAxE,YAAA4M,mBAAA,KAAA1B,mBAGA,KAAA1B,OAAAC,SAAA,qBAAAnF,EAAAL,MAEA,MAAAoJ,EAAAhD,OAAAK,OAAApG,EAAAL,KAAA/D,OAAAoF,MACAtE,GAAA,uBAAAC,SAAAD,EAAAE,SAGA,GAAAmM,EAAA,CACAH,cAAA,KAAA/D,cACA,KAAA9B,aAAA,EACA7G,aAAAuB,QAAA,8BAAAmJ,eAAA,QAEA,MAAAhL,EAAAmK,OAAAK,OAAApG,EAAAL,KAAA/D,OAGA,KAAAH,8BAAA,CACAC,SACAC,SAAA,WACAC,QACAC,UAAA,KAAA+K,eACA7K,UAAA,CACAM,QAAA,uCAAAT,EAAAN,gBACA4B,MAAA,GAAAtB,EAAAa,OAAAC,GAAA,cAAAA,EAAAE,QAAAtB,sDAAAM,EAAAa,OAAAC,GAAA,WAAAA,EAAAE,QAAAtB,wBAGAU,cAAA,CACAK,QAAA,cACAC,QAAA,eAIA,MAAAY,GAAA,IAAA8L,EACA5I,QAAAlD,MAAA,wBAAAA,GACA,eAAA8L,EAAA9L,EAAA8C,gBAAA,IAAAgJ,OAAA,EAAAA,EAAApM,UACAgM,cAAA,KAAA/D,cACA,KAAA9B,aAAA,EACA7G,aAAAyB,WAAA,qBAAAiJ,gBACA1K,aAAAyB,WAAA,8BAAAiJ,yBAKAiC,IACA,KAAAhE,aAAAoE,YAAAJ,EAAA,MAEA,gCACA,IACA,MAAAlC,EAAAzK,aAAAC,QAAA,qBAAAyK,gBACAsC,EAAAhN,aAAAC,QAAA,8BAAAyK,gBAEA,GAAAD,EAAA,CACA,aAAAjL,EAAA,YAAAmL,GAAAC,KAAAC,MAAAJ,GAEA,GAAAE,IAAA,KAAAD,eACA,UAAAkC,MAAA,qCAGA,SAAAlC,eACA,UAAAkC,MAAA,gCAGA,MAAA9I,QAAAC,OAAAC,IACA,sCAAAxE,YAAA4M,mBAAA,KAAA1B,mBAGA,GAAA5G,EAAAL,KAAA,CACA,KAAAuF,OAAAC,SAAA,qBAAAnF,EAAAL,MAEA,MAAAoJ,EAAAhD,OAAAK,OAAApG,EAAAL,KAAA/D,OAAAoF,MACAtE,GAAA,uBAAAC,SAAAD,EAAAE,SAGA,GAAAmM,GAAAG,GAGA,GAAAH,EAAA,CACA,KAAAhG,aAAA,EACA7G,aAAAuB,QAAA,8BAAAmJ,eAAA,QAEA,MAAAhL,EAAAmK,OAAAK,OAAApG,EAAAL,KAAA/D,OAGA,KAAAH,8BAAA,CACAC,SACAC,SAAA,WACAC,QACAC,UAAA,KAAA+K,eACA7K,UAAA,CACAM,QAAA,uCAAAT,EAAAN,gBACA4B,MAAA,GAAAtB,EAAAa,OAAAC,GAAA,cAAAA,EAAAE,QAAAtB,sDAAAM,EAAAa,OAAAC,GAAA,WAAAA,EAAAE,QAAAtB,wBAGAU,cAAA,CACAK,QAAA,cACAC,QAAA,oBArBA,KAAAyG,aAAA,EACA,KAAA2F,aAAAhN,KA0BA,MAAAwB,GACAkD,QAAAlD,MAAA,uCAAAA,GACAhB,aAAAyB,WAAA,qBAAAiJ,gBACA1K,aAAAyB,WAAA,8BAAAiJ,kBAGAuC,gBACA,KAAAtE,cACA+D,cAAA,KAAA/D,cAGA,KAAAK,OAAAC,SAAA,4BAEAtF,MAAA,CAEA+G,eAAA,CACAwC,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAApE,OAAAC,SAAA,2BACA,KAAAN,cACA+D,cAAA,KAAA/D,cAGA,KAAAmC,4BAGAuC,WAAA,MCvhBqV,I,wBCQjVjJ,EAAY,eACd,EACA1C,EACAyB,GACA,EACA,KACA,WACA,MAIa,aAAAiB,E,2CCnBf", "file": "static/js/chunk-b5ec4b6e.1df9223a.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "/**\r\n * 通知混入 - 提供通用的通知处理逻辑\r\n * 用于任务完成、文件上传/下载完成等场景\r\n */\r\nexport default {\r\n  methods: {\r\n    /**\r\n     * 添加任务完成通知\r\n     * @param {Object} options - 通知选项\r\n     * @param {string} options.taskId - 任务ID\r\n     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)\r\n     * @param {Array} options.nodes - 节点数组\r\n     * @param {string} options.projectId - 项目ID\r\n     * @param {Object} options.titles - 自定义标题 {success, error}\r\n     * @param {Object} options.templates - 自定义消息模板 {success, error}\r\n     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}\r\n     */\r\n    addTaskCompletionNotification({\r\n      taskId,\r\n      taskType,\r\n      nodes,\r\n      projectId,\r\n      titles = {},\r\n      templates = {},\r\n      statusMapping = {}\r\n    }) {\r\n      // 检查是否已经发送过通知\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      if (localStorage.getItem(notificationSentKey)) {\r\n        return; // 已经发送过通知，不再重复发送\r\n      }\r\n\r\n      // 设置默认状态映射\r\n      const defaultStatusMapping = {\r\n        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'\r\n        failure: ['failed']                // 失败状态通常是'failed'\r\n      };\r\n\r\n      // 合并自定义状态映射\r\n      const finalStatusMapping = {\r\n        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],\r\n        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]\r\n      };\r\n\r\n      // 计算成功和失败的节点数量\r\n      const successNodes = nodes.filter(node =>\r\n        finalStatusMapping.success.includes(node.status) && !node.error_detail\r\n      ).length;\r\n      const failedNodes = nodes.filter(node =>\r\n        finalStatusMapping.failure.includes(node.status) || node.error_detail\r\n      ).length;\r\n      const hasFailures = failedNodes > 0;\r\n\r\n      // 准备通知标题\r\n      let notificationTitle;\r\n      if (hasFailures) {\r\n        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);\r\n      } else {\r\n        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);\r\n      }\r\n\r\n      // 准备通知内容\r\n      let notificationMessage;\r\n      if (hasFailures) {\r\n        notificationMessage = templates.error ||\r\n          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;\r\n      } else {\r\n        notificationMessage = templates.success ||\r\n          `All ${nodes.length} nodes completed successfully.`;\r\n      }\r\n\r\n      // 添加到全局通知中心\r\n      this.addNotification({\r\n        title: notificationTitle,\r\n        message: notificationMessage,\r\n        type: hasFailures ? 'error' : 'success',\r\n        taskId: taskId\r\n      });\r\n\r\n      // 标记已发送通知\r\n      localStorage.setItem(notificationSentKey, 'true');\r\n    },\r\n\r\n    /**\r\n     * 获取默认的成功标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultSuccessTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed',\r\n        'upload': 'File Upload Completed',\r\n        'download': 'File Download Completed',\r\n        'tool': 'Tool Execution Completed'\r\n      };\r\n      return titles[taskType] || 'Operation Completed';\r\n    },\r\n\r\n    /**\r\n     * 获取默认的错误标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultErrorTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed with Errors',\r\n        'upload': 'File Upload Completed with Errors',\r\n        'download': 'File Download Completed with Errors',\r\n        'tool': 'Tool Execution Completed with Errors'\r\n      };\r\n      return titles[taskType] || 'Operation Completed with Errors';\r\n    },\r\n\r\n    /**\r\n     * 清除任务通知标记\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型\r\n     * @param {string} projectId - 项目ID\r\n     */\r\n    clearTaskNotificationMark(taskId, taskType, projectId) {\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      localStorage.removeItem(notificationSentKey);\r\n    }\r\n  }\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"proxy-selector\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.isDetecting,\"disabled\":_vm.disabled},on:{\"click\":_vm.fetchReachableIps}},[_vm._v(\" \"+_vm._s(_vm.$t('common.detectReachableIps') || '检测可达IP')+\" \")]),(_vm.reachableIps.length)?_c('a-select',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"16px\"},attrs:{\"placeholder\":_vm.$t('tool.selectReachableIp') || '选择可达IP',\"disabled\":_vm.disabled},model:{value:(_vm.selectedIpValue),callback:function ($$v) {_vm.selectedIpValue=$$v},expression:\"selectedIpValue\"}},_vm._l((_vm.reachableIps),function(ip){return _c('a-select-option',{key:ip,attrs:{\"value\":ip}},[_vm._v(\" \"+_vm._s(ip)+\" \")])}),1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"proxy-selector\">\r\n    <!-- 检测IP按钮 -->\r\n    <a-button\r\n      class=\"nav-style-button\"\r\n      @click=\"fetchReachableIps\"\r\n      :loading=\"isDetecting\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      {{ $t('common.detectReachableIps') || '检测可达IP' }}\r\n    </a-button>\r\n\r\n    <!-- IP选择下拉框 -->\r\n    <a-select\r\n      v-if=\"reachableIps.length\"\r\n      v-model=\"selectedIpValue\"\r\n      style=\"width: 100%; margin-top: 16px;\"\r\n      :placeholder=\"$t('tool.selectReachableIp') || '选择可达IP'\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <a-select-option v-for=\"ip in reachableIps\" :key=\"ip\" :value=\"ip\">\r\n        {{ ip }}\r\n      </a-select-option>\r\n    </a-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProxySelector',\r\n  props: {\r\n    // 是否禁用控件\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 初始选中的IP\r\n    value: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isDetecting: false,\r\n      reachableIps: [],\r\n      selectedIpValue: this.value\r\n    };\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {\r\n    // 监听外部传入的value变化\r\n    value(newValue) {\r\n      this.selectedIpValue = newValue;\r\n    },\r\n    // 监听内部selectedIpValue变化，向外发送事件\r\n    selectedIpValue(newValue) {\r\n      this.$emit('input', newValue);\r\n      this.$emit('change', newValue);\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchReachableIps() {\r\n      this.isDetecting = true;\r\n      try {\r\n        const response = await axios.get('/api/proxy/detect');\r\n        this.reachableIps = response.data.reachable_ips;\r\n        if (this.reachableIps.length) {\r\n          this.selectedIpValue = this.reachableIps[0];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error detecting reachable IPs:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to detect reachable IPs'\r\n        });\r\n      } finally {\r\n        this.isDetecting = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProxySelector.vue?vue&type=template&id=46f0b65a&scoped=true\"\nimport script from \"./ProxySelector.vue?vue&type=script&lang=js\"\nexport * from \"./ProxySelector.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46f0b65a\",\n  null\n  \n)\n\nexport default component.exports", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  every: function every(fn) {\n    anObject(this);\n    aFunction(fn);\n    return !iterate(this, function (value, stop) {\n      if (!fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"2px\"}},[_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.fileDownload')))])]),_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.downloading},on:{\"click\":_vm.handleDownload}},[_vm._v(\" \"+_vm._s(_vm.$t('fileDownload.startDownload'))+\" \")])],1),_c('div',{staticClass:\"main-content\"},[_c('div',{staticClass:\"left-section\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.downloading},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedProxyIp),callback:function ($$v) {_vm.selectedProxyIp=$$v},expression:\"selectedProxyIp\"}}),_c('p',{staticStyle:{\"margin-top\":\"16px\",\"color\":\"gray\",\"font-size\":\"12px\"}},[_vm._v(\" Default download path: \\\\infocollect\\\\cache\\\\download\\\\ \")])],1)],1),_c('div',{staticClass:\"right-section config-table\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('a-table',{staticClass:\"bordered-nodes-table\",attrs:{\"dataSource\":_vm.nodesWithPath,\"columns\":_vm.columns,\"rowKey\":\"ip\",\"pagination\":{\n            pageSize: 10,\n            total: _vm.nodes.length,\n            showSizeChanger: false\n          },\"rowSelection\":_vm.rowSelection},scopedSlots:_vm._u([{key:\"remotePath\",fn:function(text, record){return [_c('a-input',{attrs:{\"placeholder\":_vm.$t('fileDownload.enterDownloadPath')},on:{\"change\":(e) => _vm.updateRemotePath(record.ip, e.target.value)},model:{value:(record.remotePath),callback:function ($$v) {_vm.$set(record, \"remotePath\", $$v)},expression:\"record.remotePath\"}})]}}])})],1)],1)]),_c('a-card',{staticClass:\"compact-card\",staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.$t('fileDownload.downloadProgress')}},[_c('template',{slot:\"extra\"},[_c('span',[_vm._v(\"Overall Progress: \"+_vm._s(_vm.downloadProgress)+\"%\")])]),_c('a-progress',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"percent\":_vm.downloadProgress,\"status\":_vm.progressBarStatus}}),_c('a-table',{attrs:{\"dataSource\":_vm.downloadProgressTableData,\"columns\":_vm.downloadProgressColumns,\"rowKey\":\"ip\",\"pagination\":false},scopedSlots:_vm._u([{key:\"errorDetail\",fn:function(text, record){return [(record && record.error_detail)?_c('a-popover',{attrs:{\"placement\":\"topLeft\"}},[_c('template',{slot:\"content\"},[_c('p',[_vm._v(\"Time: \"+_vm._s(record.error_detail.time))]),_c('p',[_vm._v(\"Type: \"+_vm._s(record.error_detail.type))]),_c('p',[_vm._v(\"Message: \"+_vm._s(record.error_detail.message))])]),_c('a-icon',{staticStyle:{\"color\":\"#ff4d4f\"},attrs:{\"type\":\"info-circle\"}})],2):_vm._e()]}}])})],2)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"padding: 2px;\">\r\n    <div class=\"card-header-wrapper\">\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"logo-wrapper\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n            <path :fill=\"'currentColor'\" d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z\"/>\r\n          </svg>\r\n        </div>\r\n        <h6 class=\"font-semibold m-0\">{{ $t('headTopic.fileDownload') }}</h6>\r\n      </div>\r\n      <a-button @click=\"handleDownload\" :loading=\"downloading\" class=\"nav-style-button\">\r\n        {{ $t('fileDownload.startDownload') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <div class=\"left-section\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureProxy')\">\r\n          <proxy-selector\r\n            v-model=\"selectedProxyIp\"\r\n            :disabled=\"downloading\"\r\n            @change=\"handleProxyChange\"\r\n          />\r\n          <p style=\"margin-top: 16px; color: gray; font-size: 12px;\">\r\n            Default download path: \\infocollect\\cache\\download\\\r\n          </p>\r\n        </a-card>\r\n      </div>\r\n\r\n      <div class=\"right-section config-table\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureNodes')\">\r\n          <a-table\r\n            :dataSource=\"nodesWithPath\"\r\n            :columns=\"columns\"\r\n            rowKey=\"ip\"\r\n            :pagination=\"{\r\n              pageSize: 10,\r\n              total: nodes.length,\r\n              showSizeChanger: false\r\n            }\"\r\n            :rowSelection=\"rowSelection\"\r\n            class=\"bordered-nodes-table\"\r\n          >\r\n            <template slot=\"remotePath\" slot-scope=\"text, record\">\r\n              <a-input\r\n                v-model=\"record.remotePath\"\r\n                :placeholder=\"$t('fileDownload.enterDownloadPath')\"\r\n                @change=\"(e) => updateRemotePath(record.ip, e.target.value)\"\r\n              />\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- Add progress card after the alert -->\r\n    <a-card\r\n      :title=\"$t('fileDownload.downloadProgress')\"\r\n      style=\"margin-top: 16px;\"\r\n      class=\"compact-card\"\r\n    >\r\n      <template slot=\"extra\">\r\n        <span>Overall Progress: {{ downloadProgress }}%</span>\r\n      </template>\r\n\r\n      <a-progress\r\n        :percent=\"downloadProgress\"\r\n        :status=\"progressBarStatus\"\r\n        style=\"margin-bottom: 16px;\"\r\n      />\r\n\r\n      <a-table\r\n        :dataSource=\"downloadProgressTableData\"\r\n        :columns=\"downloadProgressColumns\"\r\n        rowKey=\"ip\"\r\n        :pagination=\"false\"\r\n      >\r\n        <template slot=\"errorDetail\" slot-scope=\"text, record\">\r\n          <a-popover v-if=\"record && record.error_detail\" placement=\"topLeft\">\r\n            <template slot=\"content\">\r\n              <p>Time: {{ record.error_detail.time }}</p>\r\n              <p>Type: {{ record.error_detail.type }}</p>\r\n              <p>Message: {{ record.error_detail.message }}</p>\r\n            </template>\r\n            <a-icon type=\"info-circle\" style=\"color: #ff4d4f\" />\r\n          </a-popover>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\n\r\nexport default {\r\n  name: 'FileDownload',\r\n  mixins: [NotificationMixin],\r\n  components: {\r\n    ProxySelector\r\n  },\r\n  data() {\r\n    return {\r\n      selectedNodes: [],\r\n      nodeRemotePaths: {},\r\n      downloading: false,\r\n      selectedProxyIp: null,\r\n      pollInterval: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['nodes', 'activeDownloadTask', 'currentProject', 'sidebarColor']),\r\n    activeTask: {\r\n      get() {\r\n        return this.activeDownloadTask;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateDownloadTask', value);\r\n      }\r\n    },\r\n    columns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        },\r\n        {\r\n          title: 'Remote Path',\r\n          dataIndex: 'remotePath',\r\n          scopedSlots: { customRender: 'remotePath' }\r\n        }\r\n      ];\r\n    },\r\n    nodesWithPath() {\r\n      return this.nodes.map(node => ({\r\n        ...node,\r\n        remotePath: this.nodeRemotePaths[node.ip] || ''\r\n      }));\r\n    },\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedNodes,\r\n        onChange: (selectedRowKeys) => {\r\n          this.selectedNodes = selectedRowKeys;\r\n        },\r\n      };\r\n    },\r\n    downloadProgressColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip',\r\n          width: '120px'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name',\r\n          width: '150px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: '100px',\r\n          customRender: (text) => {\r\n            const color = {\r\n              'pending': '#1890ff',\r\n              'downloading': '#1890ff',\r\n              'completed': '#52c41a',\r\n              'failed': '#f5222d'\r\n            }[text] || '#000';\r\n            return <span style={{ color }}>{text}</span>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.progress'),\r\n          dataIndex: 'progress',\r\n          key: 'progress',\r\n          width: '200px',\r\n          customRender: (text) => (\r\n            <a-progress percent={text || 0} size=\"small\" />\r\n          )\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.speed'),\r\n          dataIndex: 'speed',\r\n          key: 'speed',\r\n          width: '100px'\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.fileSize'),\r\n          dataIndex: 'file_size',\r\n          key: 'file_size',\r\n          width: '100px',\r\n          customRender: (text) => this.formatBytes(text)\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.errorDetails'),\r\n          dataIndex: 'error_detail',\r\n          key: 'error_detail',\r\n          width: '60px',\r\n          scopedSlots: { customRender: 'errorDetail' }\r\n        }\r\n      ];\r\n    },\r\n    downloadProgressTableData() {\r\n      if (!this.activeDownloadTask || !this.activeDownloadTask.nodes) return [];\r\n      return Object.keys(this.activeDownloadTask.nodes).map(ip => {\r\n        const node = this.activeDownloadTask.nodes[ip];\r\n        return {\r\n          ip,\r\n          host_name: node.host_name,\r\n          status: node.status,\r\n          progress: node.progress || 0,\r\n          speed: node.speed || '-',\r\n          error_detail: node.error_detail\r\n        };\r\n      });\r\n    },\r\n    downloadProgress() {\r\n      if (!this.activeDownloadTask || !this.activeDownloadTask.nodes) return 0;\r\n      const nodes = Object.values(this.activeDownloadTask.nodes);\r\n      if (nodes.length === 0) return 0;\r\n\r\n      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);\r\n      return Math.round(totalProgress / nodes.length);\r\n    },\r\n    progressBarStatus() {\r\n      if (!this.activeDownloadTask) return 'normal';\r\n      const nodes = Object.values(this.activeDownloadTask.nodes);\r\n      if (nodes.some(node => node.status === 'failed')) return 'exception';\r\n      if (nodes.every(node => node.status === 'completed')) return 'success';\r\n      return 'active';\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    this.$store.dispatch('fetchNodes');\r\n    // Only check active download task if we're in the same project\r\n    const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveDownloadTask();\r\n      } else {\r\n        // Clear task info if it belongs to a different project\r\n        localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateDownloadTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      console.log('Proxy IP changed:', ip);\r\n      this.selectedProxyIp = ip;\r\n    },\r\n    validatePath(path) {\r\n      // Check for absolute path and disallowed characters\r\n      if (!path.startsWith('/')) {\r\n        return 'Path must be absolute (start with /)';\r\n      }\r\n\r\n      if (path.includes('..') || path.includes('./') || path.includes('~')) {\r\n        return 'Path cannot contain ./, ../ or ~';\r\n      }\r\n\r\n      return null; // Path is valid\r\n    },\r\n    updateRemotePath(ip, path) {\r\n      const error = this.validatePath(path);\r\n      if (error) {\r\n        this.$message.error(error);\r\n        // Clear invalid path\r\n        this.$set(this.nodeRemotePaths, ip, '');\r\n        return;\r\n      }\r\n      this.$set(this.nodeRemotePaths, ip, path);\r\n    },\r\n    validatePaths() {\r\n      const selectedPaths = this.selectedNodes\r\n        .map(ip => ({\r\n          ip,\r\n          path: this.nodeRemotePaths[ip] || ''\r\n        }))\r\n        .filter(item => item.path);\r\n\r\n      if (!selectedPaths.length) {\r\n        this.$message.error('Please enter remote paths for selected nodes');\r\n        return null;\r\n      }\r\n\r\n      // Validate all paths\r\n      for (const {ip, path} of selectedPaths) {\r\n        const error = this.validatePath(path);\r\n        if (error) {\r\n          this.$message.error(`Invalid path for ${ip}: ${error}`);\r\n          return null;\r\n        }\r\n      }\r\n\r\n      return selectedPaths;\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    async handleDownload() {\r\n      if (!this.selectedNodes.length) {\r\n        this.$message.warning('Please select at least one node');\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedProxyIp) {\r\n        this.$message.warning('Please select a proxy IP');\r\n        return;\r\n      }\r\n\r\n      const nodes = this.selectedNodes.map(ip => ({\r\n        ip,\r\n        path: this.nodeRemotePaths[ip] || ''\r\n      }));\r\n\r\n      try {\r\n        this.downloading = true;\r\n\r\n        // 清除之前的下载任务通知记录\r\n        const previousTaskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n        if (previousTaskInfo) {\r\n          try {\r\n            const { taskId } = JSON.parse(previousTaskInfo);\r\n            if (taskId) {\r\n              this.clearTaskNotificationMark(taskId, 'download', this.currentProject);\r\n            }\r\n          } catch (e) {\r\n            console.error('Error clearing previous download notification:', e);\r\n          }\r\n        }\r\n        const response = await axios.post(\r\n          `/api/file_transfer/download/start?dbFile=${encodeURIComponent(this.currentProject)}`,\r\n          {\r\n            nodes,\r\n            proxyIp: this.selectedProxyIp\r\n          }\r\n        );\r\n\r\n        const taskId = response.data.task_id;\r\n        localStorage.setItem(`downloadTask_${this.currentProject}`, JSON.stringify({\r\n          taskId,\r\n          projectFile: this.currentProject\r\n        }));\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n\r\n        this.startPolling(taskId);\r\n\r\n      } catch (error) {\r\n        console.error('Download error:', error);\r\n        this.downloading = false;\r\n        this.$message.error(error.response?.data?.error || 'Failed to start download');\r\n      }\r\n    },\r\n    async startPolling(taskId) {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n\r\n      const pollStatus = async () => {\r\n        try {\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          this.$store.dispatch('updateDownloadTask', response.data);\r\n\r\n          const allCompleted = Object.values(response.data.nodes).every(\r\n            node => ['completed', 'failed'].includes(node.status)\r\n          );\r\n\r\n          if (allCompleted) {\r\n            clearInterval(this.pollInterval);\r\n            this.downloading = false;\r\n            localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n            const nodes = Object.values(response.data.nodes);\r\n\r\n            // 使用混入中的方法添加下载完成通知\r\n            this.addTaskCompletionNotification({\r\n              taskId,\r\n              taskType: 'download',\r\n              nodes,\r\n              projectId: this.currentProject,\r\n              templates: {\r\n                success: `File downloaded successfully to all ${nodes.length} nodes.`,\r\n                error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n              },\r\n              // 指定下载任务的状态映射\r\n              statusMapping: {\r\n                success: ['completed'],\r\n                failure: ['failed']\r\n              }\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Error polling status:', error);\r\n          if (error.response?.status === 404) {\r\n            clearInterval(this.pollInterval);\r\n            this.downloading = false;\r\n            localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n            localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n          }\r\n        }\r\n      };\r\n\r\n      await pollStatus();\r\n      this.pollInterval = setInterval(pollStatus, 10000); // 降低轮询频率到10秒\r\n    },\r\n    async checkActiveDownloadTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`downloadTask_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`downloadTaskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/download/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateDownloadTask', response.data);\r\n\r\n            const allCompleted = Object.values(response.data.nodes).every(\r\n              node => ['completed', 'failed'].includes(node.status)\r\n            );\r\n\r\n            if (!allCompleted && !taskCompleted) {\r\n              this.downloading = true;\r\n              this.startPolling(taskId);\r\n            } else if (allCompleted) {\r\n              this.downloading = false;\r\n              localStorage.setItem(`downloadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n              const nodes = Object.values(response.data.nodes);\r\n\r\n              // 使用混入中的方法添加下载完成通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType: 'download',\r\n                nodes,\r\n                projectId: this.currentProject,\r\n                templates: {\r\n                  success: `File downloaded successfully to all ${nodes.length} nodes.`,\r\n                  error: `${nodes.filter(node => node.status === 'completed').length} nodes completed file download successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n                },\r\n                // 指定下载任务的状态映射\r\n                statusMapping: {\r\n                  success: ['completed'],\r\n                  failure: ['failed']\r\n                }\r\n              });\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active download task:', error);\r\n        localStorage.removeItem(`downloadTask_${this.currentProject}`);\r\n        localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n    beforeDestroy() {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n      // Clear download task when component is destroyed\r\n      this.$store.dispatch('updateDownloadTask', null);\r\n    },\r\n    watch: {\r\n      // Add watcher for currentProject changes\r\n      currentProject: {\r\n        handler(newProject, oldProject) {\r\n          if (newProject !== oldProject) {\r\n            // Clear previous project's task status\r\n            this.$store.dispatch('updateDownloadTask', null);\r\n            if (this.pollInterval) {\r\n              clearInterval(this.pollInterval);\r\n            }\r\n            // Check for active tasks in new project\r\n            this.checkActiveDownloadTask();\r\n          }\r\n        },\r\n        immediate: true\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 卡片头部样式 */\r\n.card-header-wrapper {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo-wrapper {\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n}\r\n\r\n/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */\r\n.main-content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 2fr;\r\n  gap: 16px;\r\n}\r\n\r\n/* 左侧区域的Flexbox布局 */\r\n.left-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧卡片样式 */\r\n.left-section .compact-card:first-child {\r\n  margin-bottom: 8px;\r\n  flex: 2; /* Takes up 2/3 of the available space */\r\n}\r\n\r\n.left-section .compact-card:last-child {\r\n  flex: 1; /* Takes up 1/3 of the available space */\r\n}\r\n\r\n/* 响应式布局 - 在移动设备上切换为单列布局 */\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 调整输入框样式，使其更紧凑 */\r\n.bordered-nodes-table .ant-input {\r\n  height: 28px;\r\n  padding: 0 8px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileDownload.vue?vue&type=template&id=c8f24356&scoped=true\"\nimport script from \"./FileDownload.vue?vue&type=script&lang=js\"\nexport * from \"./FileDownload.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileDownload.vue?vue&type=style&index=0&id=c8f24356&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c8f24356\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileDownload.vue?vue&type=style&index=0&id=c8f24356&prod&scoped=true&lang=css\""], "sourceRoot": ""}