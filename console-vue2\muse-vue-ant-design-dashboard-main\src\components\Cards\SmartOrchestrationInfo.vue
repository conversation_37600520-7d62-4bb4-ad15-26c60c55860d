<template>
  <a-card class="header-solid h-full" :bordered="false">
    <template #title>
      <h6 class="font-semibold m-0">智能测试用例分析</h6>
    </template>
    <template #extra>
      <a-button 
        type="primary" 
        :loading="analyzing" 
        @click="showAnalysisModal"
        :disabled="!hasNodeData"
      >
        <template #icon>
          <BranchesOutlined />
        </template>
        开始智能分析
      </a-button>
    </template>

    <!-- 自然语言查询测试用例 -->
    <a-row :gutter="16" class="mb-16">
      <a-col :span="24">
        <a-card title="智能测试用例查询" size="small" class="query-card">
          <a-form layout="vertical">
            <a-form-item label="自然语言查询">
              <a-input-search
                v-model:value="queryText"
                placeholder="请输入自然语言描述，例如：查找与网络安全相关的测试用例"
                enter-button="搜索"
                size="large"
                :loading="searching"
                @search="searchTestcases"
              />
            </a-form-item>
            <a-form-item v-if="queryText">
              <a-row :gutter="8">
                <a-col :span="8">
                  <a-input-number
                    v-model:value="searchParams.top_k"
                    :min="1"
                    :max="50"
                    placeholder="返回数量"
                    style="width: 100%"
                  />
                  <div class="param-label">返回数量</div>
                </a-col>
                <a-col :span="8">
                  <a-input-number
                    v-model:value="searchParams.score_threshold"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    placeholder="相似度阈值"
                    style="width: 100%"
                  />
                  <div class="param-label">相似度阈值</div>
                </a-col>
                <a-col :span="8">
                  <a-button type="primary" @click="searchTestcases" :loading="searching" block>
                    <template #icon>
                      <SearchOutlined />
                    </template>
                    搜索
                  </a-button>
                </a-col>
              </a-row>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索结果 -->
    <a-row :gutter="16" class="mb-16" v-if="searchResults.length > 0">
      <a-col :span="24">
        <a-card title="搜索结果" size="small">
          <template #extra>
            <a-space>
              <a-tag color="blue">找到 {{ searchResults.length }} 个相关测试用例</a-tag>
              <a-button size="small" @click="clearSearchHistory">
                <template #icon>
                  <a-icon type="delete" />
                </template>
                清除结果
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="searchResultColumns"
            :data-source="searchResults"
            :pagination="false"
            size="small"
            :scroll="{ x: 800 }"
          >
            <template slot="Testcase_Number" slot-scope="text, record">
              <a @click="viewTestcaseDetail(record)" style="color: #1890ff; cursor: pointer;">
                {{ record.Testcase_Number }}
              </a>
            </template>

            <template slot="Testcase_Level" slot-scope="text, record">
              <a-tag :color="getLevelColor(record.Testcase_Level)">
                {{ record.Testcase_Level }}
              </a-tag>
            </template>

            <template slot="similarity" slot-scope="text, record">
              <a-progress
                :percent="Math.round(record.similarity * 100)"
                size="small"
                :stroke-color="getSimilarityColor(record.similarity)"
              />
              <span style="margin-left: 8px; font-size: 12px;">
                {{ (record.similarity * 100).toFixed(1) }}%
              </span>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 节点状态卡片 -->
    <a-row :gutter="16" class="mb-16">
      <a-col :span="24">
        <a-alert
          v-if="!hasNodeData"
          message="未检测到节点数据"
          description="请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析"
          type="info"
          show-icon
          class="mb-16"
        />
        <a-alert
          v-else
          message="节点数据已就绪"
          :description="`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`"
          type="success"
          show-icon
          class="mb-16"
        />
      </a-col>
    </a-row>

    <!-- 分析结果展示 -->
    <div v-if="analysisResults.length > 0">
      <a-divider orientation="left">分析结果</a-divider>
      
      <a-collapse v-model:activeKey="activeKeys" class="mb-16">
        <a-collapse-panel 
          v-for="(result, index) in analysisResults" 
          :key="index"
          :header="`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`"
        >
          <template #extra>
            <a-tag :color="getStatusColor(result.status)">
              {{ getStatusText(result.status) }}
            </a-tag>
          </template>

          <!-- 查询信息 -->
          <a-descriptions title="查询信息" :column="1" size="small" class="mb-16">
            <a-descriptions-item label="信息类型">{{ result.info_type }}</a-descriptions-item>
            <a-descriptions-item label="查询文本">{{ result.query_text }}</a-descriptions-item>
            <a-descriptions-item label="匹配用例数">{{ result.matched_testcases.length }}</a-descriptions-item>
          </a-descriptions>

          <!-- 匹配的测试用例 -->
          <a-divider orientation="left" orientation-margin="0">匹配的测试用例</a-divider>
          <a-table
            :dataSource="result.matched_testcases"
            :columns="testcaseColumns"
            :pagination="false"
            size="small"
            class="mb-16"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'Testcase_Name'">
                <a-tooltip :title="record.Testcase_Name">
                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>
                </a-tooltip>
              </template>
              <template v-if="column.key === 'Testcase_TestSteps'">
                <a-tooltip :title="record.Testcase_TestSteps">
                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>
                </a-tooltip>
              </template>
            </template>
          </a-table>

          <!-- 执行结果 -->
          <a-divider orientation="left" orientation-margin="0">执行结果</a-divider>
          <a-table
            :dataSource="result.execution_results"
            :columns="executionColumns"
            :pagination="false"
            size="small"
            :expandable="{ expandedRowRender }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'testcase_name'">
                <a-tooltip :title="record.testcase_name">
                  <span>{{ truncateText(record.testcase_name, 30) }}</span>
                </a-tooltip>
              </template>
            </template>
          </a-table>
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 分析配置模态框 -->
    <a-modal
      v-model:visible="analysisModalVisible"
      title="智能测试用例分析配置"
      :width="800"
      @ok="startAnalysis"
      :confirmLoading="analyzing"
    >
      <a-form layout="vertical">
        <a-form-item label="选择节点" required>
          <a-select 
            v-model:value="selectedNodeId" 
            placeholder="请选择要分析的节点"
            style="width: 100%"
          >
            <a-select-option 
              v-for="node in availableNodes" 
              :key="node.id" 
              :value="node.id"
            >
              {{ node.name }} ({{ node.ip }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="选择分析类型" required>
          <a-checkbox-group v-model:value="selectedAnalysisTypes">
            <a-row>
              <a-col :span="8" v-for="type in availableDataTypes" :key="type">
                <a-checkbox :value="type">{{ getTypeName(type) }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 测试用例详情模态框 -->
    <TestCaseDetailModal
      :visible="testcaseDetailVisible"
      :testcase="selectedTestcase"
      @close="testcaseDetailVisible = false"
    />
  </a-card>
</template>

<script>
import {
  BranchesOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from '@/api/axiosInstance';
import TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';

export default {
  name: 'IntelligentTestCaseInfo',
  components: {
    BranchesOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    CloseCircleOutlined,
    SearchOutlined,
    TestCaseDetailModal
  },
  data() {
    return {
      analyzing: false,
      analysisModalVisible: false,
      selectedNodeId: null,
      selectedAnalysisTypes: [],
      analysisResults: [],
      activeKeys: ['0'],
      availableNodes: [],
      availableDataTypes: [],

      // 查询相关数据
      queryText: '',
      searching: false,
      searchResults: [],
      searchParams: {
        top_k: 10,
        score_threshold: 0.5
      },
      testcaseDetailVisible: false,
      selectedTestcase: null,
      
      searchResultColumns: [
        {
          title: '用例编号',
          dataIndex: 'Testcase_Number',
          key: 'Testcase_Number',
          width: 120,
          scopedSlots: { customRender: 'Testcase_Number' }
        },
        {
          title: '用例名称',
          dataIndex: 'Testcase_Name',
          key: 'Testcase_Name',
          ellipsis: true,
          width: 300
        },
        {
          title: '用例级别',
          dataIndex: 'Testcase_Level',
          key: 'Testcase_Level',
          width: 100,
          scopedSlots: { customRender: 'Testcase_Level' }
        },
        {
          title: '相似度',
          dataIndex: 'similarity',
          key: 'similarity',
          width: 150,
          scopedSlots: { customRender: 'similarity' }
        }
      ],

      testcaseColumns: [
        {
          title: '用例编号',
          dataIndex: 'Testcase_Number',
          key: 'Testcase_Number',
          width: 120
        },
        {
          title: '用例名称',
          dataIndex: 'Testcase_Name',
          key: 'Testcase_Name',
          ellipsis: true
        },
        {
          title: '用例级别',
          dataIndex: 'Testcase_Level',
          key: 'Testcase_Level',
          width: 80
        },
        {
          title: '测试步骤',
          dataIndex: 'Testcase_TestSteps',
          key: 'Testcase_TestSteps',
          ellipsis: true
        }
      ],
      
      executionColumns: [
        {
          title: '用例编号',
          dataIndex: 'testcase_number',
          key: 'testcase_number',
          width: 120
        },
        {
          title: '用例名称',
          dataIndex: 'testcase_name',
          key: 'testcase_name',
          ellipsis: true
        },
        {
          title: '执行状态',
          dataIndex: 'status',
          key: 'status',
          width: 100
        },
        {
          title: '执行消息',
          dataIndex: 'message',
          key: 'message',
          ellipsis: true
        }
      ]
    };
  },
  
  computed: {
    hasNodeData() {
      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;
    }
  },
  
  mounted() {
    this.loadAvailableNodes();
    this.detectAvailableDataTypes();
    this.loadSearchResults();
  },
  
  methods: {
    async loadAvailableNodes() {
      try {
        const response = await this.$http.get('/api/node/nodes');
        if (response.data && response.data.length > 0) {
          this.availableNodes = response.data;
        }
      } catch (error) {
        console.error('加载节点列表失败:', error);
      }
    },
    
    detectAvailableDataTypes() {
      // 检测localStorage中是否有各种类型的数据
      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];
      const available = [];
      
      dataTypes.forEach(type => {
        const dataKey = `${type}_data`;
        if (localStorage.getItem(dataKey)) {
          available.push(type);
        }
      });
      
      this.availableDataTypes = available;
    },
    
    showAnalysisModal() {
      if (!this.hasNodeData) {
        message.warning('请先收集节点数据');
        return;
      }
      
      this.selectedAnalysisTypes = [...this.availableDataTypes];
      this.analysisModalVisible = true;
      
      if (this.availableNodes.length === 1) {
        this.selectedNodeId = this.availableNodes[0].id;
      }
    },
    
    async startAnalysis() {
      if (!this.selectedNodeId) {
        message.error('请选择节点');
        return;
      }
      
      if (this.selectedAnalysisTypes.length === 0) {
        message.error('请选择分析类型');
        return;
      }
      
      this.analyzing = true;
      this.analysisResults = [];
      
      try {
        // 对每种数据类型进行分析
        for (const infoType of this.selectedAnalysisTypes) {
          const collectedData = this.getCollectedData(infoType);
          
          if (collectedData) {
            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {
              node_id: this.selectedNodeId,
              info_type: infoType,
              collected_data: collectedData
            });
            
            this.analysisResults.push(response.data);
          }
        }
        
        this.analysisModalVisible = false;
        this.activeKeys = this.analysisResults.map((_, index) => index.toString());
        
        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);
        
      } catch (error) {
        console.error('分析失败:', error);
        message.error('分析过程中出现错误');
      } finally {
        this.analyzing = false;
      }
    },
    
    getCollectedData(infoType) {
      const dataKey = `${infoType}_data`;
      const data = localStorage.getItem(dataKey);
      return data ? JSON.parse(data) : null;
    },
    
    getTypeName(type) {
      const typeNames = {
        'process': '进程信息',
        'package': '软件包信息',
        'hardware': '硬件信息',
        'filesystem': '文件系统信息',
        'port': '端口信息',
        'docker': 'Docker信息',
        'kubernetes': 'Kubernetes信息'
      };
      return typeNames[type] || type;
    },
    
    getStatusColor(status) {
      const colors = {
        'success': 'green',
        'partial': 'orange',
        'warning': 'orange',
        'failed': 'red',
        'error': 'red',
        'info': 'blue'
      };
      return colors[status] || 'default';
    },
    
    getStatusText(status) {
      const texts = {
        'success': '成功',
        'partial': '部分成功',
        'warning': '警告',
        'failed': '失败',
        'error': '错误',
        'info': '信息'
      };
      return texts[status] || status;
    },
    
    truncateText(text, maxLength) {
      if (!text) return '';
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    },
    
    expandedRowRender(record) {
      if (!record.outputs || record.outputs.length === 0) {
        return '无执行详情';
      }

      return `
        <div style="margin: 16px 0;">
          <h4>命令执行详情:</h4>
          ${record.outputs.map((output, index) => `
            <div style="margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;">
              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>
              <p><strong>退出码:</strong> <span style="color: ${output.exit_code === 0 ? 'green' : 'red'}">${output.exit_code}</span></p>
              ${output.output ? `<p><strong>输出:</strong><br><pre style="background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;">${output.output}</pre></p>` : ''}
              ${output.error ? `<p><strong>错误:</strong><br><pre style="background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;">${output.error}</pre></p>` : ''}
            </div>
          `).join('')}
        </div>
      `;
    },

    // 搜索测试用例
    async searchTestcases() {
      if (!this.queryText.trim()) {
        message.warning('请输入查询内容');
        return;
      }

      this.searching = true;
      try {
        const response = await axios.post('/api/vector_testcase/search_with_details', {
          query: this.queryText,
          top_k: this.searchParams.top_k,
          score_threshold: this.searchParams.score_threshold
        });

        if (response.data.status === 'success') {
          this.searchResults = response.data.results;
          // 保存搜索结果到localStorage
          this.saveSearchResults();
          message.success(`找到 ${this.searchResults.length} 个相关测试用例`);
        } else {
          message.error(response.data.message || '搜索失败');
          this.searchResults = [];
          this.clearSearchResults();
        }
      } catch (error) {
        console.error('搜索测试用例失败:', error);
        message.error('搜索过程中出现错误');
        this.searchResults = [];
        this.clearSearchResults();
      } finally {
        this.searching = false;
      }
    },

    // 查看测试用例详情
    viewTestcaseDetail(record) {
      this.selectedTestcase = record;
      this.testcaseDetailVisible = true;
    },

    // 保存搜索结果到localStorage
    saveSearchResults() {
      const searchData = {
        queryText: this.queryText,
        searchParams: this.searchParams,
        searchResults: this.searchResults,
        timestamp: Date.now()
      };
      localStorage.setItem('smart_orchestration_search_results', JSON.stringify(searchData));
    },

    // 从localStorage恢复搜索结果
    loadSearchResults() {
      try {
        const savedData = localStorage.getItem('smart_orchestration_search_results');
        if (savedData) {
          const searchData = JSON.parse(savedData);
          // 检查数据是否过期（24小时）
          const isExpired = Date.now() - searchData.timestamp > 24 * 60 * 60 * 1000;

          if (!isExpired && searchData.searchResults && searchData.searchResults.length > 0) {
            this.queryText = searchData.queryText || '';
            this.searchParams = { ...this.searchParams, ...searchData.searchParams };
            this.searchResults = searchData.searchResults || [];
          }
        }
      } catch (error) {
        console.error('恢复搜索结果失败:', error);
        this.clearSearchResults();
      }
    },

    // 清理搜索结果存储
    clearSearchResults() {
      localStorage.removeItem('smart_orchestration_search_results');
    },

    // 清除搜索历史（用户手动操作）
    clearSearchHistory() {
      this.queryText = '';
      this.searchResults = [];
      this.searchParams = {
        top_k: 10,
        score_threshold: 0.5
      };
      this.clearSearchResults();
      message.success('搜索结果已清除');
    },

    // 获取相似度颜色
    getSimilarityColor(similarity) {
      if (similarity >= 0.8) return '#52c41a'; // 绿色
      if (similarity >= 0.6) return '#faad14'; // 橙色
      return '#f5222d'; // 红色
    },

    // 获取级别颜色
    getLevelColor(level) {
      const colors = {
        'level 0': 'red',
        'level 1': 'orange',
        'level 2': 'green',
        'level 3': 'blue',
        'level 4': 'purple',
        'P0': 'red',
        'P1': 'orange',
        'P2': 'blue',
        'P3': 'green',
        'P4': 'gray'
      };
      return colors[level] || 'default';
    }
  }
};
</script>

<style scoped>
.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.header-solid {
  border-radius: 12px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 600;
}

:deep(.ant-collapse-header) {
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

.query-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.query-card :deep(.ant-card-head-title) {
  color: white;
}

.query-card :deep(.ant-form-item-label > label) {
  color: white;
}

.param-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 4px;
}


</style> 