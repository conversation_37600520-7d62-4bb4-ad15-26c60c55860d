{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1751875064988}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "moment", "mapState", "RefreshButton", "TestCaseDetailModal", "components", "name", "data", "h", "$createElement", "loading", "testcases", "total", "currentPage", "detailsVisible", "selectedTestcase", "searchForm", "level", "undefined", "prepare_condition", "test_steps", "expected_result", "columns", "title", "dataIndex", "key", "width", "align", "customRender", "text", "record", "index", "ellipsis", "click", "viewDetails", "slots", "created", "fetchTestcases", "computed", "methods", "page", "params", "page_size", "response", "get", "error", "console", "$message", "handleSearch", "resetSearch", "formatDate", "date", "format", "getResultColor", "result", "colors", "getLevelColor", "handlePageChange"], "sources": ["src/components/Cards/TestCaseInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item label=\"Name\">\r\n            <a-input v-model=\"searchForm.name\" placeholder=\"Search by name\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Level\">\r\n            <a-select v-model=\"searchForm.level\" placeholder=\"Select level\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item label=\"Prepare Condition\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" placeholder=\"Search in prepare condition\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Test Steps\">\r\n            <a-input v-model=\"searchForm.test_steps\" placeholder=\"Search in test steps\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Expected Result\">\r\n            <a-input v-model=\"searchForm.expected_result\" placeholder=\"Search in expected result\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              Search\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              Reset\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: 'Test Case ID',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Level',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: 'Prepare Condition',\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Test Steps',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Expected Result',\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        // {\r\n        //   title: 'Action',\r\n        //   key: 'action',\r\n        //   fixed: 'right',\r\n        //   width: 120,\r\n        //   slots: { customRender: 'action' },\r\n        // },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n    computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n.testcase-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  padding: 12px;\r\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: rgba(0, 0, 0, 0.75);\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #1890ff;\r\n}\r\n</style>\r\n"], "mappings": "AA+GA,OAAAA,KAAA;AACA,OAAAC,MAAA;AACA,SAAAC,QAAA;AACA,OAAAC,aAAA;AACA,OAAAC,mBAAA;AAEA;EACAC,UAAA;IACAF,aAAA;IACAC;EACA;EACAE,IAAA;EACAC,KAAA;IAAA,MAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,UAAA;QACAV,IAAA;QACAW,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,YAAA,EAAAA,CAAAC,IAAA,EAAAC,MAAA,EAAAC,KAAA;UACA,aAAAlB,WAAA,cAAAkB,KAAA;QACA;MACA,GACA;QACAR,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAM,QAAA;QACAJ,YAAA,EAAAA,CAAAC,IAAA,EAAAC,MAAA;UACA,OAAAtB,CAAA;YAAA;cAAA,SAAAyB,CAAA,UAAAC,WAAA,CAAAJ,MAAA;YAAA;YAAA;UAAA,IAAAD,IAAA;QACA;MACA,GACA;QACAN,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACA;MACA,GACA;QACAH,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAS,KAAA;UAAAP,YAAA;QAAA;MACA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAM,QAAA;MACA,GACA;QACAT,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAM,QAAA;MACA,GACA;QACAT,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,KAAA;QACAM,QAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA;IAEA;EACA;EACAI,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,GAAApC,QAAA;EACA;EACAqC,OAAA;IACA,MAAAF,eAAAG,IAAA;MACA,KAAA9B,OAAA;MACA;QACA;QACA,MAAA+B,MAAA;UACAD,IAAA,EAAAA,IAAA;UACAE,SAAA;QACA;;QAEA;QACA,SAAA1B,UAAA,CAAAV,IAAA,EAAAmC,MAAA,CAAAnC,IAAA,QAAAU,UAAA,CAAAV,IAAA;QACA,SAAAU,UAAA,CAAAC,KAAA,EAAAwB,MAAA,CAAAxB,KAAA,QAAAD,UAAA,CAAAC,KAAA;QACA,SAAAD,UAAA,CAAAG,iBAAA,EAAAsB,MAAA,CAAAtB,iBAAA,QAAAH,UAAA,CAAAG,iBAAA;QACA,SAAAH,UAAA,CAAAI,UAAA,EAAAqB,MAAA,CAAArB,UAAA,QAAAJ,UAAA,CAAAI,UAAA;QACA,SAAAJ,UAAA,CAAAK,eAAA,EAAAoB,MAAA,CAAApB,eAAA,QAAAL,UAAA,CAAAK,eAAA;QAEA,MAAAsB,QAAA,SAAA3C,KAAA,CAAA4C,GAAA;UAAAH;QAAA;QACA,KAAA9B,SAAA,GAAAgC,QAAA,CAAApC,IAAA,CAAAA,IAAA;QACA,KAAAK,KAAA,GAAA+B,QAAA,CAAApC,IAAA,CAAAK,KAAA;MACA,SAAAiC,KAAA;QACAC,OAAA,CAAAD,KAAA,8BAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAnC,OAAA;MACA;IACA;IAEA;IACAsC,aAAA;MACA,KAAAnC,WAAA;MACA,KAAAwB,cAAA;IACA;IAEA;IACAY,YAAA;MACA,KAAAjC,UAAA;QACAV,IAAA;QACAW,KAAA,EAAAC,SAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA,KAAAR,WAAA;MACA,KAAAwB,cAAA;IACA;IACAa,WAAAC,IAAA;MACA,OAAAA,IAAA,GAAAlD,MAAA,CAAAkD,IAAA,EAAAC,MAAA;IACA;IACAC,eAAAC,MAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,MAAA;IACA;IACAE,cAAAvC,KAAA;MACA,MAAAsC,MAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAtC,KAAA;IACA;IACAiB,YAAAJ,MAAA;MACA,KAAAf,gBAAA,GAAAe,MAAA;MACA,KAAAhB,cAAA;IACA;IACA2C,iBAAAjB,IAAA;MACA,KAAA3B,WAAA,GAAA2B,IAAA;MACA,KAAAH,cAAA,CAAAG,IAAA;IACA;EACA;AACA", "ignoreList": []}]}