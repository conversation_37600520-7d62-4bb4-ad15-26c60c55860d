// 默认脚本内容
export const defaultScriptContent = `#!/bin/bash

# ----------------------- 第一步：上传并解压工具包 -----------------------
#
# 以下变量会自动替换，无需修改：
# {work_dir}            - 任务工作目录，格式为 "/root/.test/script_task_{task_id}"
# {proxy_ip}            - 服务器IP地址
# {server_port}         - 服务器端口
# {download_script_info} - 下载信息（自动编码）
# {output_file_name}    - 上传的工具包名称
#
cd {work_dir}
curl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}
unzip -o {output_file_name}

# ----------------------- 第二步：执行工具命令 --------------------------
#
# 以下为工具执行示例（以s-spider工具为例）
# 请根据实际工具修改此部分命令
#
# 可用变量：无需修改
# {node_ip}   - 目标节点IP
# {node_name} - 目标节点名称
#
cd scan_tools
chmod 777 run.sh
dos2unix run.sh || true
sleep 0.5
sh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall
sleep 0.5

# ----------------------- 第三步：回传结果文件 --------------------------
#
# 重要说明：
# 1. 所有工具运行结果必须打包为 {node_name}_{node_ip}.tar 格式
# 2. {result_file} 变量会被自动替换为正确的结果文件名
# 3. {upload_script_info} 为回传参数（已编码，无需修改）
#
# 如果您的工具生成了其他格式的文件，请使用以下命令将其转换：
# tar -cf {result_file} your_result_files
# 或
# mv your_result_file.ext {result_file}
#
curl -F "file=@{result_file}" "http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}"
`;