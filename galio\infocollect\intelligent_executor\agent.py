"""
安全测试Agent - 基于ReAct架构的自动化安全测试代理
"""

import asyncio
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from .testcase_matcher import TestcaseMatcher
from .ai_analyzer import AIAnalyzer
from .command_executor import CommandExecutor
from .report_generator import ReportGenerator
from infocollect.log.logger import log_info, log_error, log_debug


@dataclass
class TestSession:
    """测试会话数据类"""
    session_id: str
    start_time: str
    node_ip: Optional[str] = None
    security_info: Dict[str, Any] = field(default_factory=dict)
    matched_testcases: List[Dict[str, Any]] = field(default_factory=list)
    analysis_results: List[Dict[str, Any]] = field(default_factory=list)
    execution_results: List[Dict[str, Any]] = field(default_factory=list)
    end_time: Optional[str] = None
    duration: float = 0.0
    status: str = "running"


class SecurityTestAgent:
    """安全测试Agent - 实现完整的自动化安全测试流程"""
    
    def __init__(self):
        # 初始化各个组件
        self.testcase_matcher = TestcaseMatcher()
        self.ai_analyzer = AIAnalyzer()
        self.command_executor = CommandExecutor()
        self.report_generator = ReportGenerator()
        
        # Agent状态
        self.current_session: Optional[TestSession] = None
        self.session_history: List[TestSession] = []
        
        log_info("SecurityTestAgent initialized with ReAct architecture")

    async def run_security_test(self, 
                               security_info: Dict[str, Any], 
                               node_ip: str = None,
                               max_testcases: int = 5,
                               auto_execute: bool = True) -> Dict[str, Any]:
        """运行完整的安全测试流程"""
        # 创建新的测试会话
        session = TestSession(
            session_id=str(uuid.uuid4()),
            start_time=datetime.now().isoformat(),
            node_ip=node_ip,
            security_info=security_info
        )
        
        self.current_session = session
        start_timestamp = time.time()
        
        try:
            log_info(f"Starting security test session: {session.session_id}")
            
            # Step 1: 思考 - 分析安全信息类型
            thought_result = await self._think_about_security_info(security_info)
            log_info(f"Thought: {thought_result['analysis']}")
            
            # Step 2: 行动 - 匹配相关测试用例
            action_result = await self._act_match_testcases(thought_result, max_testcases)
            log_info(f"Action: Found {len(action_result['testcases'])} matching testcases")
            
            # Step 3: 观察 - 评估匹配结果
            observation_result = await self._observe_testcase_quality(action_result)
            log_info(f"Observation: {observation_result['assessment']}")
            
            # Step 4: 思考 - 决定分析策略
            analysis_strategy = await self._think_analysis_strategy(observation_result)
            log_info(f"Analysis Strategy: {analysis_strategy['strategy']}")
            
            # Step 5: 行动 - 分析测试用例
            analysis_results = await self._act_analyze_testcases(analysis_strategy)
            log_info(f"Analysis: Completed {len(analysis_results)} testcase analyses")
            
            # Step 6: 观察 - 评估分析结果
            analysis_observation = await self._observe_analysis_results(analysis_results)
            log_info(f"Analysis Observation: {analysis_observation['summary']}")
            
            # Step 7: 思考 - 决定执行策略
            execution_strategy = await self._think_execution_strategy(analysis_observation, auto_execute)
            log_info(f"Execution Strategy: {execution_strategy['strategy']}")
            
            # Step 8: 行动 - 执行命令
            execution_results = []
            if auto_execute and execution_strategy.get('should_execute', False):
                execution_results = await self._act_execute_commands(execution_strategy, node_ip)
                log_info(f"Execution: Completed {len(execution_results)} command sets")
            
            # Step 9: 观察 - 评估执行结果
            execution_observation = await self._observe_execution_results(execution_results)
            log_info(f"Execution Observation: {execution_observation['summary']}")
            
            # Step 10: 思考 - 总结和报告
            final_thought = await self._think_final_summary(execution_observation)
            log_info(f"Final Thought: {final_thought['conclusion']}")
            
            # 完成会话
            session.end_time = datetime.now().isoformat()
            session.duration = time.time() - start_timestamp
            session.status = "completed"
            
            # 生成报告
            report_result = await self._generate_final_report(session)
            
            # 保存会话历史
            self.session_history.append(session)
            
            return {
                "success": True,
                "session_id": session.session_id,
                "duration": session.duration,
                "total_testcases": len(session.matched_testcases),
                "analyzed_testcases": len(session.analysis_results),
                "executed_commands": sum(len(er.get('results', [])) for er in session.execution_results),
                "report": report_result,
                "session_data": self._serialize_session(session)
            }
            
        except Exception as e:
            log_error(f"Error in security test session: {e}")
            session.status = "failed"
            session.end_time = datetime.now().isoformat()
            session.duration = time.time() - start_timestamp
            
            return {
                "success": False,
                "session_id": session.session_id,
                "error": str(e),
                "session_data": self._serialize_session(session)
            }

    async def _think_about_security_info(self, security_info: Dict[str, Any]) -> Dict[str, Any]:
        """思考：分析安全信息类型和特征"""
        await asyncio.sleep(0.1)  # 模拟思考时间
        
        info_types = []
        key_features = []
        
        for key in security_info.keys():
            key_lower = key.lower()
            if 'k8s' in key_lower or 'kubernetes' in key_lower:
                info_types.append('kubernetes')
                key_features.append('容器编排平台')
            elif 'docker' in key_lower:
                info_types.append('docker')
                key_features.append('容器技术')
            elif 'network' in key_lower or 'port' in key_lower:
                info_types.append('network')
                key_features.append('网络安全')
            elif 'process' in key_lower or 'system' in key_lower:
                info_types.append('system')
                key_features.append('系统安全')
        
        return {
            "info_types": list(set(info_types)),
            "key_features": key_features,
            "analysis": f"识别到 {len(set(info_types))} 种安全信息类型: {', '.join(set(info_types))}",
            "priority": self._determine_priority(info_types)
        }

    async def _act_match_testcases(self, thought_result: Dict[str, Any], max_testcases: int) -> Dict[str, Any]:
        """行动：匹配测试用例"""
        info_types = thought_result.get('info_types', [])
        
        all_testcases = []
        
        for info_type in info_types:
            testcases = self.testcase_matcher.match_testcases_by_info_type(
                info_type, 
                self.current_session.security_info
            )
            all_testcases.extend(testcases)
        
        # 去重并排序
        unique_testcases = self._deduplicate_testcases(all_testcases)
        
        # 获取高优先级测试用例
        high_priority_testcases = self.testcase_matcher.get_high_priority_testcases(
            unique_testcases, 
            limit=max_testcases
        )
        
        # 过滤可执行的测试用例
        executable_testcases = self.testcase_matcher.filter_executable_testcases(high_priority_testcases)
        
        # 更新会话数据
        self.current_session.matched_testcases = executable_testcases
        
        return {
            "testcases": executable_testcases,
            "total_found": len(all_testcases),
            "after_dedup": len(unique_testcases),
            "high_priority": len(high_priority_testcases),
            "executable": len(executable_testcases)
        }

    async def _observe_testcase_quality(self, action_result: Dict[str, Any]) -> Dict[str, Any]:
        """观察：评估测试用例质量"""
        testcases = action_result['testcases']
        
        if not testcases:
            assessment = "未找到匹配的测试用例"
            quality_score = 0
        elif len(testcases) < 3:
            assessment = "找到少量测试用例"
            quality_score = 0.6
        else:
            assessment = "找到足够数量的测试用例"
            quality_score = 0.9
        
        return {
            "assessment": assessment,
            "quality_score": quality_score,
            "recommendation": "继续分析" if quality_score > 0 else "扩展搜索"
        }

    async def _think_analysis_strategy(self, observation_result: Dict[str, Any]) -> Dict[str, Any]:
        """思考：分析策略"""
        quality_score = observation_result.get('quality_score', 0)
        testcases = self.current_session.matched_testcases
        
        if quality_score >= 0.8:
            strategy = "深度分析所有测试用例"
        elif quality_score >= 0.5:
            strategy = "重点分析高优先级测试用例"
        else:
            strategy = "基础分析"
        
        return {
            "strategy": strategy,
            "testcases_to_analyze": testcases,
            "analysis_depth": "deep" if quality_score >= 0.8 else "medium"
        }

    async def _act_analyze_testcases(self, strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """行动：分析测试用例"""
        testcases = strategy['testcases_to_analyze']
        analysis_results = []
        
        for testcase in testcases:
            try:
                result = self.ai_analyzer.analyze_testcase(testcase)
                analysis_results.append(result)
            except Exception as e:
                log_error(f"Analysis error: {e}")
                analysis_results.append({"success": False, "error": str(e)})
        
        self.current_session.analysis_results = analysis_results
        return analysis_results

    async def _observe_analysis_results(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """观察：分析结果评估"""
        successful = sum(1 for r in analysis_results if r.get('success', False))
        total = len(analysis_results)
        
        return {
            "summary": f"完成 {successful}/{total} 个测试用例分析",
            "success_rate": successful / total if total > 0 else 0,
            "next_action": "proceed_to_execution" if successful > 0 else "stop"
        }

    async def _think_execution_strategy(self, observation: Dict[str, Any], auto_execute: bool) -> Dict[str, Any]:
        """思考：执行策略"""
        success_rate = observation.get('success_rate', 0)
        should_execute = auto_execute and success_rate > 0.3
        
        return {
            "strategy": "正常执行" if should_execute else "跳过执行",
            "should_execute": should_execute,
            "safety_level": "normal"
        }

    async def _act_execute_commands(self, strategy: Dict[str, Any], node_ip: str) -> List[Dict[str, Any]]:
        """行动：执行命令"""
        analysis_results = self.current_session.analysis_results
        execution_results = []
        
        for analysis in analysis_results:
            if not analysis.get('success', False):
                continue
            
            # 生成命令
            try:
                commands_result = self.ai_analyzer.generate_commands_for_steps(
                    analysis.get('analysis', {})
                )
                
                if commands_result.get('success', False):
                    commands = commands_result.get('commands', [])
                    
                    # 提取可执行命令
                    executable_commands = []
                    for cmd_step in commands:
                        if cmd_step.get('success', False):
                            step_commands = cmd_step.get('commands', [])
                            executable_commands.extend(step_commands)
                    
                    if executable_commands:
                        # 执行命令
                        exec_result = await self.command_executor.execute_command_set(
                            executable_commands, 
                            node_ip
                        )
                        execution_results.append(exec_result)
            except Exception as e:
                log_error(f"Command execution error: {e}")
        
        self.current_session.execution_results = execution_results
        return execution_results

    async def _observe_execution_results(self, execution_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """观察：执行结果评估"""
        if not execution_results:
            return {
                "summary": "未执行任何命令",
                "execution_stats": {"total": 0, "successful": 0, "failed": 0}
            }
        
        total = sum(er.get('total_commands', 0) for er in execution_results)
        successful = sum(er.get('successful_commands', 0) for er in execution_results)
        
        return {
            "summary": f"执行 {total} 个命令，成功 {successful} 个",
            "execution_stats": {"total": total, "successful": successful, "failed": total - successful}
        }

    async def _think_final_summary(self, execution_observation: Dict[str, Any]) -> Dict[str, Any]:
        """思考：最终总结"""
        session = self.current_session
        
        testcase_count = len(session.matched_testcases)
        analysis_count = len([r for r in session.analysis_results if r.get('success', False)])
        execution_stats = execution_observation.get('execution_stats', {})
        
        conclusion = f"测试完成：匹配{testcase_count}个用例，分析{analysis_count}个，执行{execution_stats.get('total', 0)}个命令"
        
        return {
            "conclusion": conclusion,
            "overall_assessment": "测试流程顺利完成"
        }

    async def _generate_final_report(self, session: TestSession) -> Dict[str, Any]:
        """生成最终报告"""
        session_data = self._serialize_session(session)
        
        # 生成HTML报告
        html_report = self.report_generator.generate_security_test_report(
            session_data, 'html'
        )
        
        return {
            "html_report": html_report,
            "session_summary": {
                "session_id": session.session_id,
                "duration": session.duration,
                "testcases_matched": len(session.matched_testcases),
                "analyses_completed": len([r for r in session.analysis_results if r.get('success', False)]),
                "commands_executed": sum(er.get('total_commands', 0) for er in session.execution_results),
                "status": session.status
            }
        }

    def _serialize_session(self, session: TestSession) -> Dict[str, Any]:
        """序列化会话数据"""
        return {
            "session_id": session.session_id,
            "start_time": session.start_time,
            "end_time": session.end_time,
            "duration": session.duration,
            "node_ip": session.node_ip,
            "security_info": session.security_info,
            "matched_testcases": session.matched_testcases,
            "analysis_results": session.analysis_results,
            "execution_results": session.execution_results,
            "status": session.status
        }

    def _determine_priority(self, info_types: List[str]) -> str:
        """确定优先级"""
        if 'kubernetes' in info_types or 'docker' in info_types:
            return 'high'
        elif 'network' in info_types:
            return 'medium'
        else:
            return 'low'

    def _deduplicate_testcases(self, testcases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重测试用例"""
        seen = set()
        unique = []
        for tc in testcases:
            tc_id = tc.get('Testcase_Number', tc.get('id', ''))
            if tc_id not in seen:
                seen.add(tc_id)
                unique.append(tc)
        return unique

    def get_session_history(self) -> List[Dict[str, Any]]:
        """获取会话历史"""
        return [
            {
                "session_id": session.session_id,
                "start_time": session.start_time,
                "end_time": session.end_time,
                "duration": session.duration,
                "status": session.status,
                "testcases_count": len(session.matched_testcases),
                "node_ip": session.node_ip
            }
            for session in self.session_history
        ]

    def get_current_session_status(self) -> Optional[Dict[str, Any]]:
        """获取当前会话状态"""
        if not self.current_session:
            return None
        
        session = self.current_session
        return {
            "session_id": session.session_id,
            "start_time": session.start_time,
            "status": session.status,
            "node_ip": session.node_ip,
            "progress": {
                "matched_testcases": len(session.matched_testcases),
                "analysis_completed": len([r for r in session.analysis_results if r.get('success', False)]),
                "commands_executed": sum(er.get('total_commands', 0) for er in session.execution_results)
            }
        } 