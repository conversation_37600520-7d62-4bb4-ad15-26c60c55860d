from flask import Blueprint, request, jsonify
from services.ai_service import AIService
import json

bp = Blueprint('ai', __name__)


@bp.route('/analyze/process', methods=['POST'])
def analyze_process():
    """
    分析进程信息，提供安全测试建议

    请求体格式：
    {
        "process_data": {
            "process_list": [
                {
                    "pid": "123",
                    "ppid": "1",
                    "uid": "0",
                    "gid": "0",
                    "user": "root",
                    "cmd": "...",
                    "state": "S",
                    "exe": "...",
                    "cwd": "...",
                    "capability": "...",
                    "environ": "...",
                    "memory_maps": "..."
                }
            ]
        }
    }

    响应格式：
    {
        "success": true,
        "analysis": "AI分析结果..."
    }
    """
    data = request.get_json()
    if not data or "process_data" not in data:
        return jsonify({"success": False, "error": "Missing process_data parameter"}), 400

    process_data = data.get("process_data")

    # 确保process_data是字典类型
    if isinstance(process_data, str):
        try:
            process_data = json.loads(process_data)
        except json.JSONDecodeError:
            return jsonify({"success": False, "error": "Invalid JSON in process_data"}), 400

    ai_service = AIService()  # 不再传递数据库会话
    try:
        result = ai_service.analyze_process(process_data)
        return jsonify(result), 200
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
