import json

from typing import Optional, Dict, List
from log.logger import log_warning
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.kubernetes_datamodel import (
    K8sAPIServer,
    K8sIngress,
    K8sGateway,
    K8sVirtualService,
    K8sService,
    K8sNetworkPolicy,
    K8sPod,
    K8sNode,
    K8sSecret,
    K8sConfigMap,
    K8sRole,
    K8sRoleBinding,
    K8sClusterRole,
    K8sClusterRoleBinding,
    K8sServiceAccountPermission,
)


class KubernetesService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    # **K8sAPIServer Operations**
    def get_k8s_api_server_list(self, node_id: int) -> List[Dict]:
        k8s_api_servers = self.db.query(K8sAPIServer).filter_by(node_id=node_id).all()
        return [api_server.to_dict() for api_server in k8s_api_servers] if k8s_api_servers else []

    @staticmethod
    def get_insert_objects_api_server(api_server_data: Dict, node_id: int) -> List[K8sAPIServer]:
        """获取要插入的K8s API Server对象列表"""
        insert_objects = []
        if api_server_data:
            k8s_api_server = K8sAPIServer(
                node_id=node_id,
                address=api_server_data.get("address")
            )
            insert_objects.append(k8s_api_server)
        return insert_objects

    # **K8sIngress Operations**
    def get_k8s_ingress_list(self, node_id: int) -> List[Dict]:
        k8s_ingresses = self.db.query(K8sIngress).filter_by(node_id=node_id).all()
        return [ingress.to_dict() for ingress in k8s_ingresses] if k8s_ingresses else []

    @staticmethod
    def get_insert_objects_ingress(ingress_data: Dict, node_id: int) -> List[K8sIngress]:
        insert_objects = []
        if ingress_data:
            ingress_list = ingress_data.get("items", [])
            for ingress in ingress_list:
                k8s_ingress = K8sIngress(
                    node_id=node_id,
                    namespace=ingress.get("metadata", {}).get("namespace"),
                    name=ingress.get("metadata", {}).get("name"),
                    meta_data=json.dumps(ingress.get("metadata", {})),
                    spec=json.dumps(ingress.get("spec", {})),
                    ingress_status=json.dumps(ingress.get("status", {})),
                )
                insert_objects.append(k8s_ingress)
        return insert_objects

    def get_k8s_gateway_list(self, node_id: int) -> List[Dict]:
        k8s_gateways = self.db.query(K8sGateway).filter_by(node_id=node_id).all()
        return [gateway.to_dict() for gateway in k8s_gateways] if k8s_gateways else []

    @staticmethod
    def get_insert_objects_gateway(gateway_data: Dict, node_id: int) -> List[K8sGateway]:
        insert_objects = []
        if gateway_data:
            gateway_list = gateway_data.get("items", [])
            for gateway in gateway_list:
                k8s_gateway = K8sGateway(
                    node_id=node_id,
                    namespace=gateway.get("metadata", {}).get("namespace"),
                    name=gateway.get("metadata", {}).get("name"),
                    meta_data=json.dumps(gateway.get("metadata", {})),
                    spec=json.dumps(gateway.get("spec", {})),
                    gateway_status=json.dumps(gateway.get("status", {})),
                )
                insert_objects.append(k8s_gateway)
        return insert_objects

    def get_k8s_virtual_service_list(self, node_id: int) -> List[Dict]:
        k8s_virtual_services = self.db.query(K8sVirtualService).filter_by(node_id=node_id).all()
        return [virtual_service.to_dict() for virtual_service in k8s_virtual_services] if k8s_virtual_services else []

    @staticmethod
    def get_insert_objects_virtual_service(virtual_service_data: Dict, node_id: int) -> List[K8sVirtualService]:
        insert_objects = []
        if virtual_service_data:
            virtual_service_list = virtual_service_data.get("items", [])
            for virtual_service in virtual_service_list:
                k8s_virtual_service = K8sVirtualService(
                    node_id=node_id,
                    namespace=virtual_service.get("metadata", {}).get("namespace"),
                    name=virtual_service.get("metadata", {}).get("name"),
                    meta_data=json.dumps(virtual_service.get("metadata", {})),
                    spec=json.dumps(virtual_service.get("spec", {})),
                    virtual_service_status=json.dumps(virtual_service.get("status", {})),
                )
                insert_objects.append(k8s_virtual_service)
        return insert_objects

    def get_k8s_service_list(self, node_id: int) -> List[Dict]:
        k8s_services = self.db.query(K8sService).filter_by(node_id=node_id).all()
        return [service.to_dict() for service in k8s_services] if k8s_services else []

    @staticmethod
    def get_insert_objects_service(service_data: Dict, node_id: int) -> List[K8sService]:
        insert_objects = []
        if service_data:
            service_list = service_data.get("items", [])
            if not isinstance(service_list, list):
                log_warning(f"service_list is not a list, skipping insertion.")
                return insert_objects

            for service in service_list:
                k8s_service = K8sService(
                    node_id=node_id,
                    namespace=service.get("metadata", {}).get("namespace"),
                    name=service.get("metadata", {}).get("name"),
                    meta_data=json.dumps(service.get("metadata", {})),
                    spec=json.dumps(service.get("spec", {})),
                    service_status=json.dumps(service.get("status", {})),
                )
                insert_objects.append(k8s_service)
        return insert_objects

    def get_k8s_pod_list(self, node_id: int) -> List[Dict]:
        k8s_pods = self.db.query(K8sPod).filter_by(node_id=node_id).all()
        return [pod.to_dict() for pod in k8s_pods] if k8s_pods else []

    @staticmethod
    def get_insert_objects_pod(pod_data: Dict, node_id: int) -> List[K8sPod]:
        insert_objects = []
        if pod_data:
            pod_list = pod_data.get("items", [])

            if not isinstance(pod_list, list):
                log_warning(f"Warning: pod_list is not a list, skipping insertion.")
                return insert_objects

            for pod in pod_list:
                k8s_pod = K8sPod(
                    node_id=node_id,
                    namespace=pod.get("metadata", {}).get("namespace"),
                    name=pod.get("metadata", {}).get("name"),
                    meta_data=json.dumps(pod.get("metadata", {})),
                    spec=json.dumps(pod.get("spec", {})),
                    pod_status=json.dumps(pod.get("status", {}))
                )
                insert_objects.append(k8s_pod)
        return insert_objects

    def get_k8s_node_list(self, node_id: int) -> List[Dict]:
        k8s_nodes = self.db.query(K8sNode).filter_by(node_id=node_id).all()
        return [node.to_dict() for node in k8s_nodes] if k8s_nodes else []

    @staticmethod
    def get_insert_objects_node(node_data: Dict, node_id: int) -> List[K8sNode]:
        insert_objects = []
        if node_data:
            node_list = node_data.get("items", [])
            # Ensure that node_list is a list
            if not isinstance(node_list, list):
                log_warning(f"node_list is not a list, skipping insertion.")
                return insert_objects

            for node in node_list:
                k8s_node = K8sNode(
                    node_id=node_id,
                    name=node.get("metadata").get("name"),
                    meta_data=json.dumps(node.get("metadata", {})),
                    spec=json.dumps(node.get("spec", {})),
                    pod_status=json.dumps(node.get("status", {})),
                )
                insert_objects.append(k8s_node)
        return insert_objects

    def get_k8s_network_policy_list(self, node_id: int) -> List[Dict]:
        k8s_network_policies = self.db.query(K8sNetworkPolicy).filter_by(node_id=node_id).all()
        return [network_policy.to_dict() for network_policy in k8s_network_policies] if k8s_network_policies else []

    @staticmethod
    def get_insert_objects_network_policy(network_policy_data: Dict, node_id: int) -> List[K8sNetworkPolicy]:
        insert_objects = []
        if network_policy_data:
            network_policy_list = network_policy_data.get("items", [])

            if not isinstance(network_policy_list, list):
                log_warning(f"network_policy_list is not a list, skipping insertion.")
                return insert_objects

            for network_policy in network_policy_list:
                k8s_network_policy = K8sNetworkPolicy(
                    node_id=node_id,
                    namespace=network_policy.get("metadata", {}).get("namespace"),
                    name=network_policy.get("metadata", {}).get("name"),
                    meta_data=json.dumps(network_policy.get("metadata", {})),
                    spec=json.dumps(network_policy.get("spec", {})),
                )
                insert_objects.append(k8s_network_policy)
        return insert_objects

    def get_k8s_secret_list(self, node_id: int) -> List[Dict]:
        k8s_secrets = self.db.query(K8sSecret).filter_by(node_id=node_id).all()
        return [secret.to_dict() for secret in k8s_secrets] if k8s_secrets else []

    @staticmethod
    def get_insert_objects_secret(secret_data: Dict, node_id: int) -> List[K8sSecret]:
        insert_objects = []
        if secret_data:
            secret_list = secret_data.get("items", [])

            if not isinstance(secret_list, list):
                log_warning(f"secret_list is not a list, skipping insertion.")
                return insert_objects

            for secret in secret_list:
                k8s_secret = K8sSecret(
                    node_id=node_id,
                    namespace=secret.get("metadata", {}).get("namespace"),
                    name=secret.get("metadata", {}).get("name"),
                    meta_data=json.dumps(secret.get("metadata", {})),
                    data=json.dumps(secret.get("data", {})),
                    secret_type=secret.get("type", ""),
                )
                insert_objects.append(k8s_secret)
        return insert_objects

    def get_k8s_config_map_list(self, node_id: int) -> List[Dict]:
        k8s_config_maps = self.db.query(K8sConfigMap).filter_by(node_id=node_id).all()
        return [config_map.to_dict() for config_map in k8s_config_maps] if k8s_config_maps else []

    @staticmethod
    def get_insert_objects_config_map(config_map_data: Dict, node_id: int) -> List[K8sConfigMap]:
        insert_objects = []
        if config_map_data:
            config_map_list = config_map_data.get("items", [])

            if not isinstance(config_map_list, list):
                log_warning(f"config_map_list is not a list, skipping insertion.")
                return insert_objects

            for config_map in config_map_list:
                k8s_config_map = K8sConfigMap(
                    node_id=node_id,
                    namespace=config_map.get("metadata", {}).get("namespace"),
                    name=config_map.get("metadata", {}).get("name"),
                    meta_data=json.dumps(config_map.get("metadata", {})),
                    data=json.dumps(config_map.get("data", {})),
                )
                insert_objects.append(k8s_config_map)
        return insert_objects

    def get_k8s_role_list(self, node_id: int) -> List[Dict]:
        k8s_roles = self.db.query(K8sRole).filter_by(node_id=node_id).all()
        return [role.to_dict() for role in k8s_roles] if k8s_roles else []

    @staticmethod
    def get_insert_objects_role(roles_data: Dict, node_id: int) -> List[K8sRole]:
        """获取要插入的K8s Role对象列表"""
        insert_objects = []
        if roles_data:
            roles_list = roles_data.get("items", [])

            if not isinstance(roles_list, list):
                log_warning(f"roles_list is not a list, skipping insertion.")
                return insert_objects

            for role in roles_list:
                k8s_role = K8sRole(
                    node_id=node_id,
                    namespace=role.get("metadata", {}).get("namespace"),
                    name=role.get("metadata", {}).get("name"),
                    meta_data=json.dumps(role.get("metadata", {})),
                    rules=json.dumps(role.get("rules", [])),

                )
                insert_objects.append(k8s_role)
        return insert_objects

    def get_k8s_role_binding_list(self, node_id: int) -> List[Dict]:
        """获取RoleBinding列表"""
        k8s_role_bindings = self.db.query(K8sRoleBinding).filter_by(node_id=node_id).all()
        return [binding.to_dict() for binding in k8s_role_bindings] if k8s_role_bindings else []

    @staticmethod
    def get_insert_objects_role_binding(rolebindings_data: Dict, node_id: int) -> List[K8sRoleBinding]:
        """获取要插入的K8s RoleBinding对象列表"""
        insert_objects = []
        if rolebindings_data:
            rolebindings_list = rolebindings_data.get("items", [])

            if not isinstance(rolebindings_list, list):
                log_warning(f"rolebindings_list is not a list, skipping insertion.")
                return insert_objects

            for rolebinding in rolebindings_list:
                k8s_role_binding = K8sRoleBinding(
                    node_id=node_id,
                    namespace=rolebinding.get("metadata", {}).get("namespace"),
                    name=rolebinding.get("metadata", {}).get("name"),
                    meta_data=json.dumps(rolebinding.get("metadata", {})),
                    roleRef=json.dumps(rolebinding.get("roleRef", {})),
                    subjects=json.dumps(rolebinding.get("subjects", [])),
                )
                insert_objects.append(k8s_role_binding)
        return insert_objects

    def get_k8s_cluster_role_list(self, node_id: int) -> List[Dict]:
        """获取ClusterRole列表"""
        k8s_cluster_roles = self.db.query(K8sClusterRole).filter_by(node_id=node_id).all()
        return [role.to_dict() for role in k8s_cluster_roles] if k8s_cluster_roles else []

    @staticmethod
    def get_insert_objects_cluster_role(clusterroles_data: Dict, node_id: int) -> List[K8sClusterRole]:
        """获取要插入的K8s ClusterRole对象列表"""
        insert_objects = []
        if clusterroles_data:
            clusterroles_list = clusterroles_data.get("items", [])

            if not isinstance(clusterroles_list, list):
                log_warning(f"clusterroles_list is not a list, skipping insertion.")
                return insert_objects

            for clusterrole in clusterroles_list:
                k8s_cluster_role = K8sClusterRole(
                    node_id=node_id,
                    name=clusterrole.get("metadata", {}).get("name"),
                    meta_data=json.dumps(clusterrole.get("metadata", {})),
                    aggregationRule=json.dumps(clusterrole.get("aggregationRule", [])),
                    rules=json.dumps(clusterrole.get("rules", [])),
                )
                insert_objects.append(k8s_cluster_role)
        return insert_objects

    def get_k8s_cluster_role_binding_list(self, node_id: int) -> List[Dict]:
        """获取ClusterRoleBinding列表"""
        k8s_cluster_role_bindings = self.db.query(K8sClusterRoleBinding).filter_by(node_id=node_id).all()
        return [binding.to_dict() for binding in k8s_cluster_role_bindings] if k8s_cluster_role_bindings else []

    @staticmethod
    def get_insert_objects_cluster_role_binding(clusterrolebindings_data: Dict, node_id: int):
        """获取要插入的K8s ClusterRoleBinding对象列表"""
        insert_objects = []
        if clusterrolebindings_data:
            clusterrolebindings_list = clusterrolebindings_data.get("items", [])

            if not isinstance(clusterrolebindings_list, list):
                log_warning(f"clusterrolebindings_list is not a list, skipping insertion.")
                return insert_objects

            for clusterrolebinding in clusterrolebindings_list:
                k8s_cluster_role_binding = K8sClusterRoleBinding(
                    node_id=node_id,
                    name=clusterrolebinding.get("metadata", {}).get("name"),
                    meta_data=json.dumps(clusterrolebinding.get("metadata", {})),
                    roleRef=json.dumps(clusterrolebinding.get("roleRef", {})),
                    subjects=json.dumps(clusterrolebinding.get("subjects", [])),
                )
                insert_objects.append(k8s_cluster_role_binding)
        return insert_objects

    def get_k8s_serviceaccount_permissions_list(self, node_id: int) -> List[Dict]:
        """获取ServiceAccount权限列表"""
        k8s_sa_permissions = self.db.query(K8sServiceAccountPermission).filter_by(node_id=node_id).all()
        result = []
        for permission in k8s_sa_permissions:
            perm_dict = permission.to_dict()
            # 解析JSON格式的权限数据
            try:
                perm_dict['permissions'] = json.loads(perm_dict['permissions']) if perm_dict['permissions'] else []
            except:
                perm_dict['permissions'] = []
            result.append(perm_dict)
        return result

    @staticmethod
    def get_insert_objects_serviceaccount_permissions(sa_permissions_data: List[Dict], node_id: int) -> List[K8sServiceAccountPermission]:
        """获取要插入的K8s ServiceAccount权限对象列表"""
        insert_objects = []
        if sa_permissions_data:
            if not isinstance(sa_permissions_data, list):
                log_warning(f"sa_permissions_data is not a list, skipping insertion.")
                return insert_objects

            for sa_data in sa_permissions_data:
                k8s_sa_permission = K8sServiceAccountPermission(
                    node_id=node_id,
                    pod_name=sa_data.get('pod_name', ''),
                    namespace=sa_data.get('namespace', ''),
                    service_account=sa_data.get('service_account', ''),
                    permissions=json.dumps(sa_data.get('permissions', []), ensure_ascii=False)
                )
                insert_objects.append(k8s_sa_permission)
        return insert_objects
