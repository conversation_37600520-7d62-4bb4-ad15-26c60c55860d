<template>
  <div>
    <div v-if="!envVars.length">No environment variables</div>
    <div v-else style="font-size: 12px;">
      <div v-for="(env, index) in envVars.slice(0, 1)" :key="index" style="padding: 2px 0;">
        {{ env }}
      </div>
      <a-button
        v-if="envVars.length > 1"
        type="link"
        size="small"
        @click="showDetails"
      >
        +{{ envVars.length - 1 }} more...
      </a-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnvironmentCell',
  props: {
    record: {
      type: Object,
      required: true
    },
    parseJsonField: {
      type: Function,
      default: null
    }
  },
  computed: {
    envVars() {
      try {
        // 处理不同的数据格式
        let env = this.record.env;
        
        // 如果提供了parseJsonField函数，使用它来解析
        if (this.parseJsonField) {
          return this.parseJsonField(env, []);
        }
        
        // 否则尝试自己解析
        if (typeof env === 'string') {
          return JSON.parse(env) || [];
        }
        
        return env || [];
      } catch (e) {
        console.warn('Failed to parse environment variables:', e);
        return [];
      }
    }
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.record);
    }
  }
}
</script>

<style scoped>
/* 无需额外样式 */
</style>
