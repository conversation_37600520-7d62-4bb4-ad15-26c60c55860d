(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cfa15118"],{1071:function(t,e,s){"use strict";s.r(e);var o=function(){var t=this,e=t._self._c;return e("div",[e("a-tabs",{attrs:{"active-key":t.activeTab},on:{change:t.handleTabChange}},[e("a-tab-pane",{key:"host",attrs:{tab:t.$t("sidebar.hostConfig")}},[e("HostConfig")],1),e("a-tab-pane",{key:"cbh",attrs:{tab:t.$t("sidebar.cbhConfig")}},[e("CbhConfig")],1)],1)],1)},a=[],i=function(){var t=this,e=t._self._c;return e("a-card",{staticClass:"header-solid host-config-card",attrs:{bordered:!1},scopedSlots:t._u([{key:"title",fn:function(){return[e("a-row",{attrs:{type:"flex",align:"middle"}},[e("a-col",{attrs:{span:12}},[e("h6",{staticClass:"font-semibold m-0"},[t._v(t._s(t.$t("hostConfig.title")))])]),e("a-col",{staticClass:"text-right",attrs:{span:12}},[e("div",{staticClass:"button-groups"},[e("div",{staticClass:"button-group"},[e("a-button",{staticClass:"nav-style-button action-button",attrs:{icon:"plus"},on:{click:t.addNewRow}},[t._v(" "+t._s(t.$t("hostConfig.addHost"))+" ")]),e("a-button",{staticClass:"nav-style-button action-button",attrs:{icon:"export",disabled:0===t.selectedRowKeys.length},on:{click:t.exportSelectedHosts}},[t._v(" "+t._s(t.$t("hostConfig.exportSelected"))+" ")]),e("a-button",{staticClass:"nav-style-button action-button delete-button",attrs:{type:"danger",icon:"delete",disabled:0===t.selectedRowKeys.length},on:{click:t.batchDelete}},[t._v(" "+t._s(t.$t("hostConfig.deleteSelected"))+" ")])],1),e("div",{staticClass:"button-group"},[e("a-button",{staticClass:"nav-style-button",attrs:{icon:"download"},on:{click:t.downloadTemplate}},[t._v(" "+t._s(t.$t("hostConfig.downloadTemplate"))+" ")]),e("a-upload",{attrs:{name:"file",customRequest:t.handleUpload,showUploadList:!1}},[e("a-button",{staticClass:"nav-style-button",attrs:{icon:"upload"}},[t._v(" "+t._s(t.$t("hostConfig.uploadTemplate"))+" ")])],1)],1)])])],1)]},proxy:!0}])},[e("div",{staticClass:"config-table"},[e("a-table",{attrs:{columns:t.columns,"data-source":t.hosts,rowKey:t=>t.key,size:"middle",pagination:{current:t.currentPage,pageSize:t.pageSize,total:t.hosts.length,onChange:t.onPageChange},loading:t.loading,"row-selection":{selectedRowKeys:t.selectedRowKeys,onChange:t.onSelectChange,getCheckboxProps:t=>({disabled:t.editable||t.isNew})}},scopedSlots:t._u([t._l(t.editableColumns,(function(s){return{key:s,fn:function(o,a,i){return[e("div",{key:s},[a.editable?e("a-input",{staticStyle:{margin:"-5px 0"},attrs:{value:o,placeholder:"Enter "+t.getColumnTitle(s)},on:{change:e=>t.handleChange(e.target.value,a.key,s)}}):e("span",{staticStyle:{display:"flex","align-items":"center"}},[["ip","login_pwd","switch_root_pwd"].includes(s)&&o?e("a-icon",{staticStyle:{cursor:"pointer","margin-left":"4px",opacity:"0.6","font-size":"12px"},attrs:{type:"copy"},on:{click:function(e){return t.copyText(o)},mouseenter:function(t){t.target.style.opacity="1"},mouseleave:function(t){t.target.style.opacity="0.6"}}}):t._e(),e("span",{style:["ip","login_pwd","switch_root_pwd"].includes(s)&&o?"cursor: pointer":"",on:{click:function(e){["ip","login_pwd","switch_root_pwd"].includes(s)&&o&&t.copyText(o)}}},[t._v(t._s(o||"-"))])],1)],1)]}}})),{key:"operation",fn:function(s,o,a){return[e("div",{staticClass:"editable-row-operations"},[o.editable?[e("a-button",{attrs:{type:"link"},on:{click:()=>t.save(o.key)}},[t._v(t._s(t.$t("hostConfig.save")))]),e("a-popconfirm",{attrs:{title:"Discard changes?"},on:{confirm:()=>t.cancel(o.key)}},[e("a-button",{attrs:{type:"link",danger:""}},[t._v(t._s(t.$t("hostConfig.cancel")))])],1)]:[e("a-button",{attrs:{type:"link"},on:{click:()=>t.edit(o.key)}},[t._v(t._s(t.$t("hostConfig.edit")))]),e("a-button",{attrs:{type:"link"},on:{click:()=>t.copyNodeInfo(o)}},[e("a-icon",{attrs:{type:"copy"}}),t._v(" "+t._s(t.$t("hostConfig.copy"))+" ")],1),e("a-popconfirm",{attrs:{title:"Confirm deletion?"},on:{confirm:()=>t.deleteHost(o)}},[e("a-button",{attrs:{type:"link",danger:""}},[t._v(t._s(t.$t("hostConfig.delete")))])],1)]],2)]}}],null,!0)})],1)])},n=[],c=(s("0643"),s("2382"),s("fffc"),s("a573"),s("9a9a"),s("0c63")),l=s("fec3"),r=s("416a");let d=[];var h={components:{AIcon:c["a"]},mixins:[r["a"]],computed:{},data(){return{...this.$data,hosts:[],saving:!1,loading:!1,currentPage:1,pageSize:50,editableColumns:["host_name","ip","ssh_port","login_user","login_pwd","switch_root_cmd","switch_root_pwd"],selectedRowKeys:[],currentDbFile:localStorage.getItem("currentProject"),columns:[{title:"#",dataIndex:"index",width:80,customRender:(t,e,s)=>(this.currentPage-1)*this.pageSize+s+1},{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",scopedSlots:{customRender:"host_name"},width:150},{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",scopedSlots:{customRender:"ip"},width:150},{title:this.$t("hostConfig.columns.sshPort"),dataIndex:"ssh_port",scopedSlots:{customRender:"ssh_port"},width:100},{title:this.$t("hostConfig.columns.loginUser"),dataIndex:"login_user",scopedSlots:{customRender:"login_user"},width:120},{title:this.$t("hostConfig.columns.loginPassword"),dataIndex:"login_pwd",scopedSlots:{customRender:"login_pwd"},width:150},{title:this.$t("hostConfig.columns.switchRootCmd"),dataIndex:"switch_root_cmd",scopedSlots:{customRender:"switch_root_cmd"},width:180},{title:this.$t("hostConfig.columns.switchRootPwd"),dataIndex:"switch_root_pwd",scopedSlots:{customRender:"switch_root_pwd"},width:180},{title:this.$t("hostConfig.actions"),dataIndex:"operation",scopedSlots:{customRender:"operation"},width:150,align:"center"}]}},created(){if(!this.currentDbFile)return this.$message.warning("Please select a project first"),void this.$router.push("/projects");this.fetchHostConfig()},methods:{copyNodeInfo(t){const e={...t,key:"new-"+Date.now(),id:void 0,editable:!0,isNew:!0,host_name:(t.host_name||"")+"_copy",ip:""};this.hosts=[e,...this.hosts],this.currentPage=1,d=this.hosts.map(t=>({...t})),this.selectedRowKeys=[],this.$nextTick(()=>{const t=document.querySelector(".ant-table-body");t&&(t.scrollTop=0)})},getColumnTitle(t){var e;return(null===(e=this.columns.find(e=>e.dataIndex===t))||void 0===e?void 0:e.title)||t},handleChange(t,e,s){const o=[...this.hosts],a=o.find(t=>t.key===e);a&&(a[s]=t,this.hosts=o)},edit(t){const e=[...this.hosts],s=e.find(e=>e.key===t);s&&(d=e.map(t=>({...t})),s.editable=!0,this.hosts=e)},async save(t){try{const e=this.hosts.find(e=>e.key===t);if(!e||!this.validateHost(e))return;this.saving=!0;const s={...e};delete s.editable,delete s.isNew,await l["a"].post("/api/config/",{hosts:[s],dbFile:this.currentDbFile}),this.hosts=this.hosts.map(e=>e.key===t?{...e,editable:!1,isNew:!1}:e),d=this.hosts.map(t=>({...t})),this.$message.success("Saved successfully")}catch(e){this.$message.error("Failed to save host")}finally{this.saving=!1}},cancel(t){const e=this.hosts.findIndex(e=>e.key===t);if(-1===e)return;const s=this.hosts[e];if(s.isNew)this.hosts=this.hosts.filter(e=>e.key!==t);else{const e=[...this.hosts],o=d.find(e=>e.key===t);o&&(Object.assign(s,{...o}),delete s.editable,this.hosts=e)}},addNewRow(){this.hosts=[{key:"new-"+Date.now(),host_name:"",ip:"",ssh_port:"22",login_user:"",login_pwd:"",switch_root_cmd:"",switch_root_pwd:"",editable:!0,isNew:!0},...this.hosts],this.currentPage=1,d=this.hosts.map(t=>({...t})),this.selectedRowKeys=[]},validateHost(t){var e;if(null===(e=t.host_name)||void 0===e||!e.trim())return this.$message.error("Host name is required"),!1;if(!/^(\d{1,3}\.){3}\d{1,3}$/.test(t.ip))return this.$message.error("Invalid IP format"),!1;if(!/^\d+$/.test(t.ssh_port))return this.$message.error("SSH port must be numeric"),!1;const s=this.hosts.some(e=>e.ip===t.ip&&e.key!==t.key);return!s||(this.$message.error("IP address already exists"),!1)},async fetchHostConfig(){try{this.loading=!0;const t=await l["a"].get("/api/config",{params:{detail:!0,dbFile:this.currentDbFile}});this.hosts=t.data.map(t=>{var e,s;return{...t,key:(null===(e=t.id)||void 0===e?void 0:e.toString())||"host_"+t.host_name,ssh_port:(null===(s=t.ssh_port)||void 0===s?void 0:s.toString())||"22",isNew:!1}}),d=this.hosts.map(t=>({...t}))}catch(e){var t;this.$message.error((null===(t=e.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"Failed to load hosts")}finally{this.loading=!1}},onPageChange(t){this.currentPage=t},async deleteHost(t){try{t.id?(await l["a"].delete("/api/config/"+t.id,{params:{dbFile:this.currentDbFile}}),this.hosts=this.hosts.filter(e=>e.key!==t.key),this.selectedRowKeys=this.selectedRowKeys.filter(e=>e!==t.key),this.$message.success("Deleted successfully")):(this.hosts=this.hosts.filter(e=>e.key!==t.key),this.selectedRowKeys=this.selectedRowKeys.filter(e=>e!==t.key))}catch(s){var e;this.$message.error((null===(e=s.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.error)||"Failed to delete host"),await this.fetchHostConfig()}},onSelectChange(t){this.selectedRowKeys=t},async batchDelete(){try{const t=this.hosts.filter(t=>this.selectedRowKeys.includes(t.key)).map(t=>t.id).filter(t=>t);if(0===t.length)return void this.$message.warning("No valid hosts selected for deletion");await l["a"].post("/api/config/batch-delete",{ids:t,dbFile:this.currentDbFile}),this.hosts=this.hosts.filter(t=>!this.selectedRowKeys.includes(t.key)),this.selectedRowKeys=[],this.$message.success("Batch deletion completed successfully")}catch(e){var t;this.$message.error((null===(t=e.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"Batch deletion failed")}},async downloadTemplate(){try{const t=await l["a"].get("/api/config/template",{responseType:"blob"}),e=window.URL.createObjectURL(new Blob([t.data])),s=document.createElement("a");s.href=e,s.setAttribute("download","hosts_template.csv"),document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(e),this.$message.success("Template downloaded successfully")}catch(t){this.$message.error("Failed to download template"),console.error("Download template error:",t)}},async handleUpload(t){const{file:e}=t;if(e.name.endsWith(".csv"))try{const t=new FormData;t.append("file",e),t.append("dbFile",this.currentDbFile),await l["a"].post("/api/config/upload",t,{headers:{"Content-Type":"multipart/form-data"}}),await this.fetchHostConfig(),this.$message.success("Hosts imported successfully")}catch(o){var s;this.$message.error((null===(s=o.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.error)||"Failed to import hosts")}else this.$message.error("Please upload CSV file")},async exportSelectedHosts(){try{const t=this.hosts.filter(t=>this.selectedRowKeys.includes(t.key)),e=["host_name","ip","ssh_port","login_user","login_pwd","switch_root_cmd","switch_root_pwd"],s=[e.join(","),...t.map(t=>e.map(e=>t[e]||"").join(","))].join("\n"),o=new Blob([s],{type:"text/csv;charset=utf-8;"}),a=window.URL.createObjectURL(o),i=document.createElement("a");i.href=a,i.setAttribute("download","selected_hosts.csv"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(a),this.$message.success("Hosts exported successfully")}catch(t){this.$message.error("Failed to export hosts"),console.error("Export hosts error:",t)}}}},p=h,u=(s("6ab5"),s("2877")),f=Object(u["a"])(p,i,n,!1,null,"534ea6ec",null),g=f.exports,m=function(){var t=this,e=t._self._c;return e("a-card",{staticClass:"header-solid cbh-config-card",attrs:{bordered:!1},scopedSlots:t._u([{key:"title",fn:function(){return[e("a-row",{attrs:{type:"flex",align:"middle"}},[e("a-col",{attrs:{span:12}},[e("h6",{staticClass:"font-semibold m-0"},[t._v("CBH Configuration")])]),e("a-col",{staticClass:"text-right",attrs:{span:12}},[e("a-button",{staticClass:"nav-style-button",on:{click:t.saveConfig},scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{attrs:{type:"save"}})]},proxy:!0}])},[t._v(" Save Config ")])],1)],1)]},proxy:!0}])},[e("a-descriptions",{attrs:{size:"small",column:1}},[e("a-descriptions-item",{attrs:{label:"AK (Access Key)"}},[e("a-input",{attrs:{placeholder:"Access Key"},model:{value:t.config.ak,callback:function(e){t.$set(t.config,"ak",e)},expression:"config.ak"}})],1),e("a-descriptions-item",{attrs:{label:"SK (Secret Key)"}},[e("a-input",{attrs:{placeholder:"Secret Key"},model:{value:t.config.sk,callback:function(e){t.$set(t.config,"sk",e)},expression:"config.sk"}})],1),e("a-descriptions-item",{attrs:{label:"Server (OBS Endpoint)"}},[e("a-input",{attrs:{placeholder:"OBS Endpoint"},model:{value:t.config.server,callback:function(e){t.$set(t.config,"server",e)},expression:"config.server"}})],1),e("a-descriptions-item",{attrs:{label:"Bucket Name"}},[e("a-input",{attrs:{placeholder:"Bucket Name"},model:{value:t.config.bucket_name,callback:function(e){t.$set(t.config,"bucket_name",e)},expression:"config.bucket_name"}})],1),e("a-descriptions-item",{attrs:{label:"Bucket Store Dir"}},[e("a-input",{attrs:{placeholder:"Bucket Store Dir"},model:{value:t.config.bucket_store_dir,callback:function(e){t.$set(t.config,"bucket_store_dir",e)},expression:"config.bucket_store_dir"}})],1),e("a-descriptions-item",{attrs:{label:"Upload File Path"}},[e("a-input",{attrs:{placeholder:"Upload File Path"},model:{value:t.config.upload_file_path,callback:function(e){t.$set(t.config,"upload_file_path",e)},expression:"config.upload_file_path"}})],1),e("a-descriptions-item",{attrs:{label:"Upload Object Key"}},[e("a-input",{attrs:{placeholder:"Upload Object Key"},model:{value:t.config.upload_object_key,callback:function(e){t.$set(t.config,"upload_object_key",e)},expression:"config.upload_object_key"}})],1),e("a-descriptions-item",{attrs:{label:"Download Object Key"}},[e("a-input",{attrs:{placeholder:"Download Object Key"},model:{value:t.config.download_object_key,callback:function(e){t.$set(t.config,"download_object_key",e)},expression:"config.download_object_key"}})],1),e("a-descriptions-item",{attrs:{label:"Download File Path"}},[e("a-input",{attrs:{placeholder:"Download File Path"},model:{value:t.config.download_file_path,callback:function(e){t.$set(t.config,"download_file_path",e)},expression:"config.download_file_path"}})],1),e("a-descriptions-item",{attrs:{label:"CBH Host"}},[e("a-input",{attrs:{placeholder:"CBH Host"},model:{value:t.config.cbh_host,callback:function(e){t.$set(t.config,"cbh_host",e)},expression:"config.cbh_host"}})],1),e("a-descriptions-item",{attrs:{label:"CBH User Name"}},[e("a-input",{attrs:{placeholder:"CBH User Name"},model:{value:t.config.cbh_user_name,callback:function(e){t.$set(t.config,"cbh_user_name",e)},expression:"config.cbh_user_name"}})],1),e("a-descriptions-item",{attrs:{label:"CBH User Port"}},[e("a-input",{attrs:{placeholder:"CBH User Port"},model:{value:t.config.cbh_user_port,callback:function(e){t.$set(t.config,"cbh_user_port",e)},expression:"config.cbh_user_port"}})],1),e("a-descriptions-item",{attrs:{label:"CBH Private Key Path"}},[e("a-input",{attrs:{placeholder:"CBH Private Key Path"},model:{value:t.config.cbh_private_key_path,callback:function(e){t.$set(t.config,"cbh_private_key_path",e)},expression:"config.cbh_private_key_path"}})],1),e("a-descriptions-item",{attrs:{label:"CBH Private Key Passwd"}},[e("a-input",{attrs:{placeholder:"CBH Private Key Passwd"},model:{value:t.config.cbh_private_key_passwd,callback:function(e){t.$set(t.config,"cbh_private_key_passwd",e)},expression:"config.cbh_private_key_passwd"}})],1),e("a-descriptions-item",{attrs:{label:"CBH Switch Account"}},[e("a-input",{attrs:{placeholder:"CBH Switch Account"},model:{value:t.config.cbh_switch_account,callback:function(e){t.$set(t.config,"cbh_switch_account",e)},expression:"config.cbh_switch_account"}})],1)],1)],1)},_=[],b={name:"CbhConfig",components:{AIcon:c["a"]},computed:{},data(){return{config:{ak:"",sk:"",server:"",bucket_name:"",bucket_store_dir:"",upload_file_path:"",upload_object_key:"",download_object_key:"",download_file_path:"",cbh_host:"",cbh_user_name:"",cbh_user_port:"",cbh_private_key_path:"",cbh_private_key_passwd:"",cbh_switch_account:""}}},created(){this.fetchCbhConfig()},methods:{async fetchCbhConfig(){try{const t=await l["a"].get("/api/cbh_config"),e=t.data;this.config=e}catch(t){console.error("Error fetching CBH config:",t),this.$message.error("Failed to load CBH configuration")}},async saveConfig(){try{await l["a"].post("/api/cbh_config",{headers:{"Content-Type":"application/json"},body:JSON.stringify(this.config)});this.$message.success("CBH configuration saved successfully")}catch(t){console.error("Error saving CBH config:",t),this.$message.error("Failed to save CBH configuration")}}}},y=b,w=(s("7da1"),Object(u["a"])(y,m,_,!1,null,"0ef905d8",null)),v=w.exports,k={components:{HostConfig:g,CbhConfig:v},props:{defaultTab:{type:String,default:"host"}},data(){return{activeTab:this.defaultTab}},watch:{$route:{immediate:!0,handler(t){const e=t.hash.replace("#","")||"host";e!==this.activeTab&&(this.activeTab=e,console.log("Tab  updated from route:",e))}}},methods:{handleTabChange(t){console.log("Tab  changed to:",t),this.$router.replace({path:"/config",hash:"#"+t,query:this.$route.query}).catch(t=>{t.message.includes("Avoided  redundant navigation")||console.error("Navigation  error:",t)})}}},C=k,$=Object(u["a"])(C,o,a,!1,null,null,null);e["default"]=$.exports},"416a":function(t,e,s){"use strict";e["a"]={methods:{copyText(t){const e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message.success("Copied to clipboard")}}}},"4a91":function(t,e,s){},"4d0a":function(t,e,s){},"6ab5":function(t,e,s){"use strict";s("4d0a")},"7da1":function(t,e,s){"use strict";s("4a91")},"9a9a":function(t,e,s){"use strict";var o=s("23e7"),a=s("2266"),i=s("1c0b"),n=s("825a");o({target:"Iterator",proto:!0,real:!0},{some:function(t){return n(this),i(t),a(this,(function(e,s){if(t(e))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})}}]);
//# sourceMappingURL=chunk-cfa15118.8a0cb356.js.map