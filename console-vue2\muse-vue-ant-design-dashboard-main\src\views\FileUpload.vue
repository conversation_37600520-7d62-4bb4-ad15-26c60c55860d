<template>
  <div style="padding: 2px;">
    <div class="card-header-wrapper">
      <div class="header-wrapper">
        <div class="logo-wrapper">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" height="20" width="20" :class="`text-${sidebarColor}`">
              <path :fill="'currentColor'" d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"/>
          </svg>
        </div>
        <h6 class="font-semibold m-0">{{ $t('headTopic.fileUpload') }}</h6>
      </div>
      <a-button
        class="nav-style-button"
        @click="handleUpload"
        :loading="uploading"
      >
        {{ $t('fileUpload.startUpload') }}
      </a-button>
    </div>

    <div class="main-content">
      <div class="left-section">
        <a-card size="small" class="compact-card">
          <a-form layout="vertical" @submit.prevent="handleUpload">
            <a-form-item :label="$t('fileUpload.selectFile')" style="margin-bottom: 8px;">
              <a-upload :before-upload="beforeUpload" :show-upload-list="false">
                <a-button class="nav-style-button">
                  <a-icon type="upload" /> {{ $t('fileUpload.clickToSelect') }}
                </a-button>
              </a-upload>
              <span v-if="fileName" style="margin-left: 8px;">{{ fileName }}</span>
            </a-form-item>

            <a-form-item :label="$t('fileUpload.uploadPath')" style="margin-bottom: 8px;">
              <a-input v-model="uploadDir" :placeholder="$t('fileUpload.enterUploadPath')" />
            </a-form-item>
          </a-form>
        </a-card>

        <a-card size="small" class="compact-card" :title="$t('common.configureProxy')">
          <proxy-selector
            v-model="selectedProxyIp"
            :disabled="uploading"
            @change="handleProxyChange"
          />
        </a-card>
      </div>

      <div class="right-section config-table">
        <a-card size="small" class="compact-card" :title="$t('common.configureNodes')">
          <a-table
            :dataSource="nodes"
            :columns="columns"
            rowKey="ip"
            :pagination="{
              pageSize: 10,
              total: nodes.length,
              showSizeChanger: false
            }"
            :rowSelection="rowSelection"
            class="bordered-nodes-table"
            >
          </a-table>
        </a-card>
      </div>
    </div>



    <a-card
      :title="$t('fileUpload.uploadProgress')"
      style="margin-top: 16px;"
      class="compact-card"
    >
      <a-progress
        :percent="uploadProgress"
        :status="progressBarStatus"
        style="margin-bottom: 16px;"
      />

      <a-table
        :dataSource="progressTableData"
        :columns="progressColumns"
        rowKey="ip"
        :pagination="false"
      >
        <template slot="errorDetail" slot-scope="text, record">
          <a-popover v-if="record && record.error_detail" placement="topLeft">
            <template slot="content">
              <p>Time: {{ record.error_detail.time }}</p>
              <p>Type: {{ record.error_detail.type }}</p>
              <p>Message: {{ record.error_detail.message }}</p>
            </template>
            <a-icon type="info-circle" style="color: #ff4d4f" />
          </a-popover>
        </template>
      </a-table>
    </a-card>

    <a-card v-if="uploadResults" :title="$t('fileUpload.uploadResults')" style="margin-top: 16px;">
      <a-table
        :dataSource="uploadResultsData"
        :columns="resultColumns"
        rowKey="ip"
        :pagination="false"
      />
    </a-card>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import NotificationMixin from '@/mixins/NotificationMixin';
import axios from '@/api/axiosInstance';
import ProxySelector from '@/components/common/ProxySelector.vue';

export default {
  mixins: [NotificationMixin],
  components: {
    ProxySelector
  },
  data() {
    return {
      file: null,
      fileName: '',
      selectedNodes: [],
      uploadDir: '/root/.test/Uploads',
      uploading: false,
      selectedProxyIp: null,
      activeTask: null,
      pollInterval: null,
      isProcessing: false,
      uploadResults: null,
    };
  },
  computed: {
    ...mapState(['nodes', 'activeUploadTask', 'currentProject', 'sidebarColor']),
    rowSelection() {
      return {
        selectedRowKeys: this.selectedNodes,
        onChange: (selectedRowKeys) => {
          this.selectedNodes = selectedRowKeys;
        },
      };
    },
    columns() {
      return [
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name'
        },
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          key: 'ip'
        }
      ];
    },
    progressColumns() {
      return [
        {
          title: this.$t('hostConfig.columns.ipAddress'),
          dataIndex: 'ip',
          key: 'ip',
          width: '120px'
        },
        {
          title: this.$t('hostConfig.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name',
          width: '150px',
          ellipsis: true
        },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          key: 'status',
          width: '100px',
          customRender: (text) => {
            const color = {
              'pending': '#1890ff',
              'in_progress': '#1890ff',
              'paused': '#faad14',
              'success': '#52c41a',
              'failed': '#f5222d'
            }[text] || '#000';
            return <span style={{ color }}>{text}</span>;
          }
        },
        {
          title: this.$t('tool.columns.progress'),
          dataIndex: 'progress',
          key: 'progress',
          width: '200px',
          customRender: (text, record) => (
            <div>
              <a-progress
                percent={text || 0}
                size="small"
                status={record.status === 'failed' ? 'exception' :
                       record.status === 'paused' ? 'normal' : undefined}
              />
              <div style="font-size: 12px; color: #999">
                {this.formatBytes(record.bytes_transferred)} / {this.formatBytes(this.activeUploadTask.metadata.file_size)}
              </div>
            </div>
          )
        },
        {
          title: this.$t('tool.columns.speed'),
          dataIndex: 'speed',
          key: 'speed',
          width: '100px'
        },
        {
          title: this.$t('tool.columns.errorDetails'),
          dataIndex: 'error_detail',
          key: 'error_detail',
          width: '60px',
          scopedSlots: { customRender: 'errorDetail' }
        }
      ];
    },
    progressTableData() {
      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return [];
      return Object.keys(this.activeUploadTask.nodes).map(ip => {
        const node = this.activeUploadTask.nodes[ip];
        return {
          ip,
          host_name: node.host_name,
          status: node.status,
          progress: node.progress || 0,
          speed: node.speed || '-',
          bytes_transferred: node.bytes_transferred,
          error_detail: node.error_detail
        };
      });
    },
    uploadProgress() {
      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return 0;
      const nodes = Object.values(this.activeUploadTask.nodes);
      if (nodes.length === 0) return 0;

      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);
      return Math.round(totalProgress / nodes.length);
    },
    progressBarStatus() {
      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return 'normal';

      const nodes = Object.values(this.activeUploadTask.nodes || {});
      if (nodes.length === 0) return 'normal';

      if (nodes.some(node => node.status === 'failed')) return 'exception';
      if (nodes.every(node => node.status === 'completed')) return 'success';
      return 'active';
    },
    formatSpeed() {
      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return '';
      const totalSpeed = Object.values(this.activeUploadTask.nodes || {})
        .reduce((sum, node) => {
          if (node.status === 'in_progress' && node.speed) {
            return sum + this.parseSpeed(node.speed);
          }
          return sum;
        }, 0);
      return this.formatBytes(totalSpeed) + '/s';
    },
    formatOverallProgress() {
      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return '';
      const nodes = Object.values(this.activeUploadTask.nodes || {});
      const totalTransferred = nodes.reduce((sum, node) => sum + (node.bytes_transferred || 0), 0);
      const totalSize = (this.activeUploadTask.metadata?.file_size || 0) * nodes.length;
      return `${this.formatBytes(totalTransferred)} / ${this.formatBytes(totalSize)}`;
    },
    uploadResultsData() {
      if (!this.uploadResults) return [];

      const results = [];
      this.uploadResults.success.forEach(ip => {
        results.push({ ip, status: 'success' });
      });
      this.uploadResults.failed.forEach(ip => {
        results.push({
          ip,
          status: 'failed',
          error: this.uploadResults.errors[ip]
        });
      });
      return results;
    },
    resultColumns() {
      return [
        { title: this.$t('hostConfig.columns.ipAddress'), dataIndex: 'ip' },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          customRender: (text) => (
            <span style={{ color: text === 'success' ? '#52c41a' : '#f5222d' }}>
              {text}
            </span>
          )
        },
        { title: this.$t('tool.columns.errorDetails'), dataIndex: 'error' }
      ];
    }
  },
  created() {
    if (!this.checkDatabaseStatus()) {
      return;
    }
    this.$store.dispatch('fetchNodes');
    // 只检查当前项目的活动上传任务
    const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);
    if (taskInfo) {
      const { taskId, projectFile } = JSON.parse(taskInfo);
      if (projectFile === this.currentProject) {
        this.checkActiveUploadTask();
      }
    }
  },
  beforeDestroy() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }
  },
  methods: {
    ...mapActions(['addNotification']),
    beforeUpload(file) {
      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB
      if (file.size > maxSize) {
        this.$message.error('File size exceeds the 5GB limit');
        return false;
      }
      this.file = file;
      this.fileName = file.name;
      return false;
    },
    // 处理代理IP变化
    handleProxyChange(ip) {
      console.log('Proxy IP changed:', ip);
      this.selectedProxyIp = ip;
    },
    validateUploadPath(path) {
      if (!path.startsWith('/')) {
        this.$message.error('Path must start with a slash (/)');
        return false;
      }
      if (path.includes('..') || path.includes('./') || path.includes('~')) {
        this.$message.error('Path cannot contain relative path components');
        return false;
      }
      return true;
    },
    async handleUpload() {
      if (!this.file || !this.selectedNodes.length || !this.selectedProxyIp) {
        this.$message.error('Please select a file, nodes, and a proxy IP');
        return;
      }

      if (!this.validateUploadPath(this.uploadDir)) {
        return;
      }

      // 获取当前项目的数据库文件
      const currentDbFile = localStorage.getItem('currentProject');
      if (!currentDbFile) {
        this.$message.error('No project selected. Please select a project first.');
        this.$router.push('/projects');
        return;
      }

      try {
        this.uploading = true;

        // 清除之前的上传任务通知记录
        const previousTaskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);
        if (previousTaskInfo) {
          try {
            const { taskId } = JSON.parse(previousTaskInfo);
            if (taskId) {
              this.clearTaskNotificationMark(taskId, 'upload', this.currentProject);
            }
          } catch (e) {
            console.error('Error clearing previous upload notification:', e);
          }
        }

        const formData = new FormData();
        formData.append('file', this.file);
        formData.append('targets', JSON.stringify(this.selectedNodes));
        formData.append('upload_dir', this.uploadDir);
        formData.append('proxyIp', this.selectedProxyIp);

        const response = await axios.post(
          `/api/file_transfer/upload?dbFile=${encodeURIComponent(this.currentProject)}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        );

        const taskId = response.data.task_id;
        localStorage.setItem(`uploadTask_${this.currentProject}`, JSON.stringify({
          taskId,
          projectFile: this.currentProject
        }));
        localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);

        this.startPolling(taskId);

      } catch (error) {
        console.error('Upload error:', error);
        this.message = `${this.$t('fileUpload.error')}: ${error.response?.data?.error || error.message}`;
        this.messageType = 'error';
        this.uploading = false;
      }
    },
    async startPolling(taskId) {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
      }

      const pollStatus = async () => {
        try {
          if (!this.currentProject) {
            throw new Error('No project database selected');
          }

          const response = await axios.get(
            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`
          );

          this.$store.dispatch('updateUploadTask', response.data);

          const allCompleted = Object.values(response.data.nodes).every(
            node => ['success', 'failed'].includes(node.status)
          );

          if (allCompleted) {
            clearInterval(this.pollInterval);
            this.uploading = false;
            localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');

            const nodes = Object.values(response.data.nodes);

            // 使用混入中的方法添加上传完成通知
            this.addTaskCompletionNotification({
              taskId,
              taskType: 'upload',
              nodes,
              projectId: this.currentProject
            });
          }
        } catch (error) {
          console.error('Error polling status:', error);
          if (error.response?.status === 404) {
            clearInterval(this.pollInterval);
            this.uploading = false;
            localStorage.removeItem(`uploadTask_${this.currentProject}`);
            localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);
          }
        }
      };

      await pollStatus();
      this.pollInterval = setInterval(pollStatus, 5000);
    },
    async checkActiveUploadTask() {
      try {
        const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);
        const taskCompleted = localStorage.getItem(`uploadTaskCompleted_${this.currentProject}`);

        if (taskInfo) {
          const { taskId, projectFile } = JSON.parse(taskInfo);

          if (projectFile !== this.currentProject) {
            return;
          }

          const response = await axios.get(
            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`
          );

          if (response.data) {
            this.$store.dispatch('updateUploadTask', response.data);

            const allCompleted = Object.values(response.data.nodes).every(
              node => ['success', 'failed'].includes(node.status)
            );

            if (!allCompleted && !taskCompleted) {
              this.uploading = true;
              this.startPolling(taskId);
            } else if (allCompleted) {
              this.uploading = false;
              localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');

              const nodes = Object.values(response.data.nodes);

              // 使用混入中的方法添加上传完成通知
              this.addTaskCompletionNotification({
                taskId,
                taskType: 'upload',
                nodes,
                projectId: this.currentProject
              });
            }
          }
        }
      } catch (error) {
        console.error('Error checking active upload task:', error);
      }
    },
    formatBytes(bytes) {
      if (!bytes) return '0 B';
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
    },
    parseSpeed(speed) {
      if (!speed || speed === '-') return 0;
      const [value, unit] = speed.split(' ');
      const multiplier = {
        'B/s': 1,
        'KB/s': 1024,
        'MB/s': 1024 * 1024
      }[unit] || 1;
      return parseFloat(value) * multiplier;
    },
    checkDatabaseStatus() {
      const currentDbFile = localStorage.getItem('currentProject');
      if (!currentDbFile) {
        this.$notify.error({
          title: this.$t('fileUpload.error'),
          message: this.$t('fileUpload.noProjectSelected')
        });
        this.$router.push('/projects');
        return false;
      }
      return true;
    }
  },
  watch: {
    // 添加对 currentProject 的监听
    currentProject: {
      handler(newProject, oldProject) {
        if (newProject !== oldProject) {
          // 清除当前的上传任务状态
          this.$store.dispatch('updateUploadTask', null);
          if (this.pollInterval) {
            clearInterval(this.pollInterval);
          }
          // 检查新项目的活动任务
          this.checkActiveUploadTask();
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped>
.card-header-wrapper {
  margin-bottom: 16px;
}

.header-wrapper {
  display: flex;
  align-items: center;
}

.logo-wrapper {
  margin-right: 8px;
}

.card-title {
  margin: 0;
}

/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* 左右两侧的Flexbox布局 */
.left-section, .right-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 左侧卡片样式 */
.left-section .compact-card {
  flex: 1;
  margin-bottom: 8px;
}

.left-section .compact-card:last-child {
  margin-bottom: 0;
}

/* 右侧卡片样式 */
.right-section .compact-card {
  flex: 1;
}

/* 响应式布局 - 在移动设备上切换为单列布局 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

/* 卡片标题样式 - 使其更紧凑 */
.compact-card >>> .ant-card-head {
  min-height: 40px;
  padding: 0 12px;
}

.compact-card >>> .ant-card-head-title {
  padding: 8px 0;
  font-size: 14px;
}

/* 表格样式 - 使用全局样式，不再硬编码边框颜色 */
</style>
