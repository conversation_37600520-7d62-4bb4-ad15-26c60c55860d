{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/components/Cards/RepositoryDownloadResults.vue?3d78", "webpack:///./src/views/Repository.vue", "webpack:///./src/components/Cards/RepositoryConfig.vue", "webpack:///./src/components/Cards/RepositoryDownloadResults.vue", "webpack:///src/components/Cards/RepositoryDownloadResults.vue", "webpack:///./src/components/Cards/RepositoryDownloadResults.vue?1b6a", "webpack:///./src/components/Cards/RepositoryDownloadResults.vue?e39e", "webpack:///src/components/Cards/RepositoryConfig.vue", "webpack:///./src/components/Cards/RepositoryConfig.vue?f84a", "webpack:///./src/components/Cards/RepositoryConfig.vue?1f67", "webpack:///src/views/Repository.vue", "webpack:///./src/views/Repository.vue?c267", "webpack:///./src/views/Repository.vue?4d91", "webpack:///./src/mixins/CopyMixin.js", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./src/views/Repository.vue?d662", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./src/components/Cards/RepositoryConfig.vue?abac", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/core-js/internals/array-reduce.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "render", "_vm", "_c", "_self", "staticRenderFns", "staticClass", "attrs", "scopedSlots", "_u", "key", "fn", "_v", "_s", "$t", "on", "addNewRow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exportSelectedRepositories", "downloadSelectedRepositories", "deleteSelectedRepositories", "downloadTemplate", "handleUpload", "proxy", "columns", "repositories", "record", "current", "currentPage", "pageSize", "total", "onChange", "onPageChange", "loading", "onSelectChange", "getCheckboxProps", "disabled", "editable", "isNew", "_l", "editableColumns", "col", "text", "index", "staticStyle", "getColumnTitle", "e", "handleChange", "value", "$event", "copyText", "style", "opacity", "_e", "click", "save", "confirm", "cancel", "edit", "copyRepository", "deleteRepository", "downloadResults", "status", "clearResults", "totalCount", "color", "successCount", "failedCount", "tableData", "progress", "error", "substring", "download_path", "name", "computed", "mapState", "repositoryDownloadResults", "Object", "keys", "values", "filter", "repo", "data", "for<PERSON>ach", "push", "microservice_name", "repository_url", "branch_name", "error_detail", "title", "width", "customRender", "dataIndex", "ellipsis", "methods", "$store", "dispatch", "component", "cacheData", "components", "AIcon", "Icon", "RepositoryDownloadResults", "mixins", "CopyMixin", "saving", "currentDbFile", "localStorage", "getItem", "downloadTaskId", "downloadPolling", "downloadPollInterval", "align", "created", "$message", "warning", "$router", "fetchRepositoryConfig", "new<PERSON>ey", "Date", "now", "newRecord", "map", "item", "titleMap", "column", "newData", "find", "repositoryData", "response", "axios", "post", "params", "dbFile", "success", "successful", "failed", "errorMsg", "_error$response", "targetIndex", "findIndex", "cachedItem", "assign", "get", "detail", "_item$id", "id", "toString", "_error$response2", "page", "delete", "r", "_error$response3", "selectedIds", "ids", "includes", "_error$response4", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "remove", "revokeObjectURL", "options", "file", "endsWith", "formData", "FormData", "append", "headers", "failedMessages", "join", "$confirm", "content", "showCancelButton", "confirmButtonText", "type", "_error$response5", "selectedRepositories", "downloadPath", "prompt", "trim", "requestData", "destroy", "taskId", "task_id", "initialResults", "acc", "timestamp", "toLocaleString", "startDownloadPolling", "_error$response6", "message", "clearInterval", "pollDownloadStatus", "setInterval", "console", "log", "taskData", "stopDownloadPolling", "_error$response7", "checkActiveDownloadTask", "taskInfo", "projectFile", "JSON", "parse", "removeItem", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "RepositoryConfig", "textarea", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "reducer", "noInitial", "accumulator", "IS_ITERATOR", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "i", "right"], "mappings": "kHACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,6DChB7F,W,2CCAA,IAAIC,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,qBAAqB,IAEjGE,EAAkB,GCFlBJ,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,SAAS,CAACG,YAAY,sCAAsCC,MAAM,CAAC,UAAW,GAAOC,YAAYN,EAAIO,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,QAAQ,CAACI,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAW,CAACJ,EAAG,QAAQ,CAACI,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACJ,EAAIU,GAAGV,EAAIW,GAAGX,EAAIY,GAAG,gCAAgCX,EAAG,QAAQ,CAACG,YAAY,aAAaC,MAAM,CAAC,KAAO,KAAK,CAACJ,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,WAAW,CAACG,YAAY,iCAAiCC,MAAM,CAAC,KAAO,QAAQQ,GAAG,CAAC,MAAQb,EAAIc,YAAY,CAACd,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,mCAAmC,OAAOX,EAAG,WAAW,CAACG,YAAY,iCAAiCC,MAAM,CAAC,KAAO,SAAS,SAA0C,IAA/BL,EAAIe,gBAAgBlB,QAAcgB,GAAG,CAAC,MAAQb,EAAIgB,6BAA6B,CAAChB,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,oCAAoC,OAAOX,EAAG,WAAW,CAACG,YAAY,iCAAiCC,MAAM,CAAC,KAAO,WAAW,SAA0C,IAA/BL,EAAIe,gBAAgBlB,QAAcgB,GAAG,CAAC,MAAQb,EAAIiB,+BAA+B,CAACjB,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,sCAAsC,OAAOX,EAAG,WAAW,CAACG,YAAY,+CAA+CC,MAAM,CAAC,KAAO,SAAS,KAAO,SAAS,SAA0C,IAA/BL,EAAIe,gBAAgBlB,QAAcgB,GAAG,CAAC,MAAQb,EAAIkB,6BAA6B,CAAClB,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,oCAAoC,QAAQ,GAAGX,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,WAAW,CAACG,YAAY,mBAAmBC,MAAM,CAAC,KAAO,UAAU,KAAO,YAAYQ,GAAG,CAAC,MAAQb,EAAImB,mBAAmB,CAACnB,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,sCAAsC,OAAOX,EAAG,WAAW,CAACI,MAAM,CAAC,oBAAmB,EAAM,iBAAiBL,EAAIoB,aAAa,OAAS,SAAS,CAACnB,EAAG,WAAW,CAACG,YAAY,mBAAmBC,MAAM,CAAC,KAAO,UAAU,KAAO,WAAW,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,oCAAoC,QAAQ,IAAI,QAAQ,KAAKS,OAAM,MAAS,CAACpB,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,UAAU,CAACI,MAAM,CAAC,QAAUL,EAAIsB,QAAQ,cAActB,EAAIuB,aAAa,OAAUC,GAAWA,EAAOhB,IAAI,WAAa,CAC5mEiB,QAASzB,EAAI0B,YACbC,SAAU3B,EAAI2B,SACdC,MAAO5B,EAAIuB,aAAa1B,OACxBgC,SAAU7B,EAAI8B,cACd,QAAU9B,EAAI+B,QAAQ,gBAAgB,CACtChB,gBAAiBf,EAAIe,gBACrBc,SAAU7B,EAAIgC,eACdC,iBAAkBT,IAAU,CAC1BU,SAAUV,EAAOW,UAAYX,EAAOY,UAErC9B,YAAYN,EAAIO,GAAG,CAACP,EAAIqC,GAAIrC,EAAIsC,iBAAiB,SAASC,GAAK,MAAO,CAAC/B,IAAI+B,EAAI9B,GAAG,SAAS+B,EAAMhB,EAAQiB,GAAO,MAAO,CAACxC,EAAG,MAAM,CAACO,IAAI+B,GAAK,CAAEf,EAAOW,SAAUlC,EAAG,UAAU,CAACyC,YAAY,CAAC,OAAS,UAAUrC,MAAM,CAAC,MAAQmC,EAAK,YAAc,SAASxC,EAAI2C,eAAeJ,IAAQ1B,GAAG,CAAC,OAAS+B,GAAK5C,EAAI6C,aAAaD,EAAEtD,OAAOwD,MAAOtB,EAAOhB,IAAK+B,MAAQtC,EAAG,OAAO,CAACyC,YAAY,CAAC,QAAU,OAAO,cAAc,WAAW,CAAU,mBAARH,GAA4BC,EAAMvC,EAAG,SAAS,CAACyC,YAAY,CAAC,OAAS,UAAU,cAAc,MAAM,QAAU,MAAM,YAAY,QAAQrC,MAAM,CAAC,KAAO,QAAQQ,GAAG,CAAC,MAAQ,SAASkC,GAAQ,OAAO/C,EAAIgD,SAASR,IAAO,WAAa,SAASO,GAAQA,EAAOzD,OAAO2D,MAAMC,QAAU,KAAK,WAAa,SAASH,GAAQA,EAAOzD,OAAO2D,MAAMC,QAAU,UAAUlD,EAAImD,KAAKlD,EAAG,OAAO,CAACgD,MAAe,mBAARV,GAA4BC,EAAO,kBAAoB,GAAI3B,GAAG,CAAC,MAAQ,SAASkC,GAAgB,mBAARR,GAA4BC,GAAOxC,EAAIgD,SAASR,MAAgB,CAACxC,EAAIU,GAAGV,EAAIW,GAAG6B,GAAQ,SAAS,IAAI,SAAQ,CAAChC,IAAI,YAAYC,GAAG,SAAS+B,EAAMhB,EAAQiB,GAAO,MAAO,CAACxC,EAAG,MAAM,CAACG,YAAY,2BAA2B,CAAEoB,EAAOW,SAAU,CAAClC,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,QAAQQ,GAAG,CAAC,MAAQuC,IAAMpD,EAAIqD,KAAK7B,EAAOhB,OAAO,CAACR,EAAIU,GAAGV,EAAIW,GAAGX,EAAIY,GAAG,6BAA6BX,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,oBAAoBQ,GAAG,CAAC,QAAUyC,IAAMtD,EAAIuD,OAAO/B,EAAOhB,OAAO,CAACP,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACL,EAAIU,GAAGV,EAAIW,GAAGX,EAAIY,GAAG,gCAAgC,IAAI,CAACX,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,QAAQQ,GAAG,CAAC,MAAQuC,IAAMpD,EAAIwD,KAAKhC,EAAOhB,OAAO,CAACR,EAAIU,GAAGV,EAAIW,GAAGX,EAAIY,GAAG,6BAA6BX,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,QAAQQ,GAAG,CAAC,MAAQuC,IAAMpD,EAAIyD,eAAejC,KAAU,CAACxB,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,0BAA0B,OAAOX,EAAG,eAAe,CAACI,MAAM,CAAC,MAAQ,qBAAqBQ,GAAG,CAAC,QAAUyC,IAAMtD,EAAI0D,iBAAiBlC,KAAU,CAACvB,EAAG,WAAW,CAACI,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACL,EAAIU,GAAGV,EAAIW,GAAGX,EAAIY,GAAG,gCAAgC,KAAK,OAAO,MAAK,MAAS,KAAKX,EAAG,gCAAgC,IAEn9DE,EAAkB,G,sFCblBJ,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,wBAAwBC,MAAM,CAAC,KAAO,QAAQ,MAAQL,EAAIY,GAAG,6BAA6BN,YAAYN,EAAIO,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,UAAU,CAAED,EAAI2D,iBAAkD,YAA/B3D,EAAI2D,gBAAgBC,OAAsB3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,mCAAmC,OAAQZ,EAAI2D,iBAAkD,YAA/B3D,EAAI2D,gBAAgBC,OAAsB3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,UAAU,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,iCAAiC,OAAQZ,EAAI2D,iBAAkD,oBAA/B3D,EAAI2D,gBAAgBC,OAA8B3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,WAAW,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,wCAAwC,OAAQZ,EAAI2D,iBAAkD,WAA/B3D,EAAI2D,gBAAgBC,OAAqB3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,QAAQ,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,8BAA8B,OAAOZ,EAAImD,KAAKlD,EAAG,WAAW,CAACG,YAAY,eAAeC,MAAM,CAAC,KAAO,OAAO,KAAO,SAASQ,GAAG,CAAC,MAAQb,EAAI6D,eAAe,CAAC5D,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,WAAWL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,6BAA6B,MAAM,IAAI,KAAKS,OAAM,MAAS,CAACpB,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,QAAQ,CAACI,MAAM,CAAC,OAAS,KAAK,CAACJ,EAAG,QAAQ,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQL,EAAIY,GAAG,4BAA4B,MAAQZ,EAAI8D,WAAW,cAAc,CAAEC,MAAO,eAAgB,GAAG9D,EAAG,QAAQ,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQL,EAAIY,GAAG,8BAA8B,MAAQZ,EAAIgE,aAAa,cAAc,CAAED,MAAO,eAAgB,GAAG9D,EAAG,QAAQ,CAACI,MAAM,CAAC,KAAO,IAAI,CAACJ,EAAG,cAAc,CAACI,MAAM,CAAC,MAAQL,EAAIY,GAAG,6BAA6B,MAAQZ,EAAIiE,YAAY,cAAc,CAAEF,MAAO,eAAgB,IAAI,IAAI,GAAG9D,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,UAAU,CAACI,MAAM,CAAC,QAAUL,EAAIsB,QAAQ,cAActB,EAAIkE,UAAU,YAAa,EAAM,KAAO,QAAQ,UAAU1C,GAAUA,EAAOhB,KAAKF,YAAYN,EAAIO,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,SAAS+B,EAAMhB,GAAQ,MAAO,CAAoB,YAAlBA,EAAOoC,OAAsB3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,UAAU,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,+BAA+B,OAA0B,WAAlBY,EAAOoC,OAAqB3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,QAAQ,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,8BAA8B,OAA0B,gBAAlBY,EAAOoC,OAA0B3D,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,SAAS,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,mCAAmC,OAAOX,EAAG,QAAQ,CAACI,MAAM,CAAC,MAAQ,WAAW,CAACL,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,+BAA+B,UAAU,CAACJ,IAAI,WAAWC,GAAG,SAAS+B,EAAMhB,GAAQ,MAAO,CAAoB,gBAAlBA,EAAOoC,OAA0B3D,EAAG,MAAM,CAACA,EAAG,aAAa,CAACI,MAAM,CAAC,QAAUmB,EAAO2C,UAAY,EAAE,KAAO,QAAQ,OAAS,aAAa,GAAsB,YAAlB3C,EAAOoC,OAAsB3D,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAAC1C,EAAIU,GAAG,YAA+B,WAAlBc,EAAOoC,OAAqB3D,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAAC1C,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,8BAA8B,OAAOX,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAAC1C,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,+BAA+B,UAAU,CAACJ,IAAI,SAASC,GAAG,SAAS+B,EAAMhB,GAAQ,MAAO,CAAEA,EAAO4C,MAAOnE,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,YAAY,CAACzC,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQmB,EAAO4C,QAAQ,CAACnE,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,wBAAwBL,EAAIU,GAAG,IAAIV,EAAIW,GAAGa,EAAO4C,MAAMvE,OAAS,GAAK2B,EAAO4C,MAAMC,UAAU,EAAG,IAAM,MAAQ7C,EAAO4C,OAAO,MAAM,IAAI,GAAI5C,EAAO8C,cAAerE,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,YAAY,CAACzC,EAAG,YAAY,CAACI,MAAM,CAAC,MAAQmB,EAAO8C,gBAAgB,CAACrE,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,kBAAkBL,EAAIU,GAAG,IAAIV,EAAIW,GAAGa,EAAO8C,cAAczE,OAAS,GAAK,MAAQ2B,EAAO8C,cAAcD,UAAU7C,EAAO8C,cAAczE,OAAS,IAAM2B,EAAO8C,eAAe,MAAM,IAAI,GAAsB,gBAAlB9C,EAAOoC,OAA0B3D,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,YAAY,CAACzC,EAAG,SAAS,CAACI,MAAM,CAAC,KAAO,UAAU,KAAO,MAAML,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,mCAAmC,SAAS,GAAGX,EAAG,OAAO,CAACyC,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAAC1C,EAAIU,GAAG,IAAIV,EAAIW,GAAGX,EAAIY,GAAG,+BAA+B,eAAe,MAE9+HT,EAAkB,G,wBC4HP,GACfoE,KAAA,4BACAC,SAAA,IACAC,eAAA,+BAEAd,kBACA,YAAAe,2BAGAZ,aACA,YAAAH,iBAAA,KAAAA,gBAAApC,aACAoD,OAAAC,KAAA,KAAAjB,gBAAApC,cAAA1B,OADA,GAIAmE,eACA,YAAAL,iBAAA,KAAAA,gBAAApC,aACAoD,OAAAE,OAAA,KAAAlB,gBAAApC,cAAAuD,OAAAC,GAAA,YAAAA,EAAAnB,QAAA/D,OADA,GAIAoE,cACA,YAAAN,iBAAA,KAAAA,gBAAApC,aACAoD,OAAAE,OAAA,KAAAlB,gBAAApC,cAAAuD,OAAAC,GAAA,WAAAA,EAAAnB,QAAA/D,OADA,GAIAqE,YACA,SAAAP,kBAAA,KAAAA,gBAAApC,aAAA,SAEA,MAAAyD,EAAA,GAgBA,OAbAL,OAAAE,OAAA,KAAAlB,gBAAApC,cAAA0D,QAAA,CAAAF,EAAAtC,KACAuC,EAAAE,KAAA,CACA1E,IAAA,QAAAiC,EACA0C,kBAAAJ,EAAAI,kBACAC,eAAAL,EAAAK,eACAC,YAAAN,EAAAM,YACAzB,OAAAmB,EAAAnB,OACAO,SAAAY,EAAAZ,UAAA,EACAG,cAAAS,EAAAT,cACAF,MAAAW,EAAAO,iBAIAN,GAGA1D,UACA,OACA,CACAiE,MAAA,IACAC,MAAA,IACAC,cAAAjD,EAAAhB,EAAAiB,MAAA,GAEA,CACA8C,MAAA,KAAA3E,GAAA,yCACA8E,UAAA,oBACAlF,IAAA,oBACAgF,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,0CACA8E,UAAA,iBACAlF,IAAA,iBACAmF,UAAA,EACAH,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,uCACA8E,UAAA,cACAlF,IAAA,cACAgF,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,uBACA8E,UAAA,SACAlF,IAAA,SACAF,YAAA,CAAAmF,aAAA,UACAD,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,+BACA8E,UAAA,WACAlF,IAAA,WACAF,YAAA,CAAAmF,aAAA,YACAD,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,uBACA8E,UAAA,SACAlF,IAAA,SACAF,YAAA,CAAAmF,aAAA,cAMAG,QAAA,CACA/B,eACA,KAAAgC,OAAAC,SAAA,qCChOiX,I,wBCQ7WC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,oBCsJf,IAAAC,EAAA,GAEe,OACfC,WAAA,CACAC,MAAAC,OACAC,6BAEAC,OAAA,CAAAC,QACA9B,SAAA,GAEAQ,OACA,OACAzD,aAAA,GACAgF,QAAA,EACAxE,SAAA,EACAL,YAAA,EACAC,SAAA,GACAW,gBAAA,CACA,oBACA,iBACA,eAEAvB,gBAAA,GACAyF,cAAAC,aAAAC,QAAA,kBACAC,eAAA,KACAC,iBAAA,EACAC,qBAAA,KACAvF,QAAA,CACA,CACAiE,MAAA,IACAG,UAAA,QACAF,MAAA,GACAC,cAAAjD,EAAAhB,EAAAiB,KACA,KAAAf,YAAA,QAAAC,SAAAc,EAAA,GAGA,CACA8C,MAAA,KAAA3E,GAAA,yCACA8E,UAAA,oBACApF,YAAA,CAAAmF,aAAA,qBACAD,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,0CACA8E,UAAA,iBACApF,YAAA,CAAAmF,aAAA,kBACAD,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,uCACA8E,UAAA,cACApF,YAAA,CAAAmF,aAAA,eACAD,MAAA,KAEA,CACAD,MAAA,KAAA3E,GAAA,4BACA8E,UAAA,YACApF,YAAA,CAAAmF,aAAA,aACAD,MAAA,IACAsB,MAAA,aAKAC,UACA,SAAAP,cAGA,OAFA,KAAAQ,SAAAC,QAAA,sCACA,KAAAC,QAAAhC,KAAA,aAGA,KAAAiC,yBAEAvB,QAAA,CACAnC,eAAAjC,GACA,MAAA4F,EAAA,OAAAC,KAAAC,MACAC,EAAA,IACA/F,EACAhB,IAAA4G,EACAjF,UAAA,EACAC,OAAA,EACA+C,kBAAA3D,EAAA2D,kBAAA,SAGA,KAAA5D,aAAA,CAAAgG,KAAA,KAAAhG,cACA,KAAAG,YAAA,EACAsE,EAAA,KAAAzE,aAAAiG,IAAAC,IAAA,IAAAA,KACA,KAAA1G,gBAAA,IAGA4B,eAAAJ,GACA,MAAAmF,EAAA,CACA,uBAAA9G,GAAA,yCACA,oBAAAA,GAAA,0CACA,iBAAAA,GAAA,wCAEA,OAAA8G,EAAAnF,OAGAM,aAAAC,EAAAtC,EAAAmH,GACA,MAAAC,EAAA,SAAArG,cACAjC,EAAAsI,EAAAC,KAAAJ,KAAAjH,SACAlB,IACAA,EAAAqI,GAAA7E,EACA,KAAAvB,aAAAqG,IAIApE,KAAAhD,GACA,MAAAoH,EAAA,SAAArG,cACAjC,EAAAsI,EAAAC,KAAAJ,KAAAjH,SACAlB,IACAA,EAAA6C,UAAA,EACA,KAAAZ,aAAAqG,IAIA,WAAApH,GACA,MAAAoH,EAAA,SAAArG,cACAjC,EAAAsI,EAAAC,KAAAJ,KAAAjH,SACA,GAAAlB,EAAA,QACAA,EAAA6C,SACA,KAAAZ,aAAAqG,EACA5B,EAAA4B,EAAAJ,IAAAC,IAAA,IAAAA,KAEA,IACA,KAAAlB,QAAA,EACA,MAAAuB,EAAA,CACA3C,kBAAA7F,EAAA6F,kBACAC,eAAA9F,EAAA8F,eACAC,YAAA/F,EAAA+F,aAGA0C,QAAAC,OAAAC,KAAA,qBACA1G,aAAA,CAAAuG,IACA,CACAI,OAAA,CAAAC,OAAA,KAAA3B,iBAGA,GAAAuB,EAAA/C,KAAAoD,QAAA,CACA,iBAAAC,EAAA,OAAAC,GAAAP,EAAA/C,UAEA,GAAAsD,EAAAzI,OAAA,GAEA,MAAA0I,EAAAD,EAAA,GAAAlE,MAEA,YADA,KAAA4C,SAAA5C,MAAA,QAAAxD,GAAA,8CAAA2H,WAIA,KAAApB,wBACA,KAAAH,SAAAoB,QAAA,sCAEA,KAAApB,SAAA5C,MAAA2D,EAAA/C,KAAAZ,OAAA,6BAEA,MAAAA,GAAA,IAAAoE,EACA,KAAAxB,SAAA5C,OAAA,QAAAoE,EAAApE,EAAA2D,gBAAA,IAAAS,GAAA,QAAAA,IAAAxD,YAAA,IAAAwD,OAAA,EAAAA,EAAApE,QAAA,mCACA,KAAA+C,wBACA,QACA,KAAAZ,QAAA,KAKAhD,OAAA/C,GACA,MAAAiI,EAAA,KAAAlH,aAAAmH,UAAAjB,KAAAjH,SACA,QAAAiI,EAAA,OAEA,MAAAnJ,EAAA,KAAAiC,aAAAkH,GAEA,GAAAnJ,EAAA8C,MAEA,KAAAb,aAAA,KAAAA,aAAAuD,OAAA2C,KAAAjH,aACA,CAEA,MAAAoH,EAAA,SAAArG,cACAoH,EAAA3C,EAAA6B,KAAAJ,KAAAjH,SACAmI,IACAhE,OAAAiE,OAAAtJ,EAAA,IAAAqJ,WACArJ,EAAA6C,SACA,KAAAZ,aAAAqG,KAKA9G,YACA,KAAAS,aAAA,CACA,CACAf,IAAA,OAAA6G,KAAAC,MACAnC,kBAAA,GACAC,eAAA,GACAC,YAAA,OACAlD,UAAA,EACAC,OAAA,MAEA,KAAAb,cAEA,KAAAG,YAAA,EACAsE,EAAA,KAAAzE,aAAAiG,IAAAC,IAAA,IAAAA,KACA,KAAA1G,gBAAA,IAGA,8BACA,IACA,KAAAgB,SAAA,EACA,MAAAgG,QAAAC,OAAAa,IAAA,qBACAX,OAAA,CACAY,QAAA,EACAX,OAAA,KAAA3B,iBAGA,KAAAjF,aAAAwG,EAAA/C,UAAAwC,IAAAC,IAAA,IAAAsB,EAAA,UACAtB,EACAjH,KAAA,QAAAuI,EAAAtB,EAAAuB,UAAA,IAAAD,OAAA,EAAAA,EAAAE,aAAA,QAAAxB,EAAAtC,kBACA/C,OAAA,KAEA4D,EAAA,KAAAzE,aAAAiG,IAAAC,IAAA,IAAAA,KACA,MAAArD,GAAA,IAAA8E,EACA,KAAAlC,SAAA5C,OAAA,QAAA8E,EAAA9E,EAAA2D,gBAAA,IAAAmB,GAAA,QAAAA,IAAAlE,YAAA,IAAAkE,OAAA,EAAAA,EAAA9E,QAAA,+BACA,QACA,KAAArC,SAAA,IAIAD,aAAAqH,GACA,KAAAzH,YAAAyH,GAGA,uBAAA3H,GACA,IACAA,EAAAwH,UACAhB,OAAAoB,OAAA,qBAAA5H,EAAAwH,GAAA,CACAd,OAAA,CAAAC,OAAA,KAAA3B,iBAGA,KAAAjF,aAAA,KAAAA,aAAAuD,OAAAuE,KAAA7I,MAAAgB,EAAAhB,KACA,KAAAO,gBAAA,KAAAA,gBAAA+D,OAAAtE,OAAAgB,EAAAhB,KACA,KAAAwG,SAAAoB,QAAA,0BAEA,KAAA7G,aAAA,KAAAA,aAAAuD,OAAAuE,KAAA7I,MAAAgB,EAAAhB,KACA,KAAAO,gBAAA,KAAAA,gBAAA+D,OAAAtE,OAAAgB,EAAAhB,MAEA,MAAA4D,GAAA,IAAAkF,EACA,KAAAtC,SAAA5C,OAAA,QAAAkF,EAAAlF,EAAA2D,gBAAA,IAAAuB,GAAA,QAAAA,IAAAtE,YAAA,IAAAsE,OAAA,EAAAA,EAAAlF,QAAA,qCACA,KAAA+C,0BAIAnF,eAAAjB,GACA,KAAAA,mBAGA,mCACA,YAAAA,gBAAAlB,OAKA,IACA,MAAA0J,EAAA,KAAAxI,gBACAyG,IAAAhH,GAAA,KAAAe,aAAAsG,KAAAwB,KAAA7I,UACAsE,OAAAC,QAAAiE,IACAxB,IAAAzC,KAAAiE,IAEAO,EAAA1J,OAAA,SACAmI,OAAAC,KAAA,kCACAuB,IAAAD,GACA,CACArB,OAAA,CAAAC,OAAA,KAAA3B,iBAIA,KAAAjF,aAAA,KAAAA,aAAAuD,OAAAuE,IAAA,KAAAtI,gBAAA0I,SAAAJ,EAAA7I,MACA,KAAAO,gBAAA,GACA,KAAAiG,SAAAoB,QAAA,8CACA,MAAAhE,GAAA,IAAAsF,EACA,KAAA1C,SAAA5C,OAAA,QAAAsF,EAAAtF,EAAA2D,gBAAA,IAAA2B,GAAA,QAAAA,IAAA1E,YAAA,IAAA0E,OAAA,EAAAA,EAAAtF,QAAA,uCACA,KAAA+C,6BAvBA,KAAAH,SAAAC,QAAA,yCA2BA,mCACA,YAAAlG,gBAAAlB,OAKA,IACA,MAAA0J,EAAA,KAAAxI,gBACAyG,IAAAhH,GAAA,KAAAe,aAAAsG,KAAAwB,KAAA7I,UACAsE,OAAAC,QAAAiE,IACAxB,IAAAzC,KAAAiE,IAEAjB,QAAAC,OAAAC,KAAA,4BACAuB,IAAAD,GACA,CACArB,OAAA,CAAAC,OAAA,KAAA3B,eACAmD,aAAA,SAGAC,EAAAC,OAAAC,IAAAC,gBAAA,IAAAC,KAAA,CAAAjC,EAAA/C,QACAiF,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAAR,EACAK,EAAAI,aAAA,sCACAH,SAAAI,KAAAC,YAAAN,GACAA,EAAA7G,QACA6G,EAAAO,SACAX,OAAAC,IAAAW,gBAAAb,GAEA,KAAA5C,SAAAoB,QAAA,sCACA,MAAAhE,GACA,KAAA4C,SAAA5C,MAAA,sCA5BA,KAAA4C,SAAAC,QAAA,yCAgCA,yBACA,IACA,MAAAc,QAAAC,OAAAa,IAAA,8BACAc,aAAA,SAGAC,EAAAC,OAAAC,IAAAC,gBAAA,IAAAC,KAAA,CAAAjC,EAAA/C,QACAiF,EAAAC,SAAAC,cAAA,KACAF,EAAAG,KAAAR,EACAK,EAAAI,aAAA,sCACAH,SAAAI,KAAAC,YAAAN,GACAA,EAAA7G,QACA6G,EAAAO,SACAX,OAAAC,IAAAW,gBAAAb,GAEA,KAAA5C,SAAAoB,QAAA,oCACA,MAAAhE,GACA,KAAA4C,SAAA5C,MAAA,iCAIA,mBAAAsG,GACA,WAAAC,GAAAD,EAEA,GAAAC,EAAApG,KAAAqG,SAAA,QAKA,IACA,MAAAC,EAAA,IAAAC,SACAD,EAAAE,OAAA,OAAAJ,GACAE,EAAAE,OAAA,cAAAvE,eAEA,MAAAuB,QAAAC,OAAAC,KAAA,2BAAA4C,EAAA,CACAG,QAAA,yCAGA,GAAAjD,EAAA/C,KAAAoD,QAAA,CACA,iBAAAC,EAAA,OAAAC,GAAAP,EAAA/C,UAEA,GAAAsD,EAAAzI,OAAA,GAEA,MAAAoL,EAAA3C,EAAAd,IAAAzC,GACA,GAAAA,EAAAI,mBAAA,cAAAJ,EAAAX,SACA8G,KAAA,MAEA,KAAAC,SAAA,CACA5F,MAAA,KAAA3E,GAAA,0CACAwK,QAAAH,EACAI,kBAAA,EACAC,kBAAA,KACAC,KAAA,YAIAlD,EAAAxI,OAAA,UACA,KAAAsH,wBACA,KAAAH,SAAAoB,QAAA,yBAAAC,EAAAxI,wBAGAyI,EAAAzI,OAAA,OAAAwI,EAAAxI,QACA,KAAAmH,SAAA5C,MAAA,gDAGA,KAAA4C,SAAA5C,MAAA2D,EAAA/C,KAAAZ,OAAA,iCAEA,MAAAA,GAAA,IAAAoH,EACA,KAAAxE,SAAA5C,OAAA,QAAAoH,EAAApH,EAAA2D,gBAAA,IAAAyD,GAAA,QAAAA,IAAAxG,YAAA,IAAAwG,OAAA,EAAAA,EAAApH,QAAA,sCA3CA,KAAA4C,SAAA5C,MAAA,2BA+CA,qCACA,YAAArD,gBAAAlB,OAEA,YADA,KAAAmH,SAAAC,QAAA,0CAKA,MAAAwE,EAAA,KAAA1K,gBACAyG,IAAAhH,GAAA,KAAAe,aAAAsG,KAAAwB,KAAA7I,UACAsE,OAAAC,QAAAiE,IAEA,OAAAyC,EAAA5L,OAEA,YADA,KAAAmH,SAAAC,QAAA,kCAKA,MAAAyE,EAAAC,OAAA,KAAA/K,GAAA,yDAEA,GAAA8K,KAAAE,OAAA,CAKA,KAAA5E,SAAAjF,QAAA,KAAAnB,GAAA,yCAEA,IACA,MAAAiL,EAAA,CACAtK,aAAAkK,EAAAjE,IAAAzC,IAAA,CACAiE,GAAAjE,EAAAiE,GACA7D,kBAAAJ,EAAAI,kBACAC,eAAAL,EAAAK,eACAC,YAAAN,EAAAM,eAEAf,cAAAoH,EAAAE,QAGA7D,QAAAC,OAAAC,KAAA,6BAAA4D,EAAA,CACA3D,OAAA,CAAAC,OAAA,KAAA3B,iBAKA,GAFA,KAAAQ,SAAA8E,UAEA/D,EAAA/C,KAAAoD,QAAA,CACA,MAAA2D,EAAAhE,EAAA/C,UAAAgH,QACA,KAAArF,eAAAoF,EAGA,MAAAE,EAAA,CACAD,QAAAD,EACAnI,OAAA,UACAyE,WAAA,GACAC,OAAA,GACA/G,aAAAkK,EAAAhM,OAAA,CAAAyM,EAAAnH,KACA,MAAAvE,EAAA,GAAAuE,EAAAI,qBAAAJ,EAAAK,iBAUA,OATA8G,EAAA1L,GAAA,CACA2E,kBAAAJ,EAAAI,kBACAC,eAAAL,EAAAK,eACAC,YAAAN,EAAAM,YACAzB,OAAA,UACAO,SAAA,EACAmB,aAAA,KACAhB,cAAA,MAEA4H,GACA,IACAC,WAAA,IAAA9E,MAAA+E,kBAIA,KAAAvG,OAAAC,SAAA,kCAAAmG,GAEA,KAAAjF,SAAAoB,QAAA,iDAGA,KAAAiE,qBAAAN,QAEA,KAAA/E,SAAA5C,MAAA2D,EAAA/C,KAAAZ,OAAA,KAAAxD,GAAA,qCAEA,MAAAwD,GAAA,IAAAkI,EACA,KAAAtF,SAAA8E,UACA,KAAA9E,SAAA5C,OAAA,QAAAkI,EAAAlI,EAAA2D,gBAAA,IAAAuE,GAAA,QAAAA,IAAAtH,YAAA,IAAAsH,OAAA,EAAAA,EAAAlI,UAAAmI,SAAA,KAAA3L,GAAA,wCAIAyL,qBAAAN,GACA,KAAAlF,sBACA2F,cAAA,KAAA3F,sBAGA,KAAAD,iBAAA,EAGA,KAAA6F,mBAAAV,GAGA,KAAAlF,qBAAA6F,YAAA,KACA,KAAAD,mBAAAV,IACA,KAEAY,QAAAC,IAAA,eAAAb,eAGA,yBAAAA,GACA,IACA,MAAAhE,QAAAC,OAAAa,IAAA,qCAAAkD,EAAA,CACA7D,OAAA,CAAAC,OAAA,KAAA3B,iBAGA,GAAAuB,EAAA/C,KAAAoD,QAAA,CACA,MAAAyE,EAAA9E,EAAA/C,UAGA,KAAAa,OAAAC,SAAA,sCACA+G,EACAV,WAAA,IAAA9E,MAAA+E,mBAIA,YAAAS,EAAAjJ,QAAA,WAAAiJ,EAAAjJ,QAAA,oBAAAiJ,EAAAjJ,SACA,KAAAkJ,sBAGA,YAAAD,EAAAjJ,OACA,KAAAoD,SAAAoB,QAAA,KAAAxH,GAAA,sCACA,WAAAiM,EAAAjJ,OACA,KAAAoD,SAAA5C,MAAA,KAAAxD,GAAA,qCAEA,KAAAoG,SAAAC,QAAA,KAAArG,GAAA,6CAGA+L,QAAAC,IAAA,wBAGAD,QAAAvI,MAAA,YAAA2D,EAAA/C,KAAAZ,OAEA,MAAAA,GAAA,IAAA2I,EACAJ,QAAAvI,MAAA,YAAAA,GAGA,eAAA2I,EAAA3I,EAAA2D,gBAAA,IAAAgF,OAAA,EAAAA,EAAAnJ,UACA,KAAAkJ,sBACA,KAAA9F,SAAA5C,MAAA,8BAKA0I,sBACA,KAAAjG,uBACA2F,cAAA,KAAA3F,sBACA,KAAAA,qBAAA,MAEA,KAAAD,iBAAA,EACA,KAAAD,eAAA,MAGAqG,0BAEA,MAAAC,EAAAxG,aAAAC,QAAA,+BAAAF,eACA,GAAAyG,EACA,IACA,aAAAlB,EAAA,YAAAmB,GAAAC,KAAAC,MAAAH,GACAC,IAAA,KAAA1G,gBACA,KAAAG,eAAAoF,EACA,KAAAM,qBAAAN,IAEA,MAAAnJ,GACA+J,QAAAvI,MAAA,+CAAAxB,GACA6D,aAAA4G,WAAA,+BAAA7G,kBAOA8G,UACA,KAAAnG,wBAEA,KAAA6F,2BAGAO,gBAEA,KAAAT,wBCjuBwW,ICQpW,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCVA,GACfvI,KAAA,aACA0B,WAAA,CACAuH,qBCZmV,ICQ/U,G,UAAY,eACd,EACAzN,EACAI,GACA,EACA,KACA,WACA,OAIa,e,6CCnBA,QACbyF,QAAS,CACP5C,SAASR,GACP,MAAMiL,EAAWvD,SAASC,cAAc,YACxCsD,EAAS3K,MAAQN,EACjB0H,SAASI,KAAKC,YAAYkD,GAC1BA,EAASC,SACTxD,SAASyD,YAAY,QACrBzD,SAASI,KAAKsD,YAAYH,GAC1B9N,KAAKqH,SAASoB,QAAQ,2B,uBCT5B,IAAIyF,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCHhC,W,oCCEA,IAAInP,EAAI,EAAQ,QACZoP,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvBtP,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAM8O,MAAM,GAAQ,CACjD5O,OAAQ,SAAgB6O,GACtBF,EAASzO,MACTwO,EAAUG,GACV,IAAIC,EAAY3O,UAAUC,OAAS,EAC/B2O,EAAcD,OAAYzO,EAAYF,UAAU,GASpD,GARAsO,EAAQvO,MAAM,SAAUmD,GAClByL,GACFA,GAAY,EACZC,EAAc1L,GAEd0L,EAAcF,EAAQE,EAAa1L,KAEpC,CAAE2L,aAAa,IACdF,EAAW,MAAMG,UAAU,kDAC/B,OAAOF,M,oCCtBX,W,yDCCA,IAAIG,EAAQ,EAAQ,QAEpBZ,EAAOC,QAAU,SAAUY,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,4CCP5D,IAAIV,EAAY,EAAQ,QACpBa,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAM3P,EAAY4P,EAAiBC,GAClDpB,EAAUzO,GACV,IAAI8P,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrB3P,EAASqP,EAASM,EAAE3P,QACpB4C,EAAQ2M,EAAWvP,EAAS,EAAI,EAChC6P,EAAIN,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAI7M,KAASgN,EAAM,CACjBF,EAAOE,EAAKhN,GACZA,GAASiN,EACT,MAGF,GADAjN,GAASiN,EACLN,EAAW3M,EAAQ,EAAI5C,GAAU4C,EACnC,MAAMiM,UAAU,+CAGpB,KAAMU,EAAW3M,GAAS,EAAI5C,EAAS4C,EAAOA,GAASiN,EAAOjN,KAASgN,IACrEF,EAAO7P,EAAW6P,EAAME,EAAKhN,GAAQA,EAAO+M,IAE9C,OAAOD,IAIXxB,EAAOC,QAAU,CAGfhP,KAAMmQ,GAAa,GAGnBQ,MAAOR,GAAa", "file": "static/js/chunk-49d38e76.b910ab97.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryDownloadResults.vue?vue&type=style&index=0&id=550fb864&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('RepositoryConfig')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-card',{staticClass:\"header-solid repository-config-card\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('a-row',{attrs:{\"type\":\"flex\",\"align\":\"middle\"}},[_c('a-col',{attrs:{\"span\":12}},[_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('repositoryConfig.title')))])]),_c('a-col',{staticClass:\"text-right\",attrs:{\"span\":12}},[_c('div',{staticClass:\"button-groups\"},[_c('div',{staticClass:\"button-group\"},[_c('a-button',{staticClass:\"nav-style-button action-button\",attrs:{\"icon\":\"plus\"},on:{\"click\":_vm.addNewRow}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.addRepository'))+\" \")]),_c('a-button',{staticClass:\"nav-style-button action-button\",attrs:{\"icon\":\"export\",\"disabled\":_vm.selectedRowKeys.length === 0},on:{\"click\":_vm.exportSelectedRepositories}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.exportSelected'))+\" \")]),_c('a-button',{staticClass:\"nav-style-button action-button\",attrs:{\"icon\":\"download\",\"disabled\":_vm.selectedRowKeys.length === 0},on:{\"click\":_vm.downloadSelectedRepositories}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.downloadSelected'))+\" \")]),_c('a-button',{staticClass:\"nav-style-button action-button delete-button\",attrs:{\"type\":\"danger\",\"icon\":\"delete\",\"disabled\":_vm.selectedRowKeys.length === 0},on:{\"click\":_vm.deleteSelectedRepositories}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.deleteSelected'))+\" \")])],1),_c('div',{staticClass:\"button-group\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"type\":\"default\",\"icon\":\"download\"},on:{\"click\":_vm.downloadTemplate}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.downloadTemplate'))+\" \")]),_c('a-upload',{attrs:{\"show-upload-list\":false,\"custom-request\":_vm.handleUpload,\"accept\":\".csv\"}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"type\":\"default\",\"icon\":\"upload\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.uploadTemplate'))+\" \")])],1)],1)])])],1)]},proxy:true}])},[_c('div',{staticClass:\"config-table\"},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.repositories,\"rowKey\":(record) => record.key,\"pagination\":{\n        current: _vm.currentPage,\n        pageSize: _vm.pageSize,\n        total: _vm.repositories.length,\n        onChange: _vm.onPageChange,\n      },\"loading\":_vm.loading,\"row-selection\":{\n        selectedRowKeys: _vm.selectedRowKeys,\n        onChange: _vm.onSelectChange,\n        getCheckboxProps: record => ({\n          disabled: record.editable || record.isNew\n        })\n      }},scopedSlots:_vm._u([_vm._l((_vm.editableColumns),function(col){return {key:col,fn:function(text, record, index){return [_c('div',{key:col},[(record.editable)?_c('a-input',{staticStyle:{\"margin\":\"-5px 0\"},attrs:{\"value\":text,\"placeholder\":`Enter ${_vm.getColumnTitle(col)}`},on:{\"change\":e => _vm.handleChange(e.target.value, record.key, col)}}):_c('span',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[(col === 'repository_url' && text)?_c('a-icon',{staticStyle:{\"cursor\":\"pointer\",\"margin-left\":\"4px\",\"opacity\":\"0.6\",\"font-size\":\"12px\"},attrs:{\"type\":\"copy\"},on:{\"click\":function($event){return _vm.copyText(text)},\"mouseenter\":function($event){$event.target.style.opacity = '1'},\"mouseleave\":function($event){$event.target.style.opacity = '0.6'}}}):_vm._e(),_c('span',{style:(col === 'repository_url' && text ? 'cursor: pointer' : ''),on:{\"click\":function($event){col === 'repository_url' && text ? _vm.copyText(text) : null}}},[_vm._v(_vm._s(text || '-'))])],1)],1)]}}}),{key:\"operation\",fn:function(text, record, index){return [_c('div',{staticClass:\"editable-row-operations\"},[(record.editable)?[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.save(record.key)}},[_vm._v(_vm._s(_vm.$t('repositoryConfig.save')))]),_c('a-popconfirm',{attrs:{\"title\":\"Discard changes?\"},on:{\"confirm\":() => _vm.cancel(record.key)}},[_c('a-button',{attrs:{\"type\":\"link\",\"danger\":\"\"}},[_vm._v(_vm._s(_vm.$t('repositoryConfig.cancel')))])],1)]:[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.edit(record.key)}},[_vm._v(_vm._s(_vm.$t('repositoryConfig.edit')))]),_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.copyRepository(record)}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryConfig.copy'))+\" \")]),_c('a-popconfirm',{attrs:{\"title\":\"Confirm deletion?\"},on:{\"confirm\":() => _vm.deleteRepository(record)}},[_c('a-button',{attrs:{\"type\":\"link\",\"danger\":\"\"}},[_vm._v(_vm._s(_vm.$t('repositoryConfig.delete')))])],1)]],2)]}}],null,true)})],1)]),_c('repository-download-results')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"download-results-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('repositoryDownload.title')},scopedSlots:_vm._u([{key:\"extra\",fn:function(){return [_c('a-space',[(_vm.downloadResults && _vm.downloadResults.status === 'running')?_c('a-tag',{attrs:{\"color\":\"blue\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.downloading'))+\" \")]):(_vm.downloadResults && _vm.downloadResults.status === 'success')?_c('a-tag',{attrs:{\"color\":\"green\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.completed'))+\" \")]):(_vm.downloadResults && _vm.downloadResults.status === 'partial_success')?_c('a-tag',{attrs:{\"color\":\"orange\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.partialCompleted'))+\" \")]):(_vm.downloadResults && _vm.downloadResults.status === 'failed')?_c('a-tag',{attrs:{\"color\":\"red\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.failed'))+\" \")]):_vm._e(),_c('a-button',{staticClass:\"clear-button\",attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":_vm.clearResults}},[_c('a-icon',{attrs:{\"type\":\"close\"}}),_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.clear'))+\" \")],1)],1)]},proxy:true}])},[_c('div',{staticClass:\"stats-summary\"},[_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-statistic',{attrs:{\"title\":_vm.$t('repositoryDownload.total'),\"value\":_vm.totalCount,\"value-style\":{ color: '#1890ff' }}})],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-statistic',{attrs:{\"title\":_vm.$t('repositoryDownload.success'),\"value\":_vm.successCount,\"value-style\":{ color: '#52c41a' }}})],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-statistic',{attrs:{\"title\":_vm.$t('repositoryDownload.failed'),\"value\":_vm.failedCount,\"value-style\":{ color: '#f5222d' }}})],1)],1)],1),_c('div',{staticClass:\"result-table\"},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.tableData,\"pagination\":false,\"size\":\"small\",\"row-key\":record => record.key},scopedSlots:_vm._u([{key:\"status\",fn:function(text, record){return [(record.status === 'success')?_c('a-tag',{attrs:{\"color\":\"green\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.success'))+\" \")]):(record.status === 'failed')?_c('a-tag',{attrs:{\"color\":\"red\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.failed'))+\" \")]):(record.status === 'downloading')?_c('a-tag',{attrs:{\"color\":\"blue\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.downloading'))+\" \")]):_c('a-tag',{attrs:{\"color\":\"orange\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.pending'))+\" \")])]}},{key:\"progress\",fn:function(text, record){return [(record.status === 'downloading')?_c('div',[_c('a-progress',{attrs:{\"percent\":record.progress || 0,\"size\":'small',\"status\":\"active\"}})],1):(record.status === 'success')?_c('span',{staticStyle:{\"color\":\"#52c41a\",\"font-weight\":\"500\"}},[_vm._v(\" 100% \")]):(record.status === 'failed')?_c('span',{staticStyle:{\"color\":\"#f5222d\",\"font-weight\":\"500\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.failed'))+\" \")]):_c('span',{staticStyle:{\"color\":\"#fa8c16\",\"font-weight\":\"500\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.pending'))+\" \")])]}},{key:\"result\",fn:function(text, record){return [(record.error)?_c('span',{staticStyle:{\"color\":\"#f5222d\"}},[_c('a-tooltip',{attrs:{\"title\":record.error}},[_c('a-icon',{attrs:{\"type\":\"exclamation-circle\"}}),_vm._v(\" \"+_vm._s(record.error.length > 50 ? record.error.substring(0, 50) + '...' : record.error)+\" \")],1)],1):(record.download_path)?_c('span',{staticStyle:{\"color\":\"#52c41a\"}},[_c('a-tooltip',{attrs:{\"title\":record.download_path}},[_c('a-icon',{attrs:{\"type\":\"check-circle\"}}),_vm._v(\" \"+_vm._s(record.download_path.length > 50 ? '...' + record.download_path.substring(record.download_path.length - 50) : record.download_path)+\" \")],1)],1):(record.status === 'downloading')?_c('span',{staticStyle:{\"color\":\"#1890ff\"}},[_c('a-icon',{attrs:{\"type\":\"loading\",\"spin\":\"\"}}),_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.downloading'))+\"... \")],1):_c('span',{staticStyle:{\"color\":\"#fa8c16\",\"font-weight\":\"500\"}},[_vm._v(\" \"+_vm._s(_vm.$t('repositoryDownload.pending'))+\" \")])]}}])})],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card class=\"download-results-card\" size=\"small\" :title=\"$t('repositoryDownload.title')\">\r\n    <template #extra>\r\n      <a-space>\r\n        <a-tag v-if=\"downloadResults && downloadResults.status === 'running'\" color=\"blue\">\r\n          {{ $t('repositoryDownload.downloading') }}\r\n        </a-tag>\r\n        <a-tag v-else-if=\"downloadResults && downloadResults.status === 'success'\" color=\"green\">\r\n          {{ $t('repositoryDownload.completed') }}\r\n        </a-tag>\r\n        <a-tag v-else-if=\"downloadResults && downloadResults.status === 'partial_success'\" color=\"orange\">\r\n          {{ $t('repositoryDownload.partialCompleted') }}\r\n        </a-tag>\r\n        <a-tag v-else-if=\"downloadResults && downloadResults.status === 'failed'\" color=\"red\">\r\n          {{ $t('repositoryDownload.failed') }}\r\n        </a-tag>\r\n        \r\n        <a-button\r\n          type=\"link\"\r\n          size=\"small\"\r\n          @click=\"clearResults\"\r\n          class=\"clear-button\"\r\n        >\r\n          <a-icon type=\"close\" />\r\n          {{ $t('repositoryDownload.clear') }}\r\n        </a-button>\r\n      </a-space>\r\n    </template>\r\n\r\n    <div class=\"stats-summary\">\r\n      <a-row :gutter=\"16\">\r\n        <a-col :span=\"8\">\r\n          <a-statistic\r\n            :title=\"$t('repositoryDownload.total')\"\r\n            :value=\"totalCount\"\r\n            :value-style=\"{ color: '#1890ff' }\"\r\n          />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic\r\n            :title=\"$t('repositoryDownload.success')\"\r\n            :value=\"successCount\"\r\n            :value-style=\"{ color: '#52c41a' }\"\r\n          />\r\n        </a-col>\r\n        <a-col :span=\"8\">\r\n          <a-statistic\r\n            :title=\"$t('repositoryDownload.failed')\"\r\n            :value=\"failedCount\"\r\n            :value-style=\"{ color: '#f5222d' }\"\r\n          />\r\n        </a-col>\r\n      </a-row>\r\n    </div>\r\n\r\n    <div class=\"result-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"tableData\"\r\n        :pagination=\"false\"\r\n        size=\"small\"\r\n        :row-key=\"record => record.key\"\r\n      >\r\n      <template slot=\"status\" slot-scope=\"text, record\">\r\n        <a-tag v-if=\"record.status === 'success'\" color=\"green\">\r\n          {{ $t('repositoryDownload.success') }}\r\n        </a-tag>\r\n        <a-tag v-else-if=\"record.status === 'failed'\" color=\"red\">\r\n          {{ $t('repositoryDownload.failed') }}\r\n        </a-tag>\r\n        <a-tag v-else-if=\"record.status === 'downloading'\" color=\"blue\">\r\n          {{ $t('repositoryDownload.downloading') }}\r\n        </a-tag>\r\n        <a-tag v-else color=\"orange\">\r\n          {{ $t('repositoryDownload.pending') }}\r\n        </a-tag>\r\n      </template>\r\n\r\n      <template slot=\"progress\" slot-scope=\"text, record\">\r\n        <div v-if=\"record.status === 'downloading'\">\r\n          <a-progress\r\n            :percent=\"record.progress || 0\"\r\n            :size=\"'small'\"\r\n            status=\"active\"\r\n          />\r\n        </div>\r\n        <span v-else-if=\"record.status === 'success'\" style=\"color: #52c41a; font-weight: 500;\">\r\n          100%\r\n        </span>\r\n        <span v-else-if=\"record.status === 'failed'\" style=\"color: #f5222d; font-weight: 500;\">\r\n          {{ $t('repositoryDownload.failed') }}\r\n        </span>\r\n        <span v-else style=\"color: #fa8c16; font-weight: 500;\">\r\n          {{ $t('repositoryDownload.pending') }}\r\n        </span>\r\n      </template>\r\n\r\n      <template slot=\"result\" slot-scope=\"text, record\">\r\n        <span v-if=\"record.error\" style=\"color: #f5222d;\">\r\n          <a-tooltip :title=\"record.error\">\r\n            <a-icon type=\"exclamation-circle\" /> \r\n            {{ record.error.length > 50 ? record.error.substring(0, 50) + '...' : record.error }}\r\n          </a-tooltip>\r\n        </span>\r\n        <span v-else-if=\"record.download_path\" style=\"color: #52c41a;\">\r\n          <a-tooltip :title=\"record.download_path\">\r\n            <a-icon type=\"check-circle\" />\r\n            {{ record.download_path.length > 50 ? '...' + record.download_path.substring(record.download_path.length - 50) : record.download_path }}\r\n          </a-tooltip>\r\n        </span>\r\n        <span v-else-if=\"record.status === 'downloading'\" style=\"color: #1890ff;\">\r\n          <a-icon type=\"loading\" spin />\r\n          {{ $t('repositoryDownload.downloading') }}...\r\n        </span>\r\n        <span v-else style=\"color: #fa8c16; font-weight: 500;\">\r\n          {{ $t('repositoryDownload.pending') }}\r\n        </span>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n  name: 'RepositoryDownloadResults',\r\n  computed: {\r\n    ...mapState(['repositoryDownloadResults']),\r\n\r\n    downloadResults() {\r\n      return this.repositoryDownloadResults;\r\n    },\r\n\r\n    totalCount() {\r\n      if (!this.downloadResults || !this.downloadResults.repositories) return 0;\r\n      return Object.keys(this.downloadResults.repositories).length;\r\n    },\r\n\r\n    successCount() {\r\n      if (!this.downloadResults || !this.downloadResults.repositories) return 0;\r\n      return Object.values(this.downloadResults.repositories).filter(repo => repo.status === 'success').length;\r\n    },\r\n\r\n    failedCount() {\r\n      if (!this.downloadResults || !this.downloadResults.repositories) return 0;\r\n      return Object.values(this.downloadResults.repositories).filter(repo => repo.status === 'failed').length;\r\n    },\r\n\r\n    tableData() {\r\n      if (!this.downloadResults || !this.downloadResults.repositories) return [];\r\n\r\n      const data = [];\r\n\r\n      // 使用 repositories 对象中的实时状态数据\r\n      Object.values(this.downloadResults.repositories).forEach((repo, index) => {\r\n        data.push({\r\n          key: `repo_${index}`,\r\n          microservice_name: repo.microservice_name,\r\n          repository_url: repo.repository_url,\r\n          branch_name: repo.branch_name,\r\n          status: repo.status,\r\n          progress: repo.progress || 0,\r\n          download_path: repo.download_path,\r\n          error: repo.error_detail\r\n        });\r\n      });\r\n\r\n      return data;\r\n    },\r\n\r\n    columns() {\r\n      return [\r\n        {\r\n          title: '#',\r\n          width: 100,\r\n          customRender: (text, record, index) => index + 1\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.microservice'),\r\n          dataIndex: 'microservice_name',\r\n          key: 'microservice_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.repositoryUrl'),\r\n          dataIndex: 'repository_url',\r\n          key: 'repository_url',\r\n          ellipsis: true,\r\n          width: 600\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.branchName'),\r\n          dataIndex: 'branch_name',\r\n          key: 'branch_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          scopedSlots: { customRender: 'status' },\r\n          width: 150\r\n        },\r\n        {\r\n          title: this.$t('repositoryDownload.progress'),\r\n          dataIndex: 'progress',\r\n          key: 'progress',\r\n          scopedSlots: { customRender: 'progress' },\r\n          width: 150\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.result'),\r\n          dataIndex: 'result',\r\n          key: 'result',\r\n          scopedSlots: { customRender: 'result' }\r\n        }\r\n      ];\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    clearResults() {\r\n      this.$store.dispatch('clearRepositoryDownloadResults');\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.download-results-card {\r\n  margin: 24px;\r\n}\r\n\r\n.clear-button {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n\r\n.stats-summary {\r\n  margin-bottom: 16px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryDownloadResults.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryDownloadResults.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RepositoryDownloadResults.vue?vue&type=template&id=550fb864&scoped=true\"\nimport script from \"./RepositoryDownloadResults.vue?vue&type=script&lang=js\"\nexport * from \"./RepositoryDownloadResults.vue?vue&type=script&lang=js\"\nimport style0 from \"./RepositoryDownloadResults.vue?vue&type=style&index=0&id=550fb864&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"550fb864\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div>\r\n    <a-card :bordered=\"false\" class=\"header-solid repository-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('repositoryConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('repositoryConfig.addRepository') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"download\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"downloadSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"deleteSelectedRepositories\"\r\n              >\r\n                {{ $t('repositoryConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                type=\"default\"\r\n                icon=\"download\"\r\n                class=\"nav-style-button\"\r\n                @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('repositoryConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                :show-upload-list=\"false\"\r\n                :custom-request=\"handleUpload\"\r\n                accept=\".csv\"\r\n              >\r\n                <a-button\r\n                    type=\"default\"\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('repositoryConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"repositories\"\r\n        :rowKey=\"(record) => record.key\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: repositories.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"col === 'repository_url' && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"col === 'repository_url' && text ? 'cursor: pointer' : ''\"\r\n              @click=\"col === 'repository_url' && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('repositoryConfig.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('repositoryConfig.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyRepository(record)\">\r\n              {{ $t('repositoryConfig.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteRepository(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('repositoryConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n\r\n  <!-- 下载结果显示 -->\r\n  <repository-download-results />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport RepositoryDownloadResults from './RepositoryDownloadResults.vue';\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n    RepositoryDownloadResults,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n  },\r\n  data() {\r\n    return {\r\n      repositories: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'microservice_name',\r\n        'repository_url',\r\n        'branch_name',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      downloadTaskId: null,\r\n      downloadPolling: false,\r\n      downloadPollInterval: null,\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.microservice'),\r\n          dataIndex: 'microservice_name',\r\n          scopedSlots: { customRender: 'microservice_name' },\r\n          width: 200,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.repositoryUrl'),\r\n          dataIndex: 'repository_url',\r\n          scopedSlots: { customRender: 'repository_url' },\r\n          width: 300,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.columns.branchName'),\r\n          dataIndex: 'branch_name',\r\n          scopedSlots: { customRender: 'branch_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('repositoryConfig.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchRepositoryConfig();\r\n  },\r\n  methods: {\r\n    copyRepository(record) {\r\n      const newKey = `new-${Date.now()}`;\r\n      const newRecord = {\r\n        ...record,\r\n        key: newKey,\r\n        editable: true,\r\n        isNew: true,\r\n        microservice_name: `${record.microservice_name}_copy`,\r\n      };\r\n      \r\n      this.repositories = [newRecord, ...this.repositories];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    getColumnTitle(col) {\r\n      const titleMap = {\r\n        'microservice_name': this.$t('repositoryConfig.columns.microservice'),\r\n        'repository_url': this.$t('repositoryConfig.columns.repositoryUrl'),\r\n        'branch_name': this.$t('repositoryConfig.columns.branchName'),\r\n      };\r\n      return titleMap[col] || col;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    edit(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target.editable = true;\r\n        this.repositories = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      const newData = [...this.repositories];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        delete target.editable;\r\n        this.repositories = newData;\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n\r\n        try {\r\n          this.saving = true;\r\n          const repositoryData = {\r\n            microservice_name: target.microservice_name,\r\n            repository_url: target.repository_url,\r\n            branch_name: target.branch_name,\r\n          };\r\n\r\n          const response = await axios.post('/api/repositories', {\r\n            repositories: [repositoryData]\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          if (response.data.success) {\r\n            const { successful, failed } = response.data.data;\r\n\r\n            if (failed.length > 0) {\r\n              // 显示验证错误\r\n              const errorMsg = failed[0].error;\r\n              this.$message.error(`${this.$t('repositoryConfig.validation.parseError')}: ${errorMsg}`);\r\n              return;\r\n            }\r\n\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success('Repository saved successfully');\r\n          } else {\r\n            this.$message.error(response.data.error || 'Failed to save repository');\r\n          }\r\n        } catch (error) {\r\n          this.$message.error(error.response?.data?.error || 'Failed to save repository');\r\n          await this.fetchRepositoryConfig();\r\n        } finally {\r\n          this.saving = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.repositories.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.repositories[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.repositories = this.repositories.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.repositories];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.repositories = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.repositories = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          microservice_name: '',\r\n          repository_url: '',\r\n          branch_name: 'main',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.repositories,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.repositories.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    async fetchRepositoryConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/repositories`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.repositories = response.data.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `repo_${item.microservice_name}`,\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.repositories.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load repositories');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteRepository(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/repositories/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.repositories = this.repositories.filter((r) => r.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repository');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async deleteSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to delete');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        if (selectedIds.length > 0) {\r\n          await axios.post('/api/repositories/batch-delete', {\r\n            ids: selectedIds\r\n          }, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n        }\r\n\r\n        this.repositories = this.repositories.filter(r => !this.selectedRowKeys.includes(r.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Selected repositories deleted successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete repositories');\r\n        await this.fetchRepositoryConfig();\r\n      }\r\n    },\r\n\r\n    async exportSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to export');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const selectedIds = this.selectedRowKeys\r\n          .map(key => this.repositories.find(r => r.key === key))\r\n          .filter(repo => repo && repo.id)\r\n          .map(repo => repo.id);\r\n\r\n        const response = await axios.post('/api/repositories/export', {\r\n          ids: selectedIds\r\n        }, {\r\n          params: { dbFile: this.currentDbFile },\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repositories_export.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Repositories exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export repositories');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/repositories/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'repository_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        link.remove();\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        const response = await axios.post('/api/repositories/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const { successful, failed } = response.data.data;\r\n\r\n          if (failed.length > 0) {\r\n            // 显示验证失败的代码仓\r\n            const failedMessages = failed.map(repo =>\r\n              `${repo.microservice_name || 'Unknown'}: ${repo.error}`\r\n            ).join('\\n');\r\n\r\n            this.$confirm({\r\n              title: this.$t('repositoryConfig.validation.parseError'),\r\n              content: failedMessages,\r\n              showCancelButton: false,\r\n              confirmButtonText: 'OK',\r\n              type: 'warning'\r\n            });\r\n          }\r\n\r\n          if (successful.length > 0) {\r\n            await this.fetchRepositoryConfig();\r\n            this.$message.success(`Successfully imported ${successful.length} repositories`);\r\n          }\r\n\r\n          if (failed.length > 0 && successful.length === 0) {\r\n            this.$message.error('No valid repositories found in the file');\r\n          }\r\n        } else {\r\n          this.$message.error(response.data.error || 'Failed to import repositories');\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import repositories');\r\n      }\r\n    },\r\n\r\n    async downloadSelectedRepositories() {\r\n      if (this.selectedRowKeys.length === 0) {\r\n        this.$message.warning('Please select repositories to download');\r\n        return;\r\n      }\r\n\r\n      // 获取选中的代码仓\r\n      const selectedRepositories = this.selectedRowKeys\r\n        .map(key => this.repositories.find(r => r.key === key))\r\n        .filter(repo => repo && repo.id);\r\n\r\n      if (selectedRepositories.length === 0) {\r\n        this.$message.warning('No valid repositories selected');\r\n        return;\r\n      }\r\n\r\n      // 弹出输入框让用户输入下载路径\r\n      const downloadPath = prompt(this.$t('repositoryConfig.download.selectPath'), 'D:\\\\downloads');\r\n\r\n      if (!downloadPath || !downloadPath.trim()) {\r\n        return;\r\n      }\r\n\r\n      // 显示开始下载的消息\r\n      this.$message.loading(this.$t('repositoryConfig.download.starting'), 0);\r\n\r\n      try {\r\n        const requestData = {\r\n          repositories: selectedRepositories.map(repo => ({\r\n            id: repo.id,\r\n            microservice_name: repo.microservice_name,\r\n            repository_url: repo.repository_url,\r\n            branch_name: repo.branch_name\r\n          })),\r\n          download_path: downloadPath.trim()\r\n        };\r\n\r\n        const response = await axios.post('/api/repositories/download', requestData, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        this.$message.destroy();\r\n\r\n        if (response.data.success) {\r\n          const taskId = response.data.data.task_id;\r\n          this.downloadTaskId = taskId;\r\n\r\n          // 初始化下载结果状态 - 显示正在进行的状态\r\n          const initialResults = {\r\n            task_id: taskId,\r\n            status: 'running',\r\n            successful: [],\r\n            failed: [],\r\n            repositories: selectedRepositories.reduce((acc, repo) => {\r\n              const key = `${repo.microservice_name}_${repo.repository_url}`;\r\n              acc[key] = {\r\n                microservice_name: repo.microservice_name,\r\n                repository_url: repo.repository_url,\r\n                branch_name: repo.branch_name,\r\n                status: 'pending',\r\n                progress: 0,\r\n                error_detail: null,\r\n                download_path: null\r\n              };\r\n              return acc;\r\n            }, {}),\r\n            timestamp: new Date().toLocaleString()\r\n          };\r\n\r\n          // 存储初始状态到store\r\n          this.$store.dispatch('updateRepositoryDownloadResults', initialResults);\r\n\r\n          this.$message.success('Repository download task started successfully');\r\n\r\n          // 开始轮询任务状态\r\n          this.startDownloadPolling(taskId);\r\n        } else {\r\n          this.$message.error(response.data.error || this.$t('repositoryConfig.download.failed'));\r\n        }\r\n      } catch (error) {\r\n        this.$message.destroy();\r\n        this.$message.error(error.response?.data?.error || error.message || this.$t('repositoryConfig.download.failed'));\r\n      }\r\n    },\r\n\r\n    startDownloadPolling(taskId) {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n      }\r\n\r\n      this.downloadPolling = true;\r\n\r\n      // 立即执行一次查询\r\n      this.pollDownloadStatus(taskId);\r\n\r\n      // 每5秒轮询一次\r\n      this.downloadPollInterval = setInterval(() => {\r\n        this.pollDownloadStatus(taskId);\r\n      }, 5000);\r\n\r\n      console.log(`开始轮询代码仓下载任务 ${taskId}，轮询间隔: 5秒`);\r\n    },\r\n\r\n    async pollDownloadStatus(taskId) {\r\n      try {\r\n        const response = await axios.get(`/api/repositories/download/status/${taskId}`, {\r\n          params: { dbFile: this.currentDbFile }\r\n        });\r\n\r\n        if (response.data.success) {\r\n          const taskData = response.data.data;\r\n\r\n          // 更新store中的下载结果\r\n          this.$store.dispatch('updateRepositoryDownloadResults', {\r\n            ...taskData,\r\n            timestamp: new Date().toLocaleString()\r\n          });\r\n\r\n          // 检查任务是否完成\r\n          if (taskData.status === 'success' || taskData.status === 'failed' || taskData.status === 'partial_success') {\r\n            this.stopDownloadPolling();\r\n\r\n            // 显示完成消息\r\n            if (taskData.status === 'success') {\r\n              this.$message.success(this.$t('repositoryConfig.download.success'));\r\n            } else if (taskData.status === 'failed') {\r\n              this.$message.error(this.$t('repositoryConfig.download.failed'));\r\n            } else {\r\n              this.$message.warning(this.$t('repositoryConfig.download.partialSuccess'));\r\n            }\r\n\r\n            console.log('代码仓下载任务完成，停止轮询');\r\n          }\r\n        } else {\r\n          console.error('获取下载状态失败:', response.data.error);\r\n        }\r\n      } catch (error) {\r\n        console.error('轮询下载状态出错:', error);\r\n        \r\n        // 如果是404错误，可能任务不存在，停止轮询\r\n        if (error.response?.status === 404) {\r\n          this.stopDownloadPolling();\r\n          this.$message.error('Download task not found');\r\n        }\r\n      }\r\n    },\r\n\r\n    stopDownloadPolling() {\r\n      if (this.downloadPollInterval) {\r\n        clearInterval(this.downloadPollInterval);\r\n        this.downloadPollInterval = null;\r\n      }\r\n      this.downloadPolling = false;\r\n      this.downloadTaskId = null;\r\n    },\r\n\r\n    checkActiveDownloadTask() {\r\n      // 检查是否有活跃的下载任务\r\n      const taskInfo = localStorage.getItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n      if (taskInfo) {\r\n        try {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n          if (projectFile === this.currentDbFile) {\r\n            this.downloadTaskId = taskId;\r\n            this.startDownloadPolling(taskId);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error parsing repository download task info:', e);\r\n          localStorage.removeItem(`repositoryDownloadTask_${this.currentDbFile}`);\r\n        }\r\n      }\r\n    },\r\n\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRepositoryConfig();\r\n    // 检查是否有活跃的下载任务\r\n    this.checkActiveDownloadTask();\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 组件销毁前停止轮询\r\n    this.stopDownloadPolling();\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.repository-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryConfig.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryConfig.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RepositoryConfig.vue?vue&type=template&id=db8d5038&scoped=true\"\nimport script from \"./RepositoryConfig.vue?vue&type=script&lang=js\"\nexport * from \"./RepositoryConfig.vue?vue&type=script&lang=js\"\nimport style0 from \"./RepositoryConfig.vue?vue&type=style&index=0&id=db8d5038&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"db8d5038\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div>\r\n    <RepositoryConfig />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RepositoryConfig from \"@/components/Cards/RepositoryConfig.vue\";\r\n\r\nexport default {\r\n  name: 'Repository',\r\n  components: { \r\n    RepositoryConfig \r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面样式 */\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Repository.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Repository.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Repository.vue?vue&type=template&id=02d9aec4&scoped=true\"\nimport script from \"./Repository.vue?vue&type=script&lang=js\"\nexport * from \"./Repository.vue?vue&type=script&lang=js\"\nimport style0 from \"./Repository.vue?vue&type=style&index=0&id=02d9aec4&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"02d9aec4\",\n  null\n  \n)\n\nexport default component.exports", "export default {\r\n  methods: {\r\n    copyText(text) {\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = text;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('Copied to clipboard');\r\n    }\r\n  }\r\n}; ", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Repository.vue?vue&type=style&index=0&id=02d9aec4&prod&scoped=true&lang=css\"", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RepositoryConfig.vue?vue&type=style&index=0&id=db8d5038&prod&scoped=true&lang=css\"", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n"], "sourceRoot": ""}