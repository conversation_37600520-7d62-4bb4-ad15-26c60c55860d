from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from .config_datamodel import Base
import json


class DockerHostConfig(Base):
    __tablename__ = 'docker_host_config'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    docker_version = Column(String)
    daemon_config = Column(Text)
    user_in_docker_group = Column(Boolean)
    is_root_user = Column(Boolean)

    host = relationship("HostConfig", back_populates="docker_host_config")

    def to_dict(self):
        """Convert model to dictionary with proper JSON deserialization"""
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'docker_version': self.docker_version,
            'user_in_docker_group': self.user_in_docker_group,
            'is_root_user': self.is_root_user
        }

        # Safely parse daemon_config JSO<PERSON>
        try:
            data['daemon_config'] = json.loads(self.daemon_config) if self.daemon_config else {}
        except (json.JSONDecodeError, TypeError):
            data['daemon_config'] = {}

        return data


class DockerContainer(Base):
    __tablename__ = 'docker_container'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    container_id = Column(String, unique=True)
    mounts = Column(Text)
    processes = Column(Text)
    exposures = Column(Text)
    env = Column(Text)
    inspect_data = Column(Text)

    host = relationship("HostConfig", back_populates="docker_containers")

    def to_dict(self):
        """Convert model to dictionary with proper JSON deserialization"""
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'container_id': self.container_id,
        }

        # Handle JSON fields
        json_fields = {
            'mounts': self.mounts,
            'processes': self.processes,
            'exposures': self.exposures,
            'env': self.env
        }

        for field, value in json_fields.items():
            try:
                data[field] = json.loads(value) if value else []
            except (json.JSONDecodeError, TypeError):
                data[field] = []

        # 处理inspect_data
        try:
            data['inspect_data'] = json.loads(self.inspect_data) if self.inspect_data else {}
        except (json.JSONDecodeError, TypeError):
            data['inspect_data'] = {}

        return data


class DockerNetwork(Base):
    __tablename__ = 'docker_network'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    network_id = Column(String)
    name = Column(String)
    driver = Column(String)
    scope = Column(String)
    created_at = Column(String)
    ipv6 = Column(Boolean)
    internal = Column(Boolean)
    labels = Column(Text)

    host = relationship("HostConfig", back_populates="docker_networks")

    def to_dict(self):
        """Convert model to dictionary with proper JSON deserialization"""
        data = {
            'id': self.id,
            'node_id': self.node_id,
            'network_id': self.network_id,
            'name': self.name,
            'driver': self.driver,
            'scope': self.scope,
            'created_at': self.created_at,
            'ipv6': self.ipv6,
            'internal': self.internal
        }

        try:
            data['labels'] = json.loads(self.labels) if self.labels else {}
        except (json.JSONDecodeError, TypeError):
            data['labels'] = {}

        return data
