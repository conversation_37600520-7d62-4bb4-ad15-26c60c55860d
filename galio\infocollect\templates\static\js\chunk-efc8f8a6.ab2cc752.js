(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-efc8f8a6"],{"13c1":function(e,t,s){"use strict";s("be15")},be15:function(e,t,s){},c570:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("TestCaseInfo")],1)],1)],1)},r=[],l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"layout-content"},[t("a-card",{staticClass:"criclebox",attrs:{bordered:!1},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",height:"20",width:"20"}},[t("path",{attrs:{fill:"currentColor",d:"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.testcase")))])]),t("div",[t("RefreshButton",{on:{refresh:function(t){return e.fetchTestcases(e.currentPage)}}})],1)])]},proxy:!0}])},[t("div",{staticClass:"search-form"},[t("a-form",{attrs:{layout:"inline"},on:{submit:function(t){return t.preventDefault(),e.handleSearch.apply(null,arguments)}}},[t("a-form-item",{attrs:{label:"Name"}},[t("a-input",{attrs:{placeholder:"Search by name",allowClear:""},model:{value:e.searchForm.name,callback:function(t){e.$set(e.searchForm,"name",t)},expression:"searchForm.name"}})],1),t("a-form-item",{attrs:{label:"Level"}},[t("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:"Select level",allowClear:""},model:{value:e.searchForm.level,callback:function(t){e.$set(e.searchForm,"level",t)},expression:"searchForm.level"}},[t("a-select-option",{attrs:{value:"level 0"}},[e._v("Level 0")]),t("a-select-option",{attrs:{value:"level 1"}},[e._v("Level 1")]),t("a-select-option",{attrs:{value:"level 2"}},[e._v("Level 2")]),t("a-select-option",{attrs:{value:"level 3"}},[e._v("Level 3")]),t("a-select-option",{attrs:{value:"level 4"}},[e._v("Level 4")])],1)],1),t("a-form-item",{attrs:{label:"Prepare Condition"}},[t("a-input",{attrs:{placeholder:"Search in prepare condition",allowClear:""},model:{value:e.searchForm.prepare_condition,callback:function(t){e.$set(e.searchForm,"prepare_condition",t)},expression:"searchForm.prepare_condition"}})],1),t("a-form-item",{attrs:{label:"Test Steps"}},[t("a-input",{attrs:{placeholder:"Search in test steps",allowClear:""},model:{value:e.searchForm.test_steps,callback:function(t){e.$set(e.searchForm,"test_steps",t)},expression:"searchForm.test_steps"}})],1),t("a-form-item",{attrs:{label:"Expected Result"}},[t("a-input",{attrs:{placeholder:"Search in expected result",allowClear:""},model:{value:e.searchForm.expected_result,callback:function(t){e.$set(e.searchForm,"expected_result",t)},expression:"searchForm.expected_result"}})],1),t("a-form-item",[t("a-button",{class:"bg-"+e.sidebarColor,staticStyle:{color:"white"},attrs:{"html-type":"submit",loading:e.loading}},[t("a-icon",{attrs:{type:"search"}}),e._v(" Search ")],1),t("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.resetSearch}},[t("a-icon",{attrs:{type:"reload"}}),e._v(" Reset ")],1)],1)],1),e.testcases.length>0?t("div",{staticClass:"search-result-count"},[t("a-tag",{attrs:{color:"blue"}},[e._v("Found: "+e._s(e.total)+" test cases")])],1):e._e()],1),t("a-table",{attrs:{columns:e.columns,"data-source":e.testcases,loading:e.loading,pagination:{total:e.total,pageSize:100,current:e.currentPage,showSizeChanger:!1,showQuickJumper:!0,onChange:e.handlePageChange},scroll:{x:1500}},scopedSlots:e._u([{key:"Testcase_LastResult",fn:function({text:s}){return[t("a-tag",{attrs:{color:e.getResultColor(s)}},[e._v(" "+e._s(s||"N/A")+" ")])]}},{key:"Testcase_Level",fn:function({text:s}){return[t("a-tag",{attrs:{color:e.getLevelColor(s)}},[e._v(" "+e._s(s||"N/A")+" ")])]}},{key:"lastModified",fn:function({text:t}){return[e._v(" "+e._s(e.formatDate(t))+" ")]}},{key:"action",fn:function({record:s}){return[t("a-space",[t("a-button",{attrs:{type:"link"},on:{click:function(t){return e.viewDetails(s)}}},[e._v(" View Details ")])],1)]}}])}),t("a-modal",{attrs:{title:"Test Case Details",width:"800px",footer:null},model:{value:e.detailsVisible,callback:function(t){e.detailsVisible=t},expression:"detailsVisible"}},[e.selectedTestcase?t("a-descriptions",{attrs:{bordered:""}},[t("a-descriptions-item",{attrs:{label:"Test Case Number",span:"3"}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.selectedTestcase.Testcase_Number)+" ")])]),t("a-descriptions-item",{attrs:{label:"Name",span:"3"}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.selectedTestcase.Testcase_Name)+" ")])]),t("a-descriptions-item",{attrs:{label:"Level",span:"3"}},[t("a-tag",{attrs:{color:e.getLevelColor(e.selectedTestcase.Testcase_Level)}},[e._v(" "+e._s(e.selectedTestcase.Testcase_Level)+" ")])],1),t("a-descriptions-item",{attrs:{label:"Prepare Condition",span:"3"}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.selectedTestcase.Testcase_PrepareCondition)+" ")])]),t("a-descriptions-item",{attrs:{label:"Test Steps",span:"3"}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.selectedTestcase.Testcase_TestSteps)+" ")])]),t("a-descriptions-item",{attrs:{label:"Expected Result",span:"3"}},[t("div",{staticClass:"testcase-content"},[e._v(" "+e._s(e.selectedTestcase.Testcase_ExpectedResult)+" ")])])],1):e._e()],1)],1)],1)},c=[],o=s("fec3"),i=s("c1df"),n=s.n(i),d=s("2f62"),p=s("f188"),u={components:{RefreshButton:p["a"]},name:"TestCases",data(){const e=this.$createElement;return{loading:!1,testcases:[],total:0,currentPage:1,detailsVisible:!1,selectedTestcase:null,searchForm:{name:"",level:void 0,prepare_condition:"",test_steps:"",expected_result:""},columns:[{title:"#",dataIndex:"index",key:"index",width:100,align:"center",customRender:(e,t,s)=>100*(this.currentPage-1)+s+1},{title:"Test Case ID",dataIndex:"Testcase_Number",key:"Testcase_Number",width:130,ellipsis:!0,customRender:(t,s)=>e("a",{on:{click:()=>this.viewDetails(s)},style:"color: #1890ff; cursor: pointer;"},[t])},{title:"Name",dataIndex:"Testcase_Name",key:"Testcase_Name",width:200},{title:"Level",dataIndex:"Testcase_Level",key:"Testcase_Level",width:100,slots:{customRender:"Testcase_Level"}},{title:"Prepare Condition",dataIndex:"Testcase_PrepareCondition",key:"Testcase_PrepareCondition",width:250,ellipsis:!0},{title:"Test Steps",dataIndex:"Testcase_TestSteps",key:"Testcase_TestSteps",width:400,ellipsis:!0},{title:"Expected Result",dataIndex:"Testcase_ExpectedResult",key:"Testcase_ExpectedResult",width:400,ellipsis:!0}]}},created(){this.fetchTestcases()},computed:{...Object(d["e"])(["selectedNodeIp","currentProject","sidebarColor"])},methods:{async fetchTestcases(e=1){this.loading=!0;try{const t={page:e,page_size:100};this.searchForm.name&&(t.name=this.searchForm.name),this.searchForm.level&&(t.level=this.searchForm.level),this.searchForm.prepare_condition&&(t.prepare_condition=this.searchForm.prepare_condition),this.searchForm.test_steps&&(t.test_steps=this.searchForm.test_steps),this.searchForm.expected_result&&(t.expected_result=this.searchForm.expected_result);const s=await o["a"].get("/api/testcase/",{params:t});this.testcases=s.data.data,this.total=s.data.total}catch(t){console.error("Error fetching testcases:",t),this.$message.error("Failed to load test cases")}finally{this.loading=!1}},handleSearch(){this.currentPage=1,this.fetchTestcases(1)},resetSearch(){this.searchForm={name:"",level:void 0,prepare_condition:"",test_steps:"",expected_result:""},this.currentPage=1,this.fetchTestcases(1)},formatDate(e){return e?n()(e).format("YYYY-MM-DD HH:mm"):"N/A"},getResultColor(e){const t={PASS:"success",FAIL:"error",BLOCKED:"warning","NOT RUN":"default"};return t[e]||"default"},getLevelColor(e){const t={"level 0":"red","level 1":"orange","level 2":"green","level 3":"blue","level 4":"purple"};return t[e]||"default"},async viewDetails(e){try{const t=await o["a"].get("/api/testcase/"+e.Testcase_Number);this.selectedTestcase=t.data,this.detailsVisible=!0}catch(t){console.error("Error fetching testcase details:",t),this.$message.error("Failed to load test case details")}},handlePageChange(e){this.currentPage=e,this.fetchTestcases(e)}}},h=u,m=(s("13c1"),s("2877")),v=Object(m["a"])(h,l,c,!1,null,"6e3c2172",null),_=v.exports,f={components:{TestCaseInfo:_}},b=f,g=Object(m["a"])(b,a,r,!1,null,null,null);t["default"]=g.exports},f188:function(e,t,s){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},r=[],l=s("2f62"),c={computed:{...Object(l["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},o=c,i=s("2877"),n=Object(i["a"])(o,a,r,!1,null,"80cb1374",null);t["a"]=n.exports}}]);
//# sourceMappingURL=chunk-efc8f8a6.ab2cc752.js.map