{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1751875408706}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SmartOrchestrationInfo.vue"], "names": [], "mappings": ";AA+PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SmartOrchestrationInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card title=\"智能测试用例查询\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item label=\"自然语言查询\">\r\n              <a-input-search\r\n                v-model:value=\"queryText\"\r\n                placeholder=\"请输入自然语言描述，例如：查找与网络安全相关的测试用例\"\r\n                enter-button=\"搜索\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item v-if=\"queryText\">\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.top_k\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    placeholder=\"返回数量\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">返回数量</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.score_threshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    placeholder=\"相似度阈值\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">相似度阈值</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block>\r\n                    <template #icon>\r\n                      <SearchOutlined />\r\n                    </template>\r\n                    搜索\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\" v-if=\"searchResults.length > 0\">\r\n      <a-col :span=\"24\">\r\n        <a-card title=\"搜索结果\" size=\"small\">\r\n          <template #extra>\r\n            <a-tag color=\"blue\">找到 {{ searchResults.length }} 个相关测试用例</a-tag>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"searchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  SearchOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined,\r\n    SearchOutlined,\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      queryText: '',\r\n      searching: false,\r\n      searchResults: [],\r\n      searchParams: {\r\n        top_k: 10,\r\n        score_threshold: 0.5\r\n      },\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      searchResultColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: '相似度',\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ],\r\n\r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    this.loadSearchResults();\r\n  },\r\n  \r\n  methods: {\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.queryText.trim()) {\r\n        message.warning('请输入查询内容');\r\n        return;\r\n      }\r\n\r\n      this.searching = true;\r\n      try {\r\n        const response = await axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.queryText,\r\n          top_k: this.searchParams.top_k,\r\n          score_threshold: this.searchParams.score_threshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          this.searchResults = response.data.results;\r\n          // 保存搜索结果到localStorage\r\n          this.saveSearchResults();\r\n          message.success(`找到 ${this.searchResults.length} 个相关测试用例`);\r\n        } else {\r\n          message.error(response.data.message || '搜索失败');\r\n          this.searchResults = [];\r\n          this.clearSearchResults();\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error('搜索过程中出现错误');\r\n        this.searchResults = [];\r\n        this.clearSearchResults();\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "]}]}