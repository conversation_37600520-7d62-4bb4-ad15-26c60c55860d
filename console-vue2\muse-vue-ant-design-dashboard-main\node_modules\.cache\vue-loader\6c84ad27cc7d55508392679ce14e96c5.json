{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\SmartOrchestrationInfo.vue", "mtime": 1751877662954}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SmartOrchestrationInfo.vue"], "names": [], "mappings": ";AAuQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SmartOrchestrationInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card class=\"header-solid h-full\" :bordered=\"false\">\r\n    <template #title>\r\n      <h6 class=\"font-semibold m-0\">智能测试用例分析</h6>\r\n    </template>\r\n    <template #extra>\r\n      <a-button \r\n        type=\"primary\" \r\n        :loading=\"analyzing\" \r\n        @click=\"showAnalysisModal\"\r\n        :disabled=\"!hasNodeData\"\r\n      >\r\n        <template #icon>\r\n          <BranchesOutlined />\r\n        </template>\r\n        开始智能分析\r\n      </a-button>\r\n    </template>\r\n\r\n    <!-- 自然语言查询测试用例 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.title')\" size=\"small\" class=\"query-card\">\r\n          <a-form layout=\"vertical\">\r\n            <a-form-item :label=\"$t('testcase.smartOrchestration.naturalLanguageQuery')\">\r\n              <a-input-search\r\n                v-model:value=\"queryText\"\r\n                :placeholder=\"$t('testcase.smartOrchestration.queryPlaceholder')\"\r\n                :enter-button=\"$t('testcase.smartOrchestration.searchButton')\"\r\n                size=\"large\"\r\n                :loading=\"searching\"\r\n                @search=\"searchTestcases\"\r\n              />\r\n            </a-form-item>\r\n            <a-form-item v-if=\"queryText\">\r\n              <a-row :gutter=\"8\">\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.top_k\"\r\n                    :min=\"1\"\r\n                    :max=\"50\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.topK')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.topK') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-input-number\r\n                    v-model:value=\"searchParams.score_threshold\"\r\n                    :min=\"0\"\r\n                    :max=\"1\"\r\n                    :step=\"0.1\"\r\n                    :placeholder=\"$t('testcase.smartOrchestration.scoreThreshold')\"\r\n                    style=\"width: 100%\"\r\n                  />\r\n                  <div class=\"param-label\">{{ $t('testcase.smartOrchestration.scoreThreshold') }}</div>\r\n                </a-col>\r\n                <a-col :span=\"8\">\r\n                  <a-button type=\"primary\" @click=\"searchTestcases\" :loading=\"searching\" block>\r\n                    <template #icon>\r\n                      <SearchOutlined />\r\n                    </template>\r\n                    {{ $t('testcase.smartOrchestration.searchButton') }}\r\n                  </a-button>\r\n                </a-col>\r\n              </a-row>\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 搜索结果 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\" v-if=\"searchResults.length > 0\">\r\n      <a-col :span=\"24\">\r\n        <a-card :title=\"$t('testcase.smartOrchestration.searchResults')\" size=\"small\">\r\n          <template #extra>\r\n            <a-space>\r\n              <a-tag color=\"blue\">{{ $t('testcase.smartOrchestration.foundResults', { count: searchResults.length }) }}</a-tag>\r\n              <a-button size=\"small\" @click=\"clearSearchHistory\">\r\n                <template #icon>\r\n                  <a-icon type=\"delete\" />\r\n                </template>\r\n                {{ $t('testcase.smartOrchestration.clearResults') }}\r\n              </a-button>\r\n            </a-space>\r\n          </template>\r\n\r\n          <a-table\r\n            :columns=\"searchResultColumns\"\r\n            :data-source=\"searchResults\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :scroll=\"{ x: 800 }\"\r\n          >\r\n            <template slot=\"Testcase_Number\" slot-scope=\"text, record\">\r\n              <a @click=\"viewTestcaseDetail(record)\" style=\"color: #1890ff; cursor: pointer;\">\r\n                {{ record.Testcase_Number }}\r\n              </a>\r\n            </template>\r\n\r\n            <template slot=\"Testcase_Level\" slot-scope=\"text, record\">\r\n              <a-tag :color=\"getLevelColor(record.Testcase_Level)\">\r\n                {{ record.Testcase_Level }}\r\n              </a-tag>\r\n            </template>\r\n\r\n            <template slot=\"similarity\" slot-scope=\"text, record\">\r\n              <a-progress\r\n                :percent=\"Math.round(record.similarity * 100)\"\r\n                size=\"small\"\r\n                :stroke-color=\"getSimilarityColor(record.similarity)\"\r\n              />\r\n              <span style=\"margin-left: 8px; font-size: 12px;\">\r\n                {{ (record.similarity * 100).toFixed(1) }}%\r\n              </span>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 节点状态卡片 -->\r\n    <a-row :gutter=\"16\" class=\"mb-16\">\r\n      <a-col :span=\"24\">\r\n        <a-alert\r\n          v-if=\"!hasNodeData\"\r\n          message=\"未检测到节点数据\"\r\n          description=\"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析\"\r\n          type=\"info\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n        <a-alert\r\n          v-else\r\n          message=\"节点数据已就绪\"\r\n          :description=\"`已检测到 ${availableDataTypes.length} 种类型的数据：${availableDataTypes.join('、')}`\"\r\n          type=\"success\"\r\n          show-icon\r\n          class=\"mb-16\"\r\n        />\r\n      </a-col>\r\n    </a-row>\r\n\r\n    <!-- 分析结果展示 -->\r\n    <div v-if=\"analysisResults.length > 0\">\r\n      <a-divider orientation=\"left\">分析结果</a-divider>\r\n      \r\n      <a-collapse v-model:activeKey=\"activeKeys\" class=\"mb-16\">\r\n        <a-collapse-panel \r\n          v-for=\"(result, index) in analysisResults\" \r\n          :key=\"index\"\r\n          :header=\"`${result.info_type.toUpperCase()} 信息分析 - ${result.status === 'success' ? '成功' : result.status === 'warning' ? '警告' : '失败'}`\"\r\n        >\r\n          <template #extra>\r\n            <a-tag :color=\"getStatusColor(result.status)\">\r\n              {{ getStatusText(result.status) }}\r\n            </a-tag>\r\n          </template>\r\n\r\n          <!-- 查询信息 -->\r\n          <a-descriptions title=\"查询信息\" :column=\"1\" size=\"small\" class=\"mb-16\">\r\n            <a-descriptions-item label=\"信息类型\">{{ result.info_type }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"查询文本\">{{ result.query_text }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"匹配用例数\">{{ result.matched_testcases.length }}</a-descriptions-item>\r\n          </a-descriptions>\r\n\r\n          <!-- 匹配的测试用例 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">匹配的测试用例</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.matched_testcases\"\r\n            :columns=\"testcaseColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            class=\"mb-16\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'Testcase_Name'\">\r\n                <a-tooltip :title=\"record.Testcase_Name\">\r\n                  <span>{{ truncateText(record.Testcase_Name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n              <template v-if=\"column.key === 'Testcase_TestSteps'\">\r\n                <a-tooltip :title=\"record.Testcase_TestSteps\">\r\n                  <span>{{ truncateText(record.Testcase_TestSteps, 50) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n\r\n          <!-- 执行结果 -->\r\n          <a-divider orientation=\"left\" orientation-margin=\"0\">执行结果</a-divider>\r\n          <a-table\r\n            :dataSource=\"result.execution_results\"\r\n            :columns=\"executionColumns\"\r\n            :pagination=\"false\"\r\n            size=\"small\"\r\n            :expandable=\"{ expandedRowRender }\"\r\n          >\r\n            <template #bodyCell=\"{ column, record }\">\r\n              <template v-if=\"column.key === 'status'\">\r\n                <a-tag :color=\"getStatusColor(record.status)\">\r\n                  {{ getStatusText(record.status) }}\r\n                </a-tag>\r\n              </template>\r\n              <template v-if=\"column.key === 'testcase_name'\">\r\n                <a-tooltip :title=\"record.testcase_name\">\r\n                  <span>{{ truncateText(record.testcase_name, 30) }}</span>\r\n                </a-tooltip>\r\n              </template>\r\n            </template>\r\n          </a-table>\r\n        </a-collapse-panel>\r\n      </a-collapse>\r\n    </div>\r\n\r\n    <!-- 分析配置模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"analysisModalVisible\"\r\n      title=\"智能测试用例分析配置\"\r\n      :width=\"800\"\r\n      @ok=\"startAnalysis\"\r\n      :confirmLoading=\"analyzing\"\r\n    >\r\n      <a-form layout=\"vertical\">\r\n        <a-form-item label=\"选择节点\" required>\r\n          <a-select \r\n            v-model:value=\"selectedNodeId\" \r\n            placeholder=\"请选择要分析的节点\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <a-select-option \r\n              v-for=\"node in availableNodes\" \r\n              :key=\"node.id\" \r\n              :value=\"node.id\"\r\n            >\r\n              {{ node.name }} ({{ node.ip }})\r\n            </a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n\r\n        <a-form-item label=\"选择分析类型\" required>\r\n          <a-checkbox-group v-model:value=\"selectedAnalysisTypes\">\r\n            <a-row>\r\n              <a-col :span=\"8\" v-for=\"type in availableDataTypes\" :key=\"type\">\r\n                <a-checkbox :value=\"type\">{{ getTypeName(type) }}</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n          </a-checkbox-group>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 测试用例详情模态框 -->\r\n    <TestCaseDetailModal\r\n      :visible=\"testcaseDetailVisible\"\r\n      :testcase=\"selectedTestcase\"\r\n      @close=\"testcaseDetailVisible = false\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  BranchesOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  SearchOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message } from 'ant-design-vue';\r\nimport { mapState, mapActions, mapGetters } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'IntelligentTestCaseInfo',\r\n  components: {\r\n    BranchesOutlined,\r\n    CheckCircleOutlined,\r\n    ExclamationCircleOutlined,\r\n    CloseCircleOutlined,\r\n    SearchOutlined,\r\n    TestCaseDetailModal\r\n  },\r\n  data() {\r\n    return {\r\n      analyzing: false,\r\n      analysisModalVisible: false,\r\n      selectedNodeId: null,\r\n      selectedAnalysisTypes: [],\r\n      analysisResults: [],\r\n      activeKeys: ['0'],\r\n      availableNodes: [],\r\n      availableDataTypes: [],\r\n\r\n      // 查询相关数据\r\n      searching: false,\r\n      testcaseDetailVisible: false,\r\n      selectedTestcase: null,\r\n      \r\n      searchResultColumns: [\r\n        {\r\n          title: this.$t('testcase.columns.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120,\r\n          scopedSlots: { customRender: 'Testcase_Number' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true,\r\n          width: 300\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          scopedSlots: { customRender: 'Testcase_Level' }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.similarity'),\r\n          dataIndex: 'similarity',\r\n          key: 'similarity',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'similarity' }\r\n        }\r\n      ],\r\n\r\n      testcaseColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '用例级别',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 80\r\n        },\r\n        {\r\n          title: '测试步骤',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          ellipsis: true\r\n        }\r\n      ],\r\n      \r\n      executionColumns: [\r\n        {\r\n          title: '用例编号',\r\n          dataIndex: 'testcase_number',\r\n          key: 'testcase_number',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '用例名称',\r\n          dataIndex: 'testcase_name',\r\n          key: 'testcase_name',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: '执行状态',\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '执行消息',\r\n          dataIndex: 'message',\r\n          key: 'message',\r\n          ellipsis: true\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapGetters('smartOrchestration', ['hasSearchResults', 'isSearchExpired']),\r\n\r\n    // 双向绑定的计算属性\r\n    queryText: {\r\n      get() {\r\n        return this.$store.state.smartOrchestration.queryText;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('smartOrchestration/updateQueryText', value);\r\n      }\r\n    },\r\n\r\n    searchParams: {\r\n      get() {\r\n        return this.$store.state.smartOrchestration.searchParams;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('smartOrchestration/updateSearchParams', value);\r\n      }\r\n    },\r\n\r\n    searchResults() {\r\n      return this.$store.state.smartOrchestration.searchResults;\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    hasNodeData() {\r\n      return this.availableNodes.length > 0 && this.availableDataTypes.length > 0;\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.loadAvailableNodes();\r\n    this.detectAvailableDataTypes();\r\n    // 检查是否有过期的搜索结果需要清理\r\n    if (this.isSearchExpired) {\r\n      this.clearSearchResults();\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    ...mapActions('smartOrchestration', [\r\n      'updateQueryText',\r\n      'updateSearchParams',\r\n      'updateSearchResults',\r\n      'clearSearchResults'\r\n    ]),\r\n    async loadAvailableNodes() {\r\n      try {\r\n        const response = await this.$http.get('/api/node/nodes');\r\n        if (response.data && response.data.length > 0) {\r\n          this.availableNodes = response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('加载节点列表失败:', error);\r\n      }\r\n    },\r\n    \r\n    detectAvailableDataTypes() {\r\n      // 检测localStorage中是否有各种类型的数据\r\n      const dataTypes = ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes'];\r\n      const available = [];\r\n      \r\n      dataTypes.forEach(type => {\r\n        const dataKey = `${type}_data`;\r\n        if (localStorage.getItem(dataKey)) {\r\n          available.push(type);\r\n        }\r\n      });\r\n      \r\n      this.availableDataTypes = available;\r\n    },\r\n    \r\n    showAnalysisModal() {\r\n      if (!this.hasNodeData) {\r\n        message.warning('请先收集节点数据');\r\n        return;\r\n      }\r\n      \r\n      this.selectedAnalysisTypes = [...this.availableDataTypes];\r\n      this.analysisModalVisible = true;\r\n      \r\n      if (this.availableNodes.length === 1) {\r\n        this.selectedNodeId = this.availableNodes[0].id;\r\n      }\r\n    },\r\n    \r\n    async startAnalysis() {\r\n      if (!this.selectedNodeId) {\r\n        message.error('请选择节点');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedAnalysisTypes.length === 0) {\r\n        message.error('请选择分析类型');\r\n        return;\r\n      }\r\n      \r\n      this.analyzing = true;\r\n      this.analysisResults = [];\r\n      \r\n      try {\r\n        // 对每种数据类型进行分析\r\n        for (const infoType of this.selectedAnalysisTypes) {\r\n          const collectedData = this.getCollectedData(infoType);\r\n          \r\n          if (collectedData) {\r\n            const response = await this.$http.post('/api/intelligent/analyze-and-execute', {\r\n              node_id: this.selectedNodeId,\r\n              info_type: infoType,\r\n              collected_data: collectedData\r\n            });\r\n            \r\n            this.analysisResults.push(response.data);\r\n          }\r\n        }\r\n        \r\n        this.analysisModalVisible = false;\r\n        this.activeKeys = this.analysisResults.map((_, index) => index.toString());\r\n        \r\n        message.success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`);\r\n        \r\n      } catch (error) {\r\n        console.error('分析失败:', error);\r\n        message.error('分析过程中出现错误');\r\n      } finally {\r\n        this.analyzing = false;\r\n      }\r\n    },\r\n    \r\n    getCollectedData(infoType) {\r\n      const dataKey = `${infoType}_data`;\r\n      const data = localStorage.getItem(dataKey);\r\n      return data ? JSON.parse(data) : null;\r\n    },\r\n    \r\n    getTypeName(type) {\r\n      const typeNames = {\r\n        'process': '进程信息',\r\n        'package': '软件包信息',\r\n        'hardware': '硬件信息',\r\n        'filesystem': '文件系统信息',\r\n        'port': '端口信息',\r\n        'docker': 'Docker信息',\r\n        'kubernetes': 'Kubernetes信息'\r\n      };\r\n      return typeNames[type] || type;\r\n    },\r\n    \r\n    getStatusColor(status) {\r\n      const colors = {\r\n        'success': 'green',\r\n        'partial': 'orange',\r\n        'warning': 'orange',\r\n        'failed': 'red',\r\n        'error': 'red',\r\n        'info': 'blue'\r\n      };\r\n      return colors[status] || 'default';\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const texts = {\r\n        'success': '成功',\r\n        'partial': '部分成功',\r\n        'warning': '警告',\r\n        'failed': '失败',\r\n        'error': '错误',\r\n        'info': '信息'\r\n      };\r\n      return texts[status] || status;\r\n    },\r\n    \r\n    truncateText(text, maxLength) {\r\n      if (!text) return '';\r\n      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n    },\r\n    \r\n    expandedRowRender(record) {\r\n      if (!record.outputs || record.outputs.length === 0) {\r\n        return '无执行详情';\r\n      }\r\n\r\n      return `\r\n        <div style=\"margin: 16px 0;\">\r\n          <h4>命令执行详情:</h4>\r\n          ${record.outputs.map((output, index) => `\r\n            <div style=\"margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;\">\r\n              <p><strong>命令 ${index + 1}:</strong> <code>${output.command}</code></p>\r\n              <p><strong>退出码:</strong> <span style=\"color: ${output.exit_code === 0 ? 'green' : 'red'}\">${output.exit_code}</span></p>\r\n              ${output.output ? `<p><strong>输出:</strong><br><pre style=\"background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;\">${output.output}</pre></p>` : ''}\r\n              ${output.error ? `<p><strong>错误:</strong><br><pre style=\"background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;\">${output.error}</pre></p>` : ''}\r\n            </div>\r\n          `).join('')}\r\n        </div>\r\n      `;\r\n    },\r\n\r\n    // 搜索测试用例\r\n    async searchTestcases() {\r\n      if (!this.queryText.trim()) {\r\n        message.warning(this.$t('testcase.smartOrchestration.inputRequired'));\r\n        return;\r\n      }\r\n\r\n      this.searching = true;\r\n      try {\r\n        const response = await axios.post('/api/vector_testcase/search_with_details', {\r\n          query: this.queryText,\r\n          top_k: this.searchParams.top_k,\r\n          score_threshold: this.searchParams.score_threshold\r\n        });\r\n\r\n        if (response.data.status === 'success') {\r\n          // 使用 Vuex action 更新搜索结果\r\n          this.updateSearchResults(response.data.results);\r\n          message.success(this.$t('testcase.smartOrchestration.foundResults', { count: response.data.results.length }));\r\n        } else {\r\n          message.error(response.data.message || this.$t('testcase.smartOrchestration.searchFailed'));\r\n          this.clearSearchResults();\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索测试用例失败:', error);\r\n        message.error(this.$t('testcase.smartOrchestration.searchError'));\r\n        this.clearSearchResults();\r\n      } finally {\r\n        this.searching = false;\r\n      }\r\n    },\r\n\r\n    // 查看测试用例详情\r\n    viewTestcaseDetail(record) {\r\n      this.selectedTestcase = record;\r\n      this.testcaseDetailVisible = true;\r\n    },\r\n\r\n    // 清除搜索历史（用户手动操作）\r\n    clearSearchHistory() {\r\n      this.clearSearchResults();\r\n      message.success(this.$t('testcase.smartOrchestration.resultsCleared'));\r\n    },\r\n\r\n    // 获取相似度颜色\r\n    getSimilarityColor(similarity) {\r\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\r\n      if (similarity >= 0.6) return '#faad14'; // 橙色\r\n      return '#f5222d'; // 红色\r\n    },\r\n\r\n    // 获取级别颜色\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n        'P0': 'red',\r\n        'P1': 'orange',\r\n        'P2': 'blue',\r\n        'P3': 'green',\r\n        'P4': 'gray'\r\n      };\r\n      return colors[level] || 'default';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.mb-16 {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.mb-24 {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.header-solid {\r\n  border-radius: 12px;\r\n}\r\n\r\n:deep(.ant-descriptions-item-label) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-collapse-header) {\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 8px 12px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n.query-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-card-head-title) {\r\n  color: white;\r\n}\r\n\r\n.query-card :deep(.ant-form-item-label > label) {\r\n  color: white;\r\n}\r\n\r\n.param-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  text-align: center;\r\n  margin-top: 4px;\r\n}\r\n\r\n\r\n</style> "]}]}