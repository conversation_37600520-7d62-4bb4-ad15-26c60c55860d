// CbhConfig.vue
<template>
  <a-card :bordered="false" class="header-solid cbh-config-card">
    <template #title>
      <a-row type="flex" align="middle">
        <a-col :span="12">
          <h6 class="font-semibold m-0">CBH Configuration</h6>
        </a-col>
        <a-col :span="12" class="text-right">
          <a-button @click="saveConfig" class="nav-style-button">
            <template #icon><a-icon type="save" /></template>
            Save Config
          </a-button>
        </a-col>
      </a-row>
    </template>

    <a-descriptions size="small" :column="1">
      <a-descriptions-item label="AK (Access Key)">
        <a-input v-model="config.ak" placeholder="Access Key" />
      </a-descriptions-item>
      <a-descriptions-item label="SK (Secret Key)">
        <a-input v-model="config.sk" placeholder="Secret Key" />
      </a-descriptions-item>
      <a-descriptions-item label="Server (OBS Endpoint)">
        <a-input v-model="config.server" placeholder="OBS Endpoint" />
      </a-descriptions-item>
      <a-descriptions-item label="Bucket Name">
        <a-input v-model="config.bucket_name" placeholder="Bucket Name" />
      </a-descriptions-item>
      <a-descriptions-item label="Bucket Store Dir">
        <a-input v-model="config.bucket_store_dir" placeholder="Bucket Store Dir" />
      </a-descriptions-item>
      <a-descriptions-item label="Upload File Path">
        <a-input v-model="config.upload_file_path" placeholder="Upload File Path" />
      </a-descriptions-item>
      <a-descriptions-item label="Upload Object Key">
        <a-input v-model="config.upload_object_key" placeholder="Upload Object Key" />
      </a-descriptions-item>
      <a-descriptions-item label="Download Object Key">
        <a-input v-model="config.download_object_key" placeholder="Download Object Key" />
      </a-descriptions-item>
      <a-descriptions-item label="Download File Path">
        <a-input v-model="config.download_file_path" placeholder="Download File Path" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH Host">
        <a-input v-model="config.cbh_host" placeholder="CBH Host" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH User Name">
        <a-input v-model="config.cbh_user_name" placeholder="CBH User Name" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH User Port">
        <a-input v-model="config.cbh_user_port" placeholder="CBH User Port" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH Private Key Path">
        <a-input v-model="config.cbh_private_key_path" placeholder="CBH Private Key Path" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH Private Key Passwd">
        <a-input v-model="config.cbh_private_key_passwd" placeholder="CBH Private Key Passwd" />
      </a-descriptions-item>
      <a-descriptions-item label="CBH Switch Account">
        <a-input v-model="config.cbh_switch_account" placeholder="CBH Switch Account" />
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>

<script>
import { Icon } from 'ant-design-vue';
import axios from '@/api/axiosInstance';
import {mapState} from "vuex";

export default {
  name: 'CbhConfig',
  components: {
    AIcon: Icon,
  },
  computed: {
    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式
  },
  data() {
    return {
      config: {
        ak: '',
        sk: '',
        server: '',
        bucket_name: '',
        bucket_store_dir: '',
        upload_file_path: '',
        upload_object_key: '',
        download_object_key: '',
        download_file_path: '',
        cbh_host: '',
        cbh_user_name: '',
        cbh_user_port: '',
        cbh_private_key_path: '',
        cbh_private_key_passwd: '',
        cbh_switch_account: '',
      },
    };
  },
  created() {
    this.fetchCbhConfig();
  },
  methods: {
    async fetchCbhConfig() {
      try {
        const response = await axios.get('/api/cbh_config');
        const data = response.data;
        this.config = data; // 直接赋值整个配置对象
      } catch (error) {
        console.error('Error fetching CBH config:', error);
        this.$message.error('Failed to load CBH configuration');
      }
    },
    async saveConfig() {
      try {
        const response = await axios.post('/api/cbh_config', {
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(this.config),
        });

        this.$message.success('CBH configuration saved successfully');
      } catch (error) {
        console.error('Error saving CBH config:', error);
        this.$message.error('Failed to save CBH configuration');
      }
    },
  },
};
</script>

<style scoped>
.cbh-config-card {
  margin: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
