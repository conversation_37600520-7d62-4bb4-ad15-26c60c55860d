<template>
  <a-card style="margin-top: 16px;" size="small" :title="$t('common.taskProgress')">
    <template slot="extra">
      <a-button
        type="link"
        size="small"
        @click="clearTask"
        class="clear-button"
      >
        <a-icon type="close" />
        {{ $t('common.clear') }}
      </a-button>
    </template>
    <div v-if="taskId || hasNodes">
      <!-- Overall progress -->
      <a-progress
        :percent="progressPercentage"
        :status="progressStatus"
        style="margin-bottom: 16px;"
      />

      <!-- Node-level progress table -->
      <div class="result-table">
        <a-table
          :dataSource="nodeProgressData"
          :columns="progressColumns"
          :pagination="false"
        >
        <template slot="progress" slot-scope="text, record">
          <a-progress
            :percent="record.progress"
            :status="record.status === 'failed' ? 'exception' :
                    (record.status === 'success' && !record.error_detail ? 'success' : 'active')"
            size="small"
          />
        </template>

        <template slot="status" slot-scope="text, record">
          <a-tag :color="getStatusColor(text, record.error_detail)">
            {{ record.error_detail ? $t('tool.status.failed') : text }}
          </a-tag>
        </template>

        <template slot="error" slot-scope="text, record">
          <div v-if="record.error_detail" style="max-width: 300px;">
            <a-tooltip placement="topLeft">
              <template slot="title">
                <div style="white-space: pre-wrap;">{{ record.error_detail }}</div>
              </template>
              <span style="color: #ff4d4f; cursor: pointer;">
                <a-icon type="warning" theme="filled" style="margin-right: 4px;" />
                {{ truncateError(record.error_detail) }}
              </span>
            </a-tooltip>
          </div>
        </template>
        </a-table>
      </div>
    </div>
    <div v-else>
      <a-empty
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import { Empty } from 'ant-design-vue';

export default {
  name: 'TaskProgressCard',
  data() {
    return {
      Empty
    };
  },
  props: {
    // 任务类型，用于区分不同类型的任务（如 'task' 或 'tool'）
    taskType: {
      type: String,
      default: 'task'
    },
    // 是否正在处理中
    isProcessing: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['activeTask', 'activeToolTask', 'activeUploadTask', 'activeDownloadTask', 'currentProject']),

    // 根据任务类型获取对应的任务数据
    currentTask() {
      if (this.taskType === 'tool') {
        return this.activeToolTask;
      } else if (this.taskType === 'upload') {
        return this.activeUploadTask;
      } else if (this.taskType === 'download') {
        return this.activeDownloadTask;
      } else {
        // task 使用 activeTask
        return this.activeTask;
      }
    },

    // 任务ID
    taskId() {
      return this.currentTask?.task_id;
    },

    // 是否有节点数据
    hasNodes() {
      return this.currentTask && this.currentTask.nodes && Object.keys(this.currentTask.nodes).length > 0;
    },



    // 进度百分比
    progressPercentage() {
      if (!this.currentTask?.nodes) return 0;
      const nodes = Object.values(this.currentTask.nodes);
      if (nodes.length === 0) return 0;
      const totalProgress = nodes.reduce((sum, node) => sum + (parseInt(node.progress) || 0), 0);
      return Math.round(totalProgress / nodes.length);
    },

    // 进度状态
    progressStatus() {
      if (!this.currentTask?.nodes) return 'active';
      const nodes = Object.values(this.currentTask.nodes);
      const hasFailures = nodes.some(node => node.status === 'failed' || node.error_detail);
      return hasFailures ? 'exception' :
             (this.progressPercentage === 100 ? 'success' : 'active');
    },

    // 节点进度数据
    nodeProgressData() {
      if (!this.currentTask?.nodes) return [];
      return Object.entries(this.currentTask.nodes).map(([ip, node]) => ({
        key: ip,
        ip: ip,
        host_name: node.host_name,
        status: node.error_detail ? 'failed' : node.status,
        progress: node.progress || 0,
        error_detail: node.error_detail
      }));
    },

    // 进度列定义
    progressColumns() {
      return [
        {
          title: this.$t('tool.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name'
        },
        {
          title: this.$t('tool.columns.ip'),
          dataIndex: 'ip',
          key: 'ip'
        },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: this.$t('tool.columns.progress'),
          dataIndex: 'progress',
          key: 'progress',
          scopedSlots: { customRender: 'progress' }
        },
        {
          title: this.$t('tool.columns.errorDetails'),
          key: 'error',
          width: 300,
          scopedSlots: { customRender: 'error' }
        }
      ];
    }
  },
  methods: {
    // 获取状态颜色
    getStatusColor(status, hasError) {
      const colors = {
        success: '#52c41a',
        error: '#ff4d4f',
        processing: '#1890ff'
      }
      return hasError ? colors.error : colors[status] || '#d9d9d9'
    },

    // 截断错误信息
    truncateError(error) {
      if (!error) return '';
      return error.length > 50 ? error.substring(0, 47) + '...' : error;
    },

    // 清除任务
    clearTask() {
      // 根据任务类型清除对应的 store 状态
      if (this.taskType === 'tool') {
        this.$store.dispatch('clearActiveToolTask');
      } else if (this.taskType === 'upload') {
        this.$store.commit('setActiveUploadTask', null);
      } else if (this.taskType === 'download') {
        this.$store.commit('setActiveDownloadTask', null);
      } else {
        // task 清除 activeTask
        this.$store.dispatch('clearActiveTask');
      }
      
      // 清除 localStorage 中的任务相关信息
      if (this.currentProject) {
        if (this.taskType === 'tool') {
          localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);
          localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);
        } else if (this.taskType === 'upload') {
          localStorage.removeItem(`uploadTaskInfo_${this.currentProject}`);
          localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);
        } else if (this.taskType === 'download') {
          localStorage.removeItem(`downloadTaskInfo_${this.currentProject}`);
          localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);
        } else {
          localStorage.removeItem(`taskInfo_${this.currentProject}`);
          localStorage.removeItem(`taskCompleted_${this.currentProject}`);
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.clear-button {
  padding: 0;
  height: auto;
}

::v-deep {
  .ant-progress {
    border-radius: 3px;
  }

  .ant-tooltip-inner {
    max-width: 500px;
    white-space: pre-wrap;
  }
}
</style>
