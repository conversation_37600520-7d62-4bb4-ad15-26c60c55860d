from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.hardware_datamodel import HardwareSnapshot


class HardwareService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_hardware(self, node_id: int) -> List[Dict]:
        """获取指定节点的所有硬件信息"""
        hardware_items = self.db.query(HardwareSnapshot).filter_by(node_id=node_id).all()
        return [hw.to_dict() for hw in hardware_items] if hardware_items else []

    @staticmethod
    def get_insert_objects(hardware_info, node_id: int) -> List[HardwareSnapshot]:
        """获取要插入的硬件对象列表"""
        insert_objects = []
        for hardware in hardware_info:
            hardware_snapshot = HardwareSnapshot(
                node_id=node_id,
                device_info=hardware['device_info'],
                device_type=hardware['device_type']
            )
            insert_objects.append(hardware_snapshot)
        return insert_objects
