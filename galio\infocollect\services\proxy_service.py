import socket
import psutil
from log.logger import log_debug, log_info, log_error


class ProxyService:
    @staticmethod
    def get_local_ips():
        ips = []
        interfaces = psutil.net_if_addrs().values()
        for interface in interfaces:
            for addr in interface:
                if addr.family == socket.AF_INET and not (addr.address.startswith('127.') or addr.address.startswith('169.254')):
                    ips.append(addr.address)
        log_debug(f'Available local IPs: {", ".join(ips)}')
        return ips

    @staticmethod
    def check_ip_reachability(ip: str, target_port: int = 9998, timeout: int = 3) -> bool:
        try:
            with socket.create_connection((ip, target_port), timeout=timeout):
                log_debug(f"Successfully connected to {ip}:{target_port}")
                return True
        except Exception as e:
            log_debug(f"Failed to connect to {ip}:{target_port}: {e}")
            return False

    @staticmethod
    def detect_reachable_ips(target_port: int = 9998, timeout: int = 3):
        local_ips = ProxyService.get_local_ips()
        reachable_ips = [ip for ip in local_ips if ProxyService.check_ip_reachability(ip, target_port, timeout)]
        if reachable_ips:
            log_info(f'Reachable IPs: {", ".join(reachable_ips)}')
        else:
            log_error('No reachable IP addresses found!')
        return reachable_ips
