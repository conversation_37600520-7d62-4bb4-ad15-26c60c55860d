from flask import Blueprint, jsonify, request
from services.project_service import ProjectService
from db.init_db import get_db
from log.logger import log_error, log_info
import re

bp = Blueprint('projects', __name__)


@bp.route('', methods=['GET'])
def get_projects():
    """获取所有项目列表"""
    with get_db('project.db') as db:
        try:
            project_service = ProjectService(db)
            projects = project_service.get_all_projects()
            return jsonify(projects)
        except Exception as e:
            log_error(f"获取项目列表失败: {str(e)}")
            return jsonify({"error": str(e)}), 500


@bp.route('/new', methods=['POST'])
def create_project():
    """创建新项目"""
    with get_db('project.db') as db:
        try:
            data = request.get_json()
            project_name = data.get('name')
            
            if not project_name:
                return jsonify({"error": "项目名称不能为空"}), 400
            
            if not re.match(r'^[a-zA-Z0-9_-]+$', project_name):
                return jsonify({"error": "项目名称只能包含大小写字母、数字、下划线和连字符"}), 400
                
            project_service = ProjectService(db)
            new_project = project_service.create_new_project(project_name)
            return jsonify(new_project)
        except Exception as e:
            log_error(f"创建项目失败: {str(e)}")
            return jsonify({"error": str(e)}), 500


@bp.route('/validate/<string:db_file>', methods=['GET'])
def validate_project(db_file):
    """验证项目数据库是否有效"""
    with get_db('project.db') as db:
        try:
            project_service = ProjectService(db)
            is_valid = project_service.validate_project(db_file)
            return jsonify({"valid": is_valid})
        except Exception as e:
            log_error(f"验证项目失败: {str(e)}")
            return jsonify({"error": str(e)}), 500


@bp.route('/<string:db_file>', methods=['DELETE'])
def delete_project(db_file):
    """删除项目"""
    with get_db('project.db') as db:
        try:
            project_service = ProjectService(db)
            success = project_service.delete_project(db_file)
            if success:
                return jsonify({"message": "Project deleted successfully"})
            return jsonify({"error": "Failed to delete project"}), 404
        except Exception as e:
            log_error(f"删除项目失败: {str(e)}")
            return jsonify({"error": str(e)}), 500
