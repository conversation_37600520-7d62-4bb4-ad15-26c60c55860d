import os
from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
import time

from datamodel.project_datamodel import Project
from db.init_db import initialize_database, get_db, close_all_connections
from log.logger import log_error


class ProjectService:
    def __init__(self, db: Session = None):
        self.db = db
        self.projects_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'db')
        if not os.path.exists(self.projects_dir):
            os.makedirs(self.projects_dir)

    def get_all_projects(self) -> List[Dict]:
        """获取所有项目列表"""
        try:
            projects = self.db.query(Project).order_by(Project.created_at.desc()).all()
            return [project.to_dict() for project in projects]
        except Exception as e:
            log_error(f"获取项目列表失败: {str(e)}")
            raise

    def create_new_project(self, project_name: str) -> Dict:
        """创建新项目"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            db_file = f"project_{timestamp}.db"
            db_path = os.path.join(self.projects_dir, db_file)

            initialize_database(db_path)

            new_project = Project(
                name=project_name,
                db_file=db_file,
                created_at=datetime.now()
            )
            self.db.add(new_project)
            self.db.flush()

            self.db.commit()
            return new_project.to_dict()

        except Exception as e:
            self.db.rollback()
            log_error(f"创建新项目失败: {str(e)}")
            raise

    def validate_project(self, db_file: str) -> bool:
        """验证项目数据库是否有效"""
        try:
            # 检查项目是否存在于主数据库中
            project = self.db.query(Project).filter_by(db_file=db_file).first()
            if not project:
                return False

            db_path = os.path.join(self.projects_dir, db_file)
            if not os.path.exists(db_path):
                return False

            with get_db(db_file) as project_db:
                # 检查必要的表是否存在
                tables = project_db.execute(
                    text("SELECT name FROM sqlite_master WHERE type='table'")
                ).fetchall()
                required_tables = {'host_config', 'docker_host_config', 'docker_container'}
                existing_tables = {row[0] for row in tables}
                return all(table in existing_tables for table in required_tables)

        except Exception as e:
            log_error(f"验证项目失败: {str(e)}")
            return False

    def delete_project(self, db_file: str) -> bool:
        """删除项目"""
        try:
            project = self.db.query(Project).filter_by(db_file=db_file).one_or_none()
            if not project:
                log_error(f"项目不存在: {db_file}")
                return False

            # 删除数据库文件
            db_path = os.path.join(self.projects_dir, db_file)
            if os.path.exists(db_path):
                try:
                    # 先删除项目记录
                    self.db.delete(project)
                    self.db.commit()
                    close_all_connections(db_file)
            
                    time.sleep(0.5)
                    max_attempts = 3
                    for attempt in range(max_attempts):
                        try:
                            if os.path.exists(db_path):
                                os.close(os.open(db_path, os.O_RDONLY))
                            return True
                        except Exception as e:
                            if attempt == max_attempts - 1:
                                raise
                            time.sleep(0.5)
                            # 再次尝试强制关闭连接
                            close_all_connections(db_file)
                    
                except Exception as file_error:
                    log_error(f"删除数据库文件失败: {str(file_error)}")
                    self.db.rollback()
                    raise

            return True

        except Exception as e:
            self.db.rollback()
            log_error(f"删除项目失败: {str(e)}")
            raise

    def get_project_by_db_file(self, db_file: str) -> Optional[Dict]:
        """根据数据库文件名获取项目信息"""
        try:
            project = self.db.query(Project).filter_by(db_file=db_file).first()
            return project.to_dict() if project else None
        except Exception as e:
            log_error(f"获取项目信息失败: {str(e)}")
            raise 