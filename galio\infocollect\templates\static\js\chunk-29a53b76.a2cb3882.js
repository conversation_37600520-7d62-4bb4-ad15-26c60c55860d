(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-29a53b76"],{"13d5":function(t,e,s){"use strict";var o=s("23e7"),r=s("d58f").left,a=s("a640"),l=s("2d00"),i=s("605d"),n=a("reduce"),c=!i&&l>79&&l<83;o({target:"Array",proto:!0,forced:!n||c},{reduce:function(t){return r(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"16e4":function(t,e,s){"use strict";s("2294")},2294:function(t,e,s){},"45ec":function(t,e,s){"use strict";s("913f")},"4f4d":function(t,e,s){"use strict";s("0643"),s("2382");e["a"]={methods:{addTaskCompletionNotification({taskId:t,taskType:e,nodes:s,projectId:o,titles:r={},templates:a={},statusMapping:l={}}){const i=`${e}Notified_${o}_${t}`;if(localStorage.getItem(i))return;const n={success:["success","completed"],failure:["failed"]},c={success:[...l.success||[],...n.success],failure:[...l.failure||[],...n.failure]},d=s.filter(t=>c.success.includes(t.status)&&!t.error_detail).length,u=s.filter(t=>c.failure.includes(t.status)||t.error_detail).length,p=u>0;let h,f;h=p?r.error||this.getDefaultErrorTitle(e):r.success||this.getDefaultSuccessTitle(e),f=p?a.error||`${d} nodes completed successfully, ${u} nodes failed.`:a.success||`All ${s.length} nodes completed successfully.`,this.addNotification({title:h,message:f,type:p?"error":"success",taskId:t}),localStorage.setItem(i,"true")},getDefaultSuccessTitle(t){const e={task:"Task Completed",upload:"File Upload Completed",download:"File Download Completed",tool:"Tool Execution Completed"};return e[t]||"Operation Completed"},getDefaultErrorTitle(t){const e={task:"Task Completed with Errors",upload:"File Upload Completed with Errors",download:"File Download Completed with Errors",tool:"Tool Execution Completed with Errors"};return e[t]||"Operation Completed with Errors"},clearTaskNotificationMark(t,e,s){const o=`${e}Notified_${s}_${t}`;localStorage.removeItem(o)}}}},"524b":function(t,e,s){"use strict";s("13d5"),s("0643"),s("76d6"),s("2382"),s("fffc"),s("9d4a"),s("9a9a");var o=s("fec3"),r=s("2f62");e["a"]={computed:{...Object(r["e"])(["activeTask","currentProject"]),taskId(){var t;return null===(t=this.activeTask)||void 0===t?void 0:t.task_id},progressPercentage(){var t;if(null===(t=this.activeTask)||void 0===t||!t.nodes)return 0;const e=Object.values(this.activeTask.nodes);if(0===e.length)return 0;const s=e.reduce((t,e)=>t+(parseInt(e.progress)||0),0);return Math.round(s/e.length)},taskInProgress(){return this.isProcessing&&this.pollInterval}},data(){return{isProcessing:!1,pollInterval:null,pollDelay:1e4}},methods:{async checkActiveTask(t,e){try{const s=`${t}Info_${this.currentProject}`,r=`${t}Completed_${this.currentProject}`,a=localStorage.getItem(s),l=localStorage.getItem(r);if(a){const{taskId:s,projectFile:i}=JSON.parse(a);if(i!==this.currentProject)throw new Error("Task belongs to different project");const n=await o["a"].get(`/api/${e}/${s}`);if(n.data&&(this.$store.dispatch("updateTask",n.data),n.data.nodes)){const o=Object.values(n.data.nodes),a=o.every(t=>["success","failed"].includes(t.status));a||l?a&&(this.isProcessing=!1,localStorage.setItem(r,"true")):(this.isProcessing=!0,this.startPolling(s,t,e))}}}catch(s){console.error("Error checking active task:",s),localStorage.removeItem(`${t}Info_${this.currentProject}`),localStorage.removeItem(`${t}Completed_${this.currentProject}`)}},startPolling(t,e,s){this.pollInterval&&(clearInterval(this.pollInterval),this.pollInterval=null);const r=async()=>{try{if(!this.pollInterval)return void console.log("轮询已停止，不再请求数据");const r=`${e}Completed_${this.currentProject}`,a=localStorage.getItem(r);if("true"===a)return console.log("任务已标记为完成，停止轮询"),this.stopPolling(),void(this.isProcessing=!1);if(!this.currentProject)throw new Error("No project database selected");const l=await o["a"].get(`/api/${s}/${t}`);if(console.log("轮询任务状态:",l.data),l.data&&!l.data.task_id&&(l.data.task_id=t),this.$store.dispatch("updateTask",l.data),l.data&&l.data.nodes){const s=Object.values(l.data.nodes),o=s.every(t=>["success","failed"].includes(t.status)),a=s.some(t=>"failed"===t.status||t.error_detail);o?(console.log("所有节点已完成处理，停止轮询"),this.stopPolling(),this.isProcessing=!1,localStorage.setItem(r,"true"),this.addTaskCompletionNotification({taskId:t,taskType:e,nodes:s,projectId:this.currentProject,templates:this.getNotificationTemplates(e,s)})):a&&"failed"===l.data.status&&(console.log("任务状态为失败，停止轮询"),this.stopPolling(),this.isProcessing=!1,localStorage.setItem(r,"true"),this.addTaskCompletionNotification({taskId:t,taskType:e,nodes:s,projectId:this.currentProject,templates:{error:`Task failed: ${s.filter(t=>"failed"===t.status).length} nodes reported failure.`}})),this.$forceUpdate(),this.$nextTick(()=>{const t=this.$children.find(t=>"TaskProgressCard"===t.$options.name);t&&t.$forceUpdate()})}else l.data&&"failed"===l.data.status&&(console.log("任务状态为失败，停止轮询"),this.stopPolling(),this.isProcessing=!1,localStorage.setItem(r,"true"),this.addTaskCompletionNotification({taskId:t,taskType:e,nodes:[],projectId:this.currentProject,templates:{error:"Task failed: The server reported a failure status."}}))}catch(a){var r;console.error("Error polling status:",a),404===(null===(r=a.response)||void 0===r?void 0:r.status)&&(this.stopPolling(),this.isProcessing=!1)}};r(),this.pollInterval=setInterval(r,this.pollDelay),console.log(`开始轮询任务 ${t}，轮询间隔: ${this.pollDelay}ms`)},stopPolling(){this.pollInterval&&(clearInterval(this.pollInterval),this.pollInterval=null)},getNotificationTemplates(t,e){return"tool"===t?{success:`Tool executed successfully on all ${e.length} nodes.`,error:`${e.filter(t=>"success"===t.status).length} nodes completed tool execution successfully, ${e.filter(t=>"failed"===t.status).length} nodes failed.`}:{success:`Task completed successfully on all ${e.length} nodes.`,error:`${e.filter(t=>"success"===t.status).length} nodes completed successfully, ${e.filter(t=>"failed"===t.status).length} nodes failed.`}},beforeDestroy(){this.stopPolling()},deactivated(){this.stopPolling()}}}},"5a3b":function(t,e,s){"use strict";var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"proxy-selector"},[e("a-button",{staticClass:"nav-style-button",attrs:{loading:t.isDetecting,disabled:t.disabled},on:{click:t.fetchReachableIps}},[t._v(" "+t._s(t.$t("common.detectReachableIps")||"检测可达IP")+" ")]),t.reachableIps.length?e("a-select",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{placeholder:t.$t("tool.selectReachableIp")||"选择可达IP",disabled:t.disabled},model:{value:t.selectedIpValue,callback:function(e){t.selectedIpValue=e},expression:"selectedIpValue"}},t._l(t.reachableIps,(function(s){return e("a-select-option",{key:s,attrs:{value:s}},[t._v(" "+t._s(s)+" ")])})),1):t._e()],1)},r=[],a=s("fec3"),l={name:"ProxySelector",props:{disabled:{type:Boolean,default:!1},value:{type:String,default:null}},data(){return{isDetecting:!1,reachableIps:[],selectedIpValue:this.value}},computed:{},watch:{value(t){this.selectedIpValue=t},selectedIpValue(t){this.$emit("input",t),this.$emit("change",t)}},methods:{async fetchReachableIps(){this.isDetecting=!0;try{const t=await a["a"].get("/api/proxy/detect");this.reachableIps=t.data.reachable_ips,this.reachableIps.length&&(this.selectedIpValue=this.reachableIps[0])}catch(t){console.error("Error detecting reachable IPs:",t),this.$notify.error({title:"Error",message:"Failed to detect reachable IPs"})}finally{this.isDetecting=!1}}}},i=l,n=s("2877"),c=Object(n["a"])(i,o,r,!1,null,"46f0b65a",null);e["a"]=c.exports},"605d":function(t,e,s){var o=s("c6b6"),r=s("da84");t.exports="process"==o(r.process)},"76d6":function(t,e,s){"use strict";var o=s("23e7"),r=s("2266"),a=s("1c0b"),l=s("825a");o({target:"Iterator",proto:!0,real:!0},{every:function(t){return l(this),a(t),!r(this,(function(e,s){if(!t(e))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"913f":function(t,e,s){},9304:function(t,e,s){"use strict";var o=function(){var t=this,e=t._self._c;return e("a-card",{staticStyle:{"margin-top":"16px"},attrs:{size:"small",title:t.$t("common.taskProgress")}},[e("template",{slot:"extra"},[e("a-button",{staticClass:"clear-button",attrs:{type:"link",size:"small"},on:{click:t.clearTask}},[e("a-icon",{attrs:{type:"close"}}),t._v(" "+t._s(t.$t("common.clear"))+" ")],1)],1),t.taskId||t.hasNodes?e("div",[e("a-progress",{staticStyle:{"margin-bottom":"16px"},attrs:{percent:t.progressPercentage,status:t.progressStatus}}),e("div",{staticClass:"result-table"},[e("a-table",{attrs:{dataSource:t.nodeProgressData,columns:t.progressColumns,pagination:!1},scopedSlots:t._u([{key:"progress",fn:function(t,s){return[e("a-progress",{attrs:{percent:s.progress,status:"failed"===s.status?"exception":"success"!==s.status||s.error_detail?"active":"success",size:"small"}})]}},{key:"status",fn:function(s,o){return[e("a-tag",{attrs:{color:t.getStatusColor(s,o.error_detail)}},[t._v(" "+t._s(o.error_detail?t.$t("tool.status.failed"):s)+" ")])]}},{key:"error",fn:function(s,o){return[o.error_detail?e("div",{staticStyle:{"max-width":"300px"}},[e("a-tooltip",{attrs:{placement:"topLeft"}},[e("template",{slot:"title"},[e("div",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(o.error_detail))])]),e("span",{staticStyle:{color:"#ff4d4f",cursor:"pointer"}},[e("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:"warning",theme:"filled"}}),t._v(" "+t._s(t.truncateError(o.error_detail))+" ")],1)],2)],1):t._e()]}}],null,!1,3266794743)})],1)],1):e("div",[e("a-empty",{attrs:{image:t.Empty.PRESENTED_IMAGE_SIMPLE}})],1)],2)},r=[],a=(s("13d5"),s("0643"),s("a573"),s("9d4a"),s("9a9a"),s("2f62")),l=s("fc25"),i={name:"TaskProgressCard",data(){return{Empty:l["a"]}},props:{taskType:{type:String,default:"task"},isProcessing:{type:Boolean,default:!1}},computed:{...Object(a["e"])(["activeTask","activeToolTask","activeUploadTask","activeDownloadTask","currentProject"]),currentTask(){return"tool"===this.taskType?this.activeToolTask:"upload"===this.taskType?this.activeUploadTask:"download"===this.taskType?this.activeDownloadTask:this.activeTask},taskId(){var t;return null===(t=this.currentTask)||void 0===t?void 0:t.task_id},hasNodes(){return this.currentTask&&this.currentTask.nodes&&Object.keys(this.currentTask.nodes).length>0},progressPercentage(){var t;if(null===(t=this.currentTask)||void 0===t||!t.nodes)return 0;const e=Object.values(this.currentTask.nodes);if(0===e.length)return 0;const s=e.reduce((t,e)=>t+(parseInt(e.progress)||0),0);return Math.round(s/e.length)},progressStatus(){var t;if(null===(t=this.currentTask)||void 0===t||!t.nodes)return"active";const e=Object.values(this.currentTask.nodes),s=e.some(t=>"failed"===t.status||t.error_detail);return s?"exception":100===this.progressPercentage?"success":"active"},nodeProgressData(){var t;return null!==(t=this.currentTask)&&void 0!==t&&t.nodes?Object.entries(this.currentTask.nodes).map(([t,e])=>({key:t,ip:t,host_name:e.host_name,status:e.error_detail?"failed":e.status,progress:e.progress||0,error_detail:e.error_detail})):[]},progressColumns(){return[{title:this.$t("tool.columns.hostName"),dataIndex:"host_name",key:"host_name"},{title:this.$t("tool.columns.ip"),dataIndex:"ip",key:"ip"},{title:this.$t("tool.columns.status"),dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:this.$t("tool.columns.progress"),dataIndex:"progress",key:"progress",scopedSlots:{customRender:"progress"}},{title:this.$t("tool.columns.errorDetails"),key:"error",width:300,scopedSlots:{customRender:"error"}}]}},methods:{getStatusColor(t,e){const s={success:"#52c41a",error:"#ff4d4f",processing:"#1890ff"};return e?s.error:s[t]||"#d9d9d9"},truncateError(t){return t?t.length>50?t.substring(0,47)+"...":t:""},clearTask(){"tool"===this.taskType?this.$store.dispatch("clearActiveToolTask"):"upload"===this.taskType?this.$store.commit("setActiveUploadTask",null):"download"===this.taskType?this.$store.commit("setActiveDownloadTask",null):this.$store.dispatch("clearActiveTask"),this.currentProject&&("tool"===this.taskType?(localStorage.removeItem("toolTaskInfo_"+this.currentProject),localStorage.removeItem("toolTaskCompleted_"+this.currentProject)):"upload"===this.taskType?(localStorage.removeItem("uploadTaskInfo_"+this.currentProject),localStorage.removeItem("uploadTaskCompleted_"+this.currentProject)):"download"===this.taskType?(localStorage.removeItem("downloadTaskInfo_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject)):(localStorage.removeItem("taskInfo_"+this.currentProject),localStorage.removeItem("taskCompleted_"+this.currentProject)))}}},n=i,c=(s("45ec"),s("2877")),d=Object(c["a"])(n,o,r,!1,null,"7dd5f704",null);e["a"]=d.exports},"9a9a":function(t,e,s){"use strict";var o=s("23e7"),r=s("2266"),a=s("1c0b"),l=s("825a");o({target:"Iterator",proto:!0,real:!0},{some:function(t){return l(this),a(t),r(this,(function(e,s){if(t(e))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9d4a":function(t,e,s){"use strict";var o=s("23e7"),r=s("2266"),a=s("1c0b"),l=s("825a");o({target:"Iterator",proto:!0,real:!0},{reduce:function(t){l(this),a(t);var e=arguments.length<2,s=e?void 0:arguments[1];if(r(this,(function(o){e?(e=!1,s=o):s=t(s,o)}),{IS_ITERATOR:!0}),e)throw TypeError("Reduce of empty iterator with no initial value");return s}})},a640:function(t,e,s){"use strict";var o=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&o((function(){s.call(null,e||function(){throw 1},1)}))}},c9da:function(t,e,s){"use strict";var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"config-table"},[t.selectedRowKeys.length>0?e("div",{staticClass:"selected-count"},[t._v(" "+t._s(t.$t("common.selectedNodes",{count:t.selectedRowKeys.length}))+" ")]):t._e(),e("a-table",{attrs:{dataSource:t.nodes,columns:t.columns,rowKey:"ip",size:"middle",pagination:{pageSize:10,total:t.nodes.length,showSizeChanger:!1},rowSelection:t.rowSelection},on:{rowClick:t.handleRowClick}})],1)},r=[],a=s("fec3"),l={name:"NodeSelector",props:{value:{type:Array,default:()=>[]},projectFile:{type:String,required:!0},disabled:{type:Boolean,default:!1}},data(){return{nodes:[]}},computed:{selectedRowKeys:{get(){return this.value},set(t){this.$emit("input",t)}},rowSelection(){return{selectedRowKeys:this.selectedRowKeys,onChange:t=>{this.selectedRowKeys=t},getCheckboxProps:()=>({disabled:this.disabled})}},columns(){return[{title:this.$t("tool.columns.hostName"),dataIndex:"host_name",key:"host_name"},{title:this.$t("tool.columns.ip"),dataIndex:"ip",key:"ip"}]}},watch:{projectFile:{handler(t){t&&this.fetchNodes()},immediate:!0}},methods:{async fetchNodes(){try{const t=await a["a"].get("/api/node/nodes?dbFile="+encodeURIComponent(this.projectFile));this.nodes=t.data.items}catch(t){console.error("Error fetching nodes:",t),this.$notify.error({title:"Error",message:"Failed to fetch nodes"})}},handleRowClick(){}}},i=l,n=(s("16e4"),s("2877")),c=Object(n["a"])(i,o,r,!1,null,"1bd9e7ce",null);e["a"]=c.exports},d58f:function(t,e,s){var o=s("1c0b"),r=s("7b0b"),a=s("44ad"),l=s("50c4"),i=function(t){return function(e,s,i,n){o(s);var c=r(e),d=a(c),u=l(c.length),p=t?u-1:0,h=t?-1:1;if(i<2)while(1){if(p in d){n=d[p],p+=h;break}if(p+=h,t?p<0:u<=p)throw TypeError("Reduce of empty array with no initial value")}for(;t?p>=0:u>p;p+=h)p in d&&(n=s(n,d[p],p,c));return n}};t.exports={left:i(!1),right:i(!0)}}}]);
//# sourceMappingURL=chunk-29a53b76.a2cb3882.js.map