// 预置spider
export const defaultScriptContent = `#!/bin/bash
# This script will be executed on the remote node

# 以下示例以运行s-spider工具为例：

# 第一步：上传并解压zip包，
cd {work_dir}
curl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}
unzip -o scan_tools.zip

# 第二步： 执行运行命令，按照实际工具执行步骤往下写即可
cd scan_tools
chmod 777 run.sh
dos2unix run.sh || true
sleep 0.5
sh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall
sleep 0.5

# 第三步： 回传结果文件
# 注意：所有工具运行结果必须打包为{node_name}_{node_ip}.tar格式
# 如果您的工具生成了其他格式的文件，请将其打包为tar格式
# 例如：tar -cf {node_name}_{node_ip}.tar your_result_files
# 或者：mv your_result_file.ext {node_name}_{node_ip}.tar

# 上传结果文件到服务器
curl -F "file=@{result_file}" "http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}"`;