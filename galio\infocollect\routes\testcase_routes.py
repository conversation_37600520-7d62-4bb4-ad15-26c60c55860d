from flask import Blueprint, jsonify, request
from services.testcase_service import TestcaseService

bp = Blueprint('testcase', __name__)
testcase_service = TestcaseService()


@bp.route('/', methods=['GET'])
def get_testcases():
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 100, type=int)

        search_params = {}
        if request.args.get('name'):
            search_params['name'] = request.args.get('name')
        if request.args.get('level'):
            search_params['level'] = request.args.get('level')
        if request.args.get('prepare_condition'):
            search_params['prepare_condition'] = request.args.get('prepare_condition')
        if request.args.get('test_steps'):
            search_params['test_steps'] = request.args.get('test_steps')
        if request.args.get('expected_result'):
            search_params['expected_result'] = request.args.get('expected_result')

        search_params = search_params if search_params else None

        testcases = testcase_service.get_all_testcases(page, page_size, search_params)
        return jsonify(testcases)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/<testcase_number>', methods=['GET'])
def get_testcase(testcase_number):
    try:
        testcase = testcase_service.get_testcase_by_number(testcase_number)
        if testcase:
            return jsonify(testcase)
        return jsonify({'error': 'Testcase not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500
