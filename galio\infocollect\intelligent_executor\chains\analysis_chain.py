"""
分析链 - 用于处理用例分析的LangChain链
"""

from typing import Dict, List, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_openai import ChatOpenAI

from infocollect.log.logger import log_info, log_error, log_debug


class AnalysisChain:
    """
    分析链
    
    主要功能:
    1. 用例信息分析
    2. 执行步骤生成
    3. 风险评估
    4. 建议生成
    """
    
    def __init__(self, llm: Optional[ChatOpenAI] = None):
        """
        初始化分析链
        
        Args:
            llm: LangChain LLM实例
        """
        self.llm = llm or ChatOpenAI(temperature=0.1)
        self.json_parser = JsonOutputParser()
        self.str_parser = StrOutputParser()
        
        # 初始化各种提示词模板
        self._init_prompts()
        
        # 构建分析链
        self._build_chains()
        
        log_info("AnalysisChain initialized")

    def _init_prompts(self):
        """初始化提示词模板"""
        
        # 主分析提示词
        self.main_analysis_prompt = PromptTemplate(
            input_variables=["case_info", "similar_cases"],
            template="""
你是一个专业的用例分析专家。请分析以下用例信息，并结合相似用例的经验，提供详细的分析结果。

当前用例信息:
{case_info}

相似用例参考:
{similar_cases}

请按照以下JSON格式返回分析结果:
{{
    "case_analysis": {{
        "case_type": "用例类型",
        "complexity_level": "复杂度等级（简单/中等/复杂）",
        "key_features": ["关键特征1", "关键特征2"],
        "domain": "所属领域"
    }},
    "execution_plan": {{
        "steps": [
            {{
                "step_number": 1,
                "description": "步骤描述",
                "command": "具体命令或操作",
                "expected_result": "预期结果",
                "estimated_time": "预估时间（分钟）"
            }}
        ],
        "total_estimated_time": "总预估时间",
        "prerequisites": ["前置条件1", "前置条件2"]
    }},
    "risk_assessment": {{
        "risk_level": "风险等级（低/中/高）",
        "potential_risks": [
            {{
                "risk": "风险描述",
                "probability": "发生概率（低/中/高）",
                "impact": "影响程度（低/中/高）",
                "mitigation": "缓解措施"
            }}
        ]
    }},
    "recommendations": {{
        "execution_suggestions": ["执行建议1", "执行建议2"],
        "optimization_tips": ["优化建议1", "优化建议2"],
        "monitoring_points": ["监控要点1", "监控要点2"]
    }}
}}

请确保返回有效的JSON格式。
            """
        )
        
        # 快速分析提示词（用于简单场景）
        self.quick_analysis_prompt = PromptTemplate(
            input_variables=["case_info"],
            template="""
请对以下用例进行快速分析：

用例信息:
{case_info}

请返回简洁的分析结果（JSON格式）:
{{
    "case_type": "用例类型",
    "complexity": "复杂度",
    "key_steps": ["主要步骤1", "主要步骤2"],
    "estimated_time": "预估时间",
    "risk_level": "风险等级"
}}
            """
        )
        
        # 相似性评估提示词
        self.similarity_prompt = PromptTemplate(
            input_variables=["current_case", "reference_cases"],
            template="""
请评估当前用例与参考用例的相似性，并提供基于相似性的分析建议。

当前用例:
{current_case}

参考用例:
{reference_cases}

请返回相似性分析结果（JSON格式）:
{{
    "similarity_analysis": {{
        "most_similar_case": "最相似用例ID",
        "similarity_score": 0.85,
        "similar_aspects": ["相似点1", "相似点2"],
        "different_aspects": ["差异点1", "差异点2"]
    }},
    "recommendations_based_on_similarity": {{
        "reusable_steps": ["可复用步骤1", "可复用步骤2"],
        "adaptations_needed": ["需要调整的地方1", "需要调整的地方2"],
        "lessons_learned": ["经验教训1", "经验教训2"]
    }}
}}
            """
        )

    def _build_chains(self):
        """构建分析链"""
        
        # 主分析链
        self.main_chain = (
            RunnablePassthrough.assign(
                formatted_similar_cases=RunnableLambda(self._format_similar_cases)
            )
            | self.main_analysis_prompt
            | self.llm
            | self.json_parser
        )
        
        # 快速分析链
        self.quick_chain = (
            self.quick_analysis_prompt
            | self.llm
            | self.json_parser
        )
        
        # 相似性分析链
        self.similarity_chain = (
            self.similarity_prompt
            | self.llm
            | self.json_parser
        )

    def _format_similar_cases(self, inputs: Dict[str, Any]) -> str:
        """格式化相似用例信息"""
        similar_cases = inputs.get("similar_cases", [])
        
        if not similar_cases:
            return "暂无相似用例参考"
        
        formatted_cases = []
        for i, case in enumerate(similar_cases[:3], 1):  # 只取前3个最相似的
            case_info = f"""
相似用例 {i}:
- 用例名称: {case.get('testcase_name', 'Unknown')}
- 相似度: {case.get('similarity_score', 0):.2f}
- 用例内容: {case.get('document_text', '')[:200]}...
            """
            formatted_cases.append(case_info.strip())
        
        return "\n\n".join(formatted_cases)

    async def analyze_case_comprehensive(self, 
                                       case_info: Dict[str, Any],
                                       similar_cases: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        全面分析用例
        
        Args:
            case_info: 用例信息
            similar_cases: 相似用例列表
            
        Returns:
            分析结果
        """
        try:
            log_debug(f"Starting comprehensive analysis for case: {case_info.get('name', 'Unknown')}")
            
            inputs = {
                "case_info": str(case_info),
                "similar_cases": similar_cases or []
            }
            
            result = await self.main_chain.ainvoke(inputs)
            
            # 添加元数据
            result["analysis_metadata"] = {
                "analysis_type": "comprehensive",
                "similar_cases_count": len(similar_cases) if similar_cases else 0,
                "original_case_info": case_info
            }
            
            log_info("Comprehensive case analysis completed")
            return result
            
        except Exception as e:
            log_error(f"Error in comprehensive analysis: {e}")
            return self._get_error_result(str(e))

    async def analyze_case_quick(self, case_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        快速分析用例
        
        Args:
            case_info: 用例信息
            
        Returns:
            快速分析结果
        """
        try:
            log_debug(f"Starting quick analysis for case: {case_info.get('name', 'Unknown')}")
            
            result = await self.quick_chain.ainvoke({"case_info": str(case_info)})
            
            # 添加元数据
            result["analysis_metadata"] = {
                "analysis_type": "quick",
                "original_case_info": case_info
            }
            
            log_info("Quick case analysis completed")
            return result
            
        except Exception as e:
            log_error(f"Error in quick analysis: {e}")
            return self._get_error_result(str(e))

    async def analyze_similarity(self, 
                               current_case: Dict[str, Any],
                               reference_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析用例相似性
        
        Args:
            current_case: 当前用例
            reference_cases: 参考用例列表
            
        Returns:
            相似性分析结果
        """
        try:
            log_debug("Starting similarity analysis")
            
            inputs = {
                "current_case": str(current_case),
                "reference_cases": str(reference_cases)
            }
            
            result = await self.similarity_chain.ainvoke(inputs)
            
            log_info("Similarity analysis completed")
            return result
            
        except Exception as e:
            log_error(f"Error in similarity analysis: {e}")
            return self._get_error_result(str(e))

    async def batch_analyze_cases(self, 
                                cases: List[Dict[str, Any]],
                                analysis_type: str = "quick") -> List[Dict[str, Any]]:
        """
        批量分析用例
        
        Args:
            cases: 用例列表
            analysis_type: 分析类型 (quick/comprehensive)
            
        Returns:
            分析结果列表
        """
        try:
            log_info(f"Starting batch analysis for {len(cases)} cases with type: {analysis_type}")
            
            if analysis_type == "comprehensive":
                # 全面分析（需要相似用例，这里简化处理）
                tasks = [self.analyze_case_comprehensive(case) for case in cases]
            else:
                # 快速分析
                tasks = [self.analyze_case_quick(case) for case in cases]
            
            import asyncio
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    log_error(f"Error analyzing case {i}: {result}")
                    processed_results.append(self._get_error_result(str(result)))
                else:
                    processed_results.append(result)
            
            log_info(f"Batch analysis completed: {len(processed_results)} results")
            return processed_results
            
        except Exception as e:
            log_error(f"Error in batch analysis: {e}")
            return [self._get_error_result(str(e)) for _ in cases]

    def _get_error_result(self, error_message: str) -> Dict[str, Any]:
        """获取错误结果格式"""
        return {
            "error": error_message,
            "case_analysis": {
                "case_type": "unknown",
                "complexity_level": "unknown",
                "key_features": [],
                "domain": "unknown"
            },
            "execution_plan": {
                "steps": [],
                "total_estimated_time": "unknown",
                "prerequisites": []
            },
            "risk_assessment": {
                "risk_level": "high",
                "potential_risks": [{"risk": "分析失败", "probability": "高", "impact": "高", "mitigation": "重新分析"}]
            },
            "recommendations": {
                "execution_suggestions": ["请检查用例信息格式"],
                "optimization_tips": [],
                "monitoring_points": []
            }
        }

    def get_chain_info(self) -> Dict[str, Any]:
        """获取链信息"""
        return {
            "chain_type": "AnalysisChain",
            "available_methods": [
                "analyze_case_comprehensive",
                "analyze_case_quick", 
                "analyze_similarity",
                "batch_analyze_cases"
            ],
            "llm_model": str(self.llm.model_name) if hasattr(self.llm, 'model_name') else "unknown"
        } 