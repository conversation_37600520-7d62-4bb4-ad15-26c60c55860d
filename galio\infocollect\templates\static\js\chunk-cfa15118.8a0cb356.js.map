{"version": 3, "sources": ["webpack:///./src/views/Config.vue", "webpack:///./src/components/Cards/HostConfig.vue", "webpack:///src/components/Cards/HostConfig.vue", "webpack:///./src/components/Cards/HostConfig.vue?8c6a", "webpack:///./src/components/Cards/HostConfig.vue?c9ed", "webpack:///./src/components/Cards/CbhConfig.vue", "webpack:///src/components/Cards/CbhConfig.vue", "webpack:///./src/components/Cards/CbhConfig.vue?c2e5", "webpack:///./src/components/Cards/CbhConfig.vue?ed2b", "webpack:///src/views/Config.vue", "webpack:///./src/views/Config.vue?ed51", "webpack:///./src/views/Config.vue?8a0a", "webpack:///./src/mixins/CopyMixin.js", "webpack:///./src/components/Cards/HostConfig.vue?a13d", "webpack:///./src/components/Cards/CbhConfig.vue?4ba6", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "activeTab", "on", "handleTabChange", "key", "$t", "staticRenderFns", "staticClass", "scopedSlots", "_u", "fn", "_v", "_s", "addNewRow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "exportSelectedHosts", "batchDelete", "downloadTemplate", "handleUpload", "proxy", "columns", "hosts", "record", "current", "currentPage", "pageSize", "total", "onChange", "onPageChange", "loading", "onSelectChange", "getCheckboxProps", "disabled", "editable", "isNew", "_l", "editableColumns", "col", "text", "index", "staticStyle", "getColumnTitle", "e", "handleChange", "target", "value", "includes", "$event", "copyText", "style", "opacity", "_e", "click", "save", "confirm", "cancel", "edit", "copyNodeInfo", "deleteHost", "cacheData", "components", "AIcon", "Icon", "mixins", "CopyMixin", "computed", "data", "$data", "saving", "currentDbFile", "localStorage", "getItem", "title", "dataIndex", "width", "customRender", "align", "created", "$message", "warning", "$router", "push", "fetchHostConfig", "methods", "newRecord", "Date", "now", "id", "undefined", "host_name", "ip", "map", "item", "$nextTick", "tableBody", "document", "querySelector", "scrollTop", "_this$columns$find", "find", "c", "column", "newData", "validateHost", "hostData", "axios", "post", "dbFile", "success", "error", "targetIndex", "findIndex", "filter", "cachedItem", "Object", "assign", "ssh_port", "login_user", "login_pwd", "switch_root_cmd", "switch_root_pwd", "host", "_host$host_name", "trim", "test", "exist", "some", "h", "response", "get", "params", "detail", "_item$id", "_item$ssh_port", "toString", "_error$response", "page", "delete", "_error$response2", "selectedIds", "ids", "_error$response3", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "console", "options", "file", "name", "endsWith", "formData", "FormData", "append", "headers", "_error$response4", "selectedHosts", "csv<PERSON><PERSON>nt", "join", "header", "blob", "type", "component", "saveConfig", "model", "config", "ak", "callback", "$$v", "$set", "expression", "sk", "server", "bucket_name", "bucket_store_dir", "upload_file_path", "upload_object_key", "download_object_key", "download_file_path", "cbh_host", "cbh_user_name", "cbh_user_port", "cbh_private_key_path", "cbh_private_key_passwd", "cbh_switch_account", "fetchCbhConfig", "JSON", "stringify", "HostConfig", "CbhConfig", "props", "defaultTab", "String", "default", "watch", "immediate", "handler", "to", "targetTab", "hash", "replace", "log", "path", "query", "$route", "catch", "err", "message", "textarea", "select", "execCommand", "$", "iterate", "aFunction", "anObject", "proto", "real", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped"], "mappings": "uHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,SAAS,CAACE,MAAM,CAAC,aAAaJ,EAAIK,WAAWC,GAAG,CAAC,OAASN,EAAIO,kBAAkB,CAACL,EAAG,aAAa,CAACM,IAAI,OAAOJ,MAAM,CAAC,IAAMJ,EAAIS,GAAG,wBAAwB,CAACP,EAAG,eAAe,GAAGA,EAAG,aAAa,CAACM,IAAI,MAAMJ,MAAM,CAAC,IAAMJ,EAAIS,GAAG,uBAAuB,CAACP,EAAG,cAAc,IAAI,IAAI,IAE3VQ,EAAkB,GCFlBX,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACS,YAAY,gCAAgCP,MAAM,CAAC,UAAW,GAAOQ,YAAYZ,EAAIa,GAAG,CAAC,CAACL,IAAI,QAAQM,GAAG,WAAW,MAAO,CAACZ,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAW,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,KAAK,CAACS,YAAY,qBAAqB,CAACX,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIS,GAAG,0BAA0BP,EAAG,QAAQ,CAACS,YAAY,aAAaP,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,MAAM,CAACS,YAAY,iBAAiB,CAACT,EAAG,MAAM,CAACS,YAAY,gBAAgB,CAACT,EAAG,WAAW,CAACS,YAAY,iCAAiCP,MAAM,CAAC,KAAO,QAAQE,GAAG,CAAC,MAAQN,EAAIiB,YAAY,CAACjB,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,uBAAuB,OAAOP,EAAG,WAAW,CAACS,YAAY,iCAAiCP,MAAM,CAAC,KAAO,SAAS,SAA0C,IAA/BJ,EAAIkB,gBAAgBC,QAAcb,GAAG,CAAC,MAAQN,EAAIoB,sBAAsB,CAACpB,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,8BAA8B,OAAOP,EAAG,WAAW,CAACS,YAAY,+CAA+CP,MAAM,CAAC,KAAO,SAAS,KAAO,SAAS,SAA0C,IAA/BJ,EAAIkB,gBAAgBC,QAAcb,GAAG,CAAC,MAAQN,EAAIqB,cAAc,CAACrB,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,8BAA8B,QAAQ,GAAGP,EAAG,MAAM,CAACS,YAAY,gBAAgB,CAACT,EAAG,WAAW,CAACS,YAAY,mBAAmBP,MAAM,CAAC,KAAO,YAAYE,GAAG,CAAC,MAAQN,EAAIsB,mBAAmB,CAACtB,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,gCAAgC,OAAOP,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,cAAgBJ,EAAIuB,aAAa,gBAAiB,IAAQ,CAACrB,EAAG,WAAW,CAACS,YAAY,mBAAmBP,MAAM,CAAC,KAAO,WAAW,CAACJ,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,8BAA8B,QAAQ,IAAI,QAAQ,KAAKe,OAAM,MAAS,CAACtB,EAAG,MAAM,CAACS,YAAY,gBAAgB,CAACT,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIyB,QAAQ,cAAczB,EAAI0B,MAAM,OAAUC,GAAWA,EAAOnB,IAAI,KAAO,SAAS,WAAa,CACtwDoB,QAAS5B,EAAI6B,YACbC,SAAU9B,EAAI8B,SACdC,MAAO/B,EAAI0B,MAAMP,OACjBa,SAAUhC,EAAIiC,cACd,QAAUjC,EAAIkC,QAAQ,gBAAgB,CACtChB,gBAAiBlB,EAAIkB,gBACrBc,SAAUhC,EAAImC,eACdC,iBAAkBT,IAAU,CAC1BU,SAAUV,EAAOW,UAAYX,EAAOY,UAErC3B,YAAYZ,EAAIa,GAAG,CAACb,EAAIwC,GAAIxC,EAAIyC,iBAAiB,SAASC,GAAK,MAAO,CAAClC,IAAIkC,EAAI5B,GAAG,SAAS6B,EAAMhB,EAAQiB,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACM,IAAIkC,GAAK,CAAEf,EAAOW,SAAUpC,EAAG,UAAU,CAAC2C,YAAY,CAAC,OAAS,UAAUzC,MAAM,CAAC,MAAQuC,EAAK,YAAc,SAAS3C,EAAI8C,eAAeJ,IAAQpC,GAAG,CAAC,OAASyC,GAAK/C,EAAIgD,aAAaD,EAAEE,OAAOC,MAAOvB,EAAOnB,IAAKkC,MAAQxC,EAAG,OAAO,CAAC2C,YAAY,CAAC,QAAU,OAAO,cAAc,WAAW,CAAE,CAAC,KAAM,YAAa,mBAAmBM,SAAST,IAAQC,EAAMzC,EAAG,SAAS,CAAC2C,YAAY,CAAC,OAAS,UAAU,cAAc,MAAM,QAAU,MAAM,YAAY,QAAQzC,MAAM,CAAC,KAAO,QAAQE,GAAG,CAAC,MAAQ,SAAS8C,GAAQ,OAAOpD,EAAIqD,SAASV,IAAO,WAAa,SAASS,GAAQA,EAAOH,OAAOK,MAAMC,QAAU,KAAK,WAAa,SAASH,GAAQA,EAAOH,OAAOK,MAAMC,QAAU,UAAUvD,EAAIwD,KAAKtD,EAAG,OAAO,CAACoD,MAAO,CAAC,KAAM,YAAa,mBAAmBH,SAAST,IAAQC,EAAO,kBAAoB,GAAIrC,GAAG,CAAC,MAAQ,SAAS8C,GAAQ,CAAC,KAAM,YAAa,mBAAmBD,SAAST,IAAQC,GAAO3C,EAAIqD,SAASV,MAAgB,CAAC3C,EAAIe,GAAGf,EAAIgB,GAAG2B,GAAQ,SAAS,IAAI,SAAQ,CAACnC,IAAI,YAAYM,GAAG,SAAS6B,EAAMhB,EAAQiB,GAAO,MAAO,CAAC1C,EAAG,MAAM,CAACS,YAAY,2BAA2B,CAAEgB,EAAOW,SAAU,CAACpC,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQE,GAAG,CAAC,MAAQmD,IAAMzD,EAAI0D,KAAK/B,EAAOnB,OAAO,CAACR,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIS,GAAG,uBAAuBP,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,oBAAoBE,GAAG,CAAC,QAAUqD,IAAM3D,EAAI4D,OAAOjC,EAAOnB,OAAO,CAACN,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACJ,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIS,GAAG,0BAA0B,IAAI,CAACP,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQE,GAAG,CAAC,MAAQmD,IAAMzD,EAAI6D,KAAKlC,EAAOnB,OAAO,CAACR,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIS,GAAG,uBAAuBP,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQE,GAAG,CAAC,MAAQmD,IAAMzD,EAAI8D,aAAanC,KAAU,CAACzB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAUJ,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIS,GAAG,oBAAoB,MAAM,GAAGP,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,qBAAqBE,GAAG,CAAC,QAAUqD,IAAM3D,EAAI+D,WAAWpC,KAAU,CAACzB,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACJ,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIS,GAAG,0BAA0B,KAAK,OAAO,MAAK,MAAS,MAEhgEC,EAAkB,G,wFC6ItB,IAAAsD,EAAA,GAEe,OACfC,WAAA,CACAC,MAAAC,QAEAC,OAAA,CAAAC,QACAC,SAAA,GAGAC,OACA,UACA,KAAAC,MACA9C,MAAA,GACA+C,QAAA,EACAvC,SAAA,EACAL,YAAA,EACAC,SAAA,GACAW,gBAAA,CACA,YACA,KACA,WACA,aACA,YACA,kBACA,mBAEAvB,gBAAA,GACAwD,cAAAC,aAAAC,QAAA,kBACAnD,QAAA,CACA,CACAoD,MAAA,IACAC,UAAA,QACAC,MAAA,GACAC,cAAArC,EAAAhB,EAAAiB,KACA,KAAAf,YAAA,QAAAC,SAAAc,EAAA,GAGA,CACAiC,MAAA,KAAApE,GAAA,+BACAqE,UAAA,YACAlE,YAAA,CAAAoE,aAAA,aACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,gCACAqE,UAAA,KACAlE,YAAA,CAAAoE,aAAA,MACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,8BACAqE,UAAA,WACAlE,YAAA,CAAAoE,aAAA,YACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,gCACAqE,UAAA,aACAlE,YAAA,CAAAoE,aAAA,cACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,oCACAqE,UAAA,YACAlE,YAAA,CAAAoE,aAAA,aACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,oCACAqE,UAAA,kBACAlE,YAAA,CAAAoE,aAAA,mBACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,oCACAqE,UAAA,kBACAlE,YAAA,CAAAoE,aAAA,mBACAD,MAAA,KAEA,CACAF,MAAA,KAAApE,GAAA,sBACAqE,UAAA,YACAlE,YAAA,CAAAoE,aAAA,aACAD,MAAA,IACAE,MAAA,aAKAC,UACA,SAAAR,cAGA,OAFA,KAAAS,SAAAC,QAAA,sCACA,KAAAC,QAAAC,KAAA,aAGA,KAAAC,mBAEAC,QAAA,CACA1B,aAAAnC,GAEA,MAAA8D,EAAA,IACA9D,EACAnB,IAAA,OAAAkF,KAAAC,MACAC,QAAAC,EACAvD,UAAA,EACAC,OAAA,EACAuD,WAAAnE,EAAAmE,WAAA,YACAC,GAAA,IAIA,KAAArE,MAAA,CAAA+D,KAAA,KAAA/D,OACA,KAAAG,YAAA,EACAmC,EAAA,KAAAtC,MAAAsE,IAAAC,IAAA,IAAAA,KACA,KAAA/E,gBAAA,GAGA,KAAAgF,UAAA,KACA,MAAAC,EAAAC,SAAAC,cAAA,mBACAF,IACAA,EAAAG,UAAA,MAKAxD,eAAAgC,GAAA,IAAAyB,EACA,eAAAA,EAAA,KAAA9E,QAAA+E,KAAAC,KAAA3B,sBAAA,IAAAyB,OAAA,EAAAA,EAAA1B,QAAAC,GAGA9B,aAAAE,EAAA1C,EAAAkG,GACA,MAAAC,EAAA,SAAAjF,OACAuB,EAAA0D,EAAAH,KAAAP,KAAAzF,SACAyC,IACAA,EAAAyD,GAAAxD,EACA,KAAAxB,MAAAiF,IAGA9C,KAAArD,GACA,MAAAmG,EAAA,SAAAjF,OACAuB,EAAA0D,EAAAH,KAAAP,KAAAzF,SACAyC,IACAe,EAAA2C,EAAAX,IAAAC,IAAA,IAAAA,KACAhD,EAAAX,UAAA,EACA,KAAAZ,MAAAiF,IAIA,WAAAnG,GACA,IACA,MAAAyC,EAAA,KAAAvB,MAAA8E,KAAAP,KAAAzF,SACA,IAAAyC,IAAA,KAAA2D,aAAA3D,GAAA,OAEA,KAAAwB,QAAA,EACA,MAAAoC,EAAA,IAAA5D,UACA4D,EAAAvE,gBACAuE,EAAAtE,YAEAuE,OAAAC,KAAA,gBACArF,MAAA,CAAAmF,GACAG,OAAA,KAAAtC,gBAGA,KAAAhD,MAAA,KAAAA,MAAAsE,IAAAC,GACAA,EAAAzF,QAAA,IAAAyF,EAAA3D,UAAA,EAAAC,OAAA,GAAA0D,GAEAjC,EAAA,KAAAtC,MAAAsE,IAAAC,IAAA,IAAAA,KACA,KAAAd,SAAA8B,QAAA,sBACA,MAAAC,GACA,KAAA/B,SAAA+B,MAAA,uBACA,QACA,KAAAzC,QAAA,IAIAb,OAAApD,GACA,MAAA2G,EAAA,KAAAzF,MAAA0F,UAAAnB,KAAAzF,SACA,QAAA2G,EAAA,OAEA,MAAAlE,EAAA,KAAAvB,MAAAyF,GAEA,GAAAlE,EAAAV,MAEA,KAAAb,MAAA,KAAAA,MAAA2F,OAAApB,KAAAzF,aACA,CAEA,MAAAmG,EAAA,SAAAjF,OACA4F,EAAAtD,EAAAwC,KAAAP,KAAAzF,SACA8G,IACAC,OAAAC,OAAAvE,EAAA,IAAAqE,WACArE,EAAAX,SACA,KAAAZ,MAAAiF,KAKA1F,YACA,KAAAS,MAAA,CACA,CACAlB,IAAA,OAAAkF,KAAAC,MACAG,UAAA,GACAC,GAAA,GACA0B,SAAA,KACAC,WAAA,GACAC,UAAA,GACAC,gBAAA,GACAC,gBAAA,GACAvF,UAAA,EACAC,OAAA,MAEA,KAAAb,OAEA,KAAAG,YAAA,EACAmC,EAAA,KAAAtC,MAAAsE,IAAAC,IAAA,IAAAA,KACA,KAAA/E,gBAAA,IAGA0F,aAAAkB,GAAA,IAAAC,EACA,WAAAA,EAAAD,EAAAhC,iBAAA,IAAAiC,MAAAC,OAEA,OADA,KAAA7C,SAAA+B,MAAA,0BACA,EAEA,8BAAAe,KAAAH,EAAA/B,IAEA,OADA,KAAAZ,SAAA+B,MAAA,sBACA,EAEA,YAAAe,KAAAH,EAAAL,UAEA,OADA,KAAAtC,SAAA+B,MAAA,6BACA,EAEA,MAAAgB,EAAA,KAAAxG,MAAAyG,KAAAC,KAAArC,KAAA+B,EAAA/B,IAAAqC,EAAA5H,MAAAsH,EAAAtH,KACA,OAAA0H,IACA,KAAA/C,SAAA+B,MAAA,8BACA,IAKA,wBACA,IACA,KAAAhF,SAAA,EACA,MAAAmG,QAAAvB,OAAAwB,IAAA,eACAC,OAAA,CACAC,QAAA,EACAxB,OAAA,KAAAtC,iBAGA,KAAAhD,MAAA2G,EAAA9D,KAAAyB,IAAAC,IAAA,IAAAwC,EAAAC,EAAA,UACAzC,EACAzF,KAAA,QAAAiI,EAAAxC,EAAAL,UAAA,IAAA6C,OAAA,EAAAA,EAAAE,aAAA,QAAA1C,EAAAH,UACA2B,UAAA,QAAAiB,EAAAzC,EAAAwB,gBAAA,IAAAiB,OAAA,EAAAA,EAAAC,aAAA,KACApG,OAAA,KAEAyB,EAAA,KAAAtC,MAAAsE,IAAAC,IAAA,IAAAA,KACA,MAAAiB,GAAA,IAAA0B,EACA,KAAAzD,SAAA+B,OAAA,QAAA0B,EAAA1B,EAAAmB,gBAAA,IAAAO,GAAA,QAAAA,IAAArE,YAAA,IAAAqE,OAAA,EAAAA,EAAA1B,QAAA,wBACA,QACA,KAAAhF,SAAA,IAGAD,aAAA4G,GACA,KAAAhH,YAAAgH,GAGA,iBAAAlH,GACA,IACAA,EAAAiE,UACAkB,OAAAgC,OAAA,eAAAnH,EAAAiE,GAAA,CACA2C,OAAA,CAAAvB,OAAA,KAAAtC,iBAGA,KAAAhD,MAAA,KAAAA,MAAA2F,OAAAe,KAAA5H,MAAAmB,EAAAnB,KACA,KAAAU,gBAAA,KAAAA,gBAAAmG,OAAA7G,OAAAmB,EAAAnB,KACA,KAAA2E,SAAA8B,QAAA,0BAEA,KAAAvF,MAAA,KAAAA,MAAA2F,OAAAe,KAAA5H,MAAAmB,EAAAnB,KACA,KAAAU,gBAAA,KAAAA,gBAAAmG,OAAA7G,OAAAmB,EAAAnB,MAEA,MAAA0G,GAAA,IAAA6B,EACA,KAAA5D,SAAA+B,OAAA,QAAA6B,EAAA7B,EAAAmB,gBAAA,IAAAU,GAAA,QAAAA,IAAAxE,YAAA,IAAAwE,OAAA,EAAAA,EAAA7B,QAAA,+BACA,KAAA3B,oBAIApD,eAAAjB,GACA,KAAAA,mBAGA,oBACA,IACA,MAAA8H,EAAA,KAAAtH,MACA2F,OAAAS,GAAA,KAAA5G,gBAAAiC,SAAA2E,EAAAtH,MACAwF,IAAA8B,KAAAlC,IACAyB,OAAAzB,MAEA,OAAAoD,EAAA7H,OAEA,YADA,KAAAgE,SAAAC,QAAA,8CAIA0B,OAAAC,KAAA,4BACAkC,IAAAD,EACAhC,OAAA,KAAAtC,gBAGA,KAAAhD,MAAA,KAAAA,MAAA2F,OAAAS,IAAA,KAAA5G,gBAAAiC,SAAA2E,EAAAtH,MACA,KAAAU,gBAAA,GACA,KAAAiE,SAAA8B,QAAA,yCACA,MAAAC,GAAA,IAAAgC,EACA,KAAA/D,SAAA+B,OAAA,QAAAgC,EAAAhC,EAAAmB,gBAAA,IAAAa,GAAA,QAAAA,IAAA3E,YAAA,IAAA2E,OAAA,EAAAA,EAAAhC,QAAA,2BAIA,yBACA,IACA,MAAAmB,QAAAvB,OAAAwB,IAAA,wBACAa,aAAA,SAGAC,EAAAC,OAAAC,IAAAC,gBAAA,IAAAC,KAAA,CAAAnB,EAAA9D,QACAkF,EAAArD,SAAAsD,cAAA,KACAD,EAAAE,KAAAP,EACAK,EAAAG,aAAA,iCACAxD,SAAAyD,KAAAC,YAAAL,GACAA,EAAAhG,QACA2C,SAAAyD,KAAAE,YAAAN,GACAJ,OAAAC,IAAAU,gBAAAZ,GAEA,KAAAjE,SAAA8B,QAAA,oCACA,MAAAC,GACA,KAAA/B,SAAA+B,MAAA,+BACA+C,QAAA/C,MAAA,2BAAAA,KAIA,mBAAAgD,GACA,WAAAC,GAAAD,EAEA,GAAAC,EAAAC,KAAAC,SAAA,QAKA,IACA,MAAAC,EAAA,IAAAC,SACAD,EAAAE,OAAA,OAAAL,GACAG,EAAAE,OAAA,cAAA9F,qBAEAoC,OAAAC,KAAA,qBAAAuD,EAAA,CACAG,QAAA,+CAGA,KAAAlF,kBACA,KAAAJ,SAAA8B,QAAA,+BACA,MAAAC,GAAA,IAAAwD,EACA,KAAAvF,SAAA+B,OAAA,QAAAwD,EAAAxD,EAAAmB,gBAAA,IAAAqC,GAAA,QAAAA,IAAAnG,YAAA,IAAAmG,OAAA,EAAAA,EAAAxD,QAAA,+BAhBA,KAAA/B,SAAA+B,MAAA,2BAoBA,4BACA,IACA,MAAAyD,EAAA,KAAAjJ,MAAA2F,OAAAS,GAAA,KAAA5G,gBAAAiC,SAAA2E,EAAAtH,MAGAiK,EAAA,CACA,YACA,KACA,WACA,aACA,YACA,kBACA,mBAGAG,EAAA,CACAH,EAAAI,KAAA,QACAF,EAAA3E,IAAA8B,GACA2C,EAAAzE,IAAA8E,GAAAhD,EAAAgD,IAAA,IAAAD,KAAA,OAEAA,KAAA,MAGAE,EAAA,IAAAvB,KAAA,CAAAoB,GAAA,CAAAI,KAAA,4BACA5B,EAAAC,OAAAC,IAAAC,gBAAAwB,GACAtB,EAAArD,SAAAsD,cAAA,KACAD,EAAAE,KAAAP,EACAK,EAAAG,aAAA,iCACAxD,SAAAyD,KAAAC,YAAAL,GACAA,EAAAhG,QACA2C,SAAAyD,KAAAE,YAAAN,GACAJ,OAAAC,IAAAU,gBAAAZ,GAEA,KAAAjE,SAAA8B,QAAA,+BACA,MAAAC,GACA,KAAA/B,SAAA+B,MAAA,0BACA+C,QAAA/C,MAAA,sBAAAA,OCriBkW,I,wBCQ9V+D,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBXlL,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACS,YAAY,+BAA+BP,MAAM,CAAC,UAAW,GAAOQ,YAAYZ,EAAIa,GAAG,CAAC,CAACL,IAAI,QAAQM,GAAG,WAAW,MAAO,CAACZ,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,MAAQ,WAAW,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,KAAK,CAACS,YAAY,qBAAqB,CAACX,EAAIe,GAAG,yBAAyBb,EAAG,QAAQ,CAACS,YAAY,aAAaP,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,WAAW,CAACS,YAAY,mBAAmBL,GAAG,CAAC,MAAQN,EAAIkL,YAAYtK,YAAYZ,EAAIa,GAAG,CAAC,CAACL,IAAI,OAAOM,GAAG,WAAW,MAAO,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYoB,OAAM,MAAS,CAACxB,EAAIe,GAAG,oBAAoB,IAAI,KAAKS,OAAM,MAAS,CAACtB,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,QAAQ,OAAS,IAAI,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,oBAAoB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,cAAc+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOC,GAAIC,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,KAAMG,IAAME,WAAW,gBAAgB,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,oBAAoB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,cAAc+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOM,GAAIJ,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,KAAMG,IAAME,WAAW,gBAAgB,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,0BAA0B,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,gBAAgB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOO,OAAQL,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,SAAUG,IAAME,WAAW,oBAAoB,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,gBAAgB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,eAAe+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOQ,YAAaN,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,cAAeG,IAAME,WAAW,yBAAyB,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,qBAAqB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,oBAAoB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOS,iBAAkBP,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,mBAAoBG,IAAME,WAAW,8BAA8B,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,qBAAqB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,oBAAoB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOU,iBAAkBR,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,mBAAoBG,IAAME,WAAW,8BAA8B,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,sBAAsB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,qBAAqB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOW,kBAAmBT,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,oBAAqBG,IAAME,WAAW,+BAA+B,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,wBAAwB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,uBAAuB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOY,oBAAqBV,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,sBAAuBG,IAAME,WAAW,iCAAiC,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,uBAAuB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,sBAAsB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOa,mBAAoBX,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,qBAAsBG,IAAME,WAAW,gCAAgC,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,aAAa,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,YAAY+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOc,SAAUZ,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,WAAYG,IAAME,WAAW,sBAAsB,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,kBAAkB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,iBAAiB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOe,cAAeb,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,gBAAiBG,IAAME,WAAW,2BAA2B,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,kBAAkB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,iBAAiB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOgB,cAAed,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,gBAAiBG,IAAME,WAAW,2BAA2B,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,yBAAyB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,wBAAwB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOiB,qBAAsBf,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,uBAAwBG,IAAME,WAAW,kCAAkC,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,2BAA2B,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,0BAA0B+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOkB,uBAAwBhB,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,yBAA0BG,IAAME,WAAW,oCAAoC,GAAGvL,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,uBAAuB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,sBAAsB+K,MAAM,CAACjI,MAAOlD,EAAIoL,OAAOmB,mBAAoBjB,SAAS,SAAUC,GAAMvL,EAAIwL,KAAKxL,EAAIoL,OAAQ,qBAAsBG,IAAME,WAAW,gCAAgC,IAAI,IAAI,IAEhoJ/K,EAAkB,GCsEP,GACf0J,KAAA,YACAnG,WAAA,CACAC,MAAAC,QAEAG,SAAA,GAGAC,OACA,OACA6G,OAAA,CACAC,GAAA,GACAK,GAAA,GACAC,OAAA,GACAC,YAAA,GACAC,iBAAA,GACAC,iBAAA,GACAC,kBAAA,GACAC,oBAAA,GACAC,mBAAA,GACAC,SAAA,GACAC,cAAA,GACAC,cAAA,GACAC,qBAAA,GACAC,uBAAA,GACAC,mBAAA,MAIArH,UACA,KAAAsH,kBAEAhH,QAAA,CACA,uBACA,IACA,MAAA6C,QAAAvB,OAAAwB,IAAA,mBACA/D,EAAA8D,EAAA9D,KACA,KAAA6G,OAAA7G,EACA,MAAA2C,GACA+C,QAAA/C,MAAA,6BAAAA,GACA,KAAA/B,SAAA+B,MAAA,sCAGA,mBACA,UACAJ,OAAAC,KAAA,mBACA0D,QAAA,CACA,mCAEAZ,KAAA4C,KAAAC,UAAA,KAAAtB,UAGA,KAAAjG,SAAA8B,QAAA,wCACA,MAAAC,GACA+C,QAAA/C,MAAA,2BAAAA,GACA,KAAA/B,SAAA+B,MAAA,wCC/HiW,ICQ7V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCFA,GACfjD,WAAA,CAAA0I,aAAAC,aACAC,MAAA,CACAC,WAAA,CACA9B,KAAA+B,OACAC,QAAA,SAGAzI,OACA,OACAlE,UAAA,KAAAyM,aAGAG,MAAA,CACA,QACAC,WAAA,EACAC,QAAAC,GACA,MAAAC,EAAAD,EAAAE,KAAAC,QAAA,gBACAF,IAAA,KAAAhN,YACA,KAAAA,UAAAgN,EACApD,QAAAuD,IAAA,2BAAAH,OAKA7H,QAAA,CACAjF,gBAAAC,GACAyJ,QAAAuD,IAAA,mBAAAhN,GACA,KAAA6E,QAAAkI,QAAA,CACAE,KAAA,UACAH,KAAA,IAAA9M,EACAkN,MAAA,KAAAC,OAAAD,QACAE,MAAAC,IACAA,EAAAC,QAAA3K,SAAA,kCACA8G,QAAA/C,MAAA,qBAAA2G,QCnD+U,ICO3U,EAAY,eACd,EACA9N,EACAW,GACA,EACA,KACA,KACA,MAIa,e,6CClBA,QACb8E,QAAS,CACPnC,SAASV,GACP,MAAMoL,EAAW3H,SAASsD,cAAc,YACxCqE,EAAS7K,MAAQP,EACjByD,SAASyD,KAAKC,YAAYiE,GAC1BA,EAASC,SACT5H,SAAS6H,YAAY,QACrB7H,SAASyD,KAAKE,YAAYgE,GAC1B9N,KAAKkF,SAAS8B,QAAQ,2B,sFCT5B,W,oCCAA,W,oCCEA,IAAIiH,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvBH,EAAE,CAAEjL,OAAQ,WAAYqL,OAAO,EAAMC,MAAM,GAAQ,CACjDpG,KAAM,SAAcrH,GAGlB,OAFAuN,EAASpO,MACTmO,EAAUtN,GACHqN,EAAQlO,MAAM,SAAUiD,EAAOsL,GACpC,GAAI1N,EAAGoC,GAAQ,OAAOsL,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC", "file": "static/js/chunk-cfa15118.8a0cb356.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-tabs',{attrs:{\"active-key\":_vm.activeTab},on:{\"change\":_vm.handleTabChange}},[_c('a-tab-pane',{key:\"host\",attrs:{\"tab\":_vm.$t('sidebar.hostConfig')}},[_c('HostConfig')],1),_c('a-tab-pane',{key:\"cbh\",attrs:{\"tab\":_vm.$t('sidebar.cbhConfig')}},[_c('CbhConfig')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid host-config-card\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('a-row',{attrs:{\"type\":\"flex\",\"align\":\"middle\"}},[_c('a-col',{attrs:{\"span\":12}},[_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('hostConfig.title')))])]),_c('a-col',{staticClass:\"text-right\",attrs:{\"span\":12}},[_c('div',{staticClass:\"button-groups\"},[_c('div',{staticClass:\"button-group\"},[_c('a-button',{staticClass:\"nav-style-button action-button\",attrs:{\"icon\":\"plus\"},on:{\"click\":_vm.addNewRow}},[_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.addHost'))+\" \")]),_c('a-button',{staticClass:\"nav-style-button action-button\",attrs:{\"icon\":\"export\",\"disabled\":_vm.selectedRowKeys.length === 0},on:{\"click\":_vm.exportSelectedHosts}},[_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.exportSelected'))+\" \")]),_c('a-button',{staticClass:\"nav-style-button action-button delete-button\",attrs:{\"type\":\"danger\",\"icon\":\"delete\",\"disabled\":_vm.selectedRowKeys.length === 0},on:{\"click\":_vm.batchDelete}},[_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.deleteSelected'))+\" \")])],1),_c('div',{staticClass:\"button-group\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"icon\":\"download\"},on:{\"click\":_vm.downloadTemplate}},[_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.downloadTemplate'))+\" \")]),_c('a-upload',{attrs:{\"name\":\"file\",\"customRequest\":_vm.handleUpload,\"showUploadList\":false}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"icon\":\"upload\"}},[_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.uploadTemplate'))+\" \")])],1)],1)])])],1)]},proxy:true}])},[_c('div',{staticClass:\"config-table\"},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.hosts,\"rowKey\":(record) => record.key,\"size\":\"middle\",\"pagination\":{\n        current: _vm.currentPage,\n        pageSize: _vm.pageSize,\n        total: _vm.hosts.length,\n        onChange: _vm.onPageChange,\n      },\"loading\":_vm.loading,\"row-selection\":{\n        selectedRowKeys: _vm.selectedRowKeys,\n        onChange: _vm.onSelectChange,\n        getCheckboxProps: record => ({\n          disabled: record.editable || record.isNew\n        })\n      }},scopedSlots:_vm._u([_vm._l((_vm.editableColumns),function(col){return {key:col,fn:function(text, record, index){return [_c('div',{key:col},[(record.editable)?_c('a-input',{staticStyle:{\"margin\":\"-5px 0\"},attrs:{\"value\":text,\"placeholder\":`Enter ${_vm.getColumnTitle(col)}`},on:{\"change\":e => _vm.handleChange(e.target.value, record.key, col)}}):_c('span',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"}},[(['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text)?_c('a-icon',{staticStyle:{\"cursor\":\"pointer\",\"margin-left\":\"4px\",\"opacity\":\"0.6\",\"font-size\":\"12px\"},attrs:{\"type\":\"copy\"},on:{\"click\":function($event){return _vm.copyText(text)},\"mouseenter\":function($event){$event.target.style.opacity = '1'},\"mouseleave\":function($event){$event.target.style.opacity = '0.6'}}}):_vm._e(),_c('span',{style:(['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''),on:{\"click\":function($event){['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? _vm.copyText(text) : null}}},[_vm._v(_vm._s(text || '-'))])],1)],1)]}}}),{key:\"operation\",fn:function(text, record, index){return [_c('div',{staticClass:\"editable-row-operations\"},[(record.editable)?[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.save(record.key)}},[_vm._v(_vm._s(_vm.$t('hostConfig.save')))]),_c('a-popconfirm',{attrs:{\"title\":\"Discard changes?\"},on:{\"confirm\":() => _vm.cancel(record.key)}},[_c('a-button',{attrs:{\"type\":\"link\",\"danger\":\"\"}},[_vm._v(_vm._s(_vm.$t('hostConfig.cancel')))])],1)]:[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.edit(record.key)}},[_vm._v(_vm._s(_vm.$t('hostConfig.edit')))]),_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":() => _vm.copyNodeInfo(record)}},[_c('a-icon',{attrs:{\"type\":\"copy\"}}),_vm._v(\" \"+_vm._s(_vm.$t('hostConfig.copy'))+\" \")],1),_c('a-popconfirm',{attrs:{\"title\":\"Confirm deletion?\"},on:{\"confirm\":() => _vm.deleteHost(record)}},[_c('a-button',{attrs:{\"type\":\"link\",\"danger\":\"\"}},[_vm._v(_vm._s(_vm.$t('hostConfig.delete')))])],1)]],2)]}}],null,true)})],1)])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card :bordered=\"false\" class=\"header-solid host-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">{{ $t('hostConfig.title') }}</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <!-- 在表格上方添加分组按钮布局 -->\r\n          <div class=\"button-groups\">\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  class=\"nav-style-button action-button\"\r\n                  icon=\"plus\"\r\n                  @click=\"addNewRow\">\r\n                {{ $t('hostConfig.addHost') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                icon=\"export\"\r\n                class=\"nav-style-button action-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"exportSelectedHosts\"\r\n              >\r\n                {{ $t('hostConfig.exportSelected') }}\r\n              </a-button>\r\n\r\n              <a-button\r\n                type=\"danger\"\r\n                icon=\"delete\"\r\n                class=\"nav-style-button action-button delete-button\"\r\n                :disabled=\"selectedRowKeys.length === 0\"\r\n                @click=\"batchDelete\"\r\n              >\r\n                {{ $t('hostConfig.deleteSelected') }}\r\n              </a-button>\r\n            </div>\r\n\r\n            <div class=\"button-group\">\r\n              <a-button\r\n                  icon=\"download\"\r\n                  class=\"nav-style-button\"\r\n                  @click=\"downloadTemplate\"\r\n              >\r\n                {{ $t('hostConfig.downloadTemplate') }}\r\n              </a-button>\r\n\r\n              <a-upload\r\n                name=\"file\"\r\n                :customRequest=\"handleUpload\"\r\n                :showUploadList=\"false\"\r\n              >\r\n                <a-button\r\n                    icon=\"upload\"\r\n                    class=\"nav-style-button\"\r\n                >\r\n                  {{ $t('hostConfig.uploadTemplate') }}\r\n                </a-button>\r\n              </a-upload>\r\n            </div>\r\n          </div>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <div class=\"config-table\">\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"hosts\"\r\n        :rowKey=\"(record) => record.key\"\r\n        size=\"middle\"\r\n        :pagination=\"{\r\n          current: currentPage,\r\n          pageSize: pageSize,\r\n          total: hosts.length,\r\n          onChange: onPageChange,\r\n        }\"\r\n        :loading=\"loading\"\r\n        :row-selection=\"{\r\n          selectedRowKeys: selectedRowKeys,\r\n          onChange: onSelectChange,\r\n          getCheckboxProps: record => ({\r\n            disabled: record.editable || record.isNew\r\n          })\r\n        }\"\r\n      >\r\n      <template\r\n        v-for=\"col in editableColumns\"\r\n        :slot=\"col\"\r\n        slot-scope=\"text, record, index\"\r\n      >\r\n        <div :key=\"col\">\r\n          <a-input\r\n            v-if=\"record.editable\"\r\n            style=\"margin: -5px 0\"\r\n            :value=\"text\"\r\n            @change=\"e => handleChange(e.target.value, record.key, col)\"\r\n            :placeholder=\"`Enter ${getColumnTitle(col)}`\"\r\n          />\r\n          <span v-else style=\"display: flex; align-items: center;\">\r\n            <a-icon \r\n              v-if=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text\"\r\n              type=\"copy\" \r\n              style=\"cursor: pointer; margin-left: 4px; opacity: 0.6; font-size: 12px;\"\r\n              @click=\"copyText(text)\"\r\n              @mouseenter=\"$event.target.style.opacity = '1'\"\r\n              @mouseleave=\"$event.target.style.opacity = '0.6'\"\r\n            />\r\n            <span \r\n              :style=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? 'cursor: pointer' : ''\"\r\n              @click=\"['ip', 'login_pwd', 'switch_root_pwd'].includes(col) && text ? copyText(text) : null\"\r\n            >{{ text || '-' }}</span>            \r\n          </span>\r\n        </div>\r\n      </template>\r\n\r\n      <template #operation=\"text, record, index\">\r\n        <div class=\"editable-row-operations\">\r\n          <template v-if=\"record.editable\">\r\n            <a-button type=\"link\" @click=\"() => save(record.key)\">{{ $t('hostConfig.save') }}</a-button>\r\n            <a-popconfirm\r\n              title=\"Discard changes?\"\r\n              @confirm=\"() => cancel(record.key)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('hostConfig.cancel') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n          <template v-else>\r\n            <a-button type=\"link\" @click=\"() => edit(record.key)\">{{ $t('hostConfig.edit') }}</a-button>\r\n            <a-button type=\"link\" @click=\"() => copyNodeInfo(record)\">\r\n              <a-icon type=\"copy\" />\r\n              {{ $t('hostConfig.copy') }}\r\n            </a-button>\r\n            <a-popconfirm\r\n              title=\"Confirm deletion?\"\r\n              @confirm=\"() => deleteHost(record)\"\r\n            >\r\n              <a-button type=\"link\" danger>{{ $t('hostConfig.delete') }}</a-button>\r\n            </a-popconfirm>\r\n          </template>\r\n        </div>\r\n      </template>\r\n      </a-table>\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\n// 使用 ant-design-vue 内置的图标\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\nimport CopyMixin from '@/mixins/CopyMixin';\r\n\r\nlet cacheData = [];\r\n\r\nexport default {\r\n  components: {\r\n    AIcon: Icon,\r\n  },\r\n  mixins: [CopyMixin],\r\n  computed: {\r\n    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式\r\n  },\r\n  data() {\r\n    return {\r\n      ...this.$data,\r\n      hosts: [],\r\n      saving: false,\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      editableColumns: [\r\n        'host_name',\r\n        'ip',\r\n        'ssh_port',\r\n        'login_user',\r\n        'login_pwd',\r\n        'switch_root_cmd',\r\n        'switch_root_pwd',\r\n      ],\r\n      selectedRowKeys: [],\r\n      currentDbFile: localStorage.getItem('currentProject'),\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          width: 80,\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * this.pageSize) + index + 1;\r\n          },\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          scopedSlots: { customRender: 'host_name' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          scopedSlots: { customRender: 'ip' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.sshPort'),\r\n          dataIndex: 'ssh_port',\r\n          scopedSlots: { customRender: 'ssh_port' },\r\n          width: 100,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginUser'),\r\n          dataIndex: 'login_user',\r\n          scopedSlots: { customRender: 'login_user' },\r\n          width: 120,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.loginPassword'),\r\n          dataIndex: 'login_pwd',\r\n          scopedSlots: { customRender: 'login_pwd' },\r\n          width: 150,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootCmd'),\r\n          dataIndex: 'switch_root_cmd',\r\n          scopedSlots: { customRender: 'switch_root_cmd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.switchRootPwd'),\r\n          dataIndex: 'switch_root_pwd',\r\n          scopedSlots: { customRender: 'switch_root_pwd' },\r\n          width: 180,\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.actions'),\r\n          dataIndex: 'operation',\r\n          scopedSlots: { customRender: 'operation' },\r\n          width: 150,\r\n          align: 'center',\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    if (!this.currentDbFile) {\r\n      this.$message.warning('Please select a project first');\r\n      this.$router.push('/projects');\r\n      return;\r\n    }\r\n    this.fetchHostConfig();\r\n  },\r\n  methods: {\r\n    copyNodeInfo(record) {\r\n      // 创建新的节点数据，复制原节点的所有属性\r\n      const newRecord = {\r\n        ...record,\r\n        key: `new-${Date.now()}`,\r\n        id: undefined,\r\n        editable: true,\r\n        isNew: true,\r\n        host_name: `${record.host_name || ''}_copy`,\r\n        ip: '' // Clear IP as it should be unique\r\n      };\r\n      \r\n      // 在表格开头添加新行\r\n      this.hosts = [newRecord, ...this.hosts];\r\n      this.currentPage = 1; // Reset to first page to show the new row\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n      \r\n      // 滚动到顶部以显示新添加的行\r\n      this.$nextTick(() => {\r\n        const tableBody = document.querySelector('.ant-table-body');\r\n        if (tableBody) {\r\n          tableBody.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    \r\n    getColumnTitle(dataIndex) {\r\n      return this.columns.find((c) => c.dataIndex === dataIndex)?.title || dataIndex;\r\n    },\r\n\r\n    handleChange(value, key, column) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        target[column] = value;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n    edit(key) {\r\n      const newData = [...this.hosts];\r\n      const target = newData.find((item) => item.key === key);\r\n      if (target) {\r\n        cacheData = newData.map((item) => ({ ...item }));\r\n        target.editable = true;\r\n        this.hosts = newData;\r\n      }\r\n    },\r\n\r\n    async save(key) {\r\n      try {\r\n        const target = this.hosts.find((item) => item.key === key);\r\n        if (!target || !this.validateHost(target)) return;\r\n\r\n        this.saving = true;\r\n        const hostData = { ...target };\r\n        delete hostData.editable;\r\n        delete hostData.isNew;\r\n\r\n        await axios.post('/api/config/', {\r\n          hosts: [hostData],\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.map((item) =>\r\n          item.key === key ? { ...item, editable: false, isNew: false } : item\r\n        );\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n        this.$message.success('Saved successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to save host');\r\n      } finally {\r\n        this.saving = false;\r\n      }\r\n    },\r\n\r\n    cancel(key) {\r\n      const targetIndex = this.hosts.findIndex((item) => item.key === key);\r\n      if (targetIndex === -1) return;\r\n      \r\n      const target = this.hosts[targetIndex];\r\n      \r\n      if (target.isNew) {\r\n        // For new rows, remove them completely\r\n        this.hosts = this.hosts.filter(item => item.key !== key);\r\n      } else {\r\n        // For existing rows, revert changes\r\n        const newData = [...this.hosts];\r\n        const cachedItem = cacheData.find((item) => item.key === key);\r\n        if (cachedItem) {\r\n          Object.assign(target, { ...cachedItem });\r\n          delete target.editable;\r\n          this.hosts = newData;\r\n        }\r\n      }\r\n    },\r\n\r\n    addNewRow() {\r\n      this.hosts = [\r\n        {\r\n          key: `new-${Date.now()}`,\r\n          host_name: '',\r\n          ip: '',\r\n          ssh_port: '22',\r\n          login_user: '',\r\n          login_pwd: '',\r\n          switch_root_cmd: '',\r\n          switch_root_pwd: '',\r\n          editable: true,\r\n          isNew: true,\r\n        },\r\n        ...this.hosts,\r\n      ];\r\n      this.currentPage = 1;\r\n      cacheData = this.hosts.map((item) => ({ ...item }));\r\n      this.selectedRowKeys = [];\r\n    },\r\n\r\n    validateHost(host) {\r\n      if (!host.host_name?.trim()) {\r\n        this.$message.error('Host name is required');\r\n        return false;\r\n      }\r\n      if (!/^(\\d{1,3}\\.){3}\\d{1,3}$/.test(host.ip)) {\r\n        this.$message.error('Invalid IP format');\r\n        return false;\r\n      }\r\n      if (!/^\\d+$/.test(host.ssh_port)) {\r\n        this.$message.error('SSH port must be numeric');\r\n        return false;\r\n      }\r\n      const exist = this.hosts.some((h) => h.ip === host.ip && h.key !== host.key);\r\n      if (exist) {\r\n        this.$message.error('IP address already exists');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    async fetchHostConfig() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/config`, {\r\n          params: {\r\n            detail: true,\r\n            dbFile: this.currentDbFile\r\n          }\r\n        });\r\n        this.hosts = response.data.map((item) => ({\r\n          ...item,\r\n          key: item.id?.toString() || `host_${item.host_name}`,\r\n          ssh_port: item.ssh_port?.toString() || '22',\r\n          isNew: false,\r\n        }));\r\n        cacheData = this.hosts.map((item) => ({ ...item }));\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to load hosts');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    onPageChange(page) {\r\n      this.currentPage = page;\r\n    },\r\n\r\n    async deleteHost(record) {\r\n      try {\r\n        if (record.id) {\r\n          await axios.delete(`/api/config/${record.id}`, {\r\n            params: { dbFile: this.currentDbFile }\r\n          });\r\n\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n          this.$message.success('Deleted successfully');\r\n        } else {\r\n          this.hosts = this.hosts.filter((h) => h.key !== record.key);\r\n          this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== record.key);\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to delete host');\r\n        await this.fetchHostConfig();\r\n      }\r\n    },\r\n\r\n    onSelectChange(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    async batchDelete() {\r\n      try {\r\n        const selectedIds = this.hosts\r\n          .filter(host => this.selectedRowKeys.includes(host.key))\r\n          .map(host => host.id)\r\n          .filter(id => id);\r\n\r\n        if (selectedIds.length === 0) {\r\n          this.$message.warning('No valid hosts selected for deletion');\r\n          return;\r\n        }\r\n\r\n        await axios.post('/api/config/batch-delete', {\r\n          ids: selectedIds,\r\n          dbFile: this.currentDbFile\r\n        });\r\n\r\n        this.hosts = this.hosts.filter(host => !this.selectedRowKeys.includes(host.key));\r\n        this.selectedRowKeys = [];\r\n        this.$message.success('Batch deletion completed successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Batch deletion failed');\r\n      }\r\n    },\r\n\r\n    async downloadTemplate() {\r\n      try {\r\n        const response = await axios.get('/api/config/template', {\r\n          responseType: 'blob'\r\n        });\r\n\r\n        const url = window.URL.createObjectURL(new Blob([response.data]));\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'hosts_template.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Template downloaded successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to download template');\r\n        console.error('Download template error:', error);\r\n      }\r\n    },\r\n\r\n    async handleUpload(options) {\r\n      const { file } = options;\r\n\r\n      if (!file.name.endsWith('.csv')) {\r\n        this.$message.error('Please upload CSV file');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('dbFile', this.currentDbFile);\r\n\r\n        await axios.post('/api/config/upload', formData, {\r\n          headers: { 'Content-Type': 'multipart/form-data' }\r\n        });\r\n\r\n        await this.fetchHostConfig();\r\n        this.$message.success('Hosts imported successfully');\r\n      } catch (error) {\r\n        this.$message.error(error.response?.data?.error || 'Failed to import hosts');\r\n      }\r\n    },\r\n\r\n    async exportSelectedHosts() {\r\n      try {\r\n        const selectedHosts = this.hosts.filter(host => this.selectedRowKeys.includes(host.key));\r\n\r\n        // Create CSV content\r\n        const headers = [\r\n          'host_name',\r\n          'ip',\r\n          'ssh_port',\r\n          'login_user',\r\n          'login_pwd',\r\n          'switch_root_cmd',\r\n          'switch_root_pwd'\r\n        ];\r\n\r\n        const csvContent = [\r\n          headers.join(','),\r\n          ...selectedHosts.map(host =>\r\n            headers.map(header => host[header] || '').join(',')\r\n          )\r\n        ].join('\\n');\r\n\r\n        // Create and trigger download\r\n        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', 'selected_hosts.csv');\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n\r\n        this.$message.success('Hosts exported successfully');\r\n      } catch (error) {\r\n        this.$message.error('Failed to export hosts');\r\n        console.error('Export hosts error:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.host-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .ant-upload-select {\r\n  display: inline-block;\r\n}\r\n\r\n/* 删除按钮保持红色 */\r\n::v-deep .delete-button {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n::v-deep .delete-button .anticon {\r\n  color: #ff4d4f !important;\r\n}\r\n\r\n/* 添加按钮组样式 */\r\n.button-groups {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 0;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  align-items: center;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .button-groups {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HostConfig.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HostConfig.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./HostConfig.vue?vue&type=template&id=534ea6ec&scoped=true\"\nimport script from \"./HostConfig.vue?vue&type=script&lang=js\"\nexport * from \"./HostConfig.vue?vue&type=script&lang=js\"\nimport style0 from \"./HostConfig.vue?vue&type=style&index=0&id=534ea6ec&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"534ea6ec\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid cbh-config-card\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('a-row',{attrs:{\"type\":\"flex\",\"align\":\"middle\"}},[_c('a-col',{attrs:{\"span\":12}},[_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(\"CBH Configuration\")])]),_c('a-col',{staticClass:\"text-right\",attrs:{\"span\":12}},[_c('a-button',{staticClass:\"nav-style-button\",on:{\"click\":_vm.saveConfig},scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{attrs:{\"type\":\"save\"}})]},proxy:true}])},[_vm._v(\" Save Config \")])],1)],1)]},proxy:true}])},[_c('a-descriptions',{attrs:{\"size\":\"small\",\"column\":1}},[_c('a-descriptions-item',{attrs:{\"label\":\"AK (Access Key)\"}},[_c('a-input',{attrs:{\"placeholder\":\"Access Key\"},model:{value:(_vm.config.ak),callback:function ($$v) {_vm.$set(_vm.config, \"ak\", $$v)},expression:\"config.ak\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"SK (Secret Key)\"}},[_c('a-input',{attrs:{\"placeholder\":\"Secret Key\"},model:{value:(_vm.config.sk),callback:function ($$v) {_vm.$set(_vm.config, \"sk\", $$v)},expression:\"config.sk\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Server (OBS Endpoint)\"}},[_c('a-input',{attrs:{\"placeholder\":\"OBS Endpoint\"},model:{value:(_vm.config.server),callback:function ($$v) {_vm.$set(_vm.config, \"server\", $$v)},expression:\"config.server\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Bucket Name\"}},[_c('a-input',{attrs:{\"placeholder\":\"Bucket Name\"},model:{value:(_vm.config.bucket_name),callback:function ($$v) {_vm.$set(_vm.config, \"bucket_name\", $$v)},expression:\"config.bucket_name\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Bucket Store Dir\"}},[_c('a-input',{attrs:{\"placeholder\":\"Bucket Store Dir\"},model:{value:(_vm.config.bucket_store_dir),callback:function ($$v) {_vm.$set(_vm.config, \"bucket_store_dir\", $$v)},expression:\"config.bucket_store_dir\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Upload File Path\"}},[_c('a-input',{attrs:{\"placeholder\":\"Upload File Path\"},model:{value:(_vm.config.upload_file_path),callback:function ($$v) {_vm.$set(_vm.config, \"upload_file_path\", $$v)},expression:\"config.upload_file_path\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Upload Object Key\"}},[_c('a-input',{attrs:{\"placeholder\":\"Upload Object Key\"},model:{value:(_vm.config.upload_object_key),callback:function ($$v) {_vm.$set(_vm.config, \"upload_object_key\", $$v)},expression:\"config.upload_object_key\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Download Object Key\"}},[_c('a-input',{attrs:{\"placeholder\":\"Download Object Key\"},model:{value:(_vm.config.download_object_key),callback:function ($$v) {_vm.$set(_vm.config, \"download_object_key\", $$v)},expression:\"config.download_object_key\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"Download File Path\"}},[_c('a-input',{attrs:{\"placeholder\":\"Download File Path\"},model:{value:(_vm.config.download_file_path),callback:function ($$v) {_vm.$set(_vm.config, \"download_file_path\", $$v)},expression:\"config.download_file_path\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH Host\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH Host\"},model:{value:(_vm.config.cbh_host),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_host\", $$v)},expression:\"config.cbh_host\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH User Name\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH User Name\"},model:{value:(_vm.config.cbh_user_name),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_user_name\", $$v)},expression:\"config.cbh_user_name\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH User Port\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH User Port\"},model:{value:(_vm.config.cbh_user_port),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_user_port\", $$v)},expression:\"config.cbh_user_port\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH Private Key Path\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH Private Key Path\"},model:{value:(_vm.config.cbh_private_key_path),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_private_key_path\", $$v)},expression:\"config.cbh_private_key_path\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH Private Key Passwd\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH Private Key Passwd\"},model:{value:(_vm.config.cbh_private_key_passwd),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_private_key_passwd\", $$v)},expression:\"config.cbh_private_key_passwd\"}})],1),_c('a-descriptions-item',{attrs:{\"label\":\"CBH Switch Account\"}},[_c('a-input',{attrs:{\"placeholder\":\"CBH Switch Account\"},model:{value:(_vm.config.cbh_switch_account),callback:function ($$v) {_vm.$set(_vm.config, \"cbh_switch_account\", $$v)},expression:\"config.cbh_switch_account\"}})],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// CbhConfig.vue\r\n<template>\r\n  <a-card :bordered=\"false\" class=\"header-solid cbh-config-card\">\r\n    <template #title>\r\n      <a-row type=\"flex\" align=\"middle\">\r\n        <a-col :span=\"12\">\r\n          <h6 class=\"font-semibold m-0\">CBH Configuration</h6>\r\n        </a-col>\r\n        <a-col :span=\"12\" class=\"text-right\">\r\n          <a-button @click=\"saveConfig\" class=\"nav-style-button\">\r\n            <template #icon><a-icon type=\"save\" /></template>\r\n            Save Config\r\n          </a-button>\r\n        </a-col>\r\n      </a-row>\r\n    </template>\r\n\r\n    <a-descriptions size=\"small\" :column=\"1\">\r\n      <a-descriptions-item label=\"AK (Access Key)\">\r\n        <a-input v-model=\"config.ak\" placeholder=\"Access Key\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"SK (Secret Key)\">\r\n        <a-input v-model=\"config.sk\" placeholder=\"Secret Key\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Server (OBS Endpoint)\">\r\n        <a-input v-model=\"config.server\" placeholder=\"OBS Endpoint\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Bucket Name\">\r\n        <a-input v-model=\"config.bucket_name\" placeholder=\"Bucket Name\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Bucket Store Dir\">\r\n        <a-input v-model=\"config.bucket_store_dir\" placeholder=\"Bucket Store Dir\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Upload File Path\">\r\n        <a-input v-model=\"config.upload_file_path\" placeholder=\"Upload File Path\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Upload Object Key\">\r\n        <a-input v-model=\"config.upload_object_key\" placeholder=\"Upload Object Key\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Download Object Key\">\r\n        <a-input v-model=\"config.download_object_key\" placeholder=\"Download Object Key\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"Download File Path\">\r\n        <a-input v-model=\"config.download_file_path\" placeholder=\"Download File Path\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH Host\">\r\n        <a-input v-model=\"config.cbh_host\" placeholder=\"CBH Host\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH User Name\">\r\n        <a-input v-model=\"config.cbh_user_name\" placeholder=\"CBH User Name\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH User Port\">\r\n        <a-input v-model=\"config.cbh_user_port\" placeholder=\"CBH User Port\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH Private Key Path\">\r\n        <a-input v-model=\"config.cbh_private_key_path\" placeholder=\"CBH Private Key Path\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH Private Key Passwd\">\r\n        <a-input v-model=\"config.cbh_private_key_passwd\" placeholder=\"CBH Private Key Passwd\" />\r\n      </a-descriptions-item>\r\n      <a-descriptions-item label=\"CBH Switch Account\">\r\n        <a-input v-model=\"config.cbh_switch_account\" placeholder=\"CBH Switch Account\" />\r\n      </a-descriptions-item>\r\n    </a-descriptions>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { Icon } from 'ant-design-vue';\r\nimport axios from '@/api/axiosInstance';\r\nimport {mapState} from \"vuex\";\r\n\r\nexport default {\r\n  name: 'CbhConfig',\r\n  components: {\r\n    AIcon: Icon,\r\n  },\r\n  computed: {\r\n    // 移除了 sidebarColor 依赖，现在使用通用 nav-style-button 样式\r\n  },\r\n  data() {\r\n    return {\r\n      config: {\r\n        ak: '',\r\n        sk: '',\r\n        server: '',\r\n        bucket_name: '',\r\n        bucket_store_dir: '',\r\n        upload_file_path: '',\r\n        upload_object_key: '',\r\n        download_object_key: '',\r\n        download_file_path: '',\r\n        cbh_host: '',\r\n        cbh_user_name: '',\r\n        cbh_user_port: '',\r\n        cbh_private_key_path: '',\r\n        cbh_private_key_passwd: '',\r\n        cbh_switch_account: '',\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchCbhConfig();\r\n  },\r\n  methods: {\r\n    async fetchCbhConfig() {\r\n      try {\r\n        const response = await axios.get('/api/cbh_config');\r\n        const data = response.data;\r\n        this.config = data; // 直接赋值整个配置对象\r\n      } catch (error) {\r\n        console.error('Error fetching CBH config:', error);\r\n        this.$message.error('Failed to load CBH configuration');\r\n      }\r\n    },\r\n    async saveConfig() {\r\n      try {\r\n        const response = await axios.post('/api/cbh_config', {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(this.config),\r\n        });\r\n\r\n        this.$message.success('CBH configuration saved successfully');\r\n      } catch (error) {\r\n        console.error('Error saving CBH config:', error);\r\n        this.$message.error('Failed to save CBH configuration');\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.cbh-config-card {\r\n  margin: 24px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CbhConfig.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CbhConfig.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CbhConfig.vue?vue&type=template&id=0ef905d8&scoped=true\"\nimport script from \"./CbhConfig.vue?vue&type=script&lang=js\"\nexport * from \"./CbhConfig.vue?vue&type=script&lang=js\"\nimport style0 from \"./CbhConfig.vue?vue&type=style&index=0&id=0ef905d8&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ef905d8\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div>\r\n    <a-tabs :active-key=\"activeTab\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"host\" :tab=\"$t('sidebar.hostConfig')\">\r\n        <HostConfig />\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"cbh\" :tab=\"$t('sidebar.cbhConfig')\">\r\n        <CbhConfig />\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport HostConfig from \"@/components/Cards/HostConfig.vue\";\r\nimport CbhConfig from \"@/components/Cards/CbhConfig.vue\";\r\n\r\nexport default {\r\n  components: { HostConfig, CbhConfig },\r\n  props: {\r\n    defaultTab: {\r\n      type: String,\r\n      default: 'host'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: this.defaultTab\r\n    }\r\n  },\r\n  watch: {\r\n    '$route': {\r\n      immediate: true, // 初始化时立即执行\r\n      handler(to) {\r\n        const targetTab = to.hash.replace('#',  '') || 'host'\r\n        if (targetTab !== this.activeTab)  {\r\n          this.activeTab  = targetTab\r\n          console.log('Tab  updated from route:', targetTab)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      console.log('Tab  changed to:', key)\r\n      this.$router.replace({\r\n        path: '/config',\r\n        hash: `#${key}`,\r\n        query: this.$route.query\r\n      }).catch(err => {\r\n        if (!err.message.includes('Avoided  redundant navigation')) {\r\n          console.error('Navigation  error:', err)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Config.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Config.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Config.vue?vue&type=template&id=0c338d33\"\nimport script from \"./Config.vue?vue&type=script&lang=js\"\nexport * from \"./Config.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export default {\r\n  methods: {\r\n    copyText(text) {\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = text;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('Copied to clipboard');\r\n    }\r\n  }\r\n}; ", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HostConfig.vue?vue&type=style&index=0&id=534ea6ec&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CbhConfig.vue?vue&type=style&index=0&id=0ef905d8&prod&scoped=true&lang=css\"", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n"], "sourceRoot": ""}