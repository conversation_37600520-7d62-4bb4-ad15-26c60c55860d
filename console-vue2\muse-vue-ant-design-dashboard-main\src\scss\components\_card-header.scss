.card-header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  
    .logo-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(36, 150, 237, 0.1);
      padding: 8px;
      border-radius: 8px;
  
      svg {
        width: 32px;
        height: 32px;
      }
    }
  
    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1f36;
    }
  
    .refresh-button {
      border-radius: 6px;
      height: 36px;
      display: flex;
      align-items: center;
      
      .anticon {
        margin-right: 6px;
      }
      
      &:hover {
        opacity: 0.9;
      }
    }
  }