{"version": 3, "sources": ["webpack:///./src/components/Widgets/Network/NetworkListeningModal.vue?7658", "webpack:///./src/views/Docker.vue", "webpack:///./src/components/Cards/DockerInfo.vue", "webpack:///./src/components/Cards/CrictlInfo.vue", "webpack:///./src/components/Widgets/Process/ProcessDetailModal.vue", "webpack:///src/components/Widgets/Process/ProcessDetailModal.vue", "webpack:///./src/components/Widgets/Process/ProcessDetailModal.vue?f17e", "webpack:///./src/components/Widgets/Process/ProcessDetailModal.vue?8f5f", "webpack:///./src/components/Widgets/Process/ProcessTable.vue", "webpack:///src/components/Widgets/Process/ProcessTable.vue", "webpack:///./src/components/Widgets/Process/ProcessTable.vue?ff02", "webpack:///./src/components/Widgets/Process/ProcessTable.vue?f86b", "webpack:///./src/components/Widgets/Network/NetworkListeningModal.vue", "webpack:///src/components/Widgets/Network/NetworkListeningModal.vue", "webpack:///./src/components/Widgets/Network/NetworkListeningModal.vue?f41c", "webpack:///./src/components/Widgets/Network/NetworkListeningModal.vue?fb6b", "webpack:///./src/components/Widgets/Network/NetworkListeningCell.vue", "webpack:///src/components/Widgets/Network/NetworkListeningCell.vue", "webpack:///./src/components/Widgets/Network/NetworkListeningCell.vue?71ab", "webpack:///./src/components/Widgets/Network/NetworkListeningCell.vue?e3f1", "webpack:///./src/components/Widgets/Mount/MountCell.vue", "webpack:///src/components/Widgets/Mount/MountCell.vue", "webpack:///./src/components/Widgets/Mount/MountCell.vue?d462", "webpack:///./src/components/Widgets/Mount/MountCell.vue?91c1", "webpack:///./src/components/Widgets/Mount/CriMountCell.vue", "webpack:///src/components/Widgets/Mount/CriMountCell.vue", "webpack:///./src/components/Widgets/Mount/CriMountCell.vue?8d2b", "webpack:///./src/components/Widgets/Mount/CriMountCell.vue?8aaf", "webpack:///src/components/Cards/CrictlInfo.vue", "webpack:///./src/components/Cards/CrictlInfo.vue?905d", "webpack:///./src/components/Cards/CrictlInfo.vue?fe10", "webpack:///src/components/Cards/DockerInfo.vue", "webpack:///./src/components/Cards/DockerInfo.vue?fd3c", "webpack:///./src/components/Cards/DockerInfo.vue?59b7", "webpack:///src/views/Docker.vue", "webpack:///./src/views/Docker.vue?03f6", "webpack:///./src/views/Docker.vue?6bd6", "webpack:///./src/components/Widgets/JsonDetailModal.vue?2b24", "webpack:///./src/components/Cards/DockerInfo.vue?3015", "webpack:///./src/components/Widgets/Process/ProcessTable.vue?23f3", "webpack:///./src/components/Widgets/Mount/MountCell.vue?be5e", "webpack:///./src/components/Widgets/Network/NetworkListeningCell.vue?d544", "webpack:///./src/components/Cards/CrictlInfo.vue?9048", "webpack:///./src/components/Widgets/Process/ProcessDetailModal.vue?8168", "webpack:///./src/components/Widgets/Mount/CriMountCell.vue?6d17", "webpack:///./src/components/Widgets/JsonDetailModal.vue", "webpack:///src/components/Widgets/JsonDetailModal.vue", "webpack:///./src/components/Widgets/JsonDetailModal.vue?c6b5", "webpack:///./src/components/Widgets/JsonDetailModal.vue?94a2", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "_vm$hostConfigData$", "_vm$hostConfigData$2", "_vm$hostConfigData$3", "_vm$getDaemonConfig", "padding", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "fetchActiveTabData", "proxy", "ref", "model", "value", "activeEngine", "callback", "$$v", "expression", "handleTabChange", "activeTab", "hostConfigData", "docker_version", "getDaemonConfig", "formatBytes", "user_in_docker_group", "is_root_user", "join", "_l", "getRegistryMirrors", "mirror", "_e", "networkColumns", "networkData", "record", "network_id", "loadingNetworks", "pagination", "containerColumns", "containerData", "x", "pageSize", "container_id", "loadingContainers", "mounts", "length", "mount", "index", "Source", "Destination", "networkDetailsVisible", "selectedNetwork", "$event", "handleNetworkDetailsClose", "selectedNodeIp", "handleMountDetailsClose", "mountDetailsVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllMounts", "Mode", "RW", "Propagation", "handleEnvironmentDetailsClose", "environmentDetailsVisible", "selectedEnvironment", "getAllEnvironmentVars", "env", "processDetailsVisible", "selectedProcessContainer", "handleProcessDetailsClose", "processDetailInfoVisible", "selectedProcessInfo", "handleProcessDetailInfoClose", "hostConfigCards", "card", "title", "items", "item", "label", "getValue", "hostConfig", "podColumns", "podData", "pod_id", "loadingPods", "host_path", "container_path", "readonly", "propagation", "replace", "selinux_relabel", "localVisible", "handleClose", "processInfo", "pid", "ppid", "userField", "gid", "state", "cmd", "exe", "cwd", "capability", "environ", "memory_maps", "name", "props", "visible", "type", "Boolean", "required", "Object", "default", "String", "data", "watch", "newValue", "methods", "$emit", "component", "processContainer", "processColumns", "getProcessList", "components", "ProcessDetailModal", "includeTty", "computed", "h", "$createElement", "columns", "dataIndex", "width", "push", "ellipsis", "customRender", "text", "children", "style", "whiteSpace", "wordBreak", "align", "_", "hasDetails", "e", "stopPropagation", "showProcessDetailInfo", "_this$processContaine", "processes", "process_list", "Array", "isArray", "map", "time", "stime", "process", "networkListeningColumns", "getNetworkListening", "_this$networkData", "exposures", "listening_ports", "listeningPorts", "staticStyle", "proto", "local_address", "foreign_address", "pid_program", "showDetails", "apply", "arguments", "parseJsonField", "Function", "field", "defaultValue", "JSON", "parse", "console", "warn", "exposedServices", "error", "JsonDetailModal", "ProcessTable", "NetworkListeningModal", "NetworkListeningCell", "CriMountCell", "nodeIp", "config", "version_info", "match", "trim", "_config$runtime_info", "condition", "runtime_info", "status", "conditions", "find", "c", "_config$runtime_info2", "_config$runtime_info3", "sandboxImage", "loadingHostConfig", "network", "keys", "ips", "ip", "additionalIps", "_inspectData$status", "inspectData", "inspect_data", "metadata", "envVars", "slice", "substring", "showEnvironmentDetails", "showMountDetails", "showNetworkDetails", "processCount", "showProcessDetails", "showInspectDetails", "mapState", "newVal", "created", "currentProject", "loadingKey", "char<PERSON>t", "toUpperCase", "response", "axios", "get", "params", "dbFile", "dataKey", "container", "_this$selectedMountCo", "_this$selectedEnviron", "_this$selectedProcess", "user", "$refs", "jsonDetailModal", "showDetailModal", "$message", "RefreshButton", "CrictlInfo", "MountCell", "hostConfigColumns", "Name", "_inspectData$HostConf", "privileged", "HostConfig", "Privileged", "_processData$process_", "processData", "undefined", "labels", "entries", "resourceMapping", "newIp", "resetData", "mounted", "resourceType", "dataName", "camelCaseToDataName", "capitalizeFirstLetter", "camelCase", "<PERSON><PERSON><PERSON><PERSON>", "letter", "string", "_this$hostConfigData$", "daemon_config", "registryConfig", "Mirrors", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "cleanReactiveObject", "renderPreviewWithMore", "renderItem", "moreText", "onClickMore", "obj", "cleaned", "startsWith", "_this$selectedContain", "<PERSON><PERSON><PERSON><PERSON>", "DockerInfo", "options", "modalWidth", "min", "window", "innerWidth", "contentHeight", "innerHeight", "ids", "search", "Date", "now", "counter", "theme", "isDarkTheme", "header", "contentElement", "$root", "$confirm", "content", "okText", "icon", "cancelButtonProps", "display", "maskClosable", "getContainer", "document", "body", "append<PERSON><PERSON><PERSON>", "createElement", "setTimeout", "getElementById", "JsonViewer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deep", "Infinity", "showDoubleQuotes", "showLength", "showLineNumbers", "height", "overflow", "$mount", "$el", "searchInput", "counterElement", "matches", "currentMatchIndex", "highlightMatches", "searchTerm", "textContent", "jsonNodes", "querySelectorAll", "regex", "RegExp", "for<PERSON>ach", "el", "classList", "remove", "node", "lastIndex", "exec", "add", "navigateToMatch", "max", "currentMatch", "parent", "parentElement", "contains", "expandBtn", "querySelector", "click", "scrollIntoView", "behavior", "block", "searchTimeout", "addEventListener", "clearTimeout", "target", "preventDefault", "shift<PERSON>ey", "themeButton", "backgroundColor", "copyButton", "textToCopy", "stringify", "navigator", "clipboard", "isSecureContext", "writeText", "then", "success", "catch", "err", "textArea", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "kHAAA,W,2CCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,eAAe,IAAI,IAAI,IAEvMI,EAAkB,GCFlBP,EAAS,WAAiB,IAAAQ,EAAAC,EAAAC,EAAAC,EAAKV,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,kCAAkCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEO,QAAS,IAAKC,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACb,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACc,MAAM,QAAQhB,EAAIiB,aAAeb,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,2qDAA2qDF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIoB,GAAG,0BAA0BlB,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACmB,GAAG,CAAC,QAAUrB,EAAIsB,uBAAuB,OAAOC,OAAM,MAAS,CAACrB,EAAG,kBAAkB,CAACsB,IAAI,oBAAoBtB,EAAG,MAAM,CAACG,YAAY,0BAA0B,CAACH,EAAG,SAAS,CAACuB,MAAM,CAACC,MAAO1B,EAAI2B,aAAcC,SAAS,SAAUC,GAAM7B,EAAI2B,aAAaE,GAAKC,WAAW,iBAAiB,CAAC5B,EAAG,aAAa,CAACY,IAAI,SAASV,MAAM,CAAC,IAAM,WAAW,CAACF,EAAG,SAAS,CAACmB,GAAG,CAAC,OAASrB,EAAI+B,iBAAiBN,MAAM,CAACC,MAAO1B,EAAIgC,UAAWJ,SAAS,SAAUC,GAAM7B,EAAIgC,UAAUH,GAAKC,WAAW,cAAc,CAAC5B,EAAG,aAAa,CAACY,IAAI,qBAAqBV,MAAM,CAAC,IAAM,gBAAgB,CAAoB,uBAAlBJ,EAAIgC,UAAoC9B,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,CAAC,GAAI,MAAM,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,oBAAoB,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,qBAAqBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAwB,QAAtBZ,EAACP,EAAIiC,eAAe,UAAE,IAAA1B,OAAA,EAArBA,EAAuB2B,mBAAmBhC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,uBAAuBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,uBAAuBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,mBAAmBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,oBAAoBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,qBAAqBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,wBAAwB,GAAGjC,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,YAAY,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,gBAAgBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,YAAYjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,mBAAmBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAIoC,YAAYpC,EAAImC,gBAAgB,iBAAiBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,sBAAsBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,wBAAwB,GAAGjC,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,WAAW,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,2BAA2BlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAwB,QAArBX,EAAAR,EAAIiC,eAAe,UAAE,IAAAzB,GAArBA,EAAuB6B,qBAAuB,MAAQ,SAASnC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,gBAAgBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAwB,QAArBV,EAAAT,EAAIiC,eAAe,UAAE,IAAAxB,GAArBA,EAAuB6B,aAAe,MAAQ,SAASpC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,uBAAuBlB,EAAIkB,GAAG,IAAIlB,EAAImB,IAAyC,QAAtCT,EAAAV,EAAImC,gBAAgB,0BAAkB,IAAAzB,OAAA,EAAtCA,EAAwC6B,KAAK,QAAS,cAAc,GAAGrC,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,uBAAuB,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,uBAAuBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,kBAAkBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,cAAclB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,yBAAyBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,cAAclB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,yBAAyBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,aAAalB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,iBAAiB,GAAGjC,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,iBAAiB,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,aAAalB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,cAAcjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,qBAAqBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,qBAAqBjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,oBAAoBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,uBAAuB,GAAGjC,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,yBAAyB,UAAW,IAAQ,CAACF,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,mBAAmBlB,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImC,gBAAgB,0BAA0BjC,EAAG,IAAI,CAACG,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAG,yBAAyBhB,EAAG,KAAKF,EAAIwC,GAAIxC,EAAIyC,sBAAsB,SAASC,GAAQ,OAAOxC,EAAG,KAAK,CAACY,IAAI4B,EAAOrC,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuB,SAAa,MAAM,IAAI,IAAI,GAAG1C,EAAI2C,OAAOzC,EAAG,aAAa,CAACY,IAAI,iBAAiBV,MAAM,CAAC,IAAM,aAAa,CAAoB,mBAAlBJ,EAAIgC,UAAgC9B,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI4C,eAAe,cAAc5C,EAAI6C,YAAY,OAAUC,GAAWA,EAAOC,WAAW,QAAU/C,EAAIgD,gBAAgB,WAAahD,EAAIiD,cAAcjD,EAAI2C,MAAM,GAAGzC,EAAG,aAAa,CAACY,IAAI,mBAAmBV,MAAM,CAAC,IAAM,eAAe,CAAoB,qBAAlBJ,EAAIgC,UAAkC9B,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIkD,iBAAiB,cAAclD,EAAImD,cAAc,OAAS,CAAEC,EAAG,MAAO,WAAa,CAAEC,SAAU,IAAK,UAAUP,GAAUA,EAAOQ,aAAa,QAAUtD,EAAIuD,mBAAmB3C,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,eAAeC,GAAG,UAAS,OAAE+B,IAAU,MAAO,CAAEA,EAAQ5C,EAAG,MAAM,CAAE4C,EAAOU,QAAUV,EAAOU,OAAOC,OAAQzD,EAAIwC,GAAIM,EAAOU,QAAQ,SAASE,EAAMC,GAAO,OAAOzD,EAAG,MAAM,CAACY,IAAI6C,GAAO,CAAC3D,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGuC,EAAME,QAAQ,MAAM5D,EAAImB,GAAGuC,EAAMG,aAAa,UAAS3D,EAAG,OAAO,CAACF,EAAIkB,GAAG,UAAU,GAAGlB,EAAI2C,SAAS,MAAK,EAAM,cAAc3C,EAAI2C,KAAKzC,EAAG,0BAA0B,CAACE,MAAM,CAAC,QAAUJ,EAAI8D,sBAAsB,eAAe9D,EAAI+D,iBAAiB1C,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAI8D,sBAAwBE,GAAQ,MAAQhE,EAAIiE,8BAA8B,IAAI,IAAI,GAAG/D,EAAG,aAAa,CAACY,IAAI,SAASV,MAAM,CAAC,IAAM,QAAQ,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,UAAUJ,EAAIkE,mBAAmB,IAAI,IAAI,GAAGhE,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,gBAAgB,MAAQ,SAASiB,GAAG,CAAC,OAASrB,EAAImE,yBAAyBvD,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAImE,0BAA0B,CAACnE,EAAIkB,GAAG,cAAcK,OAAM,KAAQE,MAAM,CAACC,MAAO1B,EAAIoE,oBAAqBxC,SAAS,SAAUC,GAAM7B,EAAIoE,oBAAoBvC,GAAKC,WAAW,wBAAwB,CAAE9B,EAAIqE,uBAAwB,CAACnE,EAAG,MAAM,CAACG,YAAY,oBAAoBL,EAAIwC,GAAIxC,EAAIsE,gBAAgB,SAASZ,EAAMC,GAAO,OAAOzD,EAAG,MAAM,CAACY,IAAI6C,EAAMtD,YAAY,cAAc,CAACH,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,OAAO,CAACG,YAAY,gBAAgB,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAME,WAAW1D,EAAG,OAAO,CAACG,YAAY,eAAe,CAACL,EAAIkB,GAAG,OAAOhB,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMG,kBAAkB3D,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAAEqD,EAAMa,KAAMrE,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMa,SAASvE,EAAI2C,KAAKzC,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMc,GAAK,KAAO,SAAUd,EAAMe,YAAavE,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMe,gBAAgBzE,EAAI2C,YAAW,IAAI3C,EAAI2C,MAAM,GAAGzC,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,wBAAwB,MAAQ,SAASiB,GAAG,CAAC,OAASrB,EAAI0E,+BAA+B9D,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAI0E,gCAAgC,CAAC1E,EAAIkB,GAAG,aAAaK,OAAM,KAAQE,MAAM,CAACC,MAAO1B,EAAI2E,0BAA2B/C,SAAS,SAAUC,GAAM7B,EAAI2E,0BAA0B9C,GAAKC,WAAW,8BAA8B,CAAE9B,EAAI4E,oBAAqB,CAAC1E,EAAG,MAAM,CAACG,YAAY,iBAAiBL,EAAIwC,GAAIxC,EAAI6E,yBAAyB,SAASC,EAAInB,GAAO,OAAOzD,EAAG,MAAM,CAACY,IAAI6C,EAAMtD,YAAY,YAAY,CAACL,EAAIkB,GAAG,IAAIlB,EAAImB,GAAG2D,GAAK,UAAS,IAAI9E,EAAI2C,MAAM,GAAGzC,EAAG,gBAAgB,CAACE,MAAM,CAAC,QAAUJ,EAAI+E,sBAAsB,oBAAoB/E,EAAIgF,yBAAyB,aAAa,MAAM,eAAc,GAAM3D,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAI+E,sBAAwBf,GAAQ,MAAQhE,EAAIiF,6BAA6B/E,EAAG,uBAAuB,CAACE,MAAM,CAAC,QAAUJ,EAAIkF,yBAAyB,eAAelF,EAAImF,oBAAoB,aAAa,OAAO9D,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAIkF,yBAA2BlB,GAAQ,MAAQhE,EAAIoF,iCAAiC,IAEpuT9E,EAAkB,G,0DCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,kCAAkCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEO,QAAS,KAAM,CAACT,EAAG,kBAAkB,CAACsB,IAAI,oBAAoBtB,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,SAAS,CAACmB,GAAG,CAAC,OAASrB,EAAI+B,iBAAiBN,MAAM,CAACC,MAAO1B,EAAIgC,UAAWJ,SAAS,SAAUC,GAAM7B,EAAIgC,UAAUH,GAAKC,WAAW,cAAc,CAAC5B,EAAG,aAAa,CAACY,IAAI,qBAAqBV,MAAM,CAAC,IAAM,gBAAgB,CAAoB,uBAAlBJ,EAAIgC,UAAoC9B,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,CAAC,GAAI,MAAMJ,EAAIwC,GAAIxC,EAAIqF,iBAAiB,SAASC,GAAM,OAAOpF,EAAG,QAAQ,CAACY,IAAIwE,EAAKC,MAAMnF,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACG,YAAY,mBAAmBD,MAAM,CAAC,MAAQkF,EAAKC,MAAM,UAAW,IAAQvF,EAAIwC,GAAI8C,EAAKE,OAAO,SAASC,EAAK9B,GAAO,OAAOzD,EAAG,IAAI,CAACY,IAAI6C,EAAMtD,YAAY,aAAa,CAACH,EAAG,SAAS,CAACF,EAAIkB,GAAGlB,EAAImB,GAAGsE,EAAKC,OAAO,OAAO1F,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGsE,EAAKE,SAAS3F,EAAI4F,aAAa,UAAS,IAAI,MAAK,IAAI,GAAG5F,EAAI2C,OAAOzC,EAAG,aAAa,CAACY,IAAI,aAAaV,MAAM,CAAC,IAAM,SAAS,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI6F,WAAW,cAAc7F,EAAI8F,QAAQ,OAAShD,GAAUA,EAAOiD,OAAO,QAAU/F,EAAIgG,YAAY,WAAa,CAAE3C,SAAU,IAAK,OAAS,CAAED,EAAG,UAAW,GAAGlD,EAAG,aAAa,CAACY,IAAI,mBAAmBV,MAAM,CAAC,IAAM,eAAe,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIkD,iBAAiB,cAAclD,EAAImD,cAAc,OAASL,GAAUA,EAAOQ,aAAa,QAAUtD,EAAIuD,kBAAkB,WAAa,CAAEF,SAAU,IAAK,OAAS,CAAED,EAAG,UAAW,IAAI,IAAI,GAAGlD,EAAG,0BAA0B,CAACE,MAAM,CAAC,QAAUJ,EAAI8D,sBAAsB,eAAe9D,EAAI+D,iBAAiB1C,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAI8D,sBAAwBE,GAAQ,MAAQhE,EAAIiE,6BAA6B/D,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,wBAAwB,MAAQ,SAASiB,GAAG,CAAC,OAASrB,EAAI0E,+BAA+B9D,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAI0E,gCAAgC,CAAC1E,EAAIkB,GAAG,aAAaK,OAAM,KAAQE,MAAM,CAACC,MAAO1B,EAAI2E,0BAA2B/C,SAAS,SAAUC,GAAM7B,EAAI2E,0BAA0B9C,GAAKC,WAAW,8BAA8B,CAAE9B,EAAI4E,oBAAqB,CAAC1E,EAAG,MAAM,CAACG,YAAY,iBAAiBL,EAAIwC,GAAIxC,EAAI6E,yBAAyB,SAASC,EAAInB,GAAO,OAAOzD,EAAG,MAAM,CAACY,IAAI6C,EAAMtD,YAAY,YAAY,CAACL,EAAIkB,GAAG,IAAIlB,EAAImB,GAAG2D,GAAK,UAAS,IAAI9E,EAAI2C,MAAM,GAAGzC,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,gBAAgB,MAAQ,SAASiB,GAAG,CAAC,OAASrB,EAAImE,yBAAyBvD,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAImE,0BAA0B,CAACnE,EAAIkB,GAAG,cAAcK,OAAM,KAAQE,MAAM,CAACC,MAAO1B,EAAIoE,oBAAqBxC,SAAS,SAAUC,GAAM7B,EAAIoE,oBAAoBvC,GAAKC,WAAW,wBAAwB,CAAE9B,EAAIqE,uBAAwB,CAACnE,EAAG,MAAM,CAACG,YAAY,oBAAoBL,EAAIwC,GAAIxC,EAAIsE,gBAAgB,SAASZ,EAAMC,GAAO,OAAOzD,EAAG,MAAM,CAACY,IAAI6C,EAAMtD,YAAY,cAAc,CAACH,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,OAAO,CAACG,YAAY,gBAAgB,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMuC,cAAc/F,EAAG,OAAO,CAACG,YAAY,eAAe,CAACL,EAAIkB,GAAG,OAAOhB,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMwC,qBAAqBhG,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGuC,EAAMyC,SAAW,KAAO,SAAUzC,EAAM0C,YAAalG,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGuC,EAAM0C,YAAYC,QAAQ,eAAgB,KAAK,OAAOrG,EAAI2C,KAAMe,EAAM4C,gBAAiBpG,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAG,qBAAqBlB,EAAI2C,YAAW,IAAI3C,EAAI2C,MAAM,GAAGzC,EAAG,gBAAgB,CAACE,MAAM,CAAC,QAAUJ,EAAI+E,sBAAsB,oBAAoB/E,EAAIgF,yBAAyB,aAAa,OAAO,eAAc,GAAO3D,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAI+E,sBAAwBf,GAAQ,MAAQhE,EAAIiF,6BAA6B/E,EAAG,uBAAuB,CAACE,MAAM,CAAC,QAAUJ,EAAIkF,yBAAyB,eAAelF,EAAImF,oBAAoB,aAAa,QAAQ9D,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAIkF,yBAA2BlB,GAAQ,MAAQhE,EAAIoF,iCAAiC,IAEviI9E,EAAkB,G,wBCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACG,YAAY,uBAAuBD,MAAM,CAAC,QAAUJ,EAAIuG,aAAa,MAAQ,+BAA+B,MAAQ,SAASlF,GAAG,CAAC,OAASrB,EAAIwG,aAAa5F,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAIwG,cAAc,CAACxG,EAAIkB,GAAG,aAAaK,OAAM,MAAS,CAAEvB,EAAIyG,YAAa,CAACvG,EAAG,SAAS,CAACE,MAAM,CAAC,qBAAqB,MAAM,CAACF,EAAG,aAAa,CAACY,IAAI,IAAIV,MAAM,CAAC,IAAM,eAAe,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAW,GAAG,OAAS,IAAI,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYC,QAAQxG,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYE,SAASzG,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYzG,EAAI4G,YAAc,UAAU1G,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYI,KAAO,UAAU3G,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYK,OAAS,UAAU5G,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYM,QAAQ7G,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,oBAAoB,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYO,KAAO,UAAU9G,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,sBAAsB,CAACJ,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYQ,KAAO,WAAW,IAAI,GAAIjH,EAAIyG,YAAYS,WAAYhH,EAAG,aAAa,CAACY,IAAI,IAAIV,MAAM,CAAC,IAAM,iBAAiB,CAACF,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYS,iBAAiBlH,EAAI2C,KAAM3C,EAAIyG,YAAYU,QAASjH,EAAG,aAAa,CAACY,IAAI,IAAIV,MAAM,CAAC,IAAM,0BAA0B,CAACF,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYU,cAAcnH,EAAI2C,KAAM3C,EAAIyG,YAAYW,YAAalH,EAAG,aAAa,CAACY,IAAI,IAAIV,MAAM,CAAC,IAAM,gBAAgB,CAACF,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIyG,YAAYW,kBAAkBpH,EAAI2C,MAAM,IAAI3C,EAAI2C,MAAM,IAEh3DrC,EAAkB,GCsCP,GACf+G,KAAA,qBACAC,MAAA,CACAC,QAAA,CACAC,KAAAC,QACAC,UAAA,GAEAjB,YAAA,CACAe,KAAAG,OACAC,QAAA,MAGAhB,UAAA,CACAY,KAAAK,OACAD,QAAA,SAGAE,OACA,OACAvB,aAAA,KAAAgB,UAGAQ,MAAA,CACAR,QAAAS,GACA,KAAAzB,aAAAyB,IAGAC,QAAA,CACAzB,cACA,KAAAD,cAAA,EACA,KAAA2B,MAAA,qBACA,KAAAA,MAAA,YCvEyX,I,wBCQrXC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCnBXpI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACG,YAAY,sBAAsBD,MAAM,CAAC,QAAUJ,EAAIuG,aAAa,MAAQ,kBAAkB,MAAQ,UAAUlF,GAAG,CAAC,OAASrB,EAAIwG,aAAa5F,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAIwG,cAAc,CAACxG,EAAIkB,GAAG,aAAaK,OAAM,MAAS,CAAEvB,EAAIoI,iBAAkB,CAAClI,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIqI,eAAe,WAAarI,EAAIsI,iBAAiB,WAAa,CAAEjF,SAAU,IAAK,KAAO,aAAarD,EAAI2C,MAAM,GAAGzC,EAAG,uBAAuB,CAACE,MAAM,CAAC,QAAUJ,EAAIkF,yBAAyB,eAAelF,EAAImF,oBAAoB,aAAanF,EAAI4G,WAAWvF,GAAG,CAAC,iBAAiB,SAAS2C,GAAQhE,EAAIkF,yBAA2BlB,GAAQ,MAAQhE,EAAIoF,iCAAiC,IAEhyB9E,EAAkB,GCkCP,GACf+G,KAAA,eACAkB,WAAA,CACAC,sBAEAlB,MAAA,CACAC,QAAA,CACAC,KAAAC,QACAC,UAAA,GAEAU,iBAAA,CACAZ,KAAAG,OACAC,QAAA,MAGAhB,UAAA,CACAY,KAAAK,OACAD,QAAA,QAGAa,WAAA,CACAjB,KAAAC,QACAG,SAAA,IAGAE,OACA,OACAvB,aAAA,KAAAgB,QACArC,0BAAA,EACAC,oBAAA,OAGA4C,MAAA,CACAR,QAAAS,GACA,KAAAzB,aAAAyB,IAGAU,SAAA,CACAL,iBAAA,MAAAM,EAAA,KAAAC,eACAC,EAAA,CACA,CAAAtD,MAAA,MAAAuD,UAAA,MAAAhI,IAAA,MAAAiI,MAAA,QACA,CAAAxD,MAAA,OAAAuD,UAAA,OAAAhI,IAAA,OAAAiI,MAAA,QACA,CACAxD,MAAA,MACAuD,UAAA,KAAAlC,UACA9F,IAAA,KAAA8F,UACAmC,MAAA,QAEA,CAAAxD,MAAA,MAAAuD,UAAA,MAAAhI,IAAA,MAAAiI,MAAA,QACA,CAAAxD,MAAA,QAAAuD,UAAA,QAAAhI,IAAA,QAAAiI,MAAA,QACA,CAAAxD,MAAA,aAAAuD,UAAA,QAAAhI,IAAA,QAAAiI,MAAA,UAkDA,OA/CA,KAAAN,YACAI,EAAAG,KAAA,CAAAzD,MAAA,MAAAuD,UAAA,MAAAhI,IAAA,MAAAiI,MAAA,SAGAF,EAAAG,KACA,CAAAzD,MAAA,OAAAuD,UAAA,OAAAhI,IAAA,OAAAiI,MAAA,QACA,CACAxD,MAAA,UACAuD,UAAA,MACAhI,IAAA,MACAiI,MAAA,QACAE,UAAA,EACAC,aAAAC,IACA,CACAC,SAAAD,EACA7B,MAAA,CACA+B,MAAA,CACAC,WAAA,SACAC,UAAA,kBAMA,CACAhE,MAAA,UACAzE,IAAA,UACAiI,MAAA,QACAS,MAAA,SACAN,cAAAO,EAAA3G,KACA,MAAA4G,EAAA5G,EAAAkE,KAAAlE,EAAAmE,KAAAnE,EAAAoE,YAAApE,EAAAqE,SAAArE,EAAAsE,YACA,OAAAsC,EAAAf,EAAA,wBAEA,YACA,mBACAgB,IACAA,EAAAC,kBACA,KAAAC,sBAAA/G,MACA,kBAIA,SAKA+F,IAGAZ,QAAA,CACAzB,cACA,KAAAD,cAAA,EACA,KAAA2B,MAAA,qBACA,KAAAA,MAAA,UAEAI,iBAAA,IAAAwB,EACA,WAAAA,EAAA,KAAA1B,wBAAA,IAAA0B,MAAAC,UAAA,SAGA,MAAAA,EAAA,KAAA3B,iBAAA2B,UAGA,OAAAA,EAAAC,cAAAD,EAAAC,aAAAvG,OAAA,EACAsG,EAAAC,aAIAD,EAAArD,KAAAuD,MAAAC,QAAAH,EAAArD,KACAqD,EAAArD,IAAAyD,IAAA,CAAAV,EAAA9F,KAAA,CACA+C,IAAAqD,EAAArD,IAAA/C,GACAgD,KAAAoD,EAAApD,KAAAhD,GACA,MAAAiD,WAAAmD,EAAA,KAAAnD,WAAAjD,GACAoD,IAAAgD,EAAAhD,IAAApD,GACAyG,KAAAL,EAAAK,KAAAzG,GACA0G,MAAAN,EAAAM,MAAA1G,MAIA,IAEAkG,sBAAAS,GACA,KAAAnF,oBAAAmF,EACA,KAAApF,0BAAA,GAEAE,+BACA,KAAAF,0BAAA,EACA,KAAAC,oBAAA,QChLmX,ICQ/W,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBXpF,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,UAAU,CAACG,YAAY,0BAA0BD,MAAM,CAAC,QAAUJ,EAAIuG,aAAa,MAAQ,4BAA4B,MAAQ,SAASlF,GAAG,CAAC,OAASrB,EAAIwG,aAAa5F,YAAYZ,EAAIa,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACb,EAAG,WAAW,CAACmB,GAAG,CAAC,MAAQrB,EAAIwG,cAAc,CAACxG,EAAIkB,GAAG,aAAaK,OAAM,MAAS,CAAEvB,EAAI6C,YAAa,CAAC3C,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIuK,wBAAwB,WAAavK,EAAIwK,sBAAsB,WAAa,CAAEnH,SAAU,IAAK,KAAO,aAAarD,EAAI2C,MAAM,IAEniBrC,EAAkB,GCsBP,GACf+G,KAAA,wBACAC,MAAA,CACAC,QAAA,CACAC,KAAAC,QACAC,UAAA,GAEA7E,YAAA,CACA2E,KAAAG,OACAC,QAAA,OAGAE,OACA,OACAvB,aAAA,KAAAgB,QACAgD,wBAAA,CACA,CAAAhF,MAAA,WAAAuD,UAAA,QAAAhI,IAAA,QAAAiI,MAAA,QACA,CAAAxD,MAAA,gBAAAuD,UAAA,gBAAAhI,IAAA,gBAAAiI,MAAA,SACA,CAAAxD,MAAA,kBAAAuD,UAAA,kBAAAhI,IAAA,kBAAAiI,MAAA,SACA,CAAAxD,MAAA,QAAAuD,UAAA,QAAAhI,IAAA,QAAAiI,MAAA,SACA,CAAAxD,MAAA,cAAAuD,UAAA,cAAAhI,IAAA,cAAAiI,MAAA,YAIAhB,MAAA,CACAR,QAAAS,GACA,KAAAzB,aAAAyB,IAGAC,QAAA,CACAzB,cACA,KAAAD,cAAA,EACA,KAAA2B,MAAA,qBACA,KAAAA,MAAA,UAEAsC,sBAAA,IAAAC,EACA,eAAAA,EAAA,KAAA5H,mBAAA,IAAA4H,KAAAC,WACA,KAAA7H,YAAA6H,UAAAC,iBADA,MC5D4X,ICQxX,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBX5K,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAgC,IAA9BF,EAAI4K,eAAenH,OAAcvD,EAAG,MAAM,CAACF,EAAIkB,GAAG,wBAAwBhB,EAAG,MAAM,CAAC2K,YAAY,CAAC,YAAY,SAAS,CAAE7K,EAAI4K,eAAenH,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,QAAU,UAAU,CAAC3K,EAAG,MAAM,CAACA,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAI4K,eAAe,GAAGE,UAAU5K,EAAG,OAAO,CAACF,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAI4K,eAAe,GAAGG,kBAAkB7K,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAG,SAAShB,EAAG,OAAO,CAACF,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAI4K,eAAe,GAAGI,oBAAoB9K,EAAG,OAAO,CAACG,YAAY,gBAAgB,CAACL,EAAIkB,GAAG,KAAKlB,EAAImB,GAAGnB,EAAI4K,eAAe,GAAGK,aAAa,SAAUjL,EAAI4K,eAAenH,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,aAAa,QAAQ,CAAC3K,EAAG,WAAW,CAAC2K,YAAY,CAAC,eAAe,KAAKzK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASiB,GAAG,CAAC,MAAQ,SAAS2C,GAAiC,OAAzBA,EAAO4F,kBAAyB5J,EAAIkL,YAAYC,MAAM,KAAMC,cAAc,CAACpL,EAAIkB,GAAG,KAAKlB,EAAImB,GAAGnB,EAAI4K,eAAenH,OAAS,GAAG,gBAAgB,GAAGzD,EAAI2C,OAAO3C,EAAI2C,UAE9+BrC,EAAkB,GC0BP,GACf+G,KAAA,uBACAC,MAAA,CACAxE,OAAA,CACA0E,KAAAG,OACAD,UAAA,GAEA2D,eAAA,CACA7D,KAAA8D,SACA1D,SAAA2D,EAAAC,KACA,IACA,OAAAvB,MAAAC,QAAAqB,IAGA,kBAAAA,GAAA,OAAAA,EAFAA,EAKA,kBAAAA,EAAAE,KAAAC,MAAAH,GAAAC,EACA,MAAA7B,GAEA,OADAgC,QAAAC,KAAA,8BAAAjC,GACA6B,MAKA9C,SAAA,CACAkC,iBACA,IACA,MAAAiB,EAAA,uBAAA/I,OAAA4H,UACAe,KAAAC,MAAA,KAAA5I,OAAA4H,WACA,KAAA5H,OAAA4H,UACA,cAAAmB,QAAA,IAAAA,OAAA,EAAAA,EAAAlB,kBAAA,GACA,MAAAhB,GAEA,OADAgC,QAAAG,MAAA,oCAAAnC,GACA,MAIA1B,QAAA,CACAiD,YAAAvB,GAEAA,GACAA,EAAAC,kBAIA,KAAA1B,MAAA,oBAAApF,QACA,KAAAoF,MAAA,mBAAApF,WC3E2X,ICQvX,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBX/C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAwB,IAAtBF,EAAIwD,OAAOC,OAAcvD,EAAG,MAAM,CAACF,EAAIkB,GAAG,SAAShB,EAAG,MAAM,CAAC2K,YAAY,CAAC,YAAY,SAAS,CAAE7K,EAAIwD,OAAOC,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,QAAU,UAAU,CAAC3K,EAAG,MAAM,CAACA,EAAG,OAAO,CAACG,YAAY,eAAe,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAGI,QAAU,UAAU1D,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAG,SAAShB,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAGK,aAAe,UAAU3D,EAAG,OAAO,CAACG,YAAY,YAAYwK,YAAY,CAAC,cAAc,QAAQ,CAAC7K,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAGe,MAAQ,OAAOrE,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAGgB,GAAK,KAAO,WAAYxE,EAAIwD,OAAOC,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,aAAa,QAAQ,CAAC3K,EAAG,WAAW,CAAC2K,YAAY,CAAC,eAAe,KAAKzK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASiB,GAAG,CAAC,MAAQrB,EAAIkL,cAAc,CAAClL,EAAIkB,GAAG,KAAKlB,EAAImB,GAAGnB,EAAIwD,OAAOC,OAAS,GAAG,gBAAgB,GAAGzD,EAAI2C,OAAO3C,EAAI2C,UAE96BrC,EAAkB,GC0BP,GACf+G,KAAA,YACAC,MAAA,CACAxE,OAAA,CACA0E,KAAAG,OACAD,UAAA,GAEA2D,eAAA,CACA7D,KAAA8D,SACA1D,SAAA2D,EAAAC,KACA,IACA,OAAAvB,MAAAC,QAAAqB,IAGA,kBAAAA,GAAA,OAAAA,EAFAA,EAKA,kBAAAA,EAAAE,KAAAC,MAAAH,GAAAC,EACA,MAAA7B,GAEA,OADAgC,QAAAC,KAAA,8BAAAjC,GACA6B,MAKA9C,SAAA,CACAlF,SACA,IACA,YAAA6H,eAAA,KAAAvI,OAAAU,OAAA,IACA,MAAAmG,GAEA,OADAgC,QAAAG,MAAA,0BAAAnC,GACA,MAIA1B,QAAA,CACAiD,cACA,KAAAhD,MAAA,oBAAApF,WCjEgX,ICQ5W,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCnBX/C,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAwB,IAAtBF,EAAIwD,OAAOC,OAAcvD,EAAG,MAAM,CAACF,EAAIkB,GAAG,SAAShB,EAAG,MAAM,CAAC2K,YAAY,CAAC,YAAY,SAAS,CAAE7K,EAAIwD,OAAOC,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,QAAU,UAAU,CAAC3K,EAAG,MAAM,CAACA,EAAG,OAAO,CAACG,YAAY,eAAe,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAGyC,WAAa,UAAU/F,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIkB,GAAG,SAAShB,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAG0C,gBAAkB,UAAUhG,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAG2C,SAAW,KAAO,SAAUnG,EAAIwD,OAAO,GAAG4C,YAAalG,EAAG,OAAO,CAACG,YAAY,aAAa,CAACL,EAAIkB,GAAGlB,EAAImB,GAAGnB,EAAIwD,OAAO,GAAG4C,YAAYC,QAAQ,eAAgB,QAAQrG,EAAI2C,OAAQ3C,EAAIwD,OAAOC,OAAS,EAAGvD,EAAG,MAAM,CAAC2K,YAAY,CAAC,aAAa,QAAQ,CAAC3K,EAAG,WAAW,CAAC2K,YAAY,CAAC,eAAe,KAAKzK,MAAM,CAAC,KAAO,OAAO,KAAO,SAASiB,GAAG,CAAC,MAAQrB,EAAIkL,cAAc,CAAClL,EAAIkB,GAAG,KAAKlB,EAAImB,GAAGnB,EAAIwD,OAAOC,OAAS,GAAG,gBAAgB,GAAGzD,EAAI2C,OAAO3C,EAAI2C,UAE19BrC,EAAkB,GC0BP,GACf+G,KAAA,eACAC,MAAA,CACAxE,OAAA,CACA0E,KAAAG,OACAD,UAAA,IAGAgB,SAAA,CACAlF,SACA,IACA,6BAAAV,OAAAU,OACAiI,KAAAC,MAAA,KAAA5I,OAAAU,QACA,KAAAV,OAAAU,QAAA,GACA,MAAAmG,GAEA,OADAgC,QAAAG,MAAA,0BAAAnC,GACA,MAIA1B,QAAA,CACAiD,cACA,KAAAhD,MAAA,oBAAApF,WClDmX,ICQ/W,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCsHA,GACfyF,WAAA,CACAwD,uBACAvD,qBACAwD,eACAC,wBACAC,uBACAC,gBAIA9E,KAAA,aACAC,MAAA,CACA8E,OAAA,CACA5E,KAAAK,OACAH,UAAA,IAGAI,OAAA,MAAAa,EAAA,KAAAC,eACA,OAEAvD,gBAAA,CACA,CACAE,MAAA,sBACAC,MAAA,CACA,CACAE,MAAA,UACAC,SAAA0G,IACA,IAAAA,EAAAC,aAAA,YACA,MAAAC,EAAAF,EAAAC,aAAAC,MAAA,mBACA,OAAAA,IAAA,GAAAC,OAAA,QAGA,CACA9G,MAAA,eACAC,SAAA0G,IACA,IAAAA,EAAAC,aAAA,YACA,MAAAC,EAAAF,EAAAC,aAAAC,MAAA,uBACA,OAAAA,IAAA,GAAAC,OAAA,QAGA,CACA9G,MAAA,kBACAC,SAAA0G,IACA,IAAAA,EAAAC,aAAA,YACA,MAAAC,EAAAF,EAAAC,aAAAC,MAAA,0BACA,OAAAA,IAAA,GAAAC,OAAA,QAGA,CACA9G,MAAA,sBACAC,SAAA0G,IACA,IAAAA,EAAAC,aAAA,YACA,MAAAC,EAAAF,EAAAC,aAAAC,MAAA,6BACA,OAAAA,IAAA,GAAAC,OAAA,UAKA,CACAjH,MAAA,iBACAC,MAAA,CACA,CACAE,MAAA,gBACAC,SAAA0G,IAAA,IAAAI,EACA,MAAAC,EAAA,QAAAD,EAAAJ,EAAAM,oBAAA,IAAAF,GAAA,QAAAA,IAAAG,cAAA,IAAAH,GAAA,QAAAA,IAAAI,kBAAA,IAAAJ,OAAA,EAAAA,EAAAK,KAAAC,GAAA,iBAAAA,EAAAvF,MACA,OAAAkF,EAAA,IAAAA,EAAAE,OAAA,6BAGA,CACAlH,MAAA,gBACAC,SAAA0G,IAAA,IAAAW,EACA,MAAAN,EAAA,QAAAM,EAAAX,EAAAM,oBAAA,IAAAK,GAAA,QAAAA,IAAAJ,cAAA,IAAAI,GAAA,QAAAA,IAAAH,kBAAA,IAAAG,OAAA,EAAAA,EAAAF,KAAAC,GAAA,iBAAAA,EAAAvF,MACA,OAAAkF,EAAA,IAAAA,EAAAE,OAAA,6BAGA,CACAlH,MAAA,gBACAC,SAAA0G,IAAA,IAAAY,EAAA,eAAAA,EAAAZ,EAAAM,oBAAA,IAAAM,GAAA,QAAAA,IAAAZ,cAAA,IAAAY,OAAA,EAAAA,EAAAC,eAAA,UAIA,CACA3H,MAAA,WACAC,MAAA,CACA,CACAE,MAAA,YACAC,SAAA0G,KAAA/J,aAAA,eAKAsD,WAAA,GACAE,QAAA,GACA3C,cAAA,GACAgK,mBAAA,EACAnH,aAAA,EACAzC,mBAAA,EACAvB,UAAA,qBAGA6D,WAAA,CACA,CAAAN,MAAA,SAAAuD,UAAA,SAAAhI,IAAA,SAAAiI,MAAA,QAAAE,UAAA,GACA,CACA1D,MAAA,OACAuD,UAAA,oBACAhI,IAAA,OACAiI,MAAA,QACAE,UAAA,GAEA,CACA1D,MAAA,YACAuD,UAAA,yBACAhI,IAAA,YACAiI,MAAA,QAEA,CACAxD,MAAA,QACAuD,UAAA,QACAhI,IAAA,QACAiI,MAAA,SAEA,CACAxD,MAAA,UACAuD,UAAA,UACAhI,IAAA,UACAiI,MAAA,QACAG,cAAAO,EAAA3G,KACA,MAAAD,EAAAC,EAAAsK,QACA,IAAAvK,GAAA,IAAA8E,OAAA0F,KAAAxK,GAAAY,OACA,YAEA,MAAA6J,EAAA,GAOA,OANAzK,EAAA0K,IACAD,EAAAtE,KAAAnG,EAAA0K,IAEA1K,EAAA2K,eAAA3K,EAAA2K,cAAA/J,OAAA,GACA6J,EAAAtE,QAAAnG,EAAA2K,eAEAF,EAAA7J,OAAA,EAAA6J,EAAA/K,KAAA,gBAMAW,iBAAA,CACA,CACAqC,MAAA,eACAuD,UAAA,eACAhI,IAAA,eACAiI,MAAA,QACAE,UAAA,GAEA,CACA1D,MAAA,OACAuD,UAAA,eACAhI,IAAA,OACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,KACA,QAAA2K,EACA,MAAAC,EAAA,kBAAA5K,EAAA6K,aACAlC,KAAAC,MAAA5I,EAAA6K,cACA7K,EAAA6K,aACA,OACAvE,UAAA,OAAAsE,QAAA,IAAAA,GAAA,QAAAD,EAAAC,EAAAd,cAAA,IAAAa,GAAA,QAAAA,IAAAG,gBAAA,IAAAH,OAAA,EAAAA,EAAApG,OAAA,MACAC,MAAA,CACA+B,MAAA,CACAC,WAAA,SACAC,UAAA,gBAIA,MAAAI,GAEA,OADAgC,QAAAC,KAAA,mCAAAjC,GACA,SAKA,CACApE,MAAA,cACAuD,UAAA,MACAhI,IAAA,MACAiI,MAAA,QACAG,cAAAO,EAAA3G,KACA,IACA,MAAA+K,EAAA,kBAAA/K,EAAAgC,IAAA2G,KAAAC,MAAA5I,EAAAgC,KAAAhC,EAAAgC,KAAA,GACA,OAAA+I,EAAApK,OAEAkF,EAAA,aACA,qBACAkF,EAAAC,MAAA,KAAA3D,IAAA,CAAArF,EAAAnB,IAAAgF,EAAA,WACAhF,EAAA,4GACAmB,EAAArB,OAAA,IAAAqB,EAAAiJ,UAAA,aAAAjJ,KAGA+I,EAAApK,OAAA,GAAAkF,EAAA,wBAEA,YACA,mBACAgB,IACAA,EAAAC,kBACA,KAAAoE,uBAAAlL,MACA,KAEA+K,EAAApK,OAAA,iBAlBA,MAuBA,MAAAkG,GAEA,OADAgC,QAAAC,KAAA,6BAAAjC,GACA,UAIA,CACApE,MAAA,SACAuD,UAAA,SACAhI,IAAA,SACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,IACA6F,EAAA,gCAEA7F,GAAA,mBACA,KAAAmL,qBAKA,CACA1I,MAAA,oBACAuD,UAAA,YACAhI,IAAA,YACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,IACA6F,EAAA,wCAEA7F,GAAA,mBACA,KAAAoL,uBAKA,CACA3I,MAAA,YACAuD,UAAA,YACAhI,IAAA,YACAiI,MAAA,QACAS,MAAA,SACAN,cAAAO,EAAA3G,KACA,IAAAA,MAAAiH,UAAA,YACA,MAAAA,EAAA,kBAAAjH,EAAAiH,UAAA0B,KAAAC,MAAA5I,EAAAiH,WAAAjH,EAAAiH,UAEAoE,EAAApE,EAAArD,IAAAqD,EAAArD,IAAAjD,OAAA,EAEA,OAAAkF,EAAA,wBAEA,YACA,mBACAgB,IACAA,EAAAC,kBACA,KAAAwE,mBAAAtL,MACA,CAEAqL,EAAA,QAAAA,EAAA,0BAKA,CACA5I,MAAA,eACAuD,UAAA,eACAhI,IAAA,eACAiI,MAAA,QACAS,MAAA,SACAN,cAAAO,EAAA3G,IAAA6F,EAAA,cACA,8CAAAA,EAAA,eAEAgB,IACAA,EAAAC,kBACA,KAAAyE,mBAAAvL,KACA,MACA,0DAUAgB,uBAAA,EACAC,gBAAA,KAGAK,qBAAA,EACAC,uBAAA,KAGAM,2BAAA,EACAC,oBAAA,KAGAG,uBAAA,EACAC,yBAAA,KACAE,0BAAA,EACAC,oBAAA,OAKAuD,SAAA,IACA4F,eAAA,sCAEAvG,MAAA,CACA7D,eAAAqK,GACAA,GACA,KAAAjN,uBAIAkN,UACA,KAAAtK,gBACA,KAAA5C,sBAGA2G,QAAA,CACA,2BACA,SAAA/D,iBAAA,KAAAuK,eAAA,OAEA,MAAAC,EAAA,gBAAA1M,UAAAqE,QAAA,cAAAsI,OAAA,GAAAC,cAAA,KAAA5M,UAAA8L,MAAA,IACA,KAAAY,IAAA,EAEA,IACA,MAAAG,QAAAC,OAAAC,IAAA,oBAAA/M,aAAA,KAAAkC,iBAAA,CACA8K,OAAA,CACAC,OAAA,KAAAR,kBAIAS,EAAA,KAAAlN,UAAAqE,QAAA,qBACA,4BAAArE,UACA,KAAA4D,WAAAiJ,EAAA/G,KAEA,KAAAoH,GAAAL,EAAA/G,KAEA,MAAAgE,GACAH,QAAAG,MAAA,uBAAA9J,aAAA8J,GACA,QACA,KAAA4C,IAAA,IAGA3M,gBAAAjB,GACA,KAAAkB,UAAAlB,EACA,KAAAQ,sBAEA4M,mBAAAiB,GACA,KAAApL,gBAAA,IACAoL,EACAzE,UAAA,kBAAAyE,EAAAzE,UACAe,KAAAC,MAAAyD,EAAAzE,WACAyE,EAAAzE,WAEA,KAAA5G,uBAAA,GAEAG,4BACA,KAAAH,uBAAA,EACA,KAAAC,gBAAA,MAGAkK,iBAAAkB,GACA,KAAA9K,uBAAA,IACA8K,EACA3L,OAAA,kBAAA2L,EAAA3L,OACAiI,KAAAC,MAAAyD,EAAA3L,QACA2L,EAAA3L,QAEA,KAAAY,qBAAA,GAEAD,0BACA,KAAAC,qBAAA,EACA,KAAAC,uBAAA,MAEAC,eAAA,IAAA8K,EACA,eAAAA,EAAA,KAAA/K,8BAAA,IAAA+K,KAAA5L,OACA,KAAAa,uBAAAb,OADA,IAGAwK,uBAAAmB,GACA,KAAAvK,oBAAA,IACAuK,EACArK,IAAA,kBAAAqK,EAAArK,IACA2G,KAAAC,MAAAyD,EAAArK,KACAqK,EAAArK,KAAA,IAEA,KAAAH,2BAAA,GAEAD,gCACA,KAAAC,2BAAA,EACA,KAAAC,oBAAA,MAEAC,wBAAA,IAAAwK,EACA,eAAAA,EAAA,KAAAzK,2BAAA,IAAAyK,KAAAvK,IACA,KAAAF,oBAAAE,IADA,IAGAsJ,mBAAAe,GACA,KAAAnK,yBAAAmK,EACA,KAAApK,uBAAA,GAEAE,4BACA,KAAAF,uBAAA,EACA,KAAAC,yBAAA,MAEAsD,iBAAA,IAAAgH,EACA,WAAAA,EAAA,KAAAtK,gCAAA,IAAAsK,MAAAvF,UAAA,SAGA,MAAAA,EAAA,KAAA/E,yBAAA+E,UACA,OAAAA,EAAAC,cAAAD,EAAAC,aAAAvG,OAAA,EACAsG,EAAAC,aAIAD,EAAArD,IAAAyD,IAAA,CAAAV,EAAA9F,KAAA,CACA+C,IAAAqD,EAAArD,IAAA/C,GACAgD,KAAAoD,EAAApD,KAAAhD,GACA4L,KAAAxF,EAAAwF,KAAA5L,GACAoD,IAAAgD,EAAAhD,IAAApD,GACAyG,KAAAL,EAAAK,KAAAzG,GACA0G,MAAAN,EAAAM,MAAA1G,OAGAkG,sBAAAS,GACA,KAAAnF,oBAAAmF,EACA,KAAApF,0BAAA,GAEAE,+BACA,KAAAF,0BAAA,EACA,KAAAC,oBAAA,MAEAkJ,mBAAAc,GACA,IACA,MAAAzB,EAAA,kBAAAyB,EAAAxB,aACAlC,KAAAC,MAAAyD,EAAAxB,cACAwB,EAAAxB,cAAA,GACA,KAAA6B,MAAAC,gBAAAC,gBAAA,yBAAAhC,GACA,MAAA/D,GACAgC,QAAAG,MAAA,gCAAAnC,GACA,KAAAgG,SAAA7D,MAAA,oCC7kBkW,ICQ9V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QC8MA,IACfvD,WAAA,CACAqH,qBACAC,aACA9D,uBACAvD,qBACAwD,eACAC,wBACAC,uBACA4D,aAIAzI,KAAA,aACAC,MAAA,GAGAQ,OAAA,MAAAa,EAAA,KAAAC,eACA,OACA5G,UAAA,qBACAC,eAAA,GACAkB,cAAA,GACAN,YAAA,GACAsK,mBAAA,EACA5J,mBAAA,EACAP,iBAAA,EACA+M,kBAAA,CACA,CAAAxK,MAAA,iBAAAuD,UAAA,iBAAAhI,IAAA,kBACA,CACAyE,MAAA,gBACAuD,UAAA,gBACAhI,IAAA,iBAEA,CACAyE,MAAA,kBACAuD,UAAA,uBACAhI,IAAA,uBACAf,OAAAoJ,KAAA,YAEA,CACA5D,MAAA,UACAuD,UAAA,eACAhI,IAAA,eACAf,OAAAoJ,KAAA,aAGAjG,iBAAA,CACA,CACAqC,MAAA,eACAuD,UAAA,eACAhI,IAAA,eACAiI,MAAA,QACAE,UAAA,GAEA,CACA1D,MAAA,OACAuD,UAAA,eACAhI,IAAA,OACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,KACA,IACA,MAAA4K,EAAA,KAAArC,eAAAvI,EAAA6K,aAAA,IACA,OACAvE,SAAAsE,EAAAsC,MAAA,MACA1I,MAAA,CACA+B,MAAA,CACAC,WAAA,SACAC,UAAA,gBAIA,MAAAI,GAEA,OADAgC,QAAAC,KAAA,mCAAAjC,GACA,SAIA,CACApE,MAAA,aACAuD,UAAA,eACAhI,IAAA,aACAiI,MAAA,QACAG,cAAAO,EAAA3G,KACA,QAAAmN,EACA,MAAAvC,EAAA,KAAArC,eAAAvI,EAAA6K,aAAA,IACAuC,EAAA,QAAAD,EAAAvC,EAAAyC,kBAAA,IAAAF,OAAA,EAAAA,EAAAG,WAEA,WAAAF,EAAA,WACA,MAAAvG,GAEA,OADAgC,QAAAC,KAAA,sCAAAjC,GACA,QAIA,CACApE,MAAA,cACAuD,UAAA,MACAhI,IAAA,MACAiI,MAAA,QACAG,cAAAO,EAAA3G,KACA,IACA,MAAA+K,EAAA,KAAAxC,eAAAvI,EAAAgC,IAAA,IACA,OAAA+I,EAAApK,OAEAkF,EAAA,aACA,qBACAkF,EAAAC,MAAA,KAAA3D,IAAA,CAAArF,EAAAnB,IAAAgF,EAAA,WACAhF,EAAA,4GACAmB,EAAArB,OAAA,IAAAqB,EAAAiJ,UAAA,aAAAjJ,KAGA+I,EAAApK,OAAA,GAAAkF,EAAA,wBAEA,YACA,mBACAgB,IACAA,EAAAC,kBACA,KAAAoE,uBAAAlL,MACA,KAEA+K,EAAApK,OAAA,iBAlBA,2BAuBA,MAAAkG,GAEA,OADAgC,QAAAC,KAAA,6BAAAjC,GACA,UAIA,CACApE,MAAA,SACAuD,UAAA,SACAhI,IAAA,SACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,IACA6F,EAAA,4BAEA7F,EAAA,eACA,KAAAuI,gBAAA,mBACA,KAAA4C,qBAKA,CACA1I,MAAA,oBACAuD,UAAA,YACAhI,IAAA,YACAiI,MAAA,MACAE,UAAA,EACAC,cAAAO,EAAA3G,IACA6F,EAAA,wCAEA7F,EAAA,eACA,KAAAuI,gBAAA,mBACA,KAAA6C,uBAKA,CACA3I,MAAA,YACAuD,UAAA,YACAhI,IAAA,YACAiI,MAAA,QACAS,MAAA,SACAN,cAAAO,EAAA3G,KACA,QAAAuN,EACA,MAAAC,EAAA,KAAAjF,eAAAvI,EAAAiH,UAAA,IACA,cAAAuG,QAAA,IAAAA,GAAA,QAAAD,EAAAC,EAAAtG,oBAAA,IAAAqG,KAAA5M,OAEAkF,EAAA,eACAgB,IACAA,EAAAC,kBACA,KAAAwE,mBAAAtL,MACA,CACAwN,EAAAtG,aAAAvG,OAAA,eAPA,MAUA,MAAAkG,GAEA,OADAgC,QAAAC,KAAA,8BAAAjC,GACA,UAIA,CACApE,MAAA,eACAuD,UAAA,eACAhI,IAAA,eACAiI,MAAA,QACAS,MAAA,SACAN,cAAAO,EAAA3G,IAAA6F,EAAA,cACA,8CAAAA,EAAA,eAEAgB,IACAA,EAAAC,kBACA,KAAAyE,mBAAAvL,KACA,MACA,0DAQAF,eAAA,CACA,CACA2C,MAAA,OACAuD,UAAA,OACAhI,IAAA,OACAiI,MAAA,QACAG,aAAAC,MAAA,QAEA,CACA5D,MAAA,aACAuD,UAAA,aACAhI,IAAA,aACAiI,MAAA,QACAG,aAAAC,MAAA,QAEA,CACA5D,MAAA,SACAuD,UAAA,SACAhI,IAAA,SACAiI,MAAA,QACAG,aAAAC,MAAA,QAEA,CACA5D,MAAA,QACAuD,UAAA,QACAhI,IAAA,QACAiI,MAAA,QACAG,aAAAC,MAAA,QAEA,CACA5D,MAAA,OACAuD,UAAA,OACAhI,IAAA,OACAiI,MAAA,OACAG,aAAAC,QACAoH,IAAApH,GAAA,OAAAA,EAAA,OACAA,EAAA,YAGA,CACA5D,MAAA,WACAuD,UAAA,WACAhI,IAAA,WACAiI,MAAA,OACAG,aAAAC,QACAoH,IAAApH,GAAA,OAAAA,EAAA,OACAA,EAAA,YAGA,CACA5D,MAAA,SACAuD,UAAA,SACAhI,IAAA,SACAiI,MAAA,QACAG,aAAAsH,GACAA,GAAA,IAAA7I,OAAA0F,KAAAmD,GAAA/M,OACAkE,OAAA8I,QAAAD,GAAArG,IAAA,EAAArJ,EAAAY,KAAA,GAAAZ,KAAAY,KAAAa,KAAA,MADA,QAIA,CACAgD,MAAA,UACAuD,UAAA,aACAhI,IAAA,aACAiI,MAAA,QACAG,aAAAC,MAAA,SAGAlG,WAAA,CACAI,SAAA,KAEAqN,gBAAA,CACA,4CACA,yCACA,sCAKA5M,uBAAA,EACAC,gBAAA,KACAK,qBAAA,EACAC,uBAAA,KACAM,2BAAA,EACAC,oBAAA,KACAG,uBAAA,EACAC,yBAAA,KACAE,0BAAA,EACAC,oBAAA,KACAxD,aAAA,WAGA+G,SAAA,IACA4F,eAAA,qDAEAvG,MAAA,CACA7D,eAAAyM,GACA,KAAAC,YACAD,GACA,KAAArP,uBAIAuP,UACA,KAAA3M,gBACA,KAAA5C,sBAGA2G,QAAA,CACAlG,gBAAAjB,GACA,KAAAkB,UAAAlB,EACA,KAAA8P,YACA,KAAAtP,sBAEA,2BACA,SAAA4C,eAGA,OAFAyH,QAAAG,MAAA,+BACA,KAAA8E,YAGA,MAAAE,EAAA,KAAA9O,UACA+O,EAAA,KAAAC,oBAAAF,GACApC,EAAA,eAAAuC,sBAAAF,EAAA1K,QAAA,YAEA,KAAAqI,IAAA,EAEA,IACA,MAAAG,QAAAC,OAAAC,IAAA,eAAA+B,KAAA,KAAA5M,iBAAA,CACA8K,OAAA,CACAC,OAAA,KAAAR,kBAGA3G,EAAA+G,EAAA/G,KACA,KAAAiJ,GAAA,uBAAAD,GAAAhJ,EAAA,CAAAA,KACA,MAAAgE,GACAH,QAAAG,MAAA,kBAAAgF,KAAAhF,GACA,KAAAiF,GAAA,GACA,QACA,KAAArC,IAAA,IAGAsC,oBAAAE,GACA,MAAAC,EAAAD,EAAA7K,QAAA,cACA,OAAA8K,EAAA9K,QAAA,aAAAoD,EAAA2H,MAAAxC,eAAA,QAEAqC,sBAAAI,GACA,OAAAA,EAAA1C,OAAA,GAAAC,cAAAyC,EAAAvD,MAAA,IAEA8C,YACA,KAAA3O,eAAA,GACA,KAAAkB,cAAA,GACA,KAAAN,YAAA,IAEAV,gBAAArB,GAAA,IAAAwQ,EACA,eAAAA,EAAA,KAAArP,eAAA,cAAAqP,KAAAC,cACA,KAAAtP,eAAA,GAAAsP,cAAAzQ,GADA,MAGA2B,qBACA,MAAA+O,EAAA,KAAArP,gBAAA,kBACA,cAAAqP,QAAA,IAAAA,OAAA,EAAAA,EAAAC,UAAA,IAEArP,YAAAsP,GACA,IAAAA,EAAA,YACA,MAAAC,EAAA,KACAC,EAAA,0BACAC,EAAAC,KAAAC,MAAAD,KAAAE,IAAAN,GAAAI,KAAAE,IAAAL,IACA,SAAAM,YAAAP,EAAAI,KAAAI,IAAAP,EAAAE,IAAAM,QAAA,OAAAP,EAAAC,MAEAxG,eAAAE,EAAAC,GACA,IACA,OAAAvB,MAAAC,QAAAqB,IAGA,kBAAAA,GAAA,OAAAA,EAFA,KAAA6G,oBAAA7G,GAKA,kBAAAA,EAAAE,KAAAC,MAAAH,GAAAC,EACA,MAAA7B,GAEA,OADAgC,QAAAC,KAAA,8BAAAjC,GACA6B,IAIA6C,mBAAAc,GACA,IACA,MAAAzB,EAAA,KAAArC,eAAA8D,EAAAxB,aAAA,IACA,KAAA6B,MAAAC,gBAAAC,gBAAA,yBAAAhC,GACA,MAAA/D,GACAgC,QAAAG,MAAA,gCAAAnC,GACA,KAAAgG,SAAA7D,MAAA,kCAGAuG,sBAAA7M,EAAA8M,EAAAC,EAAAC,GAAA,MAAA7J,EAAA,KAAAC,eACA,OAAApD,KAAA/B,OAEAkF,EAAA,aACA,qBACAnD,EAAAsI,MAAA,KAAA3D,IAAA,CAAA1E,EAAA9B,IAAA2O,EAAA7M,EAAA9B,IACA6B,EAAA/B,OAAA,GAAAkF,EAAA,wBAEA,YACA,mBACAgB,IACAA,EAAAC,kBACA4I,OACA,KAEAhN,EAAA/B,OAAA,MAAA8O,MAdA,QAoBAH,oBAAAK,GACA,IAAAA,EAAA,YACA,GAAAxI,MAAAC,QAAAuI,GACA,OAAAA,EAAAtI,IAAA1E,GAAA,KAAA2M,oBAAA3M,IAEA,qBAAAgN,EAAA,CACA,MAAAC,EAAA,GACA,UAAA5R,KAAA2R,EACA3R,EAAA6R,WAAA,MAAA7R,EAAA6R,WAAA,iBAAA7R,IACA4R,EAAA5R,GAAA,KAAAsR,oBAAAK,EAAA3R,KAGA,OAAA4R,EAEA,OAAAD,GAEAnK,iBAAA,IAAAgH,EACA,eAAAA,EAAA,KAAAtK,gCAAA,IAAAsK,GAAA,QAAAA,IAAAvF,iBAAA,IAAAuF,KAAAtF,aACA,KAAAhF,yBAAA+E,UAAAC,aADA,IAGAQ,sBAAA,IAAAoI,EACA,WAAAA,EAAA,KAAAC,yBAAA,IAAAD,MAAAlI,UAAA,SACA,MAAAmB,EAAA,KAAAR,eAAA,KAAAwH,kBAAAnI,UAAA,IACA,OAAAmB,EAAAlB,iBAAA,IAEAuD,mBAAAiB,GACA,KAAApL,gBAAA,IACAoL,EACAzE,UAAA,KAAAW,eAAA8D,EAAAzE,UAAA,KAEA,KAAA5G,uBAAA,GAEAG,4BACA,KAAAH,uBAAA,EACA,KAAAC,gBAAA,MAGAkK,iBAAAkB,GACA,KAAA9K,uBAAA,IACA8K,EACA3L,OAAA,KAAA6H,eAAA8D,EAAA3L,OAAA,KAEA,KAAAY,qBAAA,GAEAD,0BACA,KAAAC,qBAAA,EACA,KAAAC,uBAAA,MAEAC,eAAA,IAAA8K,EACA,eAAAA,EAAA,KAAA/K,8BAAA,IAAA+K,KAAA5L,OACA,KAAAa,uBAAAb,OADA,IAGAwK,uBAAAmB,GACA,KAAAvK,oBAAA,IACAuK,EACArK,IAAA,KAAAuG,eAAA8D,EAAArK,IAAA,KAEA,KAAAH,2BAAA,GAEAD,gCACA,KAAAC,2BAAA,EACA,KAAAC,oBAAA,MAEAC,wBAAA,IAAAwK,EACA,eAAAA,EAAA,KAAAzK,2BAAA,IAAAyK,KAAAvK,IACA,KAAAF,oBAAAE,IADA,IAGAsJ,mBAAAe,GACA,KAAAnK,yBAAA,IACAmK,EACApF,UAAA,KAAAsB,eAAA8D,EAAApF,UAAA,KAEA,KAAAhF,uBAAA,GAEAE,4BACA,KAAAF,uBAAA,EACA,KAAAC,yBAAA,MAEA6E,sBAAAS,GACA,KAAAnF,oBAAAmF,EACA,KAAApF,0BAAA,GAEAE,+BACA,KAAAF,0BAAA,EACA,KAAAC,oBAAA,QC3tBkW,MCQ9V,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,WACA,OAIa,M,QCJA,IACfoD,WAAA,CACAuK,gBCjB+U,MCO3U,GAAY,eACd,GACA/S,EACAO,GACA,EACA,KACA,KACA,MAIa,gB,6CClBf,W,6DCAA,W,oFCAA,W,oCCAA,W,oCCAA,W,6DCAA,W,oFCAA,W,yDCAA,W,yDCAA,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,QAElEI,EAAkB,G,uDCQP,GACf+G,KAAA,kBACAY,QAAA,CAOAyH,gBAAAnK,EAAAuC,EAAAiL,EAAA,UAAApK,EAAA,KAAAC,eAEAoK,EAAAlB,KAAAmB,IAAAF,EAAAhK,OAAA,QAAAmK,OAAAC,YACAC,EAAAtB,KAAAmB,IAAA,OAAAC,OAAAG,aAGAC,EAAA,CACAC,OAAA,UAAAC,KAAAC,MACAC,QAAA,WAAAF,KAAAC,MACAE,MAAA,SAAAH,KAAAC,OAIA,IAAAG,GAAA,EAGA,MAAAC,EAAAlL,EAAA,aACA,wEAAAA,EAAA,aACA,wCAAAA,EAAA,sBACA,sDAAAA,EAAA,cACA,sBAAApD,MAAAoD,EAAA,aAEA,wCAAAA,EAAA,iBACA2K,EAAAI,SAAA,+EAAA/K,EAAA,qBAEA2K,EAAAC,OAAA,YACA,qDAAA5K,EAAA,sBAEA,kDACA,kBAAAA,EAAA,sBAGA2K,EAAAK,MAAA,KACA,YACA,kBAEA,cADA,uDAAAhL,EAAA,sBAIA,gBACA,YACA,aAEA,cADA,2DAQAmL,EAAA,kBAAAhM,EAAAa,EAAA,aACA,WAAAyK,gGAAA,gEAAAzK,EAAA,aAGA,WAAAyK,6NAAA,CACAvL,OAAAC,KAKA,KAAAiM,MAAAC,SAAA,CACAzO,MAAAsO,EACAI,QAAAH,EACA/K,MAAAiK,EACAkB,OAAAnB,EAAAmB,QAAA,KACAC,KAAA,KACAC,kBAAA,CAAA/K,MAAA,CAAAgL,QAAA,SACArT,MAAA,eACAsT,cAAA,EACAC,iBAAAC,SAAAC,KAAAC,YAAAF,SAAAG,cAAA,UAIAC,WAAA,KACA,qBAAA9M,EAAA,CAEA,MAAAqH,EAAAqF,SAAAK,eAAA,kBACA,GAAA1F,EAAA,CAEA,MAAA2F,EAAA,IAAAC,OAAA,CACAhV,OAAA4I,KAAAqM,IAAA,CACA1N,MAAA,CACAQ,OACAmN,KAAAC,IACAC,kBAAA,EACAC,YAAA,EACAC,iBAAA,GAEAhM,MAAA,CACAiM,OAAA,OACAC,SAAA,YAMAT,EAAAU,SACArG,EAAAuF,YAAAI,EAAAW,MAKA,MAAAC,EAAAlB,SAAAK,eAAAvB,EAAAC,QACAoC,EAAAnB,SAAAK,eAAAvB,EAAAI,SAGA,IAAAkC,EAAA,GACAC,GAAA,EAGA,GAAAH,GAAAC,EAAA,CAEA,MAAAG,EAAAC,IAQA,GANAH,EAAA,GACAC,GAAA,EAGAF,EAAAK,YAAA,GAEAD,EAEA,IAEA,MAAAE,EAAAzB,SAAA0B,iBAAA,wBAGAC,EAAA,IAAAC,OAAAL,EAAA1P,QAAA,oCA+BA,GA5BAmO,SAAA0B,iBAAA,qBAAAG,QAAAC,IACAA,EAAAC,UAAAC,OAAA,sBAGAhC,SAAA0B,iBAAA,uBAAAG,QAAAC,IACAA,EAAAC,UAAAC,OAAA,wBAIAP,EAAAI,QAAAI,IACA,MAAAtN,EAAAsN,EAAAT,YACA,IAAAzJ,EACA4J,EAAAO,UAAA,EAEA,cAAAnK,EAAA4J,EAAAQ,KAAAxN,IACAyM,EAAA5M,KAAA,CACAyN,OACAtN,KAAAoD,EAAA,KAIAA,EAAA5I,QAAAwS,EAAAO,WACAP,EAAAO,cAMA,IAAAd,EAAAnS,OAEA,YADAkS,EAAAK,YAAA,QAIAL,EAAAK,YAAA,KAAAJ,EAAAnS,OAGAmS,EAAAS,QAAA9J,IACAA,EAAAkK,KAAAF,UAAAK,IAAA,sBAEA,MAAA9K,GACAH,QAAAG,MAAA,QAAAA,KAKA+K,EAAAlT,IACA,OAAAiS,EAAAnS,OAAA,OAGAE,EAAAmO,KAAAgF,IAAA,EAAAhF,KAAAmB,IAAA2C,EAAAnS,OAAA,EAAAE,IAGAkS,GAAA,GAAAA,EAAAD,EAAAnS,QACAmS,EAAAC,GAAAY,KAAAF,UAAAC,OAAA,sBAIAX,EAAAlS,EACAgS,EAAAK,YAAA,GAAAH,EAAA,KAAAD,EAAAnS,SAGA,MAAAsT,EAAAnB,EAAAC,GACA,GAAAkB,EAAA,CACAA,EAAAN,KAAAF,UAAAK,IAAA,sBAGA,IAAAI,EAAAD,EAAAN,KAAAQ,cACA,MAAAD,EAAA,CACA,GAAAA,EAAAT,WAAAS,EAAAT,UAAAW,SAAA,mBAEAF,EAAAT,UAAAW,SAAA,gBACA,MAAAC,EAAAH,EAAAI,cAAA,sBACAD,KAAAE,QAGAL,IAAAC,cAIAF,EAAAN,KAAAa,eAAA,CAAAC,SAAA,SAAAC,MAAA,aAKA,IAAAC,EACA/B,EAAAgC,iBAAA,QAAA/N,IAEA8N,GAAAE,aAAAF,GACAA,EAAA7C,WAAA,KACAkB,EAAAnM,EAAAiO,OAAAlW,MAAA8K,SACA,OAIAkJ,EAAAgC,iBAAA,UAAA/N,IACA,UAAAA,EAAA7I,MACA6I,EAAAkO,iBACAhB,EAAAlN,EAAAmO,SAAAjC,EAAA,EAAAA,EAAA,MAMA,MAAAkC,EAAAvD,SAAAK,eAAAvB,EAAAK,OACAoE,GACAA,EAAAL,iBAAA,aACA,MAAAvI,EAAAqF,SAAA4C,cAAA,mBACAjI,IAEAA,EAAAoH,UAAAW,SAAA,gBACA/H,EAAAoH,UAAAC,OAAA,eACArH,EAAAoH,UAAAK,IAAA,cACAzH,EAAA9F,MAAA2O,gBAAA,UACApE,GAAA,IAEAzE,EAAAoH,UAAAC,OAAA,cACArH,EAAAoH,UAAAK,IAAA,eACAzH,EAAA9F,MAAA2O,gBAAA,OACApE,GAAA,MAOA,MAAAqE,EAAAzD,SAAAK,eAAA,YACAoD,GACAA,EAAAP,iBAAA,aACA,IACA,MAAAQ,EAAA,kBAAApQ,EAAA2D,KAAA0M,UAAArQ,EAAA,QAAAD,OAAAC,GAGA,GAAAsQ,UAAAC,WAAAnF,OAAAoF,gBACAF,UAAAC,UAAAE,UAAAL,GACAM,KAAA,KACA,KAAA7I,SAAA8I,QAAA,KAAArX,GAAA,+BAEAsX,MAAAC,IACAhN,QAAAG,MAAA,QAAA6M,GACA,KAAAhJ,SAAA7D,MAAA,cAEA,CAEA,MAAA8M,EAAApE,SAAAG,cAAA,YACAiE,EAAAlX,MAAAwW,EACA1D,SAAAC,KAAAC,YAAAkE,GACAA,EAAAC,SACA,MAAAC,EAAAtE,SAAAuE,YAAA,QACAvE,SAAAC,KAAAuE,YAAAJ,GAEAE,EACA,KAAAnJ,SAAA8I,QAAA,KAAArX,GAAA,6BAEA,KAAAuO,SAAA7D,MAAA,KAAA1K,GAAA,uBAGA,MAAAuX,GACA,KAAAhJ,SAAA7D,MAAA,KAAA1K,GAAA,0BAIA,QCtTuW,I,wBCQnW+G,EAAY,eACd,EACApI,EACAO,GACA,EACA,KACA,KACA,MAIa,OAAA6H,E,kECnBf,IAAIpI,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACc,MAAM,CAAC,iBAAkB,QAAQhB,EAAIiB,cAAgBb,MAAM,CAAC,KAAO,UAAUiB,GAAG,CAAC,MAAQ,SAAS2C,GAAQ,OAAOhE,EAAIkI,MAAM,cAAc,CAAClI,EAAIkB,GAAG,IAAIlB,EAAImB,GAAGnB,EAAImJ,MAAQnJ,EAAIoB,GAAG,mBAAmB,QAEhRd,EAAkB,G,YCWP,GACfoI,SAAA,IACA4F,eAAA,mBAEAjH,KAAA,gBACAC,MAAA,CACA6B,KAAA,CACA3B,KAAAK,OACAD,QAAA,MCrBqW,I,YCOjWO,EAAY,eACd,EACApI,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAA6H,E", "file": "static/js/chunk-b7ea2bd6.65080b43.js", "sourcesContent": ["export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningModal.vue?vue&type=style&index=0&id=18c6d2e8&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('DockerInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full docker-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"25\",\"height\":\"25\",\"viewBox\":\"0 0 24 24\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .***************.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .***************.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .***************.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.docker')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchActiveTabData}})],1)])]},proxy:true}])},[_c('JsonDetailModal',{ref:\"jsonDetailModal\"}),_c('div',{staticClass:\"container-runtime-tabs\"},[_c('a-tabs',{model:{value:(_vm.activeEngine),callback:function ($$v) {_vm.activeEngine=$$v},expression:\"activeEngine\"}},[_c('a-tab-pane',{key:\"docker\",attrs:{\"tab\":\"Docker\"}},[_c('a-tabs',{on:{\"change\":_vm.handleTabChange},model:{value:(_vm.activeTab),callback:function ($$v) {_vm.activeTab=$$v},expression:\"activeTab\"}},[_c('a-tab-pane',{key:\"docker_host_config\",attrs:{\"tab\":\"Host Config\"}},[(_vm.activeTab === 'docker_host_config')?_c('div',{staticClass:\"host-config-container\"},[_c('a-row',{attrs:{\"gutter\":[16, 16]}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Basic Information\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Docker Version:\")]),_vm._v(\" \"+_vm._s(_vm.hostConfigData[0]?.docker_version))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Operating System:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('OperatingSystem')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Architecture:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('Architecture')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Kernel Version:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('KernelVersion')))])])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Resources\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"CPU Cores:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('NCPU')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Total Memory:\")]),_vm._v(\" \"+_vm._s(_vm.formatBytes(_vm.getDaemonConfig('MemTotal'))))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Docker Root Dir:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('DockerRootDir')))])])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Security\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"User in Docker Group:\")]),_vm._v(\" \"+_vm._s(_vm.hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No'))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Root User:\")]),_vm._v(\" \"+_vm._s(_vm.hostConfigData[0]?.is_root_user ? 'Yes' : 'No'))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Security Options:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('SecurityOptions')?.join(', ') || 'None'))])])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Container Statistics\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Total Containers:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('Containers')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Running:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('ContainersRunning')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Stopped:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('ContainersStopped')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Images:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('Images')))])])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Storage Driver\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Driver:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('Driver')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Logging Driver:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('LoggingDriver')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Cgroup Driver:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('CgroupDriver')))])])],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"Registry Configuration\",\"bordered\":false}},[_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Index Server:\")]),_vm._v(\" \"+_vm._s(_vm.getDaemonConfig('IndexServerAddress')))]),_c('p',{staticClass:\"info-item\"},[_c('strong',[_vm._v(\"Registry Mirrors:\")])]),_c('ul',_vm._l((_vm.getRegistryMirrors()),function(mirror){return _c('li',{key:mirror,staticClass:\"info-item\"},[_vm._v(_vm._s(mirror))])}),0)])],1)],1)],1):_vm._e()]),_c('a-tab-pane',{key:\"docker_network\",attrs:{\"tab\":\"Networks\"}},[(_vm.activeTab === 'docker_network')?_c('a-table',{attrs:{\"columns\":_vm.networkColumns,\"data-source\":_vm.networkData,\"rowKey\":(record) => record.network_id,\"loading\":_vm.loadingNetworks,\"pagination\":_vm.pagination}}):_vm._e()],1),_c('a-tab-pane',{key:\"docker_container\",attrs:{\"tab\":\"Containers\"}},[(_vm.activeTab === 'docker_container')?_c('a-table',{attrs:{\"columns\":_vm.containerColumns,\"data-source\":_vm.containerData,\"scroll\":{ x: 1500 },\"pagination\":{ pageSize: 20 },\"row-key\":record => record.container_id,\"loading\":_vm.loadingContainers},scopedSlots:_vm._u([{key:\"mountsColumn\",fn:function({ record }){return [(record)?_c('div',[(record.mounts && record.mounts.length)?_vm._l((record.mounts),function(mount,index){return _c('div',{key:index},[_vm._v(\" \"+_vm._s(mount.Source)+\" → \"+_vm._s(mount.Destination)+\" \")])}):_c('span',[_vm._v(\"N/A\")])],2):_vm._e()]}}],null,false,2075805027)}):_vm._e(),_c('network-listening-modal',{attrs:{\"visible\":_vm.networkDetailsVisible,\"network-data\":_vm.selectedNetwork},on:{\"update:visible\":function($event){_vm.networkDetailsVisible = $event},\"close\":_vm.handleNetworkDetailsClose}})],1)],1)],1),_c('a-tab-pane',{key:\"crictl\",attrs:{\"tab\":\"CRI\"}},[_c('crictl-info',{attrs:{\"node-ip\":_vm.selectedNodeIp}})],1)],1)],1),_c('a-modal',{attrs:{\"title\":\"Mount Details\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleMountDetailsClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleMountDetailsClose}},[_vm._v(\"Cancel\")])]},proxy:true}]),model:{value:(_vm.mountDetailsVisible),callback:function ($$v) {_vm.mountDetailsVisible=$$v},expression:\"mountDetailsVisible\"}},[(_vm.selectedMountContainer)?[_c('div',{staticClass:\"mounts-container\"},_vm._l((_vm.getAllMounts()),function(mount,index){return _c('div',{key:index,staticClass:\"mount-item\"},[_c('div',{staticClass:\"mount-path\"},[_c('span',{staticClass:\"mount-source\"},[_vm._v(_vm._s(mount.Source))]),_c('span',{staticClass:\"mount-arrow\"},[_vm._v(\"→\")]),_c('span',{staticClass:\"mount-dest\"},[_vm._v(_vm._s(mount.Destination))])]),_c('div',{staticClass:\"mount-details\"},[(mount.Mode)?_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(mount.Mode))]):_vm._e(),_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(mount.RW ? 'RW' : 'RO'))]),(mount.Propagation)?_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(mount.Propagation))]):_vm._e()])])}),0)]:_vm._e()],2),_c('a-modal',{attrs:{\"title\":\"Environment Variables\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleEnvironmentDetailsClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleEnvironmentDetailsClose}},[_vm._v(\"Close\")])]},proxy:true}]),model:{value:(_vm.environmentDetailsVisible),callback:function ($$v) {_vm.environmentDetailsVisible=$$v},expression:\"environmentDetailsVisible\"}},[(_vm.selectedEnvironment)?[_c('div',{staticClass:\"env-container\"},_vm._l((_vm.getAllEnvironmentVars()),function(env,index){return _c('div',{key:index,staticClass:\"env-item\"},[_vm._v(\" \"+_vm._s(env)+\" \")])}),0)]:_vm._e()],2),_c('process-table',{attrs:{\"visible\":_vm.processDetailsVisible,\"process-container\":_vm.selectedProcessContainer,\"user-field\":\"uid\",\"include-tty\":true},on:{\"update:visible\":function($event){_vm.processDetailsVisible = $event},\"close\":_vm.handleProcessDetailsClose}}),_c('process-detail-modal',{attrs:{\"visible\":_vm.processDetailInfoVisible,\"process-info\":_vm.selectedProcessInfo,\"user-field\":\"uid\"},on:{\"update:visible\":function($event){_vm.processDetailInfoVisible = $event},\"close\":_vm.handleProcessDetailInfoClose}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full crictl-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 }}},[_c('JsonDetailModal',{ref:\"jsonDetailModal\"}),_c('div',{staticClass:\"crictl-info-container\"},[_c('a-tabs',{on:{\"change\":_vm.handleTabChange},model:{value:(_vm.activeTab),callback:function ($$v) {_vm.activeTab=$$v},expression:\"activeTab\"}},[_c('a-tab-pane',{key:\"crictl_host_config\",attrs:{\"tab\":\"Host Config\"}},[(_vm.activeTab === 'crictl_host_config')?_c('div',{staticClass:\"host-config-container\"},[_c('a-row',{attrs:{\"gutter\":[16, 16]}},_vm._l((_vm.hostConfigCards),function(card){return _c('a-col',{key:card.title,attrs:{\"span\":8}},[_c('a-card',{staticClass:\"host-config-card\",attrs:{\"title\":card.title,\"bordered\":false}},_vm._l((card.items),function(item,index){return _c('p',{key:index,staticClass:\"info-item\"},[_c('strong',[_vm._v(_vm._s(item.label)+\":\")]),_vm._v(\" \"+_vm._s(item.getValue(_vm.hostConfig))+\" \")])}),0)],1)}),1)],1):_vm._e()]),_c('a-tab-pane',{key:\"crictl_pod\",attrs:{\"tab\":\"Pods\"}},[_c('a-table',{attrs:{\"columns\":_vm.podColumns,\"data-source\":_vm.podData,\"rowKey\":record => record.pod_id,\"loading\":_vm.loadingPods,\"pagination\":{ pageSize: 30 },\"scroll\":{ x: 1500 }}})],1),_c('a-tab-pane',{key:\"crictl_container\",attrs:{\"tab\":\"Containers\"}},[_c('a-table',{attrs:{\"columns\":_vm.containerColumns,\"data-source\":_vm.containerData,\"rowKey\":record => record.container_id,\"loading\":_vm.loadingContainers,\"pagination\":{ pageSize: 20 },\"scroll\":{ x: 1800 }}})],1)],1)],1),_c('network-listening-modal',{attrs:{\"visible\":_vm.networkDetailsVisible,\"network-data\":_vm.selectedNetwork},on:{\"update:visible\":function($event){_vm.networkDetailsVisible = $event},\"close\":_vm.handleNetworkDetailsClose}}),_c('a-modal',{attrs:{\"title\":\"Environment Variables\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleEnvironmentDetailsClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleEnvironmentDetailsClose}},[_vm._v(\"Close\")])]},proxy:true}]),model:{value:(_vm.environmentDetailsVisible),callback:function ($$v) {_vm.environmentDetailsVisible=$$v},expression:\"environmentDetailsVisible\"}},[(_vm.selectedEnvironment)?[_c('div',{staticClass:\"env-container\"},_vm._l((_vm.getAllEnvironmentVars()),function(env,index){return _c('div',{key:index,staticClass:\"env-item\"},[_vm._v(\" \"+_vm._s(env)+\" \")])}),0)]:_vm._e()],2),_c('a-modal',{attrs:{\"title\":\"Mount Details\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleMountDetailsClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleMountDetailsClose}},[_vm._v(\"Cancel\")])]},proxy:true}]),model:{value:(_vm.mountDetailsVisible),callback:function ($$v) {_vm.mountDetailsVisible=$$v},expression:\"mountDetailsVisible\"}},[(_vm.selectedMountContainer)?[_c('div',{staticClass:\"mounts-container\"},_vm._l((_vm.getAllMounts()),function(mount,index){return _c('div',{key:index,staticClass:\"mount-item\"},[_c('div',{staticClass:\"mount-path\"},[_c('span',{staticClass:\"mount-source\"},[_vm._v(_vm._s(mount.host_path))]),_c('span',{staticClass:\"mount-arrow\"},[_vm._v(\"→\")]),_c('span',{staticClass:\"mount-dest\"},[_vm._v(_vm._s(mount.container_path))])]),_c('div',{staticClass:\"mount-details\"},[_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(mount.readonly ? 'RO' : 'RW'))]),(mount.propagation)?_c('span',{staticClass:\"mount-tag\"},[_vm._v(\" \"+_vm._s(mount.propagation.replace('PROPAGATION_', ''))+\" \")]):_vm._e(),(mount.selinux_relabel)?_c('span',{staticClass:\"mount-tag\"},[_vm._v(\"SELinux Relabel\")]):_vm._e()])])}),0)]:_vm._e()],2),_c('process-table',{attrs:{\"visible\":_vm.processDetailsVisible,\"process-container\":_vm.selectedProcessContainer,\"user-field\":\"user\",\"include-tty\":false},on:{\"update:visible\":function($event){_vm.processDetailsVisible = $event},\"close\":_vm.handleProcessDetailsClose}}),_c('process-detail-modal',{attrs:{\"visible\":_vm.processDetailInfoVisible,\"process-info\":_vm.selectedProcessInfo,\"user-field\":\"user\"},on:{\"update:visible\":function($event){_vm.processDetailInfoVisible = $event},\"close\":_vm.handleProcessDetailInfoClose}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-modal',{staticClass:\"process-detail-modal\",attrs:{\"visible\":_vm.localVisible,\"title\":\"Process Detailed Information\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleClose}},[_vm._v(\"Close\")])]},proxy:true}])},[(_vm.processInfo)?[_c('a-tabs',{attrs:{\"default-active-key\":\"1\"}},[_c('a-tab-pane',{key:\"1\",attrs:{\"tab\":\"Basic Info\"}},[_c('a-descriptions',{attrs:{\"bordered\":\"\",\"column\":1}},[_c('a-descriptions-item',{attrs:{\"label\":\"PID\"}},[_vm._v(_vm._s(_vm.processInfo.pid))]),_c('a-descriptions-item',{attrs:{\"label\":\"PPID\"}},[_vm._v(_vm._s(_vm.processInfo.ppid))]),_c('a-descriptions-item',{attrs:{\"label\":\"UID\"}},[_vm._v(_vm._s(_vm.processInfo[_vm.userField] || 'N/A'))]),_c('a-descriptions-item',{attrs:{\"label\":\"GID\"}},[_vm._v(_vm._s(_vm.processInfo.gid || 'N/A'))]),_c('a-descriptions-item',{attrs:{\"label\":\"State\"}},[_vm._v(_vm._s(_vm.processInfo.state || 'N/A'))]),_c('a-descriptions-item',{attrs:{\"label\":\"Command\"}},[_vm._v(_vm._s(_vm.processInfo.cmd))]),_c('a-descriptions-item',{attrs:{\"label\":\"Executable Path\"}},[_vm._v(_vm._s(_vm.processInfo.exe || 'N/A'))]),_c('a-descriptions-item',{attrs:{\"label\":\"Working Directory\"}},[_vm._v(_vm._s(_vm.processInfo.cwd || 'N/A'))])],1)],1),(_vm.processInfo.capability)?_c('a-tab-pane',{key:\"2\",attrs:{\"tab\":\"Capabilities\"}},[_c('pre',{staticClass:\"detail-pre\"},[_vm._v(_vm._s(_vm.processInfo.capability))])]):_vm._e(),(_vm.processInfo.environ)?_c('a-tab-pane',{key:\"3\",attrs:{\"tab\":\"Environment Variables\"}},[_c('pre',{staticClass:\"detail-pre\"},[_vm._v(_vm._s(_vm.processInfo.environ))])]):_vm._e(),(_vm.processInfo.memory_maps)?_c('a-tab-pane',{key:\"4\",attrs:{\"tab\":\"Memory Maps\"}},[_c('pre',{staticClass:\"detail-pre\"},[_vm._v(_vm._s(_vm.processInfo.memory_maps))])]):_vm._e()],1)]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-modal\r\n    :visible=\"localVisible\"\r\n    title=\"Process Detailed Information\"\r\n    width=\"800px\"\r\n    @cancel=\"handleClose\"\r\n    class=\"process-detail-modal\"\r\n  >\r\n    <template v-slot:footer>\r\n      <a-button @click=\"handleClose\">Close</a-button>\r\n    </template>\r\n    <template v-if=\"processInfo\">\r\n      <a-tabs default-active-key=\"1\">\r\n        <a-tab-pane key=\"1\" tab=\"Basic Info\">\r\n          <a-descriptions bordered :column=\"1\">\r\n            <a-descriptions-item label=\"PID\">{{ processInfo.pid }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"PPID\">{{ processInfo.ppid }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"UID\">{{ processInfo[userField] || 'N/A' }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"GID\">{{ processInfo.gid || 'N/A' }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"State\">{{ processInfo.state || 'N/A' }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"Command\">{{ processInfo.cmd }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"Executable Path\">{{ processInfo.exe || 'N/A' }}</a-descriptions-item>\r\n            <a-descriptions-item label=\"Working Directory\">{{ processInfo.cwd || 'N/A' }}</a-descriptions-item>\r\n          </a-descriptions>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"2\" tab=\"Capabilities\" v-if=\"processInfo.capability\">\r\n          <pre class=\"detail-pre\">{{ processInfo.capability }}</pre>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"3\" tab=\"Environment Variables\" v-if=\"processInfo.environ\">\r\n          <pre class=\"detail-pre\">{{ processInfo.environ }}</pre>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"4\" tab=\"Memory Maps\" v-if=\"processInfo.memory_maps\">\r\n          <pre class=\"detail-pre\">{{ processInfo.memory_maps }}</pre>\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </template>\r\n  </a-modal>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProcessDetailModal',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      required: true\r\n    },\r\n    processInfo: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    // 允许自定义用户字段名称（uid或user）\r\n    userField: {\r\n      type: String,\r\n      default: 'user'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localVisible: this.visible\r\n    };\r\n  },\r\n  watch: {\r\n    visible(newValue) {\r\n      this.localVisible = newValue;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClose() {\r\n      this.localVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('close');\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  background: none !important; /* 确保没有背景 */\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n/* 确保Tab内容区域没有背景色 */\r\n.ant-tabs-tabpane {\r\n  background-color: transparent !important;\r\n  background: none !important;\r\n}\r\n\r\n/* 确保Ant Design Vue的Tab内容区域没有背景色 */\r\n.ant-tabs-content {\r\n  background-color: transparent !important;\r\n  background: none !important;\r\n}\r\n\r\n/* 确保Ant Design Vue的Tab面板没有背景色 */\r\n.ant-tabs-tabpane-active {\r\n  background-color: transparent !important;\r\n  background: none !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetailModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetailModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProcessDetailModal.vue?vue&type=template&id=655588c5&scoped=true\"\nimport script from \"./ProcessDetailModal.vue?vue&type=script&lang=js\"\nexport * from \"./ProcessDetailModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./ProcessDetailModal.vue?vue&type=style&index=0&id=655588c5&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"655588c5\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-modal',{staticClass:\"process-table-modal\",attrs:{\"visible\":_vm.localVisible,\"title\":\"Process Details\",\"width\":\"1500px\"},on:{\"cancel\":_vm.handleClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleClose}},[_vm._v(\"Close\")])]},proxy:true}])},[(_vm.processContainer)?[_c('a-table',{attrs:{\"columns\":_vm.processColumns,\"dataSource\":_vm.getProcessList(),\"pagination\":{ pageSize: 10 },\"size\":\"middle\"}})]:_vm._e()],2),_c('process-detail-modal',{attrs:{\"visible\":_vm.processDetailInfoVisible,\"process-info\":_vm.selectedProcessInfo,\"user-field\":_vm.userField},on:{\"update:visible\":function($event){_vm.processDetailInfoVisible = $event},\"close\":_vm.handleProcessDetailInfoClose}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-modal\r\n      :visible=\"localVisible\"\r\n      title=\"Process Details\"\r\n      width=\"1500px\"\r\n      @cancel=\"handleClose\"\r\n      class=\"process-table-modal\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"processContainer\">\r\n        <a-table\r\n          :columns=\"processColumns\"\r\n          :dataSource=\"getProcessList()\"\r\n          :pagination=\"{ pageSize: 10 }\"\r\n          size=\"middle\"\r\n        >\r\n        </a-table>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      :user-field=\"userField\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProcessDetailModal from './ProcessDetailModal.vue';\r\n\r\nexport default {\r\n  name: 'ProcessTable',\r\n  components: {\r\n    ProcessDetailModal\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      required: true\r\n    },\r\n    processContainer: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    // 允许自定义用户字段名称（uid或user）\r\n    userField: {\r\n      type: String,\r\n      default: 'user'\r\n    },\r\n    // 是否包含TTY列\r\n    includeTty: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localVisible: this.visible,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null\r\n    };\r\n  },\r\n  watch: {\r\n    visible(newValue) {\r\n      this.localVisible = newValue;\r\n    }\r\n  },\r\n  computed: {\r\n    processColumns() {\r\n      const columns = [\r\n        { title: 'PID', dataIndex: 'pid', key: 'pid', width: '60px' },\r\n        { title: 'PPID', dataIndex: 'ppid', key: 'ppid', width: '60px' },\r\n        {\r\n          title: 'UID',\r\n          dataIndex: this.userField,\r\n          key: this.userField,\r\n          width: '80px'\r\n        },\r\n        { title: 'GID', dataIndex: 'gid', key: 'gid', width: '60px' },\r\n        { title: 'State', dataIndex: 'state', key: 'state', width: '80px' },\r\n        { title: 'Start Time', dataIndex: 'stime', key: 'stime', width: '100px' }\r\n      ];\r\n\r\n      if (this.includeTty) {\r\n        columns.push({ title: 'TTY', dataIndex: 'tty', key: 'tty', width: '60px' });\r\n      }\r\n\r\n      columns.push(\r\n        { title: 'Time', dataIndex: 'time', key: 'time', width: '60px' },\r\n        {\r\n          title: 'Command',\r\n          dataIndex: 'cmd',\r\n          key: 'cmd',\r\n          width: '500px',\r\n          ellipsis: false,\r\n          customRender: (text) => {\r\n            return {\r\n              children: text,\r\n              props: {\r\n                style: {\r\n                  whiteSpace: 'normal',\r\n                  wordBreak: 'break-word'\r\n                }\r\n              }\r\n            };\r\n          }\r\n        },\r\n        {\r\n          title: 'Details',\r\n          key: 'details',\r\n          width: '100px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            const hasDetails = record.exe || record.cwd || record.capability || record.environ || record.memory_maps;\r\n            return hasDetails ? (\r\n              <a-button\r\n                type=\"link\"\r\n                size=\"small\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetailInfo(record);\r\n                }}\r\n              >\r\n                View Details\r\n              </a-button>\r\n            ) : 'N/A';\r\n          }\r\n        }\r\n      );\r\n\r\n      return columns;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClose() {\r\n      this.localVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('close');\r\n    },\r\n    getProcessList() {\r\n      if (!this.processContainer?.processes) return [];\r\n\r\n      // 检查是否有新格式的进程列表\r\n      const processes = this.processContainer.processes;\r\n\r\n      // Docker格式\r\n      if (processes.process_list && processes.process_list.length > 0) {\r\n        return processes.process_list;\r\n      }\r\n\r\n      // Crictl格式\r\n      if (processes.pid && Array.isArray(processes.pid)) {\r\n        return processes.pid.map((_, index) => ({\r\n          pid: processes.pid[index],\r\n          ppid: processes.ppid[index],\r\n          [this.userField]: processes[this.userField][index],\r\n          cmd: processes.cmd[index],\r\n          time: processes.time[index],\r\n          stime: processes.stime[index]\r\n        }));\r\n      }\r\n\r\n      return [];\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n/* 进程表格样式 */\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessTable.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessTable.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProcessTable.vue?vue&type=template&id=424fe87c&scoped=true\"\nimport script from \"./ProcessTable.vue?vue&type=script&lang=js\"\nexport * from \"./ProcessTable.vue?vue&type=script&lang=js\"\nimport style0 from \"./ProcessTable.vue?vue&type=style&index=0&id=424fe87c&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"424fe87c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-modal',{staticClass:\"network-listening-modal\",attrs:{\"visible\":_vm.localVisible,\"title\":\"Network Listening Details\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleClose}},[_vm._v(\"Close\")])]},proxy:true}])},[(_vm.networkData)?[_c('a-table',{attrs:{\"columns\":_vm.networkListeningColumns,\"dataSource\":_vm.getNetworkListening(),\"pagination\":{ pageSize: 20 },\"size\":\"middle\"}})]:_vm._e()],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-modal\r\n    :visible=\"localVisible\"\r\n    title=\"Network Listening Details\"\r\n    width=\"800px\"\r\n    @cancel=\"handleClose\"\r\n    class=\"network-listening-modal\"\r\n  >\r\n    <template v-slot:footer>\r\n      <a-button @click=\"handleClose\">Close</a-button>\r\n    </template>\r\n    <template v-if=\"networkData\">\r\n      <a-table\r\n        :columns=\"networkListeningColumns\"\r\n        :dataSource=\"getNetworkListening()\"\r\n        :pagination=\"{ pageSize: 20 }\"\r\n        size=\"middle\"\r\n      >\r\n      </a-table>\r\n    </template>\r\n  </a-modal>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'NetworkListeningModal',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      required: true\r\n    },\r\n    networkData: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      localVisible: this.visible,\r\n      networkListeningColumns: [\r\n        { title: 'Protocol', dataIndex: 'proto', key: 'proto', width: '80px' },\r\n        { title: 'Local Address', dataIndex: 'local_address', key: 'local_address', width: '150px' },\r\n        { title: 'Foreign Address', dataIndex: 'foreign_address', key: 'foreign_address', width: '150px' },\r\n        { title: 'State', dataIndex: 'state', key: 'state', width: '100px' },\r\n        { title: 'PID/Program', dataIndex: 'pid_program', key: 'pid_program', width: '120px' }\r\n      ]\r\n    };\r\n  },\r\n  watch: {\r\n    visible(newValue) {\r\n      this.localVisible = newValue;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClose() {\r\n      this.localVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('close');\r\n    },\r\n    getNetworkListening() {\r\n      if (!this.networkData?.exposures) return [];\r\n      return this.networkData.exposures.listening_ports || [];\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./NetworkListeningModal.vue?vue&type=template&id=18c6d2e8&scoped=true\"\nimport script from \"./NetworkListeningModal.vue?vue&type=script&lang=js\"\nexport * from \"./NetworkListeningModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./NetworkListeningModal.vue?vue&type=style&index=0&id=18c6d2e8&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18c6d2e8\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.listeningPorts.length === 0)?_c('div',[_vm._v(\"No listening ports\")]):_c('div',{staticStyle:{\"font-size\":\"12px\"}},[(_vm.listeningPorts.length > 0)?_c('div',{staticStyle:{\"padding\":\"2px 0\"}},[_c('div',[_c('span',{staticClass:\"proto-text\"},[_vm._v(_vm._s(_vm.listeningPorts[0].proto))]),_c('span',[_vm._v(\" \"+_vm._s(_vm.listeningPorts[0].local_address))]),_c('span',{staticClass:\"path-arrow\"},[_vm._v(\" → \")]),_c('span',[_vm._v(_vm._s(_vm.listeningPorts[0].foreign_address))]),_c('span',{staticClass:\"program-text\"},[_vm._v(\" (\"+_vm._s(_vm.listeningPorts[0].pid_program)+\")\")])]),(_vm.listeningPorts.length > 1)?_c('div',{staticStyle:{\"margin-top\":\"4px\"}},[_c('a-button',{staticStyle:{\"padding-left\":\"0\"},attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.showDetails.apply(null, arguments)}}},[_vm._v(\" +\"+_vm._s(_vm.listeningPorts.length - 1)+\" more... \")])],1):_vm._e()]):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <div v-if=\"listeningPorts.length === 0\">No listening ports</div>\r\n    <div v-else style=\"font-size: 12px;\">\r\n      <div v-if=\"listeningPorts.length > 0\" style=\"padding: 2px 0;\">\r\n        <div>\r\n          <span class=\"proto-text\">{{ listeningPorts[0].proto }}</span>\r\n          <span> {{ listeningPorts[0].local_address }}</span>\r\n          <span class=\"path-arrow\"> → </span>\r\n          <span>{{ listeningPorts[0].foreign_address }}</span>\r\n          <span class=\"program-text\"> ({{ listeningPorts[0].pid_program }})</span>\r\n        </div>\r\n        <div v-if=\"listeningPorts.length > 1\" style=\"margin-top: 4px;\">\r\n          <a-button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            @click.stop=\"showDetails\"\r\n            style=\"padding-left: 0;\"\r\n          >\r\n            +{{ listeningPorts.length - 1 }} more...\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'NetworkListeningCell',\r\n  props: {\r\n    record: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    parseJsonField: {\r\n      type: Function,\r\n      default: (field, defaultValue) => {\r\n        try {\r\n          if (Array.isArray(field)) {\r\n            return field;\r\n          }\r\n          if (typeof field === 'object' && field !== null) {\r\n            return field;\r\n          }\r\n          return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n        } catch (e) {\r\n          console.warn('Failed to parse JSON field:', e);\r\n          return defaultValue;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    listeningPorts() {\r\n      try {\r\n        const exposedServices = typeof this.record.exposures === 'string'\r\n          ? JSON.parse(this.record.exposures)\r\n          : this.record.exposures;\r\n        return exposedServices?.listening_ports || [];\r\n      } catch (e) {\r\n        console.error('Failed to parse exposed services:', e);\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showDetails(e) {\r\n      // 阻止事件冒泡\r\n      if (e) {\r\n        e.stopPropagation();\r\n      }\r\n\r\n      // 同时支持kebab-case和camelCase的事件名\r\n      this.$emit('show-details', this.record);\r\n      this.$emit('showDetails', this.record);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningCell.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningCell.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./NetworkListeningCell.vue?vue&type=template&id=167d61b4&scoped=true\"\nimport script from \"./NetworkListeningCell.vue?vue&type=script&lang=js\"\nexport * from \"./NetworkListeningCell.vue?vue&type=script&lang=js\"\nimport style0 from \"./NetworkListeningCell.vue?vue&type=style&index=0&id=167d61b4&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"167d61b4\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.mounts.length === 0)?_c('div',[_vm._v(\"N/A\")]):_c('div',{staticStyle:{\"font-size\":\"12px\"}},[(_vm.mounts.length > 0)?_c('div',{staticStyle:{\"padding\":\"2px 0\"}},[_c('div',[_c('span',{staticClass:\"source-path\"},[_vm._v(_vm._s(_vm.mounts[0].Source || 'N/A'))]),_c('span',{staticClass:\"path-arrow\"},[_vm._v(\" → \")]),_c('span',{staticClass:\"dest-path\"},[_vm._v(_vm._s(_vm.mounts[0].Destination || 'N/A'))]),_c('span',{staticClass:\"mount-tag\",staticStyle:{\"margin-left\":\"5px\"}},[_vm._v(_vm._s(_vm.mounts[0].Mode || ''))]),_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(_vm.mounts[0].RW ? 'RW' : 'RO'))])]),(_vm.mounts.length > 1)?_c('div',{staticStyle:{\"margin-top\":\"4px\"}},[_c('a-button',{staticStyle:{\"padding-left\":\"0\"},attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":_vm.showDetails}},[_vm._v(\" +\"+_vm._s(_vm.mounts.length - 1)+\" more... \")])],1):_vm._e()]):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <div v-if=\"mounts.length === 0\">N/A</div>\r\n    <div v-else style=\"font-size: 12px;\">\r\n      <div v-if=\"mounts.length > 0\" style=\"padding: 2px 0;\">\r\n        <div>\r\n          <span class=\"source-path\">{{ mounts[0].Source || 'N/A' }}</span>\r\n          <span class=\"path-arrow\"> → </span>\r\n          <span class=\"dest-path\">{{ mounts[0].Destination || 'N/A' }}</span>\r\n          <span class=\"mount-tag\" style=\"margin-left: 5px;\">{{ mounts[0].Mode || '' }}</span>\r\n          <span class=\"mount-tag\">{{ mounts[0].RW ? 'RW' : 'RO' }}</span>\r\n        </div>\r\n        <div v-if=\"mounts.length > 1\" style=\"margin-top: 4px;\">\r\n          <a-button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            @click=\"showDetails\"\r\n            style=\"padding-left: 0;\"\r\n          >\r\n            +{{ mounts.length - 1 }} more...\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MountCell',\r\n  props: {\r\n    record: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    parseJsonField: {\r\n      type: Function,\r\n      default: (field, defaultValue) => {\r\n        try {\r\n          if (Array.isArray(field)) {\r\n            return field;\r\n          }\r\n          if (typeof field === 'object' && field !== null) {\r\n            return field;\r\n          }\r\n          return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n        } catch (e) {\r\n          console.warn('Failed to parse JSON field:', e);\r\n          return defaultValue;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    mounts() {\r\n      try {\r\n        return this.parseJsonField(this.record.mounts, []);\r\n      } catch (e) {\r\n        console.error('Failed to parse mounts:', e);\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showDetails() {\r\n      this.$emit('show-details', this.record);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MountCell.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MountCell.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./MountCell.vue?vue&type=template&id=70290246&scoped=true\"\nimport script from \"./MountCell.vue?vue&type=script&lang=js\"\nexport * from \"./MountCell.vue?vue&type=script&lang=js\"\nimport style0 from \"./MountCell.vue?vue&type=style&index=0&id=70290246&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70290246\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.mounts.length === 0)?_c('div',[_vm._v(\"N/A\")]):_c('div',{staticStyle:{\"font-size\":\"12px\"}},[(_vm.mounts.length > 0)?_c('div',{staticStyle:{\"padding\":\"2px 0\"}},[_c('div',[_c('span',{staticClass:\"source-path\"},[_vm._v(_vm._s(_vm.mounts[0].host_path || 'N/A'))]),_c('span',{staticClass:\"path-arrow\"},[_vm._v(\" → \")]),_c('span',{staticClass:\"dest-path\"},[_vm._v(_vm._s(_vm.mounts[0].container_path || 'N/A'))]),_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(_vm.mounts[0].readonly ? 'RO' : 'RW'))]),(_vm.mounts[0].propagation)?_c('span',{staticClass:\"mount-tag\"},[_vm._v(_vm._s(_vm.mounts[0].propagation.replace('PROPAGATION_', '')))]):_vm._e()]),(_vm.mounts.length > 1)?_c('div',{staticStyle:{\"margin-top\":\"4px\"}},[_c('a-button',{staticStyle:{\"padding-left\":\"0\"},attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":_vm.showDetails}},[_vm._v(\" +\"+_vm._s(_vm.mounts.length - 1)+\" more... \")])],1):_vm._e()]):_vm._e()])])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <div v-if=\"mounts.length === 0\">N/A</div>\r\n    <div v-else style=\"font-size: 12px;\">\r\n      <div v-if=\"mounts.length > 0\" style=\"padding: 2px 0;\">\r\n        <div>\r\n          <span class=\"source-path\">{{ mounts[0].host_path || 'N/A' }}</span>\r\n          <span class=\"path-arrow\"> → </span>\r\n          <span class=\"dest-path\">{{ mounts[0].container_path || 'N/A' }}</span>\r\n          <span class=\"mount-tag\">{{ mounts[0].readonly ? 'RO' : 'RW' }}</span>\r\n          <span v-if=\"mounts[0].propagation\" class=\"mount-tag\">{{ mounts[0].propagation.replace('PROPAGATION_', '') }}</span>\r\n        </div>\r\n        <div v-if=\"mounts.length > 1\" style=\"margin-top: 4px;\">\r\n          <a-button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            @click=\"showDetails\"\r\n            style=\"padding-left: 0;\"\r\n          >\r\n            +{{ mounts.length - 1 }} more...\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CriMountCell',\r\n  props: {\r\n    record: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    mounts() {\r\n      try {\r\n        return typeof this.record.mounts === 'string'\r\n          ? JSON.parse(this.record.mounts)\r\n          : this.record.mounts || [];\r\n      } catch (e) {\r\n        console.error('Failed to parse mounts:', e);\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showDetails() {\r\n      this.$emit('show-details', this.record);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n  margin-left: 5px;\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CriMountCell.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CriMountCell.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CriMountCell.vue?vue&type=template&id=3be0b9ad&scoped=true\"\nimport script from \"./CriMountCell.vue?vue&type=script&lang=js\"\nexport * from \"./CriMountCell.vue?vue&type=script&lang=js\"\nimport style0 from \"./CriMountCell.vue?vue&type=style&index=0&id=3be0b9ad&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3be0b9ad\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full crictl-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n    <div class=\"crictl-info-container\">\r\n      <a-tabs v-model:activeKey=\"activeTab\" @change=\"handleTabChange\">\r\n        <!-- Host Config Tab -->\r\n        <a-tab-pane key=\"crictl_host_config\" tab=\"Host Config\">\r\n          <div v-if=\"activeTab === 'crictl_host_config'\" class=\"host-config-container\">\r\n            <a-row :gutter=\"[16, 16]\">\r\n              <a-col :span=\"8\" v-for=\"card in hostConfigCards\" :key=\"card.title\">\r\n                <a-card :title=\"card.title\" :bordered=\"false\" class=\"host-config-card\">\r\n                  <p v-for=\"(item, index) in card.items\" :key=\"index\" class=\"info-item\">\r\n                    <strong>{{ item.label }}:</strong>\r\n                    {{ item.getValue(hostConfig) }}\r\n                  </p>\r\n                </a-card>\r\n              </a-col>\r\n            </a-row>\r\n          </div>\r\n        </a-tab-pane>\r\n\r\n        <!-- Pods Tab -->\r\n        <a-tab-pane key=\"crictl_pod\" tab=\"Pods\">\r\n          <a-table\r\n            :columns=\"podColumns\"\r\n            :data-source=\"podData\"\r\n            :rowKey=\"record => record.pod_id\"\r\n            :loading=\"loadingPods\"\r\n            :pagination=\"{ pageSize: 30 }\"\r\n            :scroll=\"{ x: 1500 }\"\r\n          >\r\n          </a-table>\r\n        </a-tab-pane>\r\n\r\n        <!-- Containers Tab -->\r\n        <a-tab-pane key=\"crictl_container\" tab=\"Containers\">\r\n          <a-table\r\n            :columns=\"containerColumns\"\r\n            :data-source=\"containerData\"\r\n            :rowKey=\"record => record.container_id\"\r\n            :loading=\"loadingContainers\"\r\n            :pagination=\"{ pageSize: 20 }\"\r\n            :scroll=\"{ x: 1800 }\"\r\n          >\r\n          </a-table>\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </div>\r\n\r\n    <network-listening-modal\r\n      :visible=\"networkDetailsVisible\"\r\n      :network-data=\"selectedNetwork\"\r\n      @update:visible=\"networkDetailsVisible = $event\"\r\n      @close=\"handleNetworkDetailsClose\"\r\n    />\r\n\r\n    <a-modal\r\n      v-model:visible=\"environmentDetailsVisible\"\r\n      title=\"Environment Variables\"\r\n      width=\"800px\"\r\n      @cancel=\"handleEnvironmentDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleEnvironmentDetailsClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"selectedEnvironment\">\r\n        <div class=\"env-container\">\r\n          <div v-for=\"(env, index) in getAllEnvironmentVars()\" :key=\"index\" class=\"env-item\">\r\n            {{ env }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <a-modal\r\n      v-model:visible=\"mountDetailsVisible\"\r\n      title=\"Mount Details\"\r\n      width=\"800px\"\r\n      @cancel=\"handleMountDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleMountDetailsClose\">Cancel</a-button>\r\n      </template>\r\n      <template v-if=\"selectedMountContainer\">\r\n        <div class=\"mounts-container\">\r\n          <div v-for=\"(mount, index) in getAllMounts()\" :key=\"index\" class=\"mount-item\">\r\n            <div class=\"mount-path\">\r\n              <span class=\"mount-source\">{{ mount.host_path }}</span>\r\n              <span class=\"mount-arrow\">→</span>\r\n              <span class=\"mount-dest\">{{ mount.container_path }}</span>\r\n            </div>\r\n            <div class=\"mount-details\">\r\n              <span class=\"mount-tag\">{{ mount.readonly ? 'RO' : 'RW' }}</span>\r\n              <span v-if=\"mount.propagation\" class=\"mount-tag\">\r\n                {{ mount.propagation.replace('PROPAGATION_', '') }}\r\n              </span>\r\n              <span v-if=\"mount.selinux_relabel\" class=\"mount-tag\">SELinux Relabel</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-table\r\n      :visible=\"processDetailsVisible\"\r\n      :process-container=\"selectedProcessContainer\"\r\n      user-field=\"user\"\r\n      :include-tty=\"false\"\r\n      @update:visible=\"processDetailsVisible = $event\"\r\n      @close=\"handleProcessDetailsClose\"\r\n    />\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      user-field=\"user\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport axios from '@/api/axiosInstance'\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue'\r\nimport { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index'\r\nimport { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index'\r\nimport { CriMountCell } from '../Widgets/Mount/mount_index'\r\n// 不再使用单独的环境变量组件\r\n// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index'\r\n\r\nexport default {\r\n  components: {\r\n    JsonDetailModal,\r\n    ProcessDetailModal,\r\n    ProcessTable,\r\n    NetworkListeningModal,\r\n    NetworkListeningCell,\r\n    CriMountCell,\r\n    // EnvironmentCell,\r\n    // EnvironmentModal\r\n  },\r\n  name: 'CrictlInfo',\r\n  props: {\r\n    nodeIp: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // Host Config related data\r\n      hostConfigCards: [\r\n        {\r\n          title: 'Version Information',\r\n          items: [\r\n            {\r\n              label: 'Version',\r\n              getValue: (config) => {\r\n                if (!config.version_info) return 'N/A'\r\n                const match = config.version_info.match(/Version:\\s*(.+)/)\r\n                return match ? match[1].trim() : 'N/A'\r\n              }\r\n            },\r\n            {\r\n              label: 'Runtime Name',\r\n              getValue: (config) => {\r\n                if (!config.version_info) return 'N/A'\r\n                const match = config.version_info.match(/RuntimeName:\\s*(.+)/)\r\n                return match ? match[1].trim() : 'N/A'\r\n              }\r\n            },\r\n            {\r\n              label: 'Runtime Version',\r\n              getValue: (config) => {\r\n                if (!config.version_info) return 'N/A'\r\n                const match = config.version_info.match(/RuntimeVersion:\\s*(.+)/)\r\n                return match ? match[1].trim() : 'N/A'\r\n              }\r\n            },\r\n            {\r\n              label: 'Runtime API Version',\r\n              getValue: (config) => {\r\n                if (!config.version_info) return 'N/A'\r\n                const match = config.version_info.match(/RuntimeApiVersion:\\s*(.+)/)\r\n                return match ? match[1].trim() : 'N/A'\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: 'Runtime Status',\r\n          items: [\r\n            {\r\n              label: 'Runtime Ready',\r\n              getValue: (config) => {\r\n                const condition = config.runtime_info?.status?.conditions?.find(c => c.type === 'RuntimeReady')\r\n                return condition ? `${condition.status ? 'Ready' : 'Not Ready'}` : 'N/A'\r\n              }\r\n            },\r\n            {\r\n              label: 'Network Ready',\r\n              getValue: (config) => {\r\n                const condition = config.runtime_info?.status?.conditions?.find(c => c.type === 'NetworkReady')\r\n                return condition ? `${condition.status ? 'Ready' : 'Not Ready'}` : 'N/A'\r\n              }\r\n            },\r\n            {\r\n              label: 'Sandbox Image',\r\n              getValue: (config) => config.runtime_info?.config?.sandboxImage || 'N/A'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          title: 'Security',\r\n          items: [\r\n            {\r\n              label: 'Root User',\r\n              getValue: (config) => config.is_root_user ? 'Yes' : 'No'\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      hostConfig: {},\r\n      podData: [],\r\n      containerData: [],\r\n      loadingHostConfig: false,\r\n      loadingPods: false,\r\n      loadingContainers: false,\r\n      activeTab: 'crictl_host_config',\r\n\r\n      // pods\r\n      podColumns: [\r\n        { title: 'Pod ID', dataIndex: 'pod_id', key: 'pod_id', width: '120px', ellipsis: true },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: ['metadata', 'name'],\r\n          key: 'name',\r\n          width: '160px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: ['metadata', 'namespace'],\r\n          key: 'namespace',\r\n          width: '80px'\r\n        },\r\n        {\r\n          title: 'State',\r\n          dataIndex: 'state',\r\n          key: 'state',\r\n          width: '120px'\r\n        },\r\n        {\r\n          title: 'Network',\r\n          dataIndex: 'network',\r\n          key: 'network',\r\n          width: '180px',\r\n          customRender: (_, record) => {\r\n            const networkData = record.network;\r\n            if (!networkData || Object.keys(networkData).length === 0) {\r\n              return 'N/A';\r\n            }\r\n            const ips = [];\r\n            if (networkData.ip) {\r\n              ips.push(networkData.ip);\r\n            }\r\n            if (networkData.additionalIps && networkData.additionalIps.length > 0) {\r\n              ips.push(...networkData.additionalIps);\r\n            }\r\n            return ips.length > 0 ? ips.join(',  ') : 'N/A';\r\n          }\r\n        },\r\n      ],\r\n\r\n      // Containers\r\n      containerColumns: [\r\n        {\r\n          title: 'Container ID',\r\n          dataIndex: 'container_id',\r\n          key: 'container_id',\r\n          width: '140px',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'inspect_data',\r\n          key: 'name',\r\n          width: '15%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = typeof record.inspect_data === 'string'\r\n                ? JSON.parse(record.inspect_data)\r\n                : record.inspect_data;\r\n              return {\r\n                children: inspectData?.status?.metadata?.name || 'N/A',\r\n                props: {\r\n                  style: {\r\n                    whiteSpace: 'normal',\r\n                    wordBreak: 'break-word'\r\n                  }\r\n                }\r\n              };\r\n            } catch (e) {\r\n              console.warn('Failed to render container name:', e);\r\n              return 'N/A';\r\n            }\r\n          }\r\n        },\r\n\r\n        {\r\n          title: 'Environment',\r\n          dataIndex: 'env',\r\n          key: 'env',\r\n          width: '200px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const envVars = typeof record.env === 'string' ? JSON.parse(record.env) : record.env || [];\r\n              if (!envVars.length) return 'N/A';\r\n\r\n              return (\r\n                <div style=\"font-size: 12px;\">\r\n                  {envVars.slice(0, 1).map((env, index) => (\r\n                    <div key={index} style=\"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">\r\n                      {env.length > 200 ? env.substring(0, 200) + '...' : env}\r\n                    </div>\r\n                  ))}\r\n                  {envVars.length > 1 && (\r\n                    <a-button\r\n                      type=\"link\"\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        this.showEnvironmentDetails(record);\r\n                      }}\r\n                    >\r\n                      +{envVars.length - 1} more...\r\n                    </a-button>\r\n                  )}\r\n                </div>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render env vars:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Mounts',\r\n          dataIndex: 'mounts',\r\n          key: 'mounts',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <cri-mount-cell\r\n                record={record}\r\n                onShow-details={this.showMountDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Network Listening',\r\n          dataIndex: 'exposures',\r\n          key: 'exposures',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <network-listening-cell\r\n                record={record}\r\n                onShow-details={this.showNetworkDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Processes',\r\n          dataIndex: 'processes',\r\n          key: 'processes',\r\n          width: '120px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            if (!record || !record.processes) return 'N/A';\r\n            const processes = typeof record.processes === 'string' ? JSON.parse(record.processes) : record.processes;\r\n\r\n            const processCount = processes.pid ? processes.pid.length : 0;\r\n\r\n            return (\r\n              <a-button\r\n                type=\"link\"\r\n                size=\"small\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetails(record);\r\n                }}\r\n              >\r\n                {processCount} {processCount === 1 ? 'process' : 'processes'}\r\n              </a-button>\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Inspect Data',\r\n          dataIndex: 'inspect_data',\r\n          key: 'inspect_data',\r\n          width: '150px',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <span style=\"display: inline-block; line-height: 22px;\">\r\n              <a\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showInspectDetails(record);\r\n                }}\r\n                style=\"color: #1890ff; cursor: pointer;\"\r\n              >\r\n                View Inspect\r\n              </a>\r\n            </span>\r\n          )\r\n        }\r\n      ],\r\n\r\n      // 添加新的数据\r\n      networkDetailsVisible: false,\r\n      selectedNetwork: null,\r\n\r\n      // 添加新的数据\r\n      mountDetailsVisible: false,\r\n      selectedMountContainer: null,\r\n\r\n      // 添加环境变量相关数据\r\n      environmentDetailsVisible: false,\r\n      selectedEnvironment: null,\r\n\r\n      // 添加新的数据\r\n      processDetailsVisible: false,\r\n      selectedProcessContainer: null,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null,\r\n\r\n\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject'])\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newVal) {\r\n      if (newVal) {\r\n        this.fetchActiveTabData()\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData()\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp || !this.currentProject) return\r\n\r\n      const loadingKey = `loading${this.activeTab.replace('crictl_', '').charAt(0).toUpperCase() + this.activeTab.slice(7)}`\r\n      this[loadingKey] = true\r\n\r\n      try {\r\n        const response = await axios.get(`/api/crictl/${this.activeTab}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        })\r\n\r\n        const dataKey = `${this.activeTab.replace('crictl_', '')}Data`\r\n        if (this.activeTab === 'crictl_host_config') {\r\n          this.hostConfig = response.data\r\n        } else {\r\n          this[dataKey] = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching ${this.activeTab}:`, error)\r\n      } finally {\r\n        this[loadingKey] = false\r\n      }\r\n    },\r\n    handleTabChange(key) {\r\n      this.activeTab = key\r\n      this.fetchActiveTabData()\r\n    },\r\n    showNetworkDetails(container) {\r\n      this.selectedNetwork = {\r\n        ...container,\r\n        exposures: typeof container.exposures === 'string'\r\n          ? JSON.parse(container.exposures)\r\n          : container.exposures\r\n      };\r\n      this.networkDetailsVisible = true;\r\n    },\r\n    handleNetworkDetailsClose() {\r\n      this.networkDetailsVisible = false;\r\n      this.selectedNetwork = null;\r\n    },\r\n\r\n    showMountDetails(container) {\r\n      this.selectedMountContainer = {\r\n        ...container,\r\n        mounts: typeof container.mounts === 'string'\r\n          ? JSON.parse(container.mounts)\r\n          : container.mounts\r\n      };\r\n      this.mountDetailsVisible = true;\r\n    },\r\n    handleMountDetailsClose() {\r\n      this.mountDetailsVisible = false;\r\n      this.selectedMountContainer = null;\r\n    },\r\n    getAllMounts() {\r\n      if (!this.selectedMountContainer?.mounts) return [];\r\n      return this.selectedMountContainer.mounts;\r\n    },\r\n    showEnvironmentDetails(container) {\r\n      this.selectedEnvironment = {\r\n        ...container,\r\n        env: typeof container.env === 'string'\r\n          ? JSON.parse(container.env)\r\n          : container.env || []\r\n      };\r\n      this.environmentDetailsVisible = true;\r\n    },\r\n    handleEnvironmentDetailsClose() {\r\n      this.environmentDetailsVisible = false;\r\n      this.selectedEnvironment = null;\r\n    },\r\n    getAllEnvironmentVars() {\r\n      if (!this.selectedEnvironment?.env) return [];\r\n      return this.selectedEnvironment.env;\r\n    },\r\n    showProcessDetails(container) {\r\n      this.selectedProcessContainer = container;\r\n      this.processDetailsVisible = true;\r\n    },\r\n    handleProcessDetailsClose() {\r\n      this.processDetailsVisible = false;\r\n      this.selectedProcessContainer = null;\r\n    },\r\n    getProcessList() {\r\n      if (!this.selectedProcessContainer?.processes) return [];\r\n\r\n      // 检查是否有新格式的进程列表\r\n      const processes = this.selectedProcessContainer.processes;\r\n      if (processes.process_list && processes.process_list.length > 0) {\r\n        return processes.process_list;\r\n      }\r\n\r\n      // 兼容旧格式\r\n      return processes.pid.map((_, index) => ({\r\n        pid: processes.pid[index],\r\n        ppid: processes.ppid[index],\r\n        user: processes.user[index],\r\n        cmd: processes.cmd[index],\r\n        time: processes.time[index],\r\n        stime: processes.stime[index]\r\n      }));\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    },\r\n    showInspectDetails(container) {\r\n      try {\r\n        const inspectData = typeof container.inspect_data === 'string'\r\n          ? JSON.parse(container.inspect_data)\r\n          : container.inspect_data || {};\r\n        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);\r\n      } catch (e) {\r\n        console.error('Failed to parse inspect data:', e);\r\n        this.$message.error('Failed to parse inspect data');\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.crictl-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 信息项样式 */\r\n.info-item {\r\n  margin-bottom: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px dashed #f0f0f0;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    padding-bottom: 0;\r\n    border-bottom: none;\r\n  }\r\n\r\n  strong {\r\n    margin-right: 8px;\r\n    color: #555;\r\n  }\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 容器详情弹窗样式 */\r\n.container-header {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.container-id {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.container-name {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-top: 8px;\r\n}\r\n\r\n/* 详情卡片样式 */\r\n.detail-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);\r\n  border-radius: 8px;\r\n  margin-bottom: 12px;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n/* 详情项样式 */\r\n.detail-item {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  background-color: #fcfcfc;\r\n  border-radius: 4px;\r\n  margin-bottom: 4px;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n/* 标签和值样式 */\r\n.detail-label-badge {\r\n  display: inline-block;\r\n  background-color: #f5f7fa;\r\n  color: #1890ff;\r\n  font-weight: 600;\r\n  padding: 4px 10px;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #1890ff;\r\n}\r\n\r\n.detail-value {\r\n  padding: 4px 0 4px 10px;\r\n  color: #333;\r\n  word-break: break-word;\r\n  line-height: 1.5;\r\n}\r\n\r\n.mounts-container {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mount-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.mount-path {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mount-source {\r\n  color: #1890ff;\r\n}\r\n\r\n.mount-arrow {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.mount-dest {\r\n  color: #52c41a;\r\n}\r\n\r\n.mount-details {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mount-tag {\r\n  background: #f5f5f5;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.inspect-data-pre, .detail-pre {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CrictlInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CrictlInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CrictlInfo.vue?vue&type=template&id=ef13263e&scoped=true\"\nimport script from \"./CrictlInfo.vue?vue&type=script&lang=js\"\nexport * from \"./CrictlInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./CrictlInfo.vue?vue&type=style&index=0&id=ef13263e&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ef13263e\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full docker-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"25\" height=\"25\"\r\n                viewBox=\"0 0 24 24\"\r\n                :class=\"`text-${sidebarColor}`\"\r\n            >\r\n              <path fill=\"currentColor\" d=\"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .***************.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .***************.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .***************.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.docker') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <div class=\"container-runtime-tabs\">\r\n      <a-tabs v-model:activeKey=\"activeEngine\">\r\n        <a-tab-pane key=\"docker\" tab=\"Docker\">\r\n          <a-tabs v-model:activeKey=\"activeTab\" @change=\"handleTabChange\">\r\n            <a-tab-pane key=\"docker_host_config\" tab=\"Host Config\">\r\n              <div v-if=\"activeTab === 'docker_host_config'\" class=\"host-config-container\">\r\n                <a-row :gutter=\"[16, 16]\">\r\n                  <!-- Basic Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Basic Information\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Docker Version:</strong> {{ hostConfigData[0]?.docker_version }}</p>\r\n                      <p class=\"info-item\"><strong>Operating System:</strong> {{ getDaemonConfig('OperatingSystem') }}</p>\r\n                      <p class=\"info-item\"><strong>Architecture:</strong> {{ getDaemonConfig('Architecture') }}</p>\r\n                      <p class=\"info-item\"><strong>Kernel Version:</strong> {{ getDaemonConfig('KernelVersion') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Resource Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Resources\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>CPU Cores:</strong> {{ getDaemonConfig('NCPU') }}</p>\r\n                      <p class=\"info-item\"><strong>Total Memory:</strong> {{ formatBytes(getDaemonConfig('MemTotal')) }}</p>\r\n                      <p class=\"info-item\"><strong>Docker Root Dir:</strong> {{ getDaemonConfig('DockerRootDir') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Security Settings -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Security\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>User in Docker Group:</strong> {{ hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Root User:</strong> {{ hostConfigData[0]?.is_root_user ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Security Options:</strong> {{ getDaemonConfig('SecurityOptions')?.join(', ') || 'None' }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Container Statistics -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Container Statistics\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Total Containers:</strong> {{ getDaemonConfig('Containers') }}</p>\r\n                      <p class=\"info-item\"><strong>Running:</strong> {{ getDaemonConfig('ContainersRunning') }}</p>\r\n                      <p class=\"info-item\"><strong>Stopped:</strong> {{ getDaemonConfig('ContainersStopped') }}</p>\r\n                      <p class=\"info-item\"><strong>Images:</strong> {{ getDaemonConfig('Images') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Driver Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Storage Driver\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Driver:</strong> {{ getDaemonConfig('Driver') }}</p>\r\n                      <p class=\"info-item\"><strong>Logging Driver:</strong> {{ getDaemonConfig('LoggingDriver') }}</p>\r\n                      <p class=\"info-item\"><strong>Cgroup Driver:</strong> {{ getDaemonConfig('CgroupDriver') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Registry Configuration -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Registry Configuration\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Index Server:</strong> {{ getDaemonConfig('IndexServerAddress') }}</p>\r\n                      <p class=\"info-item\"><strong>Registry Mirrors:</strong></p>\r\n                      <ul>\r\n                        <li v-for=\"mirror in getRegistryMirrors()\" :key=\"mirror\" class=\"info-item\">{{ mirror }}</li>\r\n                      </ul>\r\n                    </a-card>\r\n                  </a-col>\r\n                </a-row>\r\n              </div>\r\n            </a-tab-pane>\r\n            <a-tab-pane key=\"docker_network\" tab=\"Networks\">\r\n              <a-table\r\n                :columns=\"networkColumns\"\r\n                :data-source=\"networkData\"\r\n                :rowKey=\"(record) => record.network_id\"\r\n                v-if=\"activeTab === 'docker_network'\"\r\n                :loading=\"loadingNetworks\"\r\n                :pagination=\"pagination\"\r\n              >\r\n              </a-table>\r\n            </a-tab-pane>\r\n\r\n            <a-tab-pane key=\"docker_container\" tab=\"Containers\">\r\n              <a-table\r\n                :columns=\"containerColumns\"\r\n                :data-source=\"containerData\"\r\n                :scroll=\"{ x: 1500 }\"\r\n                :pagination=\"{ pageSize: 20 }\"\r\n                :row-key=\"record => record.container_id\"\r\n                v-if=\"activeTab === 'docker_container'\"\r\n                :loading=\"loadingContainers\"\r\n              >\r\n                <template #mountsColumn=\"{ record }\">\r\n                  <div v-if=\"record\">\r\n                    <template v-if=\"record.mounts && record.mounts.length\">\r\n                      <div v-for=\"(mount, index) in record.mounts\" :key=\"index\">\r\n                        {{ mount.Source }} → {{ mount.Destination }}\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>N/A</span>\r\n                  </div>\r\n                </template>\r\n              </a-table>\r\n\r\n\r\n\r\n              <network-listening-modal\r\n                :visible=\"networkDetailsVisible\"\r\n                :network-data=\"selectedNetwork\"\r\n                @update:visible=\"networkDetailsVisible = $event\"\r\n                @close=\"handleNetworkDetailsClose\"\r\n              />\r\n            </a-tab-pane>\r\n          </a-tabs>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"crictl\" tab=\"CRI\">\r\n          <crictl-info :node-ip=\"selectedNodeIp\" />\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </div>\r\n\r\n    <a-modal\r\n      v-model:visible=\"mountDetailsVisible\"\r\n      title=\"Mount Details\"\r\n      width=\"800px\"\r\n      @cancel=\"handleMountDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleMountDetailsClose\">Cancel</a-button>\r\n      </template>\r\n      <template v-if=\"selectedMountContainer\">\r\n        <div class=\"mounts-container\">\r\n          <div v-for=\"(mount, index) in getAllMounts()\" :key=\"index\" class=\"mount-item\">\r\n            <div class=\"mount-path\">\r\n              <span class=\"mount-source\">{{ mount.Source }}</span>\r\n              <span class=\"mount-arrow\">→</span>\r\n              <span class=\"mount-dest\">{{ mount.Destination }}</span>\r\n            </div>\r\n            <div class=\"mount-details\">\r\n              <span v-if=\"mount.Mode\" class=\"mount-tag\">{{ mount.Mode }}</span>\r\n              <span class=\"mount-tag\">{{ mount.RW ? 'RW' : 'RO' }}</span>\r\n              <span v-if=\"mount.Propagation\" class=\"mount-tag\">{{ mount.Propagation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <a-modal\r\n      v-model:visible=\"environmentDetailsVisible\"\r\n      title=\"Environment Variables\"\r\n      width=\"800px\"\r\n      @cancel=\"handleEnvironmentDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleEnvironmentDetailsClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"selectedEnvironment\">\r\n        <div class=\"env-container\">\r\n          <div v-for=\"(env, index) in getAllEnvironmentVars()\" :key=\"index\" class=\"env-item\">\r\n            {{ env }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-table\r\n      :visible=\"processDetailsVisible\"\r\n      :process-container=\"selectedProcessContainer\"\r\n      user-field=\"uid\"\r\n      :include-tty=\"true\"\r\n      @update:visible=\"processDetailsVisible = $event\"\r\n      @close=\"handleProcessDetailsClose\"\r\n    />\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      user-field=\"uid\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n\r\n\r\n\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport CrictlInfo from './CrictlInfo.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\nimport { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index';\r\nimport { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index';\r\nimport { MountCell } from '../Widgets/Mount/mount_index';\r\n// 不再使用单独的环境变量组件\r\n// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    CrictlInfo,\r\n    JsonDetailModal,\r\n    ProcessDetailModal,\r\n    ProcessTable,\r\n    NetworkListeningModal,\r\n    NetworkListeningCell,\r\n    MountCell,\r\n    // EnvironmentCell,\r\n    // EnvironmentModal\r\n  },\r\n  name: 'DockerInfo',\r\n  props: {\r\n    // Using selectedNodeIp from Vuex store instead of prop\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'docker_host_config',\r\n      hostConfigData: [],\r\n      containerData: [],\r\n      networkData: [],\r\n      loadingHostConfig: false,\r\n      loadingContainers: false,\r\n      loadingNetworks: false,\r\n      hostConfigColumns: [\r\n        { title: 'Docker Version', dataIndex: 'docker_version', key: 'docker_version' },\r\n        {\r\n          title: 'Daemon Config',\r\n          dataIndex: 'daemon_config',\r\n          key: 'daemon_config',\r\n        },\r\n        {\r\n          title: 'In Docker Group',\r\n          dataIndex: 'user_in_docker_group',\r\n          key: 'user_in_docker_group',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n        {\r\n          title: 'Is Root',\r\n          dataIndex: 'is_root_user',\r\n          key: 'is_root_user',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n      ],\r\n      containerColumns: [\r\n        {\r\n          title: 'Container ID',\r\n          dataIndex: 'container_id',\r\n          key: 'container_id',\r\n          width: '150px',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'inspect_data',\r\n          key: 'name',\r\n          width: '15%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              return {\r\n                children: inspectData.Name || 'N/A',\r\n                props: {\r\n                  style: {\r\n                    whiteSpace: 'normal',\r\n                    wordBreak: 'break-word'\r\n                  }\r\n                }\r\n              };\r\n            } catch (e) {\r\n              console.warn('Failed to render container name:', e);\r\n              return 'N/A';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Privileged',\r\n          dataIndex: 'inspect_data',\r\n          key: 'privileged',\r\n          width: '100px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              const privileged = inspectData.HostConfig?.Privileged;\r\n              // 将undefined、null或非布尔值视为false\r\n              return privileged === true ? 'Yes' : 'No';\r\n            } catch (e) {\r\n              console.warn('Failed to render privileged status:', e);\r\n              return 'No'; // 出错时默认显示为No\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Environment',\r\n          dataIndex: 'env',\r\n          key: 'env',\r\n          width: '200px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const envVars = this.parseJsonField(record.env, []);\r\n              if (!envVars.length) return 'No environment variables';\r\n\r\n              return (\r\n                <div style=\"font-size: 12px;\">\r\n                  {envVars.slice(0, 1).map((env, index) => (\r\n                    <div key={index} style=\"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">\r\n                      {env.length > 200 ? env.substring(0, 200) + '...' : env}\r\n                    </div>\r\n                  ))}\r\n                  {envVars.length > 1 && (\r\n                    <a-button\r\n                      type=\"link\"\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        this.showEnvironmentDetails(record);\r\n                      }}\r\n                    >\r\n                      +{envVars.length - 1} more...\r\n                    </a-button>\r\n                  )}\r\n                </div>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render env vars:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Mounts',\r\n          dataIndex: 'mounts',\r\n          key: 'mounts',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <mount-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showMountDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Network Listening',\r\n          dataIndex: 'exposures',\r\n          key: 'exposures',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <network-listening-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showNetworkDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Processes',\r\n          dataIndex: 'processes',\r\n          key: 'processes',\r\n          width: '120px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const processData = this.parseJsonField(record.processes, {});\r\n              if (!processData?.process_list?.length) return 'N/A';\r\n\r\n              return (\r\n                <a onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetails(record);\r\n                }}>\r\n                  {processData.process_list.length} processes\r\n                </a>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render processes:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Inspect Data',\r\n          dataIndex: 'inspect_data',\r\n          key: 'inspect_data',\r\n          width: '150px',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <span style=\"display: inline-block; line-height: 22px;\">\r\n              <a\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showInspectDetails(record);\r\n                }}\r\n                style=\"color: #1890ff; cursor: pointer;\"\r\n              >\r\n                View Inspect\r\n              </a>\r\n            </span>\r\n          )\r\n        }\r\n      ],\r\n      networkColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '120px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Network ID',\r\n          dataIndex: 'network_id',\r\n          key: 'network_id',\r\n          width: '180px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Driver',\r\n          dataIndex: 'driver',\r\n          key: 'driver',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Scope',\r\n          dataIndex: 'scope',\r\n          key: 'scope',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'IPv6',\r\n          dataIndex: 'ipv6',\r\n          key: 'ipv6',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Internal',\r\n          dataIndex: 'internal',\r\n          key: 'internal',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Labels',\r\n          dataIndex: 'labels',\r\n          key: 'labels',\r\n          width: '120px',\r\n          customRender: (labels) => {\r\n            if (!labels || Object.keys(labels).length === 0) return 'None';\r\n            return Object.entries(labels).map(([key, value]) => `${key}=${value}`).join(', ');\r\n          }\r\n        },\r\n        {\r\n          title: 'Created',\r\n          dataIndex: 'created_at',\r\n          key: 'created_at',\r\n          width: '160px',\r\n          customRender: (text) => text || 'None'\r\n        }\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      resourceMapping: {\r\n        'docker_host_config': 'get_docker_host_config',\r\n        'docker_container': 'get_docker_containers',\r\n        'docker_network': 'get_docker_networks',\r\n      },\r\n\r\n\r\n\r\n      networkDetailsVisible: false,\r\n      selectedNetwork: null,\r\n      mountDetailsVisible: false,\r\n      selectedMountContainer: null,\r\n      environmentDetailsVisible: false,\r\n      selectedEnvironment: null,\r\n      processDetailsVisible: false,\r\n      selectedProcessContainer: null,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null,\r\n      activeEngine: 'docker'\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.resetData();\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      const dataName = this.camelCaseToDataName(resourceType);\r\n      const loadingKey = `loading${this.capitalizeFirstLetter(dataName.replace('Data', ''))}`;\r\n\r\n      this[loadingKey] = true;\r\n\r\n      try {\r\n        const response = await axios.get(`/api/docker/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[dataName] = resourceType === 'docker_host_config' && data ? [data] : data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[dataName] = [];\r\n      } finally {\r\n        this[loadingKey] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutDocker = camelCase.replace('docker_', '');\r\n      return withoutDocker.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.hostConfigData = [];\r\n      this.containerData = [];\r\n      this.networkData = [];\r\n    },\r\n    getDaemonConfig(key) {\r\n      if (!this.hostConfigData[0]?.daemon_config) return null;\r\n      return this.hostConfigData[0].daemon_config[key];\r\n    },\r\n    getRegistryMirrors() {\r\n      const registryConfig = this.getDaemonConfig('RegistryConfig');\r\n      return registryConfig?.Mirrors || [];\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    parseJsonField(field, defaultValue) {\r\n      try {\r\n        if (Array.isArray(field)) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        if (typeof field === 'object' && field !== null) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n      } catch (e) {\r\n        console.warn('Failed to parse JSON field:', e);\r\n        return defaultValue;\r\n      }\r\n    },\r\n\r\n    showInspectDetails(container) {\r\n      try {\r\n        const inspectData = this.parseJsonField(container.inspect_data, {});\r\n        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);\r\n      } catch (e) {\r\n        console.error('Failed to parse inspect data:', e);\r\n        this.$message.error('Failed to parse inspect data');\r\n      }\r\n    },\r\n    renderPreviewWithMore(items, renderItem, moreText, onClickMore) {\r\n      if (!items || !items.length) return 'None';\r\n\r\n      return (\r\n        <div style=\"font-size: 12px;\">\r\n          {items.slice(0, 2).map((item, index) => renderItem(item, index))}\r\n          {items.length > 2 && (\r\n            <a-button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onClickMore();\r\n              }}\r\n            >\r\n              +{items.length - 2} {moreText}\r\n            </a-button>\r\n          )}\r\n        </div>\r\n      );\r\n    },\r\n    cleanReactiveObject(obj) {\r\n      if (!obj) return null;\r\n      if (Array.isArray(obj)) {\r\n        return obj.map(item => this.cleanReactiveObject(item));\r\n      }\r\n      if (typeof obj === 'object') {\r\n        const cleaned = {};\r\n        for (const key in obj) {\r\n          if (!key.startsWith('_') && !key.startsWith('$') && key !== '__ob__') {\r\n            cleaned[key] = this.cleanReactiveObject(obj[key]);\r\n          }\r\n        }\r\n        return cleaned;\r\n      }\r\n      return obj;\r\n    },\r\n    getProcessList() {\r\n      if (!this.selectedProcessContainer?.processes?.process_list) return [];\r\n      return this.selectedProcessContainer.processes.process_list;\r\n    },\r\n    getNetworkListening() {\r\n      if (!this.selectedContainer?.exposures) return [];\r\n      const exposedServices = this.parseJsonField(this.selectedContainer.exposures, {});\r\n      return exposedServices.listening_ports || [];\r\n    },\r\n    showNetworkDetails(container) {\r\n      this.selectedNetwork = {\r\n        ...container,\r\n        exposures: this.parseJsonField(container.exposures, {})\r\n      };\r\n      this.networkDetailsVisible = true;\r\n    },\r\n    handleNetworkDetailsClose() {\r\n      this.networkDetailsVisible = false;\r\n      this.selectedNetwork = null;\r\n    },\r\n\r\n    showMountDetails(container) {\r\n      this.selectedMountContainer = {\r\n        ...container,\r\n        mounts: this.parseJsonField(container.mounts, [])\r\n      };\r\n      this.mountDetailsVisible = true;\r\n    },\r\n    handleMountDetailsClose() {\r\n      this.mountDetailsVisible = false;\r\n      this.selectedMountContainer = null;\r\n    },\r\n    getAllMounts() {\r\n      if (!this.selectedMountContainer?.mounts) return [];\r\n      return this.selectedMountContainer.mounts;\r\n    },\r\n    showEnvironmentDetails(container) {\r\n      this.selectedEnvironment = {\r\n        ...container,\r\n        env: this.parseJsonField(container.env, [])\r\n      };\r\n      this.environmentDetailsVisible = true;\r\n    },\r\n    handleEnvironmentDetailsClose() {\r\n      this.environmentDetailsVisible = false;\r\n      this.selectedEnvironment = null;\r\n    },\r\n    getAllEnvironmentVars() {\r\n      if (!this.selectedEnvironment?.env) return [];\r\n      return this.selectedEnvironment.env;\r\n    },\r\n    showProcessDetails(container) {\r\n      this.selectedProcessContainer = {\r\n        ...container,\r\n        processes: this.parseJsonField(container.processes, {})\r\n      };\r\n      this.processDetailsVisible = true;\r\n    },\r\n    handleProcessDetailsClose() {\r\n      this.processDetailsVisible = false;\r\n      this.selectedProcessContainer = null;\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.docker-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.docker-title {\r\n  color: #333;\r\n  font-size: 16px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n// 表格样式会从全局样式继承，不需要在这里硬编码\r\n\r\n.host-config-container {\r\n  padding: 24px;\r\n}\r\n\r\n.ant-card {\r\n  height: 100%;\r\n}\r\n\r\n.ant-card-head {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\nul {\r\n  padding-left: 20px;\r\n  margin: 0;\r\n}\r\n\r\nli {\r\n  word-break: break-all;\r\n}\r\n\r\n.ant-descriptions-bordered .ant-descriptions-item-label {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n\r\npre {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  margin: 4px 0;\r\n}\r\n\r\n/* 添加mount项的样式 */\r\n.mount-item {\r\n  padding: 2px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.mounts-container {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mount-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.mount-path {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mount-source {\r\n  color: #1890ff;\r\n}\r\n\r\n.mount-arrow {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.mount-dest {\r\n  color: #52c41a;\r\n}\r\n\r\n.mount-details {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mount-tag {\r\n  background: #f5f5f5;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.ant-collapse-panel {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.ant-tag {\r\n  margin: 2px;\r\n}\r\n\r\n.ant-collapse-content {\r\n  background: #fafafa;\r\n}\r\n\r\n.env-container {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.env-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  word-break: break-all;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.ant-descriptions {\r\n  .ant-descriptions-item-label {\r\n    width: 180px;\r\n    background-color: #fafafa;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .ant-descriptions-item-content {\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.details-container {\r\n  width: 100%;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 4px;\r\n}\r\n\r\n.details-row {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.details-header {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-item {\r\n  padding: 12px 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.details-content {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n}\r\n\r\n.content-item {\r\n  padding: 12px 16px;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n  word-break: break-word;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.container-runtime-tabs {\r\n  padding: 24px;\r\n}\r\n\r\n/* 确保标签紧跟在标题下方 */\r\n:deep(.ant-tabs) {\r\n  margin-top: 0;\r\n}\r\n\r\n:deep(.ant-card-head) {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.inspect-data-pre, .detail-pre {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n}\r\n\r\n/* 确保Tab内容区域没有背景色 */\r\n.ant-tabs-tabpane {\r\n  background-color: transparent !important;\r\n}\r\n\r\n\r\n\r\n/* 信息项样式 */\r\n.info-item {\r\n  margin-bottom: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px dashed #f0f0f0;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    padding-bottom: 0;\r\n    border-bottom: none;\r\n  }\r\n\r\n  strong {\r\n    margin-right: 8px;\r\n    color: #555;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DockerInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DockerInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DockerInfo.vue?vue&type=template&id=43ac44d4&scoped=true\"\nimport script from \"./DockerInfo.vue?vue&type=script&lang=js\"\nexport * from \"./DockerInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./DockerInfo.vue?vue&type=style&index=0&id=43ac44d4&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43ac44d4\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<DockerInfo></DockerInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport DockerInfo from \"@/components/Cards/DockerInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        DockerInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Docker.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Docker.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Docker.vue?vue&type=template&id=6f9467ab\"\nimport script from \"./Docker.vue?vue&type=script&lang=js\"\nexport * from \"./Docker.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=style&index=0&id=7192c848&prod&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DockerInfo.vue?vue&type=style&index=0&id=43ac44d4&prod&scoped=true&lang=scss\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessTable.vue?vue&type=style&index=0&id=424fe87c&prod&scoped=true&lang=scss\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MountCell.vue?vue&type=style&index=0&id=70290246&prod&scoped=true&lang=scss\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NetworkListeningCell.vue?vue&type=style&index=0&id=167d61b4&prod&scoped=true&lang=scss\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CrictlInfo.vue?vue&type=style&index=0&id=ef13263e&prod&scoped=true&lang=scss\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetailModal.vue?vue&type=style&index=0&id=655588c5&prod&scoped=true&lang=scss\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CriMountCell.vue?vue&type=style&index=0&id=3be0b9ad&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div')\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <!-- 组件不直接渲染模态框，而是提供方法 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport VueJsonPretty from 'vue-json-pretty';\r\n\r\nexport default {\r\n  name: 'JsonDetailModal',\r\n  methods: {\r\n    /**\r\n     * 显示JSON详情模态框\r\n     * @param {string} title 模态框标题\r\n     * @param {object|string} data 要显示的数据\r\n     * @param {object} options 配置选项\r\n     */\r\n    showDetailModal(title, data, options = {}) {\r\n      // 计算响应式尺寸\r\n      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);\r\n      const contentHeight = Math.min(700, window.innerHeight * 0.6);\r\n\r\n      // 生成唯一ID\r\n      const ids = {\r\n        search: `search-${Date.now()}`,\r\n        counter: `counter-${Date.now()}`,\r\n        theme: `theme-${Date.now()}`\r\n      };\r\n\r\n      // 默认使用暗色主题\r\n      let isDarkTheme = true;\r\n\r\n      // 创建标题、搜索框和复制按钮\r\n      const header = (\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <a-icon type=\"code\" style=\"margin-right: 8px; font-size: 16px;\" />\r\n            <span style=\"font-weight: 500;\">{title}</span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <div id={ids.counter} style=\"margin-right: 10px; min-width: 60px; text-align: right; color: #666;\"></div>\r\n            <a-input\r\n              id={ids.search}\r\n              placeholder=\"搜索 (Enter: ↓  Shift+Enter: ↑)\"\r\n              allowClear\r\n              prefix={<a-icon type=\"search\" style=\"color: rgba(0,0,0,.25)\" />}\r\n              style=\"width: 250px;\"\r\n            />\r\n            <a-button\r\n              id={ids.theme}\r\n              type=\"link\"\r\n              icon=\"bg-colors\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"切换主题\"\r\n            />\r\n            <a-button\r\n              id=\"copy-btn\"\r\n              type=\"link\"\r\n              icon=\"copy\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"复制内容\"\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n      // 准备内容元素\r\n      const contentElement = typeof data === 'object' ? (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class=\"json-container theme-dark\" id=\"json-container\">\r\n        </div>\r\n      ) : (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>\r\n          {String(data)}\r\n        </div>\r\n      );\r\n\r\n      // 创建模态框\r\n      this.$root.$confirm({\r\n        title: header,\r\n        content: contentElement,\r\n        width: modalWidth,\r\n        okText: options.okText || '关闭',\r\n        icon: null,\r\n        cancelButtonProps: { style: { display: 'none' } },\r\n        class: 'detail-modal',\r\n        maskClosable: false, // 防止点击外部关闭\r\n        getContainer: () => document.body.appendChild(document.createElement('div'))\r\n      });\r\n\r\n      // 在模态框内容区域渲染VueJsonPretty组件\r\n      setTimeout(() => {\r\n        if (typeof data === 'object') {\r\n          // 查找JSON容器\r\n          const container = document.getElementById('json-container');\r\n          if (container) {\r\n            // 创建VueJsonPretty组件实例\r\n            const JsonViewer = new Vue({\r\n              render: h => h(VueJsonPretty, {\r\n                props: {\r\n                  data: data,\r\n                  deep: Infinity, // 设置为Infinity以默认展开所有节点\r\n                  showDoubleQuotes: true,\r\n                  showLength: true,\r\n                  showLineNumbers: true  // 添加行号显示\r\n                },\r\n                style: {\r\n                  height: '100%',\r\n                  overflow: 'auto'\r\n                }\r\n              })\r\n            });\r\n\r\n            // 挂载组件\r\n            JsonViewer.$mount();\r\n            container.appendChild(JsonViewer.$el);\r\n          }\r\n        }\r\n\r\n        // 获取搜索相关元素\r\n        const searchInput = document.getElementById(ids.search);\r\n        const counterElement = document.getElementById(ids.counter);\r\n\r\n        // 搜索功能变量\r\n        let matches = [];\r\n        let currentMatchIndex = -1;\r\n\r\n        // 如果有搜索框，添加搜索功能\r\n        if (searchInput && counterElement) {\r\n          // 高亮匹配项函数\r\n          const highlightMatches = (searchTerm) => {\r\n            // 重置匹配\r\n            matches = [];\r\n            currentMatchIndex = -1;\r\n\r\n            // 清除计数器\r\n            counterElement.textContent = '';\r\n\r\n            if (!searchTerm) return;\r\n\r\n            try {\r\n              // 查找所有键和值节点\r\n              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');\r\n\r\n              // 创建正则表达式\r\n              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\r\n\r\n              // 移除之前的高亮\r\n              document.querySelectorAll('.vjs-search-match').forEach(el => {\r\n                el.classList.remove('vjs-search-match');\r\n              });\r\n\r\n              document.querySelectorAll('.vjs-search-current').forEach(el => {\r\n                el.classList.remove('vjs-search-current');\r\n              });\r\n\r\n              // 查找匹配项\r\n              jsonNodes.forEach(node => {\r\n                const text = node.textContent;\r\n                let match;\r\n                regex.lastIndex = 0;\r\n\r\n                while ((match = regex.exec(text)) !== null) {\r\n                  matches.push({\r\n                    node: node,\r\n                    text: match[0]\r\n                  });\r\n\r\n                  // 防止无限循环\r\n                  if (match.index === regex.lastIndex) {\r\n                    regex.lastIndex++;\r\n                  }\r\n                }\r\n              });\r\n\r\n              // 更新计数器\r\n              if (matches.length === 0) {\r\n                counterElement.textContent = '无匹配项';\r\n                return;\r\n              }\r\n\r\n              counterElement.textContent = `0/${matches.length}`;\r\n\r\n              // 为所有匹配项添加高亮类\r\n              matches.forEach(match => {\r\n                match.node.classList.add('vjs-search-match');\r\n              });\r\n            } catch (error) {\r\n              console.error('搜索错误:', error);\r\n            }\r\n          };\r\n\r\n          // 导航到特定匹配项\r\n          const navigateToMatch = (index) => {\r\n            if (matches.length === 0) return;\r\n\r\n            // 确保索引在有效范围内\r\n            index = Math.max(0, Math.min(matches.length - 1, index));\r\n\r\n            // 移除之前匹配项的当前高亮\r\n            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {\r\n              matches[currentMatchIndex].node.classList.remove('vjs-search-current');\r\n            }\r\n\r\n            // 更新当前索引和计数器\r\n            currentMatchIndex = index;\r\n            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;\r\n\r\n            // 高亮当前匹配项\r\n            const currentMatch = matches[currentMatchIndex];\r\n            if (currentMatch) {\r\n              currentMatch.node.classList.add('vjs-search-current');\r\n\r\n              // 确保父节点是展开的\r\n              let parent = currentMatch.node.parentElement;\r\n              while (parent) {\r\n                if (parent.classList && parent.classList.contains('vjs-tree-node')) {\r\n                  // 如果节点是折叠的，点击展开按钮\r\n                  if (!parent.classList.contains('is-expanded')) {\r\n                    const expandBtn = parent.querySelector('.vjs-tree-brackets');\r\n                    if (expandBtn) expandBtn.click();\r\n                  }\r\n                }\r\n                parent = parent.parentElement;\r\n              }\r\n\r\n              // 滚动到匹配项\r\n              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          };\r\n\r\n          // 添加搜索输入事件\r\n          let searchTimeout;\r\n          searchInput.addEventListener('input', (e) => {\r\n            // 使用防抖优化性能\r\n            if (searchTimeout) clearTimeout(searchTimeout);\r\n            searchTimeout = setTimeout(() => {\r\n              highlightMatches(e.target.value.trim());\r\n            }, 300);\r\n          });\r\n\r\n          // 添加键盘导航事件\r\n          searchInput.addEventListener('keydown', (e) => {\r\n            if (e.key === 'Enter') {\r\n              e.preventDefault();\r\n              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加主题切换功能\r\n        const themeButton = document.getElementById(ids.theme);\r\n        if (themeButton) {\r\n          themeButton.addEventListener('click', () => {\r\n            const container = document.querySelector('.json-container');\r\n            if (container) {\r\n              // 切换主题类\r\n              if (container.classList.contains('theme-light')) {\r\n                container.classList.remove('theme-light');\r\n                container.classList.add('theme-dark');\r\n                container.style.backgroundColor = '#1e1e1e';\r\n                isDarkTheme = true;\r\n              } else {\r\n                container.classList.remove('theme-dark');\r\n                container.classList.add('theme-light');\r\n                container.style.backgroundColor = '#fff';\r\n                isDarkTheme = false;\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加复制功能\r\n        const copyButton = document.getElementById('copy-btn');\r\n        if (copyButton) {\r\n          copyButton.addEventListener('click', () => {\r\n            try {\r\n              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);\r\n\r\n              // 使用现代Clipboard API\r\n              if (navigator.clipboard && window.isSecureContext) {\r\n                navigator.clipboard.writeText(textToCopy)\r\n                  .then(() => {\r\n                    this.$message.success(this.$t('common.copiedToClipboard'));\r\n                  })\r\n                  .catch(err => {\r\n                    console.error('复制失败:', err);\r\n                    this.$message.error('复制失败');\r\n                  });\r\n              } else {\r\n                // 备用方法\r\n                const textArea = document.createElement('textarea');\r\n                textArea.value = textToCopy;\r\n                document.body.appendChild(textArea);\r\n                textArea.select();\r\n                const successful = document.execCommand('copy');\r\n                document.body.removeChild(textArea);\r\n\r\n                if (successful) {\r\n                  this.$message.success(this.$t('common.copiedToClipboard'));\r\n                } else {\r\n                  this.$message.error(this.$t('common.copyFailed'));\r\n                }\r\n              }\r\n            } catch (err) {\r\n              this.$message.error(this.$t('common.copyFailed'));\r\n            }\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 覆盖详情弹窗的样式 */\r\n.detail-modal {\r\n  .ant-modal-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-content {\r\n    margin-top: 12px !important;\r\n    margin-bottom: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-btns {\r\n    margin-top: 16px !important;\r\n    margin-bottom: 8px !important;\r\n  }\r\n\r\n  /* 复制按钮样式 */\r\n  #copy-btn {\r\n    transition: all 0.3s;\r\n\r\n    &:hover {\r\n      color: #40a9ff !important;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    &:active {\r\n      color: #096dd9 !important;\r\n    }\r\n  }\r\n\r\n  /* 搜索匹配项样式 */\r\n  .vjs-search-match {\r\n    background-color: rgba(255, 255, 0, 0.3);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .vjs-search-current {\r\n    background-color: rgba(255, 165, 0, 0.6);\r\n    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);\r\n  }\r\n\r\n  /* 主题样式 */\r\n  .json-container.theme-dark {\r\n    .vjs-tree {\r\n      background-color: #1e1e1e !important;\r\n      color: #d4d4d4 !important;\r\n\r\n      .vjs-key {\r\n        color: #9cdcfe !important; /* 浅蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-string {\r\n        color: #ce9178 !important; /* 橙红色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-number {\r\n        color: #b5cea8 !important; /* 浅绿色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-boolean {\r\n        color: #569cd6 !important; /* 蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-null {\r\n        color: #c586c0 !important; /* 紫色 */\r\n      }\r\n\r\n      .vjs-tree-brackets {\r\n        color: #d4d4d4 !important; /* 浅灰色 */\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 调整内容区域的左侧边距 */\r\n.ant-modal-confirm-body {\r\n  padding-left: 12px !important;\r\n}\r\n\r\n/* 调整内容区域的图标和文本间距 */\r\n.ant-modal-confirm-body > .anticon {\r\n  display: none !important;\r\n}\r\n\r\n/* 调整标题和内容的左侧边距 */\r\n.ant-modal-confirm-body > .ant-modal-confirm-title,\r\n.ant-modal-confirm-body > .ant-modal-confirm-content {\r\n  margin-left: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n/* 调整内容区域的下边距 */\r\n.detail-modal .ant-modal-confirm-content {\r\n  margin-bottom: 8px !important; /* 适当的下边距 */\r\n}\r\n\r\n/* 调整底部按钮区域的上下边距 */\r\n.detail-modal .ant-modal-confirm-btns {\r\n  margin-top: 8px !important; /* 减小上边距 */\r\n  margin-bottom: 4px !important;\r\n  padding-top: 0 !important; /* 移除上内边距 */\r\n  padding-bottom: 0 !important;\r\n  border-top: none !important; /* 移除分隔线 */\r\n}\r\n\r\n/* 调整底部按钮本身的样式 */\r\n.detail-modal .ant-modal-confirm-btns button {\r\n  margin-top: 0 !important;\r\n  margin-bottom: 0 !important;\r\n  padding: 6px 16px !important;\r\n  height: auto !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./JsonDetailModal.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./JsonDetailModal.vue?vue&type=template&id=7192c848\"\nimport script from \"./JsonDetailModal.vue?vue&type=script&lang=js\"\nexport * from \"./JsonDetailModal.vue?vue&type=script&lang=js\"\nimport style0 from \"./JsonDetailModal.vue?vue&type=style&index=0&id=7192c848&prod&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}