(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6fdea86c"],{"0ea4":function(e,t,r){},"14b2":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("HardwareInfo")],1)],1)],1)},o=[],s=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full hardware-card",attrs:{bordered:!1,bodyStyle:{padding:0},headStyle:{borderBottom:"1px solid #e8e8e8"}},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 512 512"}},[t("path",{attrs:{fill:"currentColor",d:"M160 160h192v192H160z"}}),t("path",{attrs:{fill:"currentColor",d:"M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.hardware")))])]),t("div",[t("RefreshButton",{on:{refresh:e.fetchHardware}})],1)])]},proxy:!0}])},[t("a-table",{attrs:{columns:e.columns,"data-source":e.hardwareItems,rowKey:e=>e.device_info,pagination:e.pagination},scopedSlots:e._u([{key:"bodyCell",fn:function({column:r,record:a}){return["device_info"===r.key?[t("div",{staticClass:"table-hardware-info"},[t("span",[e._v(e._s(a.device_info))]),t("span",[e._v(e._s(a.device_type))])])]:e._e()]}}])})],1)},n=[],c=r("2f62"),d=r("fec3"),i=r("f188"),l={components:{RefreshButton:i["a"]},data(){return{hardwareItems:[],columns:[{title:"Device Info",dataIndex:"device_info",key:"device_info"},{title:"Device Type",dataIndex:"device_type",key:"device_type"}],pagination:{pageSize:100}}},computed:{...Object(c["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(e){this.fetchHardware()}},mounted(){this.fetchHardware()},methods:{async fetchHardware(){if(console.log("Selected Node IP:",this.selectedNodeIp),this.selectedNodeIp)try{const e=await d["a"].get("/api/hardware/"+this.selectedNodeIp,{params:{dbFile:this.currentProject}});this.hardwareItems=e.data}catch(e){console.error("Error fetching hardware:",e)}else console.error("Node IP is not defined")}}},h=l,v=(r("5cdb"),r("2877")),f=Object(v["a"])(h,s,n,!1,null,"26073df1",null),u=f.exports,p={components:{HardwareInfo:u}},w=p,b=Object(v["a"])(w,a,o,!1,null,null,null);t["default"]=b.exports},"5cdb":function(e,t,r){"use strict";r("0ea4")},f188:function(e,t,r){"use strict";var a=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},o=[],s=r("2f62"),n={computed:{...Object(s["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},c=n,d=r("2877"),i=Object(d["a"])(c,a,o,!1,null,"80cb1374",null);t["a"]=i.exports}}]);
//# sourceMappingURL=chunk-6fdea86c.fcc82cd1.js.map