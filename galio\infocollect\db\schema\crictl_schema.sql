CREATE TABLE IF NOT EXISTS crictl_host_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER NOT NULL,
    version_info TEXT,
    runtime_info TEXT,
    is_root_user BOOLEAN,
    FOR<PERSON><PERSON><PERSON> KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS crictl_pod (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER NOT NULL,
    pod_id TEXT,
    pod_metadata TEXT,
    state TEXT,
    created_at TEXT,
    network TEXT,
    labels TEXT,
    annotations TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS crictl_container (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    container_id TEXT,
    mounts TEXT,
    processes TEXT,
    exposures TEXT,
    env TEXT,
    inspect_data TEXT,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY(node_id) <PERSON><PERSON><PERSON><PERSON>CES host_config(id) ON DELETE CASCADE,
    UNIQUE(node_id, container_id)
);
