import os
import logging
from logging.handlers import RotatingFileHandler

LOG_DIR = os.path.dirname(__file__)
LOG_FILE = os.path.join(LOG_DIR, 'infocollect.log')

LOG_FORMAT = '%(asctime)s [%(levelname)s] %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


class ColorFormatter(logging.Formatter):
    COLORS = {
        'DEBUG': '\033[0;37m',    # 灰色
        'INFO': '\033[0;32m',     # 绿色
        'WARNING': '\033[0;33m',  # 黄色
        'ERROR': '\033[0;31m',    # 红色
        'RESET': '\033[0m',       # 重置
    }

    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.msg = f"{color}{record.msg}{self.COLORS['RESET']}"
        return super().format(record)


def setup_logger():
    """设置全局logger"""
    logger = logging.getLogger('infocollect')
    logger.setLevel(logging.DEBUG)

    if logger.handlers:
        return logger

    file_handler = RotatingFileHandler(
        LOG_FILE,
        maxBytes=5*1024*1024,
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))
    logger.addHandler(file_handler)

    # 控制台处理程序
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(ColorFormatter(LOG_FORMAT, DATE_FORMAT))
    logger.addHandler(console_handler)

    return logger


logger = setup_logger()
