from flask import Flask, request, jsonify
from pymilvus import MilvusClient, IndexParams
from sentence_transformers import SentenceTransformer

app = Flask(__name__)

embedding_model = None


def get_embedding_model():
    """Get text embedding model"""
    global embedding_model
    if embedding_model is None:
        try:
            model_path = "./model/all-MiniLM-L6-v2"
            embedding_model = SentenceTransformer(model_path)
            print(f"Successfully loaded local embedding model from: {model_path}")
        except Exception as e:
            print(f"Failed to load local embedding model: {e}")
            raise
    return embedding_model


def get_milvus_client():
    """Get Milvus client instance"""
    try:
        db_path = "/mnt/d/test/milvus_testcase.db"
        print(f"Connecting to Milvus Lite database: {db_path}")
        return MilvusClient(uri=db_path)
    except Exception as e:
        print(f"Failed to connect to Milvus: {e}")
        raise


def create_testcase_text(name, level, prepare_condition, test_steps, expected_result):
    """Combine testcase fields into text for vectorization"""
    parts = []
    if name:
        parts.append(f"Name: {name}")
    if level:
        parts.append(f"Level: {level}")
    if prepare_condition:
        parts.append(f"Preparation: {prepare_condition}")
    if test_steps:
        parts.append(f"Steps: {test_steps}")
    if expected_result:
        parts.append(f"Expected: {expected_result}")

    return " | ".join(parts)


def create_collection_if_not_exists(client, collection_name):
    """Create collection with standard schema if it doesn't exist"""
    if not client.has_collection(collection_name):
        from pymilvus import FieldSchema, CollectionSchema, DataType

        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, is_primary=True, max_length=65535),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=384),
            FieldSchema(name="name", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="level", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="prepare_condition", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="test_steps", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="expected_result", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="combined_text", dtype=DataType.VARCHAR, max_length=65535)
        ]

        schema = CollectionSchema(fields, "Testcase collection")

        try:
            client.create_collection(
                collection_name=collection_name,
                schema=schema
            )
            print(f"Created collection '{collection_name}' with dimension 384 and string ID field")

            # Create index for vector field - required for search operations
            try:
                # Use IndexParams for proper parameter formatting
                index_params = IndexParams()
                index_params.add_index(
                    field_name="vector",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )
                client.create_index(
                    collection_name=collection_name,
                    index_params=index_params
                )
                print(f"Created AUTOINDEX for vector field in collection '{collection_name}'")
            except Exception as index_error:
                print(f"Warning: Failed to create AUTOINDEX: {index_error}")
                # Try alternative index type with FLAT
                try:
                    index_params = IndexParams()
                    index_params.add_index(
                        field_name="vector",
                        index_type="FLAT",
                        metric_type="COSINE"
                    )
                    client.create_index(
                        collection_name=collection_name,
                        index_params=index_params
                    )
                    print(f"Created FLAT index for vector field in collection '{collection_name}'")
                except Exception as flat_error:
                    print(f"Error: Failed to create FLAT index: {flat_error}")
                    raise

            print(f"Collection '{collection_name}' created successfully with index")

        except Exception as e:
            print(f"Failed to create collection: {e}")
            raise


@app.route('/insert_testcase', methods=['POST'])
def insert_testcase():
    """Insert testcase data"""
    client = None
    try:
        client = get_milvus_client()
        model = get_embedding_model()
        
        data = request.json
        testcase_data = data.get("testcase_data")

        if not testcase_data:
            return jsonify({"status": "error", "message": "Missing testcase_data"}), 400

        collection_name = "testcase_collection"

        # Ensure collection exists
        create_collection_if_not_exists(client, collection_name)

        if not isinstance(testcase_data, list):
            testcase_data = [testcase_data]

        entities = []
        for testcase in testcase_data:
            testcase_id = testcase.get("id") or testcase.get("Testcase_Number")
            name = testcase.get("Testcase_Name", "")
            level = testcase.get("Testcase_Level", "")
            prepare_condition = testcase.get("Testcase_PrepareCondition", "")
            test_steps = testcase.get("Testcase_TestSteps", "")
            expected_result = testcase.get("Testcase_ExpectedResult", "")
            
            if not testcase_id:
                continue
                
            combined_text = create_testcase_text(name, level, prepare_condition, test_steps, expected_result)
            vector = model.encode(combined_text).tolist()
            
            entity = {
                "id": str(testcase_id),
                "vector": vector,
                "name": name,
                "level": level,
                "prepare_condition": prepare_condition,
                "test_steps": test_steps,
                "expected_result": expected_result,
                "combined_text": combined_text
            }
            entities.append(entity)
        
        if not entities:
            return jsonify({"status": "error", "message": "No valid testcase data found"}), 400

        # Delete existing entities with same IDs to avoid duplicates
        existing_ids = [entity["id"] for entity in entities]
        if existing_ids:
            try:
                # Properly format the delete expression with escaped quotes
                ids_str = ', '.join([f'"{str(id).replace(chr(34), chr(92)+chr(34))}"' for id in existing_ids])
                delete_expr = f"id in [{ids_str}]"
                client.delete(collection_name, delete_expr)
                print(f"Deleted existing entities with IDs: {len(existing_ids)} items")
            except Exception as e:
                print(f"Warning: Failed to delete existing entities: {e}")

        client.insert(collection_name, entities)
        print(f"Inserted {len(entities)} testcase entities into collection '{collection_name}'")

        return jsonify({
            "status": "success", 
            "inserted_count": len(entities),
            "collection_name": collection_name
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/search_testcase', methods=['POST'])
def search_testcase():
    """Search testcases with natural language"""
    client = None
    try:
        client = get_milvus_client()
        model = get_embedding_model()
        
        data = request.json
        query_text = data.get("query_text")
        top_k = data.get("top_k", 10)
        
        if not query_text:
            return jsonify({"status": "error", "message": "Missing query_text"}), 400
        
        collection_name = "testcase_collection"
        
        if not client.has_collection(collection_name):
            return jsonify({"status": "error", "message": f"Collection '{collection_name}' does not exist"}), 404
        
        # Check if index exists, create if not
        try:
            indexes = client.list_indexes(collection_name)
            if not indexes:
                print(f"No indexes found in collection '{collection_name}', creating index...")
                try:
                    # Use IndexParams for proper parameter formatting
                    index_params = IndexParams()
                    index_params.add_index(
                        field_name="vector",
                        index_type="AUTOINDEX",
                        metric_type="COSINE"
                    )
                    client.create_index(
                        collection_name=collection_name,
                        index_params=index_params
                    )
                    print(f"Created AUTOINDEX for vector field")
                except Exception as index_error:
                    print(f"Warning: Failed to create AUTOINDEX: {index_error}")
                    # Try alternative index type
                    index_params = IndexParams()
                    index_params.add_index(
                        field_name="vector",
                        index_type="FLAT",
                        metric_type="COSINE"
                    )
                    client.create_index(
                        collection_name=collection_name,
                        index_params=index_params
                    )
                    print(f"Created FLAT index for vector field")
        except Exception as e:
            print(f"Warning: Could not check/create index: {e}")
        
        query_vector = model.encode(query_text).tolist()
        
        search_results = client.search(
            collection_name=collection_name,
            data=[query_vector],
            limit=top_k,
            output_fields=["id", "name", "level", "prepare_condition", "test_steps", "expected_result", "combined_text"]
        )
        
        results = []
        for hits in search_results:
            for hit in hits:
                result = {
                    "id": hit["id"],
                    "score": hit["distance"],
                    "name": hit["name"],
                    "level": hit["level"],
                    "prepare_condition": hit["prepare_condition"],
                    "test_steps": hit["test_steps"],
                    "expected_result": hit["expected_result"]
                }
                results.append(result)
        
        return jsonify({
            "status": "success",
            "query": query_text,
            "results": results
        })
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/batch_insert_testcases', methods=['POST'])
def batch_insert_testcases():
    """Batch insert testcase data"""
    client = None
    try:
        client = get_milvus_client()
        model = get_embedding_model()
        
        data = request.json
        testcases = data.get("testcases", [])
        batch_size = data.get("batch_size", 100)

        if not testcases:
            return jsonify({"status": "error", "message": "Missing testcases"}), 400

        collection_name = "testcase_collection"

        # Ensure collection exists
        create_collection_if_not_exists(client, collection_name)

        total_inserted = 0
        
        for i in range(0, len(testcases), batch_size):
            batch = testcases[i:i + batch_size]
            entities = []
            
            for testcase in batch:
                testcase_id = testcase.get("id") or testcase.get("Testcase_Number")
                if not testcase_id:
                    continue
                    
                name = testcase.get("Testcase_Name", "")
                level = testcase.get("Testcase_Level", "")
                prepare_condition = testcase.get("Testcase_PrepareCondition", "")
                test_steps = testcase.get("Testcase_TestSteps", "")
                expected_result = testcase.get("Testcase_ExpectedResult", "")
                
                combined_text = create_testcase_text(name, level, prepare_condition, test_steps, expected_result)
                vector = model.encode(combined_text).tolist()
                
                entity = {
                    "id": str(testcase_id),
                    "vector": vector,
                    "name": name,
                    "level": level,
                    "prepare_condition": prepare_condition,
                    "test_steps": test_steps,
                    "expected_result": expected_result,
                    "combined_text": combined_text
                }
                entities.append(entity)
            
            if entities:
                # Delete existing entities with same IDs to avoid duplicates
                existing_ids = [entity["id"] for entity in entities]
                if existing_ids:
                    try:
                        # Properly format the delete expression with escaped quotes
                        ids_str = ', '.join([f'"{str(id).replace(chr(34), chr(92)+chr(34))}"' for id in existing_ids])
                        delete_expr = f"id in [{ids_str}]"
                        client.delete(collection_name, delete_expr)
                        print(f"Deleted existing entities in batch {i//batch_size + 1}: {len(existing_ids)} IDs")
                    except Exception as e:
                        print(f"Warning: Failed to delete existing entities in batch {i//batch_size + 1}: {e}")

                client.insert(collection_name, entities)
                total_inserted += len(entities)
                print(f"Inserted batch {i//batch_size + 1}: {len(entities)} entities")

        return jsonify({
            "status": "success", 
            "total_inserted": total_inserted,
            "collection_name": collection_name
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/delete_testcase', methods=['POST'])
def delete_testcase():
    """Delete specified testcases"""
    client = None
    try:
        client = get_milvus_client()
        
        data = request.json
        testcase_ids = data.get("testcase_ids", [])
        
        if not testcase_ids:
            return jsonify({"status": "error", "message": "Missing testcase_ids"}), 400
            
        collection_name = "testcase_collection"
        
        if not client.has_collection(collection_name):
            return jsonify({"status": "error", "message": f"Collection '{collection_name}' does not exist"}), 404
        
        # Properly format the delete expression with escaped quotes
        ids_str = ', '.join([f'"{str(id).replace(chr(34), chr(92)+chr(34))}"' for id in testcase_ids])
        delete_expr = f"id in [{ids_str}]"
        
        client.delete(collection_name, delete_expr)
        
        return jsonify({
            "status": "success",
            "deleted_ids": testcase_ids
        })
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/status', methods=['GET'])
def get_status():
    """Get collection status info"""
    client = None
    try:
        client = get_milvus_client()
        collection_name = "testcase_collection"
        
        if client.has_collection(collection_name):
            stats = client.get_collection_stats(collection_name)
            return jsonify({
                "status": "success",
                "collection_exists": True,
                "collection_name": collection_name,
                "stats": stats
            })
        else:
            return jsonify({
                "status": "success",
                "collection_exists": False,
                "collection_name": collection_name
            })
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "Milvus Lite Testcase Service"})


@app.route('/clear_collection', methods=['POST'])
def clear_collection():
    """Clear all data in collection"""
    client = None
    try:
        client = get_milvus_client()
        collection_name = "testcase_collection"

        if not client.has_collection(collection_name):
            # Collection doesn't exist, create it first
            print(f"Collection '{collection_name}' does not exist, creating it...")
            create_collection_if_not_exists(client, collection_name)
            return jsonify({
                "status": "success",
                "message": f"Collection '{collection_name}' created (was empty)"
            })

        # Delete all entities in collection
        try:
            # Use a more reliable expression to delete all records
            client.delete(collection_name, 'id != ""')
            print(f"Cleared all data in collection '{collection_name}'")
        except Exception as e:
            print(f"Warning: Failed to clear collection data: {e}")

        return jsonify({
            "status": "success",
            "message": f"Collection '{collection_name}' cleared successfully"
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/create_index', methods=['POST'])
def create_index():
    """Create index for existing collection"""
    client = None
    try:
        client = get_milvus_client()
        collection_name = "testcase_collection"
        
        if not client.has_collection(collection_name):
            return jsonify({"status": "error", "message": f"Collection '{collection_name}' does not exist"}), 404
        
        # Check if index already exists
        try:
            indexes = client.list_indexes(collection_name)
            if indexes:
                return jsonify({
                    "status": "success",
                    "message": f"Index already exists in collection '{collection_name}'",
                    "indexes": indexes
                })
        except Exception as e:
            print(f"Could not check existing indexes: {e}")
        
        # Create index
        try:
            # Use IndexParams for proper parameter formatting
            index_params = IndexParams()
            index_params.add_index(
                field_name="vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )
            client.create_index(
                collection_name=collection_name,
                index_params=index_params
            )
            print(f"Created AUTOINDEX for vector field in collection '{collection_name}'")
            return jsonify({
                "status": "success",
                "message": f"Created AUTOINDEX for vector field in collection '{collection_name}'"
            })
        except Exception as index_error:
            print(f"Failed to create AUTOINDEX: {index_error}")
            # Try alternative index type
            try:
                index_params = IndexParams()
                index_params.add_index(
                    field_name="vector",
                    index_type="FLAT",
                    metric_type="COSINE"
                )
                client.create_index(
                    collection_name=collection_name,
                    index_params=index_params
                )
                print(f"Created FLAT index for vector field in collection '{collection_name}'")
                return jsonify({
                    "status": "success",
                    "message": f"Created FLAT index for vector field in collection '{collection_name}'"
                })
            except Exception as flat_error:
                print(f"Failed to create FLAT index: {flat_error}")
                return jsonify({
                    "status": "error",
                    "message": f"Failed to create any index: {str(flat_error)}"
                }), 500
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


@app.route('/recreate_collection', methods=['POST'])
def recreate_collection():
    """Recreate collection to fix schema issues"""
    client = None
    try:
        client = get_milvus_client()
        
        collection_name = "testcase_collection"
        
        if client.has_collection(collection_name):
            client.drop_collection(collection_name)
            print(f"Dropped existing collection '{collection_name}'")
        
        from pymilvus import FieldSchema, CollectionSchema, DataType
        
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, is_primary=True, max_length=65535),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=384),
            FieldSchema(name="name", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="level", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="prepare_condition", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="test_steps", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="expected_result", dtype=DataType.VARCHAR, max_length=65535),
            FieldSchema(name="combined_text", dtype=DataType.VARCHAR, max_length=65535)
        ]
        
        schema = CollectionSchema(fields, "Testcase collection")
        client.create_collection(
            collection_name=collection_name,
            schema=schema
        )
        print(f"Recreated collection '{collection_name}' with correct schema")
        
        # Create index after collection creation
        try:
            # Use IndexParams for proper parameter formatting
            index_params = IndexParams()
            index_params.add_index(
                field_name="vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )
            client.create_index(
                collection_name=collection_name,
                index_params=index_params
            )
            print(f"Created AUTOINDEX for vector field")
        except Exception as index_error:
            print(f"Warning: Failed to create AUTOINDEX: {index_error}")
            try:
                index_params = IndexParams()
                index_params.add_index(
                    field_name="vector",
                    index_type="FLAT",
                    metric_type="COSINE"
                )
                client.create_index(
                    collection_name=collection_name,
                    index_params=index_params
                )
                print(f"Created FLAT index for vector field")
            except Exception as flat_error:
                print(f"Error: Failed to create FLAT index: {flat_error}")
        
        return jsonify({
            "status": "success",
            "message": f"Collection '{collection_name}' recreated successfully with string ID support and index"
        })
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass


if __name__ == '__main__':
    print("Starting Milvus Lite Testcase Service...")
    print("Available endpoints:")
    print("  POST /insert_testcase - Insert single/multiple testcases")
    print("  POST /batch_insert_testcases - Batch insert testcases")
    print("  POST /search_testcase - Search testcases with natural language")
    print("  POST /delete_testcase - Delete testcases by IDs")
    print("  GET /status - Get collection status")
    print("  GET /health - Health check")
    print("  POST /clear_collection - Clear all data in collection")
    print("  POST /create_index - Create index for existing collection")
    print("  POST /recreate_collection - Recreate collection with correct schema")
    
    try:
        print("Loading local embedding model...")
        get_embedding_model()
        print("Local embedding model loaded successfully!")
    except Exception as e:
        print(f"Failed to load local embedding model: {e}")
        print("Please ensure model files are in ./model/all-MiniLM-L6-v2/ directory")
    
    app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
