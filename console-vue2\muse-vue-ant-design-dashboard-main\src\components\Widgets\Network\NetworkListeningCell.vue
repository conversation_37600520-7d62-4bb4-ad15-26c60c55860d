<template>
  <div>
    <div v-if="listeningPorts.length === 0">No listening ports</div>
    <div v-else style="font-size: 12px;">
      <div v-if="listeningPorts.length > 0" style="padding: 2px 0;">
        <div>
          <span class="proto-text">{{ listeningPorts[0].proto }}</span>
          <span> {{ listeningPorts[0].local_address }}</span>
          <span class="path-arrow"> → </span>
          <span>{{ listeningPorts[0].foreign_address }}</span>
          <span class="program-text"> ({{ listeningPorts[0].pid_program }})</span>
        </div>
        <div v-if="listeningPorts.length > 1" style="margin-top: 4px;">
          <a-button
            type="link"
            size="small"
            @click.stop="showDetails"
            style="padding-left: 0;"
          >
            +{{ listeningPorts.length - 1 }} more...
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NetworkListeningCell',
  props: {
    record: {
      type: Object,
      required: true
    },
    parseJsonField: {
      type: Function,
      default: (field, defaultValue) => {
        try {
          if (Array.isArray(field)) {
            return field;
          }
          if (typeof field === 'object' && field !== null) {
            return field;
          }
          return typeof field === 'string' ? JSON.parse(field) : defaultValue;
        } catch (e) {
          console.warn('Failed to parse JSON field:', e);
          return defaultValue;
        }
      }
    }
  },
  computed: {
    listeningPorts() {
      try {
        const exposedServices = typeof this.record.exposures === 'string'
          ? JSON.parse(this.record.exposures)
          : this.record.exposures;
        return exposedServices?.listening_ports || [];
      } catch (e) {
        console.error('Failed to parse exposed services:', e);
        return [];
      }
    }
  },
  methods: {
    showDetails(e) {
      // 阻止事件冒泡
      if (e) {
        e.stopPropagation();
      }

      // 同时支持kebab-case和camelCase的事件名
      this.$emit('show-details', this.record);
      this.$emit('showDetails', this.record);
    }
  }
}
</script>

<style scoped lang="scss">
.proto-text {
  color: var(--primary-color, #1890ff);
}

.path-arrow {
  color: var(--disabled-color, #999);
}

.program-text {
  color: var(--success-color, #52c41a);
}
</style>
