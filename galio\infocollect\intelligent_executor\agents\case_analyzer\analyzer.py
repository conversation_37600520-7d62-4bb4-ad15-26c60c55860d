"""
用例分析器 - 基于LangChain的智能用例分析组件
"""

import asyncio
from typing import Dict, List, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI

from infocollect.log.logger import log_info, log_error, log_debug


class CaseAnalyzer:
    """
    用例分析器
    
    主要功能:
    1. 分析用例信息和特征
    2. 生成执行步骤
    3. 评估用例复杂度
    4. 提供执行建议
    """
    
    def __init__(self, llm: Optional[ChatOpenAI] = None):
        """
        初始化用例分析器
        
        Args:
            llm: LangChain LLM实例
        """
        self.llm = llm or ChatOpenAI(temperature=0.1)
        self.json_parser = JsonOutputParser()
        
        # 分析提示词模板
        self.analysis_prompt = PromptTemplate(
            input_variables=["case_info"],
            template="""
你是一个专业的用例分析专家。请分析以下用例信息，并提供详细的分析结果。

用例信息:
{case_info}

请按照以下JSON格式返回分析结果:
{{
    "case_type": "用例类型（如：功能测试、性能测试、安全测试等）",
    "complexity_level": "复杂度等级（简单/中等/复杂）",
    "key_features": ["关键特征1", "关键特征2", "..."],
    "execution_steps": [
        {{
            "step_number": 1,
            "description": "步骤描述",
            "command": "具体命令或操作",
            "expected_result": "预期结果"
        }}
    ],
    "prerequisites": ["前置条件1", "前置条件2", "..."],
    "risk_assessment": {{
        "risk_level": "风险等级（低/中/高）",
        "potential_risks": ["潜在风险1", "潜在风险2", "..."]
    }},
    "execution_time_estimate": "预估执行时间（分钟）",
    "recommendations": ["建议1", "建议2", "..."]
}}

请确保返回有效的JSON格式。
            """
        )
        
        # 相似性分析提示词模板
        self.similarity_prompt = PromptTemplate(
            input_variables=["case1", "case2"],
            template="""
请分析以下两个用例的相似性：

用例1:
{case1}

用例2:
{case2}

请按照以下JSON格式返回相似性分析结果:
{{
    "similarity_score": 0.85,
    "similar_aspects": ["相似点1", "相似点2", "..."],
    "different_aspects": ["差异点1", "差异点2", "..."],
    "recommendation": "基于相似性的执行建议"
}}
            """
        )
        
        log_info("CaseAnalyzer initialized")

    async def analyze_case(self, case_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个用例
        
        Args:
            case_info: 用例信息字典
            
        Returns:
            分析结果字典
        """
        try:
            log_debug(f"Analyzing case: {case_info.get('name', 'Unknown')}")
            
            # 构建分析链
            analysis_chain = self.analysis_prompt | self.llm | self.json_parser
            
            # 执行分析
            result = await analysis_chain.ainvoke({"case_info": str(case_info)})
            
            # 添加元数据
            result["analysis_timestamp"] = asyncio.get_event_loop().time()
            result["original_case_info"] = case_info
            
            log_info(f"Case analysis completed for: {case_info.get('name', 'Unknown')}")
            return result
            
        except Exception as e:
            log_error(f"Error analyzing case: {e}")
            return {
                "error": str(e),
                "case_type": "unknown",
                "complexity_level": "unknown",
                "execution_steps": [],
                "recommendations": ["分析失败，请检查用例信息格式"]
            }

    async def analyze_multiple_cases(self, cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量分析多个用例
        
        Args:
            cases: 用例信息列表
            
        Returns:
            分析结果列表
        """
        try:
            log_info(f"Starting batch analysis for {len(cases)} cases")
            
            # 并发分析所有用例
            tasks = [self.analyze_case(case) for case in cases]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    log_error(f"Error analyzing case {i}: {result}")
                    processed_results.append({
                        "error": str(result),
                        "case_index": i,
                        "recommendations": ["分析失败"]
                    })
                else:
                    processed_results.append(result)
            
            log_info(f"Batch analysis completed: {len(processed_results)} results")
            return processed_results
            
        except Exception as e:
            log_error(f"Error in batch analysis: {e}")
            return []

    async def compare_cases(self, case1: Dict[str, Any], case2: Dict[str, Any]) -> Dict[str, Any]:
        """
        比较两个用例的相似性
        
        Args:
            case1: 第一个用例
            case2: 第二个用例
            
        Returns:
            相似性分析结果
        """
        try:
            log_debug("Comparing two cases for similarity")
            
            # 构建相似性分析链
            similarity_chain = self.similarity_prompt | self.llm | self.json_parser
            
            # 执行比较
            result = await similarity_chain.ainvoke({
                "case1": str(case1),
                "case2": str(case2)
            })
            
            log_info("Case similarity analysis completed")
            return result
            
        except Exception as e:
            log_error(f"Error comparing cases: {e}")
            return {
                "similarity_score": 0.0,
                "similar_aspects": [],
                "different_aspects": ["比较失败"],
                "recommendation": "无法进行相似性分析"
            }

    async def extract_key_features(self, case_info: Dict[str, Any]) -> List[str]:
        """
        提取用例的关键特征
        
        Args:
            case_info: 用例信息
            
        Returns:
            关键特征列表
        """
        try:
            analysis_result = await self.analyze_case(case_info)
            return analysis_result.get("key_features", [])
        except Exception as e:
            log_error(f"Error extracting key features: {e}")
            return []

    async def estimate_execution_complexity(self, case_info: Dict[str, Any]) -> str:
        """
        评估用例执行复杂度
        
        Args:
            case_info: 用例信息
            
        Returns:
            复杂度等级（简单/中等/复杂）
        """
        try:
            analysis_result = await self.analyze_case(case_info)
            return analysis_result.get("complexity_level", "unknown")
        except Exception as e:
            log_error(f"Error estimating complexity: {e}")
            return "unknown"

    def get_analysis_statistics(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取分析结果的统计信息
        
        Args:
            analysis_results: 分析结果列表
            
        Returns:
            统计信息字典
        """
        if not analysis_results:
            return {"total_cases": 0}
        
        # 统计用例类型
        case_types = {}
        complexity_levels = {}
        total_steps = 0
        
        for result in analysis_results:
            if "error" in result:
                continue
                
            case_type = result.get("case_type", "unknown")
            case_types[case_type] = case_types.get(case_type, 0) + 1
            
            complexity = result.get("complexity_level", "unknown")
            complexity_levels[complexity] = complexity_levels.get(complexity, 0) + 1
            
            total_steps += len(result.get("execution_steps", []))
        
        return {
            "total_cases": len(analysis_results),
            "case_type_distribution": case_types,
            "complexity_distribution": complexity_levels,
            "average_steps_per_case": total_steps / len(analysis_results) if analysis_results else 0,
            "total_execution_steps": total_steps
        } 