// 导入所有脚本文件
import { defaultScriptContent } from './default_command';
import { defaultScriptContent as spiderScriptContent } from './spider_command';

// 脚本映射表
export const scriptMap = {
  'default_command': {
    name: 'Default Command',
    content: defaultScriptContent
  },
  'spider_command': {
    name: 'Spider Command',
    content: spiderScriptContent
  }
};

// 获取所有脚本名称（通用）
export const getScriptNames = () => {
  return Object.keys(scriptMap).map(key => ({
    key,
    name: scriptMap[key].name
  }));
};

// 获取通用工具脚本名称
export const getGeneralScriptNames = () => {
  return [{
    key: 'default_command',
    name: scriptMap['default_command'].name
  }];
};

// 获取Spider工具脚本名称
export const getSpiderScriptNames = () => {
  return [{
    key: 'spider_command',
    name: scriptMap['spider_command'].name
  }];
};

// 根据key获取脚本内容
export const getScriptContent = (key) => {
  return scriptMap[key]?.content || defaultScriptContent;
};
