{"version": 3, "sources": ["webpack:///./src/views/Task.vue", "webpack:///./src/components/Cards/TaskPanel.vue", "webpack:///src/components/Cards/TaskPanel.vue", "webpack:///./src/components/Cards/TaskPanel.vue?ed94", "webpack:///./src/components/Cards/TaskPanel.vue?0f6b", "webpack:///src/views/Task.vue", "webpack:///./src/views/Task.vue?51bd", "webpack:///./src/views/Task.vue?14bf", "webpack:///./src/components/Cards/TaskPanel.vue?051d"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "borderBottom", "currentStepComputed", "scopedSlots", "_u", "key", "fn", "proxy", "getPlayIconTooltip", "class", "selectedIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "isProcessing", "style", "color", "on", "$event", "handleStart", "staticStyle", "$t", "currentProject", "onNodesSelected", "model", "value", "callback", "$$v", "expression", "handleProxyChange", "mixins", "NotificationMixin", "TaskPollingMixin", "components", "ProxySelector", "TaskProgressCard", "NodeSelector", "data", "currentStep", "computed", "mapState", "taskId", "get", "_this$activeTask", "activeTask", "task_id", "set", "$store", "dispatch", "created", "checkDatabaseStatus", "taskInfo", "localStorage", "getItem", "projectFile", "JSON", "parse", "checkActiveTask", "removeItem", "methods", "mapActions", "$notify", "error", "title", "message", "$router", "push", "ip", "warning", "notificationSent", "previousTaskInfo", "clearTaskNotificationMark", "e", "console", "axios", "post", "targets", "proxy_ip", "dbFile", "setItem", "stringify", "startPolling", "taskCompleted", "Error", "response", "nodes", "Object", "values", "allCompleted", "every", "node", "includes", "status", "activated", "watch", "handler", "newProject", "oldProject", "stopPolling", "immediate", "component", "TaskPanel"], "mappings": "yHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,cAAc,IAAI,IAAI,IAEtMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,gCAAgCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,YAAa,UAAY,CAAEC,aAAc,uBAAwB,CAACN,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,UAAU,CAACG,YAAY,aAAaD,MAAM,CAAC,QAAUJ,EAAIS,oBAAoB,KAAO,UAAU,CAACP,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,iBAAiBU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,cAAcU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAIe,qBAAqB,CAACb,EAAG,SAAS,CAACG,YAAY,YAAYW,MAAM,CAC3zB,UAAahB,EAAIiB,YAAcjB,EAAIkB,gBAAgBC,OAAS,IAAMnB,EAAIoB,aACtE,iBAAkBpB,EAAIiB,YAAcjB,EAAIkB,gBAAgBC,OAAS,IAAMnB,EAAIoB,cAC3EC,MAAO,CACPC,MAAQtB,EAAIiB,YAAcjB,EAAIkB,gBAAgBC,OAAS,IAAMnB,EAAIoB,aAC7D,UACA,WACHhB,MAAM,CAAC,KAAO,eAAemB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAIiB,YAAcjB,EAAIkB,gBAAgBC,OAAS,IAAMnB,EAAIoB,cAAgBpB,EAAIyB,mBAAmB,KAAKX,OAAM,QAAW,IAAI,GAAGZ,EAAG,SAAS,CAACwB,YAAY,CAAC,OAAS,YAAYtB,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAI2B,GAAG,2BAA2B,CAACzB,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeJ,EAAI4B,eAAe,SAAW5B,EAAIoB,cAAcG,GAAG,CAAC,MAAQvB,EAAI6B,iBAAiBC,MAAM,CAACC,MAAO/B,EAAIkB,gBAAiBc,SAAS,SAAUC,GAAMjC,EAAIkB,gBAAgBe,GAAKC,WAAW,sBAAsB,GAAGhC,EAAG,SAAS,CAACwB,YAAY,CAAC,gBAAgB,QAAQtB,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAI2B,GAAG,2BAA2B,CAACzB,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAWJ,EAAIoB,cAAcG,GAAG,CAAC,OAASvB,EAAImC,mBAAmBL,MAAM,CAACC,MAAO/B,EAAIiB,WAAYe,SAAS,SAAUC,GAAMjC,EAAIiB,WAAWgB,GAAKC,WAAW,iBAAiB,GAAGhC,EAAG,qBAAqB,CAACE,MAAM,CAAC,YAAY,OAAO,gBAAgBJ,EAAIoB,iBAAiB,IAE17Bd,EAAkB,G,0GCsEP,GACf8B,OAAA,CAAAC,OAAAC,QACAC,WAAA,CACAC,qBACAC,wBACAC,qBAEAC,OACA,OACAzB,gBAAA,GACAD,WAAA,KACA2B,YAAA,IAGAC,SAAA,IACAC,eAAA,gDAGAC,OAAA,CACAC,MAAA,IAAAC,EACA,eAAAA,EAAA,KAAAC,kBAAA,IAAAD,OAAA,EAAAA,EAAAE,SAEAC,IAAArB,GACA,KAAAsB,OAAAC,SAAA,aAAAvB,EAAA,CAAAoB,QAAApB,GAAA,QAIAtB,sBACA,YAAAW,aACA,EAEA,SAAAF,gBAAAC,QACA,EAEA,KAAAD,gBAAAC,OAAA,SAAAF,WACA,EAEA,GAEAF,qBACA,YAAAK,aACA,yBAEA,KAAAF,gBAAAC,OAGA,KAAAF,WAGA,6BAFA,2BAHA,8BAQAsC,UACA,SAAAC,sBACA,OAGA,MAAAC,EAAAC,aAAAC,QAAA,iBAAA/B,gBACA,GAAA6B,EAAA,CACA,kBAAAG,GAAAC,KAAAC,MAAAL,GACAG,IAAA,KAAAhC,eACA,KAAAmC,mBAGAL,aAAAM,WAAA,iBAAApC,gBACA8B,aAAAM,WAAA,sBAAApC,gBACA,KAAAyB,OAAAC,SAAA,sBAIAW,QAAA,IACAC,eAAA,qBACAV,sBACA,aAAA5B,iBACA,KAAAuC,QAAAC,MAAA,CACAC,MAAA,iBACAC,QAAA,iEAEA,KAAAC,QAAAC,KAAA,cACA,IAMA3C,gBAAAX,GACA,KAAAA,kBACA,KAAAA,gBAAAC,OACA,KAAAyB,YAAA,EAEA,KAAAA,YAAA,GAKAT,kBAAAsC,GACA,KAAAxD,WAAAwD,GAGA,oBACA,SAAAjB,sBAAA,OACA,SAAAtC,gBAAAC,SAAA,KAAAF,WAKA,YAJA,KAAAkD,QAAAO,QAAA,CACAL,MAAA,6BACAC,QAAA,4EAKA,KAAAlD,cAAA,EACA,KAAAuD,kBAAA,EAGA,MAAAC,EAAAlB,aAAAC,QAAA,iBAAA/B,gBACA,GAAAgD,EACA,IACA,aAAA7B,GAAAc,KAAAC,MAAAc,GACA7B,GACA,KAAA8B,0BAAA9B,EAAA,YAAAnB,gBAEA,MAAAkD,GACAC,QAAAX,MAAA,6CAAAU,GAIA,IACA,WAAAnC,SAAAqC,OAAAC,KAAA,qBACAC,QAAA,KAAAhE,gBACAiE,SAAA,KAAAlE,WACAmE,OAAA,KAAAxD,iBAGAe,KAAAQ,UACAO,aAAA2B,QAAA,iBAAAzD,eAAAiC,KAAAyB,UAAA,CACAvC,OAAAJ,EAAAQ,QACAS,YAAA,KAAAhC,kBAEA8B,aAAAM,WAAA,sBAAApC,gBAEA,KAAAmB,OAAAJ,EAAAQ,QACA,KAAAoC,aAAA5C,EAAAQ,QAAA,gBAEA,MAAAiB,GACAW,QAAAX,MAAA,uBAAAA,GACA,KAAAD,QAAAC,MAAA,CACAC,MAAA,oBACAC,QAAAF,EAAAE,SAAA,6BAEA,KAAAlD,cAAA,IAKA,wBACA,IACA,MAAAqC,EAAAC,aAAAC,QAAA,iBAAA/B,gBACA4D,EAAA9B,aAAAC,QAAA,sBAAA/B,gBAEA,GAAA6B,EAAA,CACA,aAAAV,EAAA,YAAAa,GAAAC,KAAAC,MAAAL,GAEA,GAAAG,IAAA,KAAAhC,eACA,UAAA6D,MAAA,qCAGA,MAAAC,QAAAV,OAAAhC,IAAA,aAAAD,GAEA,GAAA2C,EAAA/C,OACA,KAAAU,OAAAC,SAAA,aAAAoC,EAAA/C,MAEA+C,EAAA/C,KAAAgD,OAAA,CACA,MAAAA,EAAAC,OAAAC,OAAAH,EAAA/C,KAAAgD,OACAG,EAAAH,EAAAI,MAAAC,GACA,qBAAAC,SAAAD,EAAAE,SAGAJ,GAAAN,EAGAM,IACA,KAAA1E,cAAA,EACAsC,aAAA2B,QAAA,sBAAAzD,eAAA,UAJA,KAAAR,cAAA,EACA,KAAAmE,aAAAxC,EAAA,kBAQA,MAAAqB,GACAW,QAAAX,MAAA,8BAAAA,GACAV,aAAAM,WAAA,iBAAApC,gBACA8B,aAAAM,WAAA,sBAAApC,kBAMAuE,YAEA,KAAApC,oBAGAqC,MAAA,CAEAxE,eAAA,CACAyE,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAAlD,OAAAC,SAAA,mBACA,KAAAkD,cAEA,KAAAzC,oBAGA0C,WAAA,KCnSiW,I,wBCQ7VC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACfnE,WAAA,CACAoE,cCjB6U,ICOzU,EAAY,eACd,EACA5G,EACAO,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,W", "file": "static/js/chunk-129ecfa6.d536c141.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('TaskPanel')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full task-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: '8px 16px' },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }}},[_c('div',{staticClass:\"steps-container\"},[_c('a-steps',{staticClass:\"steps-flow\",attrs:{\"current\":_vm.currentStepComputed,\"size\":\"small\"}},[_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"apartment\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"global\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-tooltip',{attrs:{\"title\":_vm.getPlayIconTooltip}},[_c('a-icon',{staticClass:\"step-icon\",class:{\n                'clickable': _vm.selectedIp && _vm.selectedRowKeys.length > 0 && !_vm.isProcessing,\n                'ready-to-start': _vm.selectedIp && _vm.selectedRowKeys.length > 0 && !_vm.isProcessing\n              },style:({\n                color: (_vm.selectedIp && _vm.selectedRowKeys.length > 0 && !_vm.isProcessing)\n                  ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\n                  : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\n              }),attrs:{\"type\":\"play-circle\"},on:{\"click\":function($event){_vm.selectedIp && _vm.selectedRowKeys.length > 0 && !_vm.isProcessing && _vm.handleStart()}}})],1)]},proxy:true}])})],1)],1),_c('a-card',{staticStyle:{\"margin\":\"0 0 16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('node-selector',{attrs:{\"project-file\":_vm.currentProject,\"disabled\":_vm.isProcessing},on:{\"input\":_vm.onNodesSelected},model:{value:(_vm.selectedRowKeys),callback:function ($$v) {_vm.selectedRowKeys=$$v},expression:\"selectedRowKeys\"}})],1),_c('a-card',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.isProcessing},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedIp),callback:function ($$v) {_vm.selectedIp=$$v},expression:\"selectedIp\"}})],1),_c('task-progress-card',{attrs:{\"task-type\":'task',\"is-processing\":_vm.isProcessing}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full task-card\"\r\n    :bodyStyle=\"{ padding: '8px 16px' }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <!-- 流程图 -->\r\n    <div class=\"steps-container\">\r\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"apartment\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"global\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-tooltip :title=\"getPlayIconTooltip\">\r\n              <a-icon\r\n                type=\"play-circle\"\r\n                class=\"step-icon\"\r\n                :class=\"{\r\n                  'clickable': selectedIp && selectedRowKeys.length > 0 && !isProcessing,\r\n                  'ready-to-start': selectedIp && selectedRowKeys.length > 0 && !isProcessing\r\n                }\"\r\n                @click=\"selectedIp && selectedRowKeys.length > 0 && !isProcessing && handleStart()\"\r\n                :style=\"{\r\n                  color: (selectedIp && selectedRowKeys.length > 0 && !isProcessing)\r\n                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\r\n                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\r\n                }\"\r\n              />\r\n            </a-tooltip>\r\n          </template>\r\n        </a-step>\r\n      </a-steps>\r\n    </div>\r\n\r\n    <!-- 节点选择区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\r\n      <node-selector\r\n        v-model=\"selectedRowKeys\"\r\n        :project-file=\"currentProject\"\r\n        :disabled=\"isProcessing\"\r\n        @input=\"onNodesSelected\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 代理配置区域 -->\r\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\r\n      <proxy-selector\r\n        v-model=\"selectedIp\"\r\n        :disabled=\"isProcessing\"\r\n        @change=\"handleProxyChange\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 任务状态 -->\r\n    <task-progress-card :task-type=\"'task'\" :is-processing=\"isProcessing\" />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\r\nimport NodeSelector from '@/components/common/NodeSelector.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin, TaskPollingMixin],\r\n  components: {\r\n    ProxySelector,\r\n    TaskProgressCard,\r\n    NodeSelector\r\n  },\r\n  data() {\r\n    return {\r\n      selectedRowKeys: [],\r\n      selectedIp: null,\r\n      currentStep: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['activeTask', 'currentProject', 'sidebarColor']),\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n\r\n    taskId: {\r\n      get() {\r\n        return this.activeTask?.task_id;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateTask', value ? { task_id: value } : null);\r\n      }\r\n    },\r\n    // 任务进度相关计算属性已移至 TaskProgressCard 组件\r\n    currentStepComputed() {\r\n      if (this.isProcessing) {\r\n        return 1;  // 运行中时，只点亮前两个图标\r\n      }\r\n      if (this.selectedRowKeys.length === 0) {\r\n        return -1;  // 没有选择任何节点，所有图标不点亮\r\n      }\r\n      if (this.selectedRowKeys.length > 0 && !this.selectedIp) {\r\n        return 0;   // 选择了节点但未选择IP，点亮第一步图标和连接线\r\n      }\r\n      return 2;     // 选择了节点和IP，且未在运行时，点亮所有三个图标和连接线\r\n    },\r\n    getPlayIconTooltip() {\r\n      if (this.isProcessing) {\r\n        return 'Task is in progress...';\r\n      }\r\n      if (!this.selectedRowKeys.length) {\r\n        return 'Please select nodes first';\r\n      }\r\n      if (!this.selectedIp) {\r\n        return 'Please select a proxy IP';\r\n      }\r\n      return 'Click to start collection!'; // 当都选择完成时显示这个提示\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    // 只检查当前项目的活动任务\r\n    const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveTask();\r\n      } else {\r\n        // 清除任务信息如果属于不同项目\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    checkDatabaseStatus() {\r\n      if (!this.currentProject) {\r\n        this.$notify.error({\r\n          title: 'Database Error',\r\n          message: 'No project database selected. Please select a project first.'\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 处理节点选择变化\r\n    onNodesSelected(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n      if (this.selectedRowKeys.length) {\r\n        this.currentStep = 1;\r\n      } else {\r\n        this.currentStep = 0;\r\n      }\r\n    },\r\n\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      this.selectedIp = ip;\r\n    },\r\n\r\n    async handleStart() {\r\n      if (!this.checkDatabaseStatus()) return;\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: 'No Nodes or Proxy Selected',\r\n          message: 'Please select one or more nodes and a reachable IP to collect the data.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isProcessing = true;\r\n      this.notificationSent = false; // 重置通知标志位\r\n\r\n      // 清除之前的任务通知记录\r\n      const previousTaskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n      if (previousTaskInfo) {\r\n        try {\r\n          const { taskId } = JSON.parse(previousTaskInfo);\r\n          if (taskId) {\r\n            this.clearTaskNotificationMark(taskId, 'task', this.currentProject);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error clearing previous task notification:', e);\r\n        }\r\n      }\r\n\r\n      try {\r\n        const { data } = await axios.post('/api/task/collect', {\r\n          targets: this.selectedRowKeys,\r\n          proxy_ip: this.selectedIp,\r\n          dbFile: this.currentProject\r\n        });\r\n\r\n        if (data && data.task_id) {\r\n          localStorage.setItem(`taskInfo_${this.currentProject}`, JSON.stringify({\r\n            taskId: data.task_id,\r\n            projectFile: this.currentProject\r\n          }));\r\n          localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n\r\n          this.taskId = data.task_id;\r\n          this.startPolling(data.task_id, 'task', 'task');\r\n        }\r\n      } catch (error) {\r\n        console.error('Error starting task:', error);\r\n        this.$notify.error({\r\n          title: 'Task Start Failed',\r\n          message: error.message || 'Server connection error.',\r\n        });\r\n        this.isProcessing = false;\r\n      }\r\n    },\r\n\r\n    // 重写 checkActiveTask 方法，调用混入中的方法\r\n    async checkActiveTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`taskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/task/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, 'task', 'task');\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(`taskCompleted_${this.currentProject}`, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n\r\n\r\n    activated() {\r\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\r\n      this.checkActiveTask();\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听 currentProject 变化\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除之前项目的任务状态\r\n          this.$store.dispatch('updateTask', null);\r\n          this.stopPolling();\r\n          // 检查新项目的活动任务\r\n          this.checkActiveTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 基础卡片样式\r\n.task-card {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n}\r\n\r\n// 步骤容器\r\n.steps-container {\r\n  width: 50%;\r\n  margin: 0 auto 24px;\r\n  padding: 12px 0;\r\n}\r\n\r\n// 深度选择器样式集中管理\r\n::v-deep {\r\n  .ant-card {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    .ant-card-head {\r\n      background: #f0f2f5;\r\n    }\r\n  }\r\n\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n  .ant-table-tbody > tr:last-child > td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  .steps-flow {\r\n    .ant-steps-item {\r\n      &-process,\r\n      &-finish {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #3b4149 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      &-wait {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #d9d9d9 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n      }\r\n\r\n      &-icon {\r\n        width: 88px;\r\n        height: 88px;\r\n        line-height: 80px;\r\n        padding: 4px;\r\n        font-size: 40px;\r\n        border-width: 2px;\r\n        margin-top: -20px;\r\n        color: #3b4149;\r\n\r\n        .step-icon {\r\n          font-size: 40px;\r\n          color: #3b4149;\r\n        }\r\n      }\r\n\r\n      &-tail::after {\r\n        height: 2px;\r\n      }\r\n\r\n      &:last-child {\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n\r\n        &.ant-steps-item-process,\r\n        &.ant-steps-item-finish {\r\n          .step-icon {\r\n            color: #3b4149 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ready-to-start {\r\n    animation: pulse 1.2s infinite;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n      opacity: 0.8;\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .clickable {\r\n    cursor: pointer;\r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskPanel.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskPanel.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TaskPanel.vue?vue&type=template&id=c1256e74&scoped=true\"\nimport script from \"./TaskPanel.vue?vue&type=script&lang=js\"\nexport * from \"./TaskPanel.vue?vue&type=script&lang=js\"\nimport style0 from \"./TaskPanel.vue?vue&type=style&index=0&id=c1256e74&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c1256e74\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<TaskPanel></TaskPanel>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport TaskPanel from \"@/components/Cards/TaskPanel.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        TaskPanel,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Task.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Task.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Task.vue?vue&type=template&id=af09bef6\"\nimport script from \"./Task.vue?vue&type=script&lang=js\"\nexport * from \"./Task.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskPanel.vue?vue&type=style&index=0&id=c1256e74&prod&scoped=true&lang=scss\""], "sourceRoot": ""}