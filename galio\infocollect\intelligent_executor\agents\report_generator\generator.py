"""
报告生成器 - 负责生成智能执行的报告
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI

from infocollect.log.logger import log_info, log_error, log_debug


class ReportGenerator:
    """
    报告生成器
    
    主要功能:
    1. 执行报告生成
    2. 分析结果汇总
    3. 多格式输出
    4. 报告模板管理
    """
    
    def __init__(self, llm: Optional[ChatOpenAI] = None):
        """
        初始化报告生成器
        
        Args:
            llm: LangChain LLM实例
        """
        self.llm = llm or ChatOpenAI(temperature=0.1)
        self.str_parser = StrOutputParser()
        
        # 初始化报告模板
        self._init_templates()
        
        log_info("ReportGenerator initialized")

    def _init_templates(self):
        """初始化报告模板"""
        
        # 执行报告模板
        self.execution_report_template = PromptTemplate(
            input_variables=["session_data"],
            template="""
请根据以下会话信息生成详细的执行报告：

{session_data}

请生成一份专业的执行报告，包含以下部分：

# 智能执行报告

## 1. 执行概要
- 会话ID和时间
- 总体执行状态
- 关键指标统计

## 2. 执行结果分析
- 成功/失败统计
- 执行时间分析
- 性能指标

## 3. 建议和改进
- 执行优化建议
- 后续行动计划

请确保报告内容准确、专业、易于理解。
            """
        )

    async def generate_report(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成执行报告
        
        Args:
            session_data: 会话数据
            
        Returns:
            报告数据
        """
        try:
            log_info(f"Generating report for session: {session_data.get('session_id', 'Unknown')}")
            
            # 构建报告链
            report_chain = self.execution_report_template | self.llm | self.str_parser
            
            # 生成报告内容
            report_content = await report_chain.ainvoke({
                "session_data": json.dumps(session_data, indent=2, ensure_ascii=False)
            })
            
            # 构建报告数据
            report_data = {
                "success": True,
                "report_content": report_content,
                "metadata": {
                    "session_id": session_data.get("session_id"),
                    "generation_time": datetime.now().isoformat(),
                    "execution_duration": session_data.get("duration", 0)
                },
                "statistics": self._calculate_statistics(session_data)
            }
            
            log_info("Report generated successfully")
            return report_data
            
        except Exception as e:
            log_error(f"Error generating report: {e}")
            return {
                "success": False,
                "error": str(e),
                "report_content": "报告生成失败",
                "metadata": {
                    "generation_time": datetime.now().isoformat()
                }
            }

    def _calculate_statistics(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算统计信息"""
        try:
            return {
                "session_status": session_data.get("status", "unknown"),
                "total_duration": session_data.get("duration", 0),
                "start_time": session_data.get("start_time"),
                "end_time": session_data.get("end_time")
            }
        except Exception as e:
            log_error(f"Error calculating statistics: {e}")
            return {}

    def export_report(self, 
                     report_data: Dict[str, Any], 
                     format_type: str = "markdown",
                     file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        导出报告到文件
        
        Args:
            report_data: 报告数据
            format_type: 导出格式 (markdown/html/json)
            file_path: 文件路径
            
        Returns:
            导出结果
        """
        try:
            if not file_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                session_id = report_data.get("metadata", {}).get("session_id", "unknown")
                file_path = f"report_{session_id}_{timestamp}.{format_type}"
            
            content = report_data.get("report_content", "")
            
            if format_type == "markdown":
                self._export_markdown(content, file_path)
            elif format_type == "html":
                self._export_html(content, file_path)
            elif format_type == "json":
                self._export_json(report_data, file_path)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            log_info(f"Report exported to: {file_path}")
            return {
                "success": True,
                "file_path": file_path,
                "format": format_type
            }
            
        except Exception as e:
            log_error(f"Error exporting report: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _export_markdown(self, content: str, file_path: str):
        """导出Markdown格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def _export_html(self, content: str, file_path: str):
        """导出HTML格式"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>智能执行报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1, h2, h3 {{ color: #333; }}
        pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div id="content">
        {content.replace(chr(10), '<br>')}
    </div>
</body>
</html>
        """
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def _export_json(self, report_data: Dict[str, Any], file_path: str):
        """导出JSON格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False) 