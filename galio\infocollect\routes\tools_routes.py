from flask import Blueprint, request, jsonify
from services.tools_service import ScriptService, active_script_tasks
from routes.database_wraps import with_database_session
import os
from werkzeug.utils import secure_filename
from log.logger import log_info, log_error

bp = Blueprint('script', __name__)


# 节点获取功能已移至 node_routes.py


@bp.route('/run', methods=['POST'])
@with_database_session
def run_script(db):
    """
    启动脚本执行任务
    期望的请求体格式：
    {
        "targets": ["ip1", "ip2"],
        "proxy_ip": "proxy_ip",
        "script_path": "/path/to/script",
        "result_path": "result.txt",
        "local_save_path": "/path/to/save",
        "script_content": "#!/bin/bash\necho 'Hello World'"  # 必需
    }
    """
    data = request.get_json()
    required_fields = [
        "targets", "proxy_ip", "script_path", "script_content",
        "result_path", "local_save_path"
    ]

    if not data or not all(field in data for field in required_fields):
        return jsonify({
            "error": f"Missing required fields. Required: {', '.join(required_fields)}"
        }), 400

    if not isinstance(data["targets"], list) or not data["targets"]:
        return jsonify({"error": "Targets must be a non-empty list"}), 400

    if not os.path.exists(data["script_path"]):
        return jsonify({"error": f"Script file or tool package not found: {data['script_path']}"}), 400


    if not os.path.exists(data["local_save_path"]):
        try:
            os.makedirs(data["local_save_path"])
        except Exception as e:
            return jsonify({
                "error": f"Failed to create local save directory: {str(e)}"
            }), 400

    script_service = ScriptService(db)
    try:
        task_id = script_service.start_script_task(
            data["targets"],
            data["proxy_ip"],
            data["script_path"],
            data["result_path"],
            data["local_save_path"],
            data["script_content"]
        )
        return jsonify({"task_id": task_id}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/<task_id>', methods=['GET'])
def get_task(task_id):
    """获取任务状态"""
    task = active_script_tasks.get(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404
    return jsonify(task), 200


@bp.route('/upload', methods=['POST'])
def upload_script():
    """上传脚本文件"""
    if 'script' not in request.files:
        # 检查是否使用了其他字段名
        if 'file' in request.files:
            file = request.files['file']
        else:
            return jsonify({"error": "No script file provided"}), 400
    else:
        file = request.files['script']

    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file:
        filename = secure_filename(file.filename)
        upload_path = os.path.join('cache', 'uploads', filename)

        try:
            os.makedirs(os.path.join('cache', 'uploads'), exist_ok=True)
            file.save(upload_path)
            response = {"path": upload_path}
            return jsonify(response), 200
        except Exception as e:
            return jsonify({"error": f"Failed to save file: {str(e)}"}), 500


@bp.route('/save_script', methods=['POST'])
def save_script():
    """保存脚本内容"""
    if 'script_content' not in request.files:
        return jsonify({"error": "No script content provided"}), 400

    file = request.files['script_content']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file:
        filename = secure_filename(file.filename)
        upload_path = os.path.join('cache', 'uploads', filename)
        os.makedirs(os.path.join('cache', 'uploads'), exist_ok=True)
        file.save(upload_path)
        return jsonify({"path": upload_path}), 200


@bp.route('/run_spider', methods=['POST'])
@with_database_session
def run_spider(db):
    """
    启动Spider执行任务
    期望的请求体格式：
    {
        "targets": ["ip1", "ip2"],
        "proxy_ip": "proxy_ip",
        "script_content": "#!/bin/bash\necho 'Hello World'"  # 必需
    }
    """
    data = request.get_json()
    required_fields = ["targets", "proxy_ip", "script_content"]

    if not data or not all(field in data for field in required_fields):
        return jsonify({
            "error": f"Missing required fields. Required: {', '.join(required_fields)}"
        }), 400

    if not isinstance(data["targets"], list) or not data["targets"]:
        return jsonify({"error": "Targets must be a non-empty list"}), 400

    # 使用预设的Spider路径
    current_file_dir = os.path.dirname(os.path.abspath(__file__))

    # 获取infocollect目录（从routes目录上升一级）
    infocollect_dir = os.path.dirname(current_file_dir)

    spider_dir = os.path.join(infocollect_dir, 'cache', 'Tools', 'SEC-SPIDER')

    release_folder_path = os.path.join(spider_dir, 'release')

    # 如果release文件夹不存在，则检查release.zip是否存在并解压
    if not os.path.exists(release_folder_path):
        os.makedirs(release_folder_path, exist_ok=True)

        release_zip_path = os.path.join(spider_dir, 'release.zip')
        if os.path.exists(release_zip_path):
            # 解压release.zip到SEC-SPIDER目录
            import zipfile
            try:
                log_info(f"解压Spider工具包: {release_zip_path}")
                with zipfile.ZipFile(release_zip_path, 'r') as zip_ref:
                    zip_ref.extractall(spider_dir)
                log_info("Spider工具包解压完成")
            except Exception as e:
                log_error(f"解压Spider工具包失败: {str(e)}")
                return jsonify({"error": f"Failed to extract Spider package: {str(e)}"}), 500
        else:
            log_error(f"Spider工具包不存在: {release_zip_path}")
            return jsonify({"error": f"Spider package not found: {release_zip_path}"}), 400
    else:
        log_info(f"Spider工具已解压，使用现有文件夹: {release_folder_path}")

    # 更新spider包要注意，要手动把scan_tools.zip解压后，再统一打包，spider首次会自动解压后删除该包，导致二次运行报400
    spider_tool_path = os.path.join(spider_dir, 'release', 'ssp', 'sSpider', 'static_scan', 'scan_tools.zip')
    spider_save_path = os.path.join(spider_dir, 'release', 'ssp', 'sSpider', 'upload', 'task')
    spider_bat_path = os.path.join(spider_dir, 'release', 'ssp', 'start.bat')

    # 检查解压后的文件是否存在
    if not os.path.exists(spider_tool_path):
        return jsonify({
            "error": f"Spider tool package not found at: {spider_tool_path}"
        }), 400

    # 复制scan_tools.zip到uploads目录
    uploads_dir = os.path.join(infocollect_dir, 'cache', 'uploads')
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir, exist_ok=True)

    target_path = os.path.join(uploads_dir, 'scan_tools.zip')
    if not os.path.exists(target_path):
        import shutil
        shutil.copy2(spider_tool_path, target_path)

    # 确保保存目录存在
    if not os.path.exists(spider_save_path):
        try:
            os.makedirs(spider_save_path, exist_ok=True)
        except Exception as e:
            log_error("Failed to create save directory: {str(e)}")
            return jsonify({
                "error": f"Failed to create save directory: {str(e)}"
            }), 400

    # 检查批处理脚本是否存在
    if not os.path.exists(spider_bat_path):
        return jsonify({
            "error": f"Spider batch script not found at: {spider_bat_path}"
        }), 400

    # 修改脚本内容，替换必要的变量
    script_content = data["script_content"]

    # 创建任务
    script_service = ScriptService(db)
    try:
        task_id = script_service.start_script_task(
            data["targets"],
            data["proxy_ip"],
            spider_tool_path,
            "result.txt",
            spider_save_path,
            script_content
        )

        try:
            script_dir = os.path.dirname(spider_bat_path)
            script_name = os.path.basename(spider_bat_path)

            cmd = f'cd /d "{script_dir}" && start cmd /c "{script_name}"'
            os.system(cmd)

            log_info(f"Spider批处理脚本已启动")
        except Exception as e:
            log_error(f"启动Spider批处理脚本失败: {str(e)}")

        return jsonify({"task_id": task_id}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
