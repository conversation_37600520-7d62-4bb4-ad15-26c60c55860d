from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from sqlalchemy.orm.exc import NoResultFound


class HostConfigService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_all_hosts(self, detail: bool = True) -> List[Dict]:
        """获取所有主机配置"""
        hosts = self.db.query(HostConfig).order_by(HostConfig.id.asc()).all()
        host_list = []
        for h in hosts:
            host_data = {
                "id": h.id,
                "host_name": h.host_name,
                "ip": h.ip,
                "ssh_port": h.ssh_port,
                "login_user": h.login_user,
                "login_pwd": h.login_pwd,
                "switch_root_cmd": h.switch_root_cmd,
                "switch_root_pwd": h.switch_root_pwd,
            } if detail else {
                "id": h.id,
                "host_name": h.host_name,
                "ip": h.ip,
            }
            host_list.append(host_data)
        return host_list

    def save_hosts(self, hosts_data: List[Dict]) -> List[Dict]:
        """保存主机配置，如果IP存在则更新，否则新增"""
        result = []
        try:
            for host in hosts_data:
                # 过滤掉前端添加的非数据库字段
                filtered_host = {k: v for k, v in host.items()
                                 if k in ['host_name', 'ip', 'ssh_port', 'login_user', 
                                        'login_pwd', 'switch_root_cmd', 'switch_root_pwd']}

                # 使用 with_for_update() 来锁定查询，防止并发问题
                existing = self.db.query(HostConfig)\
                    .filter(HostConfig.ip == filtered_host['ip'])\
                    .with_for_update()\
                    .first()

                if existing:
                    # 如果 IP 存在，则更新现有记录的所有字段
                    for key, value in filtered_host.items():
                        setattr(existing, key, value)
                    host_record = existing
                else:
                    # 如果 IP 不存在，则创建新记录
                    host_record = HostConfig(**filtered_host)
                    self.db.add(host_record)

                # 立即执行flush以检测任何约束违反
                self.db.flush()
                
                # 添加到结果列表
                result.append({
                    "id": host_record.id,
                    "host_name": host_record.host_name,
                    "ip": host_record.ip,
                    "ssh_port": host_record.ssh_port,
                    "login_user": host_record.login_user,
                    "login_pwd": host_record.login_pwd,
                    "switch_root_cmd": host_record.switch_root_cmd,
                    "switch_root_pwd": host_record.switch_root_pwd,
                })

            # 如果所有操作都成功，提交事务
            self.db.commit()
            return result
        except Exception as e:
            # 发生错误时回滚事务
            self.db.rollback()
            # 重新抛出异常，但添加更多上下文信息
            raise type(e)(f"Error saving hosts: {str(e)}") from e

    def delete_host(self, host_id: int) -> None:
        """删除指定 ID 的主机配置"""
        try:
            host = self.db.query(HostConfig).get(host_id)
            if not host:
                raise NoResultFound(f"Host with id {host_id} not found")

            self.db.delete(host)
            self.db.commit()
        except NoResultFound as e:
            self.db.rollback()
            raise e
        except Exception as e:
            self.db.rollback()
            raise e

    def batch_delete_hosts(self, host_ids: List[int]) -> None:
        """批量删除主机配置"""
        try:
            self.db.query(HostConfig).filter(HostConfig.id.in_(host_ids)).delete(synchronize_session=False)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise e
