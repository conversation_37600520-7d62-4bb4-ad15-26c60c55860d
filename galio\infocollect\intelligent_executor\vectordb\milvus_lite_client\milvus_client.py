import requests
import json
import os
from typing import List, Dict, Optional, Any
import logging

logger = logging.getLogger(__name__)


class MilvusLiteClient:
    """Milvus Lite client for server communication"""
    
    def __init__(self, base_url: str = None):
        self.base_url = "http://172.22.30.65:5001"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Milvus service"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise Exception(f"Failed to {method} {endpoint}: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON response: {e}")
            raise Exception(f"Invalid JSON response from {endpoint}: {e}")
    
    def get(self, endpoint: str) -> Dict:
        """Send GET request"""
        return self._make_request('GET', endpoint)
    
    def post(self, endpoint: str, data: Dict = None) -> Dict:
        """Send POST request"""
        return self._make_request('POST', endpoint, data)
    
    def put(self, endpoint: str, data: Dict = None) -> Dict:
        """Send PUT request"""
        return self._make_request('PUT', endpoint, data)
    
    def delete(self, endpoint: str, data: Dict = None) -> Dict:
        """Send DELETE request"""
        return self._make_request('DELETE', endpoint, data)
    
    def close(self):
        """Close client connection"""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
