<template>
  <a-button
    icon="reload"
    @click="$emit('refresh')"
    :class="['refresh-button', `text-${sidebarColor}`]"
  >
    {{ text || $t('common.refresh') }}
  </a-button>
</template>

<script>
import { mapState } from 'vuex';

export default {
    computed: {
    ...mapState(['sidebarColor']),
  },
  name: 'RefreshButton',
  props: {
    text: {
      type: String,
      default: ''
    }
  },
}
</script>

<style scoped lang="scss">
</style>
