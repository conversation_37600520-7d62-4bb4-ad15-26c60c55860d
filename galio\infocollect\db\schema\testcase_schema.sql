-- 测试用例数据库表结构定义

-- 测试用例表
CREATE TABLE IF NOT EXISTS TestCases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Depth TEXT,
    Feature_Name TEXT,
    Feature_Number TEXT,
    isFeature BOOLEAN,
    Testcase_Name TEXT,
    Testcase_Number TEXT UNIQUE,  -- Testcase_Number 作为唯一索引
    Testcase_Stage TEXT,
    Testcase_AutoType TEXT,
    Testcase_TobeAutomated BOOLEAN,
    Testcase_Level TEXT,
    Testcase_PrepareCondition TEXT,
    Testcase_TestSteps TEXT,
    Testcase_ExpectedResult TEXT,
    Testcase_ScriptPath TEXT,
    Testcase_TestType TEXT,
    Testcase_EnvType TEXT,
    Testcase_Exeplatform TEXT,
    Testcase_MapRestrict TEXT,
    Testcase_TestcaseProject TEXT,
    Testcase_Tags TEXT,
    Testcase_Activity TEXT,
    Testcase_Remark TEXT,
    lastChangeTime TEXT,
    lastModified TEXT,
    executeLatestTime TEXT,
    Security_Test_Requirements TEXT,
    Security_Test_Rules TEXT,
    lastModifier TEXT,
    creationDate TEXT,
    keywords TEXT,
    drRelationID TEXT,
    market TEXT,
    testBaseNum TEXT,
    Testcase_NetworkScriptName TEXT,
    Testcase_Description TEXT,
    Testcase_NetworkProblemId TEXT,
    interfaceName TEXT,
    Testcase_Uri TEXT,
    Testcase_LastResult TEXT,
    detectType TEXT,
    AnalyseField TEXT,
    Reason_Of_Fail TEXT,
    executeParam TEXT,
    author TEXT,
    timecost REAL,
    designer TEXT,
    Last_Executor TEXT,
    SceneFlag TEXT,
    BaseFlag TEXT,
    CloudCarrier TEXT,
    TestFactorNumber TEXT,
    Testcase_TestActivity TEXT,
    customField1 TEXT,
    customField2 TEXT,
    customField3 TEXT,
    customField4 TEXT,
    customField5 TEXT,
    customField6 TEXT,
    customField7 TEXT,
    customField8 TEXT,
    testPatternNumber TEXT,
    HTSM TEXT,
    testCaseRelationID TEXT,
    is_directory BOOLEAN
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_testcase_number ON TestCases(Testcase_Number);
CREATE INDEX IF NOT EXISTS idx_testcase_name ON TestCases(Testcase_Name);
CREATE INDEX IF NOT EXISTS idx_testcase_tags ON TestCases(Testcase_Tags);
CREATE INDEX IF NOT EXISTS idx_custom_field1 ON TestCases(customField1);
CREATE INDEX IF NOT EXISTS idx_custom_field2 ON TestCases(customField2);
CREATE INDEX IF NOT EXISTS idx_custom_field3 ON TestCases(customField3);
