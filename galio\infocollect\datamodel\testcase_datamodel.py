from sqlalchemy import Column, Integer, String, Boolean, Float, Text
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class TestCase(Base):
    __tablename__ = 'TestCases'

    id = Column(Integer, primary_key=True, autoincrement=True)
    Depth = Column(String(255))
    Feature_Name = Column(String(255))
    Feature_Number = Column(String(50))
    isFeature = Column(Boolean, default=False)
    Testcase_Name = Column(String(255), index=True)
    Testcase_Number = Column(String(50), unique=True, index=True)
    Testcase_Stage = Column(String(50))
    Testcase_AutoType = Column(String(50))
    Testcase_TobeAutomated = Column(Boolean, default=False)
    Testcase_Level = Column(String(50))
    Testcase_PrepareCondition = Column(Text)
    Testcase_TestSteps = Column(Text)
    Testcase_ExpectedResult = Column(Text)
    Testcase_ScriptPath = Column(String(255))
    Testcase_TestType = Column(String(50))
    Testcase_EnvType = Column(String(50))
    Testcase_Exeplatform = Column(String(50))
    Testcase_MapRestrict = Column(String(255))
    Testcase_TestcaseProject = Column(String(255))
    Testcase_Tags = Column(String(255), index=True)
    Testcase_Activity = Column(String(255))
    Testcase_Remark = Column(Text)
    lastChangeTime = Column(String(50))
    lastModified = Column(String(50))
    executeLatestTime = Column(String(50))
    Security_Test_Requirements = Column(Text)
    Security_Test_Rules = Column(Text)
    lastModifier = Column(String(50))
    creationDate = Column(String(50))
    keywords = Column(String(255))
    drRelationID = Column(String(255))
    market = Column(String(50))
    testBaseNum = Column(String(50))
    Testcase_NetworkScriptName = Column(String(255))
    Testcase_Description = Column(Text)
    Testcase_NetworkProblemId = Column(String(50))
    interfaceName = Column(String(255))
    Testcase_Uri = Column(String(255))
    Testcase_LastResult = Column(String(50))
    detectType = Column(String(50))
    AnalyseField = Column(String(50))
    Reason_Of_Fail = Column(Text)
    executeParam = Column(Text)
    author = Column(String(50))
    timecost = Column(Float)
    designer = Column(String(50))
    Last_Executor = Column(String(50))
    SceneFlag = Column(String(50))
    BaseFlag = Column(String(50))
    CloudCarrier = Column(String(50))
    TestFactorNumber = Column(String(50))
    Testcase_TestActivity = Column(String(255))
    customField1 = Column(String(255), index=True)
    customField2 = Column(String(255), index=True)
    customField3 = Column(String(255), index=True)
    customField4 = Column(String(255))
    customField5 = Column(String(255))
    customField6 = Column(String(255))
    customField7 = Column(String(255))
    customField8 = Column(String(255))
    testPatternNumber = Column(String(50))
    HTSM = Column(String(255))
    testCaseRelationID = Column(String(255))
    is_directory = Column(Boolean, default=False)
    
    def to_dict(self):
        """转换为字典"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
    
    def __repr__(self):
        return f"TestCase(Testcase_Name={self.Testcase_Name}, Testcase_Number={self.Testcase_Number})"
