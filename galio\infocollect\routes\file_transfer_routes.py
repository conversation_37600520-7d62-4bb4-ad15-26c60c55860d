import base64
import json
import os
from pathlib import Path

from flask import Blueprint, request, jsonify, send_file
from services.upload_service import UploadService
from services.download_service import DownloadService
from log.logger import log_error
from db.init_db import get_db

bp = Blueprint('file_transfer', __name__)

UPLOAD_DIR = str(Path(__file__).parent.parent / 'cache')
DOWNLOAD_DIR = str(Path(__file__).parent.parent / 'cache' / 'download')

if not os.path.exists(UPLOAD_DIR):
    os.makedirs(UPLOAD_DIR)

if not os.path.exists(DOWNLOAD_DIR):
    os.makedirs(DOWNLOAD_DIR)

upload_tasks = {}


@bp.route('/upload/<encoded_info>', methods=['GET'])
def get_upload_file(encoded_info):
    try:
        decoded_info = base64.b64decode(encoded_info).decode()
        info = json.loads(decoded_info)

        # Get file path from the upload directory
        file_path = os.path.join(UPLOAD_DIR, info['file_name'])

        if not os.path.exists(file_path):
            log_error(f"File not found: {file_path}")
            return jsonify({"error": "File not found"}), 404

        # Send the file with proper mime type
        return send_file(
            file_path,
            as_attachment=True,
            download_name=info['file_name'],
            mimetype='application/octet-stream'
        )
    except Exception as e:
        log_error(f"Upload file retrieval error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@bp.route('/upload', methods=['POST'])
def upload_file():
    try:
        db_file = request.args.get('dbFile')
        if not db_file:
            return jsonify({"error": "Database file not specified"}), 400

        content_type = request.headers.get('Content-Type', '')
        if not content_type.startswith('multipart/form-data'):
            return jsonify({
                "error": "Content-Type must be multipart/form-data"
            }), 415

        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # 获取其他表单数据
        targets = request.form.get('targets')
        upload_dir = request.form.get('upload_dir', '/root/.test/Uploads')
        proxy_ip = request.form.get('proxyIp')

        if not targets or not proxy_ip:
            return jsonify({"error": "Missing required parameters"}), 400

        try:
            targets = json.loads(targets)
        except json.JSONDecodeError:
            return jsonify({"error": "Invalid targets format"}), 400

        if not isinstance(targets, list) or not targets:
            return jsonify({"error": "Targets must be a non-empty list"}), 400

        # 确保上传目录存在
        os.makedirs(UPLOAD_DIR, exist_ok=True)
        temp_path = os.path.join(UPLOAD_DIR, file.filename)
        file.save(temp_path)

        with get_db(db_file) as db:
            # 从数据库获取节点信息
            file_service = UploadService(db)
            file_service.db_file = db_file
            task_id = file_service.create_upload_task(
                temp_path,
                targets,
                upload_dir,
                proxy_ip
            )

            file_service.start_transfer(task_id)

            return jsonify({
                "task_id": task_id,
                "status": "started"
            }), 200

    except Exception as e:
        log_error(f"Upload error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@bp.route('/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Get the status of an upload task"""
    try:
        db_file = request.args.get('dbFile')
        if not db_file:
            return jsonify({"error": "Database file not specified"}), 400

        with get_db(db_file) as db:
            file_service = UploadService(db)
            file_service.db_file = db_file
            status = file_service.get_task_status(task_id)
            return jsonify(status), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# ============================================================================================================


@bp.route('/download/start', methods=['POST'])
def start_download():
    try:
        # Get database file from URL parameters
        db_file = request.args.get('dbFile')
        if not db_file:
            return jsonify({"error": "Database file not specified"}), 400

        data = request.get_json()
        if not data or 'nodes' not in data or 'proxyIp' not in data:
            return jsonify({"error": "Missing required parameters"}), 400

        nodes = data['nodes']
        proxy_ip = data['proxyIp']

        # Validate paths
        for node in nodes:
            if 'path' not in node or not node['path']:
                return jsonify({"error": f"Missing path for node {node.get('ip')}"}), 400

            if not node['path'].startswith('/'):
                return jsonify({"error": f"Path must be absolute for node {node['ip']}"}), 400

        with get_db(db_file) as db:
            download_service = DownloadService(db, DOWNLOAD_DIR)
            download_service.db_file = db_file  # Set database file path
            task_id = download_service.create_and_start_download(nodes, proxy_ip)

            return jsonify({
                "task_id": task_id,
                "status": "started"
            }), 200

    except Exception as e:
        log_error(f"Download error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@bp.route('/download/status/<task_id>', methods=['GET'])
def get_download_status(task_id):
    """获取下载任务状态"""
    try:
        db_file = request.args.get('dbFile')
        if not db_file:
            return jsonify({"error": "Database file not specified"}), 400

        with get_db(db_file) as db:
            download_service = DownloadService(db, DOWNLOAD_DIR)
            download_service.db_file = db_file
            status = download_service.get_task_status(task_id)
            return jsonify(status), 200
    except Exception as e:
        log_error(f"Error getting download status: {str(e)}")
        return jsonify({"error": str(e)}), 500


@bp.route('/download/<encoded_info>', methods=['GET'])
def download_file(encoded_info):
    try:
        decoded_info = base64.b64decode(encoded_info).decode()
        info = json.loads(decoded_info)

        # 修改为使用 DOWNLOAD_DIR
        file_path = os.path.join(DOWNLOAD_DIR, info['file_name'])

        if not os.path.exists(file_path):
            log_error(f"File not found: {file_path}")
            return jsonify({"error": "File not found"}), 404

        # Send the file with proper mime type
        return send_file(
            file_path,
            as_attachment=True,
            download_name=info['file_name'],
            mimetype='application/octet-stream'
        )
    except Exception as e:
        log_error(f"Download error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@bp.route('/download/<encoded_info>', methods=['POST'])
def receive_remote_file(encoded_info):
    """Handle file uploads from remote nodes during download process"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Decode the upload info
        decoded_info = json.loads(base64.b64decode(encoded_info).decode())
        local_filename = decoded_info['local_filename']

        # Create host-specific directory
        host_dir = Path(DOWNLOAD_DIR)
        host_dir.mkdir(parents=True, exist_ok=True)

        # Save the file directly to the host directory
        save_path = host_dir / local_filename
        file.save(str(save_path))

        return jsonify({"status": "success"}), 200

    except Exception as e:
        log_error(f"Remote file receive error: {str(e)}")
        return jsonify({"error": str(e)}), 500
