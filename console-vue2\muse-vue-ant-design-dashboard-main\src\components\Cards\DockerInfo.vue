<template>
  <a-card
    :bordered="false"
    class="header-solid h-full docker-card"
    :bodyStyle="{ padding: 0 }"
  >
    <!-- 引用JsonDetailModal组件 -->
    <JsonDetailModal ref="jsonDetailModal" />
    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="25" height="25"
                viewBox="0 0 24 24"
                :class="`text-${sidebarColor}`"
            >
              <path fill="currentColor" d="M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.docker') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchActiveTabData" />
        </div>
      </div>
    </template>
    <div class="container-runtime-tabs">
      <a-tabs v-model:activeKey="activeEngine">
        <a-tab-pane key="docker" tab="Docker">
          <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
            <a-tab-pane key="docker_host_config" tab="Host Config">
              <div v-if="activeTab === 'docker_host_config'" class="host-config-container">
                <a-row :gutter="[16, 16]">
                  <!-- Basic Information -->
                  <a-col :span="8">
                    <a-card title="Basic Information" :bordered="false">
                      <p class="info-item"><strong>Docker Version:</strong> {{ hostConfigData[0]?.docker_version }}</p>
                      <p class="info-item"><strong>Operating System:</strong> {{ getDaemonConfig('OperatingSystem') }}</p>
                      <p class="info-item"><strong>Architecture:</strong> {{ getDaemonConfig('Architecture') }}</p>
                      <p class="info-item"><strong>Kernel Version:</strong> {{ getDaemonConfig('KernelVersion') }}</p>
                    </a-card>
                  </a-col>

                  <!-- Resource Information -->
                  <a-col :span="8">
                    <a-card title="Resources" :bordered="false">
                      <p class="info-item"><strong>CPU Cores:</strong> {{ getDaemonConfig('NCPU') }}</p>
                      <p class="info-item"><strong>Total Memory:</strong> {{ formatBytes(getDaemonConfig('MemTotal')) }}</p>
                      <p class="info-item"><strong>Docker Root Dir:</strong> {{ getDaemonConfig('DockerRootDir') }}</p>
                    </a-card>
                  </a-col>

                  <!-- Security Settings -->
                  <a-col :span="8">
                    <a-card title="Security" :bordered="false">
                      <p class="info-item"><strong>User in Docker Group:</strong> {{ hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No' }}</p>
                      <p class="info-item"><strong>Root User:</strong> {{ hostConfigData[0]?.is_root_user ? 'Yes' : 'No' }}</p>
                      <p class="info-item"><strong>Security Options:</strong> {{ getDaemonConfig('SecurityOptions')?.join(', ') || 'None' }}</p>
                    </a-card>
                  </a-col>

                  <!-- Container Statistics -->
                  <a-col :span="8">
                    <a-card title="Container Statistics" :bordered="false">
                      <p class="info-item"><strong>Total Containers:</strong> {{ getDaemonConfig('Containers') }}</p>
                      <p class="info-item"><strong>Running:</strong> {{ getDaemonConfig('ContainersRunning') }}</p>
                      <p class="info-item"><strong>Stopped:</strong> {{ getDaemonConfig('ContainersStopped') }}</p>
                      <p class="info-item"><strong>Images:</strong> {{ getDaemonConfig('Images') }}</p>
                    </a-card>
                  </a-col>

                  <!-- Driver Information -->
                  <a-col :span="8">
                    <a-card title="Storage Driver" :bordered="false">
                      <p class="info-item"><strong>Driver:</strong> {{ getDaemonConfig('Driver') }}</p>
                      <p class="info-item"><strong>Logging Driver:</strong> {{ getDaemonConfig('LoggingDriver') }}</p>
                      <p class="info-item"><strong>Cgroup Driver:</strong> {{ getDaemonConfig('CgroupDriver') }}</p>
                    </a-card>
                  </a-col>

                  <!-- Registry Configuration -->
                  <a-col :span="8">
                    <a-card title="Registry Configuration" :bordered="false">
                      <p class="info-item"><strong>Index Server:</strong> {{ getDaemonConfig('IndexServerAddress') }}</p>
                      <p class="info-item"><strong>Registry Mirrors:</strong></p>
                      <ul>
                        <li v-for="mirror in getRegistryMirrors()" :key="mirror" class="info-item">{{ mirror }}</li>
                      </ul>
                    </a-card>
                  </a-col>
                </a-row>
              </div>
            </a-tab-pane>
            <a-tab-pane key="docker_network" tab="Networks">
              <a-table
                :columns="networkColumns"
                :data-source="networkData"
                :rowKey="(record) => record.network_id"
                v-if="activeTab === 'docker_network'"
                :loading="loadingNetworks"
                :pagination="pagination"
              >
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="docker_container" tab="Containers">
              <a-table
                :columns="containerColumns"
                :data-source="containerData"
                :scroll="{ x: 1500 }"
                :pagination="{ pageSize: 20 }"
                :row-key="record => record.container_id"
                v-if="activeTab === 'docker_container'"
                :loading="loadingContainers"
              >
                <template #mountsColumn="{ record }">
                  <div v-if="record">
                    <template v-if="record.mounts && record.mounts.length">
                      <div v-for="(mount, index) in record.mounts" :key="index">
                        {{ mount.Source }} → {{ mount.Destination }}
                      </div>
                    </template>
                    <span v-else>N/A</span>
                  </div>
                </template>
              </a-table>



              <network-listening-modal
                :visible="networkDetailsVisible"
                :network-data="selectedNetwork"
                @update:visible="networkDetailsVisible = $event"
                @close="handleNetworkDetailsClose"
              />
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
        <a-tab-pane key="crictl" tab="CRI">
          <crictl-info :node-ip="selectedNodeIp" />
        </a-tab-pane>
      </a-tabs>
    </div>

    <a-modal
      v-model:visible="mountDetailsVisible"
      title="Mount Details"
      width="800px"
      @cancel="handleMountDetailsClose"
    >
      <template v-slot:footer>
        <a-button @click="handleMountDetailsClose">Cancel</a-button>
      </template>
      <template v-if="selectedMountContainer">
        <div class="mounts-container">
          <div v-for="(mount, index) in getAllMounts()" :key="index" class="mount-item">
            <div class="mount-path">
              <span class="mount-source">{{ mount.Source }}</span>
              <span class="mount-arrow">→</span>
              <span class="mount-dest">{{ mount.Destination }}</span>
            </div>
            <div class="mount-details">
              <span v-if="mount.Mode" class="mount-tag">{{ mount.Mode }}</span>
              <span class="mount-tag">{{ mount.RW ? 'RW' : 'RO' }}</span>
              <span v-if="mount.Propagation" class="mount-tag">{{ mount.Propagation }}</span>
            </div>
          </div>
        </div>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="environmentDetailsVisible"
      title="Environment Variables"
      width="800px"
      @cancel="handleEnvironmentDetailsClose"
    >
      <template v-slot:footer>
        <a-button @click="handleEnvironmentDetailsClose">Close</a-button>
      </template>
      <template v-if="selectedEnvironment">
        <div class="env-container">
          <div v-for="(env, index) in getAllEnvironmentVars()" :key="index" class="env-item">
            {{ env }}
          </div>
        </div>
      </template>
    </a-modal>

    <process-table
      :visible="processDetailsVisible"
      :process-container="selectedProcessContainer"
      user-field="uid"
      :include-tty="true"
      @update:visible="processDetailsVisible = $event"
      @close="handleProcessDetailsClose"
    />

    <process-detail-modal
      :visible="processDetailInfoVisible"
      :process-info="selectedProcessInfo"
      user-field="uid"
      @update:visible="processDetailInfoVisible = $event"
      @close="handleProcessDetailInfoClose"
    />



  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';
import CrictlInfo from './CrictlInfo.vue';
import JsonDetailModal from '../Widgets/JsonDetailModal.vue';
import { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index';
import { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index';
import { MountCell } from '../Widgets/Mount/mount_index';
// 不再使用单独的环境变量组件
// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index';

export default {
  components: {
    RefreshButton,
    CrictlInfo,
    JsonDetailModal,
    ProcessDetailModal,
    ProcessTable,
    NetworkListeningModal,
    NetworkListeningCell,
    MountCell,
    // EnvironmentCell,
    // EnvironmentModal
  },
  name: 'DockerInfo',
  props: {
    // Using selectedNodeIp from Vuex store instead of prop
  },
  data() {
    return {
      activeTab: 'docker_host_config',
      hostConfigData: [],
      containerData: [],
      networkData: [],
      loadingHostConfig: false,
      loadingContainers: false,
      loadingNetworks: false,
      hostConfigColumns: [
        { title: 'Docker Version', dataIndex: 'docker_version', key: 'docker_version' },
        {
          title: 'Daemon Config',
          dataIndex: 'daemon_config',
          key: 'daemon_config',
        },
        {
          title: 'In Docker Group',
          dataIndex: 'user_in_docker_group',
          key: 'user_in_docker_group',
          render: (text) => (text ? 'Yes' : 'No'),
        },
        {
          title: 'Is Root',
          dataIndex: 'is_root_user',
          key: 'is_root_user',
          render: (text) => (text ? 'Yes' : 'No'),
        },
      ],
      containerColumns: [
        {
          title: 'Container ID',
          dataIndex: 'container_id',
          key: 'container_id',
          width: '150px',
          ellipsis: false,
        },
        {
          title: 'Name',
          dataIndex: 'inspect_data',
          key: 'name',
          width: '15%',
          ellipsis: false,
          customRender: (_, record) => {
            try {
              const inspectData = this.parseJsonField(record.inspect_data, {});
              return {
                children: inspectData.Name || 'N/A',
                props: {
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-word'
                  }
                }
              };
            } catch (e) {
              console.warn('Failed to render container name:', e);
              return 'N/A';
            }
          }
        },
        {
          title: 'Privileged',
          dataIndex: 'inspect_data',
          key: 'privileged',
          width: '100px',
          customRender: (_, record) => {
            try {
              const inspectData = this.parseJsonField(record.inspect_data, {});
              const privileged = inspectData.HostConfig?.Privileged;
              // 将undefined、null或非布尔值视为false
              return privileged === true ? 'Yes' : 'No';
            } catch (e) {
              console.warn('Failed to render privileged status:', e);
              return 'No'; // 出错时默认显示为No
            }
          }
        },
        {
          title: 'Environment',
          dataIndex: 'env',
          key: 'env',
          width: '200px',
          customRender: (_, record) => {
            try {
              const envVars = this.parseJsonField(record.env, []);
              if (!envVars.length) return 'No environment variables';

              return (
                <div style="font-size: 12px;">
                  {envVars.slice(0, 1).map((env, index) => (
                    <div key={index} style="padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {env.length > 200 ? env.substring(0, 200) + '...' : env}
                    </div>
                  ))}
                  {envVars.length > 1 && (
                    <a-button
                      type="link"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        this.showEnvironmentDetails(record);
                      }}
                    >
                      +{envVars.length - 1} more...
                    </a-button>
                  )}
                </div>
              );
            } catch (e) {
              console.warn('Failed to render env vars:', e);
              return 'None';
            }
          }
        },
        {
          title: 'Mounts',
          dataIndex: 'mounts',
          key: 'mounts',
          width: '25%',
          ellipsis: false,
          customRender: (_, record) => {
            return (
              <mount-cell
                record={record}
                parseJsonField={this.parseJsonField}
                onShow-details={this.showMountDetails}
              />
            );
          }
        },
        {
          title: 'Network Listening',
          dataIndex: 'exposures',
          key: 'exposures',
          width: '25%',
          ellipsis: false,
          customRender: (_, record) => {
            return (
              <network-listening-cell
                record={record}
                parseJsonField={this.parseJsonField}
                onShow-details={this.showNetworkDetails}
              />
            );
          }
        },
        {
          title: 'Processes',
          dataIndex: 'processes',
          key: 'processes',
          width: '120px',
          align: 'center',
          customRender: (_, record) => {
            try {
              const processData = this.parseJsonField(record.processes, {});
              if (!processData?.process_list?.length) return 'N/A';

              return (
                <a onClick={(e) => {
                  e.stopPropagation();
                  this.showProcessDetails(record);
                }}>
                  {processData.process_list.length} processes
                </a>
              );
            } catch (e) {
              console.warn('Failed to render processes:', e);
              return 'None';
            }
          }
        },
        {
          title: 'Inspect Data',
          dataIndex: 'inspect_data',
          key: 'inspect_data',
          width: '150px',
          align: 'center',
          customRender: (_, record) => (
            <span style="display: inline-block; line-height: 22px;">
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.showInspectDetails(record);
                }}
                style="color: #1890ff; cursor: pointer;"
              >
                View Inspect
              </a>
            </span>
          )
        }
      ],
      networkColumns: [
        {
          title: 'Name',
          dataIndex: 'name',
          key: 'name',
          width: '120px',
          customRender: (text) => text || 'None'
        },
        {
          title: 'Network ID',
          dataIndex: 'network_id',
          key: 'network_id',
          width: '180px',
          customRender: (text) => text || 'None'
        },
        {
          title: 'Driver',
          dataIndex: 'driver',
          key: 'driver',
          width: '100px',
          customRender: (text) => text || 'None'
        },
        {
          title: 'Scope',
          dataIndex: 'scope',
          key: 'scope',
          width: '100px',
          customRender: (text) => text || 'None'
        },
        {
          title: 'IPv6',
          dataIndex: 'ipv6',
          key: 'ipv6',
          width: '80px',
          customRender: (text) => {
            if (text === undefined || text === null) return 'None';
            return text ? 'Yes' : 'No';
          }
        },
        {
          title: 'Internal',
          dataIndex: 'internal',
          key: 'internal',
          width: '80px',
          customRender: (text) => {
            if (text === undefined || text === null) return 'None';
            return text ? 'Yes' : 'No';
          }
        },
        {
          title: 'Labels',
          dataIndex: 'labels',
          key: 'labels',
          width: '120px',
          customRender: (labels) => {
            if (!labels || Object.keys(labels).length === 0) return 'None';
            return Object.entries(labels).map(([key, value]) => `${key}=${value}`).join(', ');
          }
        },
        {
          title: 'Created',
          dataIndex: 'created_at',
          key: 'created_at',
          width: '160px',
          customRender: (text) => text || 'None'
        }
      ],
      pagination: {
        pageSize: 100,
      },
      resourceMapping: {
        'docker_host_config': 'get_docker_host_config',
        'docker_container': 'get_docker_containers',
        'docker_network': 'get_docker_networks',
      },



      networkDetailsVisible: false,
      selectedNetwork: null,
      mountDetailsVisible: false,
      selectedMountContainer: null,
      environmentDetailsVisible: false,
      selectedEnvironment: null,
      processDetailsVisible: false,
      selectedProcessContainer: null,
      processDetailInfoVisible: false,
      selectedProcessInfo: null,
      activeEngine: 'docker'
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp(newIp) {
      this.resetData();
      if (newIp) {
        this.fetchActiveTabData();
      }
    },
  },
  mounted() {
    if (this.selectedNodeIp) {
      this.fetchActiveTabData();
    }
  },
  methods: {
    handleTabChange(key) {
      this.activeTab = key;
      this.resetData();
      this.fetchActiveTabData();
    },
    async fetchActiveTabData() {
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        this.resetData();
        return;
      }
      const resourceType = this.activeTab;
      const dataName = this.camelCaseToDataName(resourceType);
      const loadingKey = `loading${this.capitalizeFirstLetter(dataName.replace('Data', ''))}`;

      this[loadingKey] = true;

      try {
        const response = await axios.get(`/api/docker/${resourceType}/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject
          }
        });
        const data = response.data;
        this[dataName] = resourceType === 'docker_host_config' && data ? [data] : data;
      } catch (error) {
        console.error(`Error fetching ${resourceType}:`, error);
        this[dataName] = [];
      } finally {
        this[loadingKey] = false;
      }
    },
    camelCaseToDataName(camelCase) {
      const withoutDocker = camelCase.replace('docker_', '');
      return withoutDocker.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Data';
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    resetData() {
      this.hostConfigData = [];
      this.containerData = [];
      this.networkData = [];
    },
    getDaemonConfig(key) {
      if (!this.hostConfigData[0]?.daemon_config) return null;
      return this.hostConfigData[0].daemon_config[key];
    },
    getRegistryMirrors() {
      const registryConfig = this.getDaemonConfig('RegistryConfig');
      return registryConfig?.Mirrors || [];
    },
    formatBytes(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    },
    parseJsonField(field, defaultValue) {
      try {
        if (Array.isArray(field)) {
          return this.cleanReactiveObject(field);
        }
        if (typeof field === 'object' && field !== null) {
          return this.cleanReactiveObject(field);
        }
        return typeof field === 'string' ? JSON.parse(field) : defaultValue;
      } catch (e) {
        console.warn('Failed to parse JSON field:', e);
        return defaultValue;
      }
    },

    showInspectDetails(container) {
      try {
        const inspectData = this.parseJsonField(container.inspect_data, {});
        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);
      } catch (e) {
        console.error('Failed to parse inspect data:', e);
        this.$message.error('Failed to parse inspect data');
      }
    },
    renderPreviewWithMore(items, renderItem, moreText, onClickMore) {
      if (!items || !items.length) return 'None';

      return (
        <div style="font-size: 12px;">
          {items.slice(0, 2).map((item, index) => renderItem(item, index))}
          {items.length > 2 && (
            <a-button
              type="link"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onClickMore();
              }}
            >
              +{items.length - 2} {moreText}
            </a-button>
          )}
        </div>
      );
    },
    cleanReactiveObject(obj) {
      if (!obj) return null;
      if (Array.isArray(obj)) {
        return obj.map(item => this.cleanReactiveObject(item));
      }
      if (typeof obj === 'object') {
        const cleaned = {};
        for (const key in obj) {
          if (!key.startsWith('_') && !key.startsWith('$') && key !== '__ob__') {
            cleaned[key] = this.cleanReactiveObject(obj[key]);
          }
        }
        return cleaned;
      }
      return obj;
    },
    getProcessList() {
      if (!this.selectedProcessContainer?.processes?.process_list) return [];
      return this.selectedProcessContainer.processes.process_list;
    },
    getNetworkListening() {
      if (!this.selectedContainer?.exposures) return [];
      const exposedServices = this.parseJsonField(this.selectedContainer.exposures, {});
      return exposedServices.listening_ports || [];
    },
    showNetworkDetails(container) {
      this.selectedNetwork = {
        ...container,
        exposures: this.parseJsonField(container.exposures, {})
      };
      this.networkDetailsVisible = true;
    },
    handleNetworkDetailsClose() {
      this.networkDetailsVisible = false;
      this.selectedNetwork = null;
    },

    showMountDetails(container) {
      this.selectedMountContainer = {
        ...container,
        mounts: this.parseJsonField(container.mounts, [])
      };
      this.mountDetailsVisible = true;
    },
    handleMountDetailsClose() {
      this.mountDetailsVisible = false;
      this.selectedMountContainer = null;
    },
    getAllMounts() {
      if (!this.selectedMountContainer?.mounts) return [];
      return this.selectedMountContainer.mounts;
    },
    showEnvironmentDetails(container) {
      this.selectedEnvironment = {
        ...container,
        env: this.parseJsonField(container.env, [])
      };
      this.environmentDetailsVisible = true;
    },
    handleEnvironmentDetailsClose() {
      this.environmentDetailsVisible = false;
      this.selectedEnvironment = null;
    },
    getAllEnvironmentVars() {
      if (!this.selectedEnvironment?.env) return [];
      return this.selectedEnvironment.env;
    },
    showProcessDetails(container) {
      this.selectedProcessContainer = {
        ...container,
        processes: this.parseJsonField(container.processes, {})
      };
      this.processDetailsVisible = true;
    },
    handleProcessDetailsClose() {
      this.processDetailsVisible = false;
      this.selectedProcessContainer = null;
    },
    showProcessDetailInfo(process) {
      this.selectedProcessInfo = process;
      this.processDetailInfoVisible = true;
    },
    handleProcessDetailInfoClose() {
      this.processDetailInfoVisible = false;
      this.selectedProcessInfo = null;
    },
  },
};
</script>

<style scoped lang="scss">
.docker-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.docker-title {
  color: #333;
  font-size: 16px;
}

.ant-table {
  border-radius: 0 0 8px 8px;
}

// 表格样式会从全局样式继承，不需要在这里硬编码

.host-config-container {
  padding: 24px;
}

.ant-card {
  height: 100%;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.mount-tag {
  background: var(--input-bg, #f5f5f5);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color, #666);
}

.source-path {
  color: var(--primary-color, #1890ff);
}

.path-arrow {
  color: var(--disabled-color, #999);
}

.dest-path {
  color: var(--success-color, #52c41a);
}

.proto-text {
  color: var(--primary-color, #1890ff);
}

.program-text {
  color: var(--success-color, #52c41a);
}

ul {
  padding-left: 20px;
  margin: 0;
}

li {
  word-break: break-all;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #fafafa;
  font-weight: bold;
}

.ant-table-cell {
  white-space: pre-line !important;
  vertical-align: top;
  padding: 8px;
}

pre {
  max-height: 150px;
  overflow-y: auto;
  background-color: transparent !important; /* 移除背景色 */
  padding: 8px;
  border-radius: 4px;
  margin: 4px 0;
}

/* 添加mount项的样式 */
.mount-item {
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.mount-item:last-child {
  border-bottom: none;
}

.mounts-container {
  max-height: 300px;
  overflow-y: auto;
}

.mount-item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.mount-path {
  margin-bottom: 4px;
}

.mount-source {
  color: #1890ff;
}

.mount-arrow {
  margin: 0 8px;
  color: #999;
}

.mount-dest {
  color: #52c41a;
}

.mount-details {
  display: flex;
  gap: 8px;
}

.mount-tag {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.ant-collapse-panel {
  margin-bottom: 8px;
}

.ant-tag {
  margin: 2px;
}

.ant-collapse-content {
  background: #fafafa;
}

.env-container {
  max-height: 400px;
  overflow-y: auto;
}

.env-item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  word-break: break-all;

  &:last-child {
    border-bottom: none;
  }
}

.ant-descriptions {
  .ant-descriptions-item-label {
    width: 180px;
    background-color: #fafafa;
    font-weight: 500;
  }

  .ant-descriptions-item-content {
    word-break: break-all;
  }
}

.details-container {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.details-row {
  display: flex;
  flex-direction: column;
}

.details-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.header-item {
  padding: 12px 16px;
  font-weight: 500;
  color: #262626;
  border-right: 1px solid #f0f0f0;
  text-align: center;

  &:last-child {
    border-right: none;
  }
}

.details-content {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.content-item {
  padding: 12px 16px;
  border-right: 1px solid #f0f0f0;
  text-align: center;
  word-break: break-word;

  &:last-child {
    border-right: none;
  }
}

.container-runtime-tabs {
  padding: 24px;
}

/* 确保标签紧跟在标题下方 */
:deep(.ant-tabs) {
  margin-top: 0;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

.card-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-wrapper {
  display: flex;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.inspect-data-pre, .detail-pre {
  max-height: 600px;
  overflow-y: auto;
  background-color: transparent !important; /* 移除背景色 */
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.detail-pre {
  max-height: 300px;
  margin: 0;
}

/* 确保Tab内容区域没有背景色 */
.ant-tabs-tabpane {
  background-color: transparent !important;
}



/* 信息项样式 */
.info-item {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f0f0f0;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  strong {
    margin-right: 8px;
    color: #555;
  }
}


</style>
