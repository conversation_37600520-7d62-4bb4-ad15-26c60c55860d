(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-99afb646"],{"0e13":function(t,e,s){},9283:function(t,e,s){"use strict";s("0e13")},f720:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",[e("a-row",{attrs:{type:"flex",gutter:24}},[e("a-col",{staticClass:"mb-24",attrs:{span:24}},[e("ToolsPanel")],1)],1)],1)},i=[],o=function(){var t=this,e=t._self._c;return e("a-card",{staticClass:"header-solid h-full task-card",attrs:{bordered:!1,bodyStyle:{padding:"8px 16px"},headStyle:{borderBottom:"1px solid #e8e8e8"}}},[e("div",{staticClass:"steps-container"},[e("a-steps",{staticClass:"steps-flow",attrs:{current:t.currentStepComputed,size:"small"}},[e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{staticClass:"step-icon",attrs:{type:"upload"}})]},proxy:!0}])}),e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{staticClass:"step-icon",attrs:{type:"apartment"}})]},proxy:!0}])}),e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-icon",{staticClass:"step-icon",attrs:{type:"global"}})]},proxy:!0}])}),e("a-step",{scopedSlots:t._u([{key:"icon",fn:function(){return[e("a-tooltip",{attrs:{title:t.getPlayIconTooltip}},[e("a-icon",{staticClass:"step-icon",class:{clickable:t.selectedRowKeys.length>0&&t.selectedIp&&!t.isProcessing&&t.toolConfigComplete,"ready-to-start":t.selectedRowKeys.length>0&&t.selectedIp&&!t.isProcessing&&t.toolConfigComplete},style:{color:t.selectedRowKeys.length>0&&t.selectedIp&&!t.isProcessing&&t.toolConfigComplete?"#3b4149":"#d9d9d9"},attrs:{type:"play-circle"},on:{click:function(e){t.selectedRowKeys.length>0&&t.selectedIp&&!t.isProcessing&&t.toolConfigComplete&&t.handleStart()}}})],1)]},proxy:!0}])})],1)],1),e("a-card",{staticStyle:{margin:"0 0 16px"},attrs:{size:"small",title:t.$t("tool.configureTool")}},[e("a-tabs",{attrs:{"default-active-key":"general"},on:{change:t.handleToolTabChange}},[e("a-tab-pane",{key:"general",attrs:{tab:t.$t("tool.generalTool")||"通用工具"}},[e("general-tool-tab",{ref:"generalToolTab",attrs:{"is-processing":t.isProcessing,"current-project":t.currentProject},on:{"tool-path-changed":t.onToolPathChanged,"script-saved":t.onGeneralScriptSaved}})],1),e("a-tab-pane",{key:"spider",attrs:{tab:t.$t("tool.spiderTool")||"Spider工具"}},[e("spider-tool-tab",{ref:"spiderToolTab",attrs:{"is-processing":t.isProcessing,"selected-row-keys":t.selectedRowKeys,"selected-ip":t.selectedIp,"current-project":t.currentProject},on:{"script-saved":t.onSpiderScriptSaved,"run-spider":t.onRunSpider}})],1)],1)],1),e("a-card",{staticStyle:{margin:"0 0 16px"},attrs:{size:"small",title:t.$t("common.configureNodes")}},[e("node-selector",{attrs:{"project-file":t.currentProject,disabled:t.isProcessing},on:{input:t.onNodesSelected},model:{value:t.selectedRowKeys,callback:function(e){t.selectedRowKeys=e},expression:"selectedRowKeys"}})],1),e("a-card",{staticStyle:{"margin-bottom":"16px"},attrs:{size:"small",title:t.$t("common.configureProxy")}},[e("proxy-selector",{attrs:{disabled:t.isProcessing},on:{change:t.handleProxyChange},model:{value:t.selectedIp,callback:function(e){t.selectedIp=e},expression:"selectedIp"}})],1),e("task-progress-card",{attrs:{"task-type":"tool","is-processing":t.isProcessing}})],1)},r=[],n=(s("0643"),s("76d6"),s("4e3e"),s("2f62")),l=s("fec3"),c=s("4f4d"),p=s("524b"),d=s("5a3b"),h=s("9304"),u=s("c9da"),m=function(){var t=this,e=t._self._c;return e("a-form",{attrs:{form:t.form,layout:"vertical"}},[e("a-form-item",{attrs:{label:t.$t("tool.uploadToolPackage")}},[e("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px","flex-wrap":"wrap"}},[e("div",{staticStyle:{flex:"1","min-width":"200px"}},[e("a-upload",{attrs:{name:"script","before-upload":t.beforeUpload,"show-upload-list":!0,disabled:t.isProcessing,"file-list":t.fileList,accept:".zip,application/zip,application/x-zip-compressed"},on:{change:t.handleUploadChange}},[e("a-button",{staticClass:"nav-style-button",attrs:{disabled:t.isProcessing}},[e("a-icon",{attrs:{type:"upload"}}),t._v(" "+t._s(t.$t("tool.selectToolPackage"))+" ")],1)],1),t.fileName?e("span",{staticStyle:{"margin-left":"10px",color:"#666","font-size":"12px"}},[t._v(" "+t._s(t.fileName)+" ")]):t._e()],1),e("div",{staticStyle:{flex:"1","min-width":"200px"}},[e("script-editor",{attrs:{"script-tabs":t.scriptTabs,"default-tab":t.activeScriptTab,disabled:t.isProcessing,filename:"script.sh","success-message":"Script saved successfully"},on:{"script-saved":t.onScriptSaved},model:{value:t.scriptContent,callback:function(e){t.scriptContent=e},expression:"scriptContent"}})],1)])]),e("a-form-item",{attrs:{label:t.$t("tool.localSaveDirectory")}},[e("a-input",{attrs:{placeholder:"e.g., results/tool_results",disabled:t.isProcessing},model:{value:t.localSavePath,callback:function(e){t.localSavePath=e},expression:"localSavePath"}})],1)],1)},f=[];s("a573");const g='#!/bin/bash\n\n# ----------------------- 第一步：上传并解压工具包 -----------------------\n#\n# 以下变量会自动替换，无需修改：\n# {work_dir}            - 任务工作目录，格式为 "/root/.test/script_task_{task_id}"\n# {proxy_ip}            - 服务器IP地址\n# {server_port}         - 服务器端口\n# {download_script_info} - 下载信息（自动编码）\n# {output_file_name}    - 上传的工具包名称\n#\ncd {work_dir}\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\nunzip -o {output_file_name}\n\n# ----------------------- 第二步：执行工具命令 --------------------------\n#\n# 以下为工具执行示例（以s-spider工具为例）\n# 请根据实际工具修改此部分命令\n#\n# 可用变量：无需修改\n# {node_ip}   - 目标节点IP\n# {node_name} - 目标节点名称\n#\ncd scan_tools\nchmod 777 run.sh\ndos2unix run.sh || true\nsleep 0.5\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\nsleep 0.5\n\n# ----------------------- 第三步：回传结果文件 --------------------------\n#\n# 重要说明：\n# 1. 所有工具运行结果必须打包为 {node_name}_{node_ip}.tar 格式\n# 2. {result_file} 变量会被自动替换为正确的结果文件名\n# 3. {upload_script_info} 为回传参数（已编码，无需修改）\n#\n# 如果您的工具生成了其他格式的文件，请使用以下命令将其转换：\n# tar -cf {result_file} your_result_files\n# 或\n# mv your_result_file.ext {result_file}\n#\ncurl -F "file=@{result_file}" "http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}"\n',S='#!/bin/bash\n# This script will be executed on the remote node\n\n# 以下示例以运行s-spider工具为例：\n\n# 第一步：上传并解压zip包，\ncd {work_dir}\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\nunzip -o scan_tools.zip\n\n# 第二步： 执行运行命令，按照实际工具执行步骤往下写即可\ncd scan_tools\nchmod 777 run.sh\ndos2unix run.sh || true\nsleep 0.5\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\nsleep 0.5\n\n# 第三步： 回传结果文件\n# 注意：所有工具运行结果必须打包为{node_name}_{node_ip}.tar格式\n# 如果您的工具生成了其他格式的文件，请将其打包为tar格式\n# 例如：tar -cf {node_name}_{node_ip}.tar your_result_files\n# 或者：mv your_result_file.ext {node_name}_{node_ip}.tar\n\n# 上传结果文件到服务器\ncurl -F "file=@{result_file}" "http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}"',_={default_command:{name:"Default Command",content:g},spider_command:{name:"Spider Command",content:S}},b=()=>[{key:"default_command",name:_["default_command"].name}],y=()=>[{key:"spider_command",name:_["spider_command"].name}],v=t=>{var e;return(null===(e=_[t])||void 0===e?void 0:e.content)||g};var T=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticStyle:{display:"flex","align-items":"center",gap:"10px"}},[e("a-button",{staticClass:"nav-style-button",attrs:{disabled:t.disabled},on:{click:t.showScriptModal}},[e("a-icon",{attrs:{type:"code"}}),t._v(" "+t._s(t.$t("tool.editShellScript"))+" ")],1),t.scriptContent?e("a-button",{staticClass:"nav-style-button",attrs:{disabled:t.disabled},on:{click:t.confirmScript}},[e("a-icon",{attrs:{type:"check-circle"}}),t._v(" "+t._s(t.$t("tool.confirmScript"))+" ")],1):t._e()],1),e("a-modal",{attrs:{title:t.$t("tool.editShellScript"),visible:t.scriptModalVisible,closable:!1,maskClosable:!1,keyboard:!1,width:"1200px",bodyStyle:{maxHeight:"90vh",overflow:"auto",padding:"20px"},afterVisibleChange:t.handleModalVisibleChange},on:{ok:t.handleScriptOk,cancel:t.handleScriptCancel}},[e("a-tabs",{staticStyle:{"margin-bottom":"16px"},on:{change:t.handleTabChange},model:{value:t.activeScriptTab,callback:function(e){t.activeScriptTab=e},expression:"activeScriptTab"}},t._l(t.scriptTabs,(function(t){return e("a-tab-pane",{key:t.key,attrs:{tab:t.name}})})),1),e("div",{staticStyle:{background:"#1e1e1e",padding:"16px","border-radius":"4px","min-height":"700px"}},[e("a-textarea",{staticStyle:{"font-family":"'Courier New', monospace",background:"#1e1e1e",color:"#d4d4d4",border:"1px solid #444","font-size":"14px",width:"100%",resize:"none"},attrs:{rows:30,placeholder:"#!/bin/bash","auto-size":{minRows:30,maxRows:40}},model:{value:t.scriptContentInternal,callback:function(e){t.scriptContentInternal=e},expression:"scriptContentInternal"}})],1)],1)],1)},P=[],C={name:"ScriptEditor",props:{value:{type:String,default:""},scriptType:{type:String,default:"general"},scriptTabs:{type:Array,default:()=>[]},defaultTab:{type:String,default:"default_command"},disabled:{type:Boolean,default:!1},filename:{type:String,default:"script.sh"},successMessage:{type:String,default:"Script saved successfully"}},data(){return{scriptModalVisible:!1,scriptContentInternal:this.value,activeScriptTab:this.defaultTab,scriptPath:""}},computed:{scriptContent:{get(){return this.scriptContentInternal},set(t){this.scriptContentInternal=t,this.$emit("input",t)}}},watch:{value(t){this.scriptContentInternal=t}},methods:{showScriptModal(){this.scriptContentInternal||this.loadScriptContent(this.activeScriptTab),this.scriptModalVisible=!0},loadScriptContent(t){this.activeScriptTab=t,this.scriptContent=v(t)},handleTabChange(t){this.loadScriptContent(t)},handleScriptOk(){this.scriptContent.trim()?(this.saveScriptToFile(),this.scriptModalVisible=!1):this.$message.warning("Script content cannot be empty")},handleModalVisibleChange(){},handleScriptCancel(){this.scriptModalVisible=!1},async confirmScript(){if(!this.scriptContent.trim())return void this.$message.warning("Script content cannot be empty");const t=await this.saveScriptToFile();t&&this.$message.success(this.successMessage)},async saveScriptToFile(){try{const t=new Blob([this.scriptContent],{type:"text/plain"}),e=new File([t],this.filename,{type:"text/plain"}),s=new FormData;s.append("script_content",e);const a=await l["a"].post("/api/script/save_script",s,{headers:{"Content-Type":"multipart/form-data"}});return a.data&&a.data.path?(this.scriptPath=a.data.path,this.$emit("script-saved",a.data.path),a.data.path):null}catch(e){var t;return console.error("Script save error:",e),this.$message.error("Failed to save script: "+((null===(t=e.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||e.message)),null}}}},k=C,w=s("2877"),x=Object(w["a"])(k,T,P,!1,null,null,null),I=x.exports,$={name:"GeneralToolTab",components:{ScriptEditor:I},props:{isProcessing:{type:Boolean,default:!1},currentProject:{type:String,required:!0}},data(){return{form:this.$form.createForm(this),uploadUrl:"/api/script/upload",toolPath:"",localSavePath:"eg : D:\\_Projects_python\\SEC-SPIDER\\release\\ssp\\sSpider\\upload\\task",fileList:[],fileName:"",scriptContent:"",scriptTabs:[],activeScriptTab:"default_command",scriptPath:""}},computed:{...Object(n["e"])(["sidebarColor"]),toolConfigComplete(){return this.toolPath&&this.scriptContent&&this.localSavePath}},created(){this.scriptTabs=b(),this.loadScriptContent(this.activeScriptTab)},methods:{loadScriptContent(t){this.activeScriptTab=t,this.scriptContent=v(t)},onScriptSaved(t){this.scriptPath=t,this.$emit("script-saved",t)},beforeUpload(t){const e="application/zip"===t.type||"application/x-zip-compressed"===t.type||t.name.endsWith(".zip");return e?(this.file=t,this.fileName=t.name,this.uploadFile(t),!1):(this.$message.error("You can only upload ZIP files!"),!1)},async uploadFile(t){const e=this.$message.loading("Uploading...",0);try{const s=new FormData;s.append("file",t);const a=await l["a"].post(this.uploadUrl,s,{headers:{"Content-Type":"multipart/form-data"}});if(e(),a.data&&a.data.path){this.toolPath=a.data.path,this.$emit("tool-path-changed",a.data.path),this.$message.success(t.name+" uploaded successfully");const e={uid:t.uid,name:t.name,status:"done",response:a.data};this.fileList=[e],this.$forceUpdate()}else this.$message.error(t.name+" upload response missing path field")}catch(a){var s;e(),console.error("Upload error:",a),this.$message.error(`${t.name} upload failed: ${(null===(s=a.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.error)||a.message}`)}},handleUploadChange(t){this.fileList=[...t.fileList],this.fileList=this.fileList.slice(-1);const e=t.file.status;"done"===e?t.file.response&&t.file.response.path?(this.toolPath=t.file.response.path,this.$emit("tool-path-changed",t.file.response.path),this.fileName=t.file.name,this.$message.success(t.file.name+" uploaded successfully"),this.$forceUpdate()):this.$message.error(t.file.name+" upload response missing path field"):"error"===e&&this.$message.error(t.file.name+" upload failed.")},getToolData(){return{toolPath:this.toolPath,scriptContent:this.scriptContent,scriptPath:this.scriptPath,localSavePath:this.localSavePath}}}},j=$,R=Object(w["a"])(j,m,f,!1,null,null,null),z=R.exports,K=function(){var t=this,e=t._self._c;return e("a-form",{attrs:{form:t.form,layout:"vertical"}},[e("a-form-item",{attrs:{label:t.$t("tool.editScript")}},[e("div",{staticStyle:{display:"flex","align-items":"center",gap:"12px","flex-wrap":"wrap"}},[e("div",{staticStyle:{flex:"1","min-width":"200px"}},[e("script-editor",{attrs:{"script-tabs":t.spiderScriptTabs,"default-tab":t.activeSpiderScriptTab,disabled:t.isProcessing,filename:"spider_script.sh","success-message":"Spider script saved successfully"},on:{"script-saved":t.onScriptSaved},model:{value:t.spiderScriptContent,callback:function(e){t.spiderScriptContent=e},expression:"spiderScriptContent"}})],1),e("div",{staticStyle:{flex:"0 0 auto","min-width":"100px"}},[e("a-button",{staticClass:"nav-style-button",staticStyle:{width:"100%"},attrs:{loading:t.isProcessing,disabled:t.isProcessing||!t.spiderScriptContent},on:{click:t.runSpider}},[e("a-icon",{attrs:{type:"play-circle"}}),t._v(" "+t._s(t.$t("tool.runSpider")||"运行Spider")+" ")],1)],1)])])],1)},F=[],N={name:"SpiderToolTab",components:{ScriptEditor:I},props:{isProcessing:{type:Boolean,default:!1},selectedRowKeys:{type:Array,required:!0},selectedIp:{type:String,default:null},currentProject:{type:String,required:!0}},data(){return{form:this.$form.createForm(this),spiderScriptContent:"",spiderScriptTabs:[],activeSpiderScriptTab:"spider_command",spiderToolPath:"infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\sSpider\\static_scan\\scan_tools.zip",spiderSavePath:"infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\sSpider\\upload\\task",spiderScriptPath:"infocollect\\cache\\Tools\\SEC-SPIDER\\release\\ssp\\start.bat"}},computed:{...Object(n["e"])(["sidebarColor"])},created(){this.spiderScriptTabs=y(),this.loadSpiderScriptContent(this.activeSpiderScriptTab)},methods:{loadSpiderScriptContent(t){this.activeSpiderScriptTab=t,this.spiderScriptContent=v(t)},onScriptSaved(t){this.spiderScriptPath=t,this.$emit("script-saved",t)},runSpider(){if(!this.selectedRowKeys.length||!this.selectedIp)return void this.$notify.warning({title:"未选择节点或代理",message:"请选择一个或多个节点和一个可用的代理IP来运行工具。"});const t={targets:this.selectedRowKeys,proxy_ip:this.selectedIp,script_content:this.spiderScriptContent,dbFile:this.currentProject};this.$emit("run-spider",t)},getToolData(){return{spiderScriptContent:this.spiderScriptContent,spiderScriptPath:this.spiderScriptPath,spiderToolPath:this.spiderToolPath,spiderSavePath:this.spiderSavePath}}}},O=N,E=Object(w["a"])(O,K,F,!1,null,null,null),D=E.exports,M={mixins:[c["a"],p["a"]],components:{ProxySelector:d["a"],TaskProgressCard:h["a"],NodeSelector:u["a"],GeneralToolTab:z,SpiderToolTab:D},data(){return{selectedRowKeys:[],selectedIp:null,currentStep:0,activeToolTab:"general",toolPath:"",scriptPath:"",scriptContent:"",localSavePath:"",spiderScriptPath:"",spiderScriptContent:""}},computed:{...Object(n["e"])(["activeToolTask","currentProject","sidebarColor"]),taskId:{get(){var t;return null===(t=this.activeToolTask)||void 0===t?void 0:t.task_id},set(t){this.$store.dispatch("updateToolTask",t?{task_id:t}:null)}},toolConfigComplete(){return"general"!==this.activeToolTab||this.toolPath&&this.scriptContent&&this.localSavePath},currentStepComputed(){return this.isProcessing?2:this.toolPath||"general"!==this.activeToolTab?!this.toolPath&&"spider"!==this.activeToolTab||0!==this.selectedRowKeys.length?(this.toolPath||"spider"===this.activeToolTab)&&this.selectedRowKeys.length>0&&!this.selectedIp?1:3:0:-1},getPlayIconTooltip(){return this.isProcessing?"Task is in progress...":"general"!==this.activeToolTab||this.toolPath?this.selectedRowKeys.length?this.selectedIp?"Click to start tool execution!":"Please select a proxy IP":"Please select nodes":"Please upload a tool package first"}},created(){const t=localStorage.getItem("toolTaskInfo_"+this.currentProject);if(t){const{projectFile:e}=JSON.parse(t);e===this.currentProject?this.checkActiveTask():(localStorage.removeItem("toolTaskInfo_"+this.currentProject),localStorage.removeItem("toolTaskCompleted_"+this.currentProject),this.$store.dispatch("updateToolTask",null))}},methods:{...Object(n["b"])(["addNotification"]),handleToolTabChange(t){this.activeToolTab=t},handleProxyChange(t){this.selectedIp=t},onNodesSelected(t){this.selectedRowKeys=t},onToolPathChanged(t){this.toolPath=t},onGeneralScriptSaved(t){if(this.scriptPath=t,this.$refs.generalToolTab){const t=this.$refs.generalToolTab.getToolData();this.scriptContent=t.scriptContent,this.localSavePath=t.localSavePath}},onSpiderScriptSaved(t){if(this.spiderScriptPath=t,this.$refs.spiderToolTab){const t=this.$refs.spiderToolTab.getToolData();this.spiderScriptContent=t.spiderScriptContent}},async onRunSpider(t){await this.startTaskGeneric(t,"run_spider","Spider任务已启动","Spider任务启动失败")},async handleStart(){if(!this.toolConfigComplete)return void this.$notify.warning({title:"Incomplete Configuration",message:"Please complete all tool configuration fields."});if("general"===this.activeToolTab&&!this.scriptContent)return void this.$notify.warning({title:"No Script",message:"Please create a shell script first."});const t={targets:this.selectedRowKeys,proxy_ip:this.selectedIp,script_path:this.toolPath,script_content:this.scriptContent,result_path:"result.txt",local_save_path:this.localSavePath,dbFile:this.currentProject},e=await this.startTaskGeneric(t,"run","通用工具任务已启动","通用工具任务启动失败");if(e)try{const t=await l["a"].get("/api/script/"+e);t.data&&(this.$store.dispatch("updateToolTask",t.data),this.$forceUpdate())}catch(s){console.error("获取初始任务状态失败:",s)}},async startTaskGeneric(t,e,s,a){if(!this.selectedRowKeys.length||!this.selectedIp)return this.$notify.warning({title:"未选择节点或代理",message:"请选择一个或多个节点和一个可用的代理IP来运行工具。"}),null;this.isProcessing=!0;const i=localStorage.getItem("toolTaskInfo_"+this.currentProject);if(i)try{const{taskId:t}=JSON.parse(i);t&&this.clearTaskNotificationMark(t,"tool",this.currentProject)}catch(o){console.error("Error clearing previous tool notification:",o)}try{const{data:a}=await l["a"].post("/api/script/"+e,t);if(a&&a.task_id){localStorage.setItem("toolTaskInfo_"+this.currentProject,JSON.stringify({taskId:a.task_id,projectFile:this.currentProject})),localStorage.removeItem("toolTaskCompleted_"+this.currentProject),this.taskId=a.task_id;const t={task_id:a.task_id,nodes:{}};this.selectedRowKeys.forEach(e=>{t.nodes[e]={ip:e,host_name:e,status:"processing",progress:0}}),this.$store.dispatch("updateToolTask",t),this.$nextTick(()=>{const t=document.querySelector(".ant-progress");t&&t.scrollIntoView({behavior:"smooth",block:"center"})});try{const t=await l["a"].get("/api/script/"+a.task_id);t.data&&this.$store.dispatch("updateToolTask",t.data)}catch(r){console.error("初始轮询错误:",r)}finally{this.startPolling(a.task_id,"tool","script")}return this.$message.success(s||"任务已启动"),this.$forceUpdate(),a.task_id}return null}catch(n){console.error(`启动${e}任务出错:`,n);let t="服务器连接错误。";return n.message?t=n.message:n.response&&n.response.data&&n.response.data.error&&(t=n.response.data.error),this.$notify.error({title:a||"任务启动失败",message:t}),this.isProcessing=!1,null}},async checkActiveTask(){try{const t=localStorage.getItem("toolTaskInfo_"+this.currentProject),e=localStorage.getItem("toolTaskCompleted_"+this.currentProject);if(t){const{taskId:s,projectFile:a}=JSON.parse(t);if(a!==this.currentProject)throw new Error("Task belongs to different project");const i=await l["a"].get("/api/script/"+s);if(i.data&&(this.$store.dispatch("updateToolTask",i.data),i.data.nodes)){const t=Object.values(i.data.nodes),a=t.every(t=>["success","failed"].includes(t.status));a||e?a&&(this.isProcessing=!1,localStorage.setItem("toolTaskCompleted_"+this.currentProject,"true")):(this.isProcessing=!0,this.startPolling(s,"tool","script"))}}}catch(t){console.error("Error checking active task:",t),localStorage.removeItem("toolTaskInfo_"+this.currentProject),localStorage.removeItem("toolTaskCompleted_"+this.currentProject)}},startPollingWrapper(t){this.startPolling(t,"tool","script")},activated(){this.checkActiveTask()}},watch:{currentProject:{handler(t,e){t!==e&&(this.$store.dispatch("updateToolTask",null),this.stopPolling(),this.checkActiveTask())},immediate:!0}}},U=M,V=(s("9283"),Object(w["a"])(U,o,r,!1,null,"02b63088",null)),G=V.exports,L={components:{ToolsPanel:G}},A=L,J=Object(w["a"])(A,a,i,!1,null,null,null);e["default"]=J.exports}}]);
//# sourceMappingURL=chunk-99afb646.b2e8f92c.js.map