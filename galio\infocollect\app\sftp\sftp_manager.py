from contextlib import contextmanager
from log.logger import log_error
from app.sftp.sftp_client import SFTPClient
import os


class SFTPManager:
    def __init__(self, node):
        self.node = node

    @contextmanager
    def connection(self):
        """SFTP连接的上下文管理器"""
        client = None
        try:
            client = SFTPClient(self.node)
            client.connect()
            yield client
        except Exception as e:
            log_error(f"SFTP connection failed for {self.node.host_name}: {str(e)}")
            raise
        finally:
            if client:
                client.cleanup()

    def upload_file(self, local_path, remote_path, callback=None):
        """上传文件到远程服务器
        
        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径
            callback: 进度回调函数
            
        Returns:
            bool: 上传是否成功
        """
        try:
            with self.connection() as client:
                # 确保远程目录存在
                remote_dir = os.path.dirname(remote_path)
                client.ensure_remote_dir(remote_dir)
                # 上传文件
                client.upload_file(local_path, remote_path, callback)
            return True
        except Exception as e:
            log_error(f"Failed to upload file {local_path} to {self.node.host_name}: {str(e)}")
            return False

    def download_file(self, remote_path, local_path, callback=None):
        """从远程服务器下载文件
        
        Args:
            remote_path: 远程文件路径
            local_path: 本地文件路径
            callback: 进度回调函数
            
        Returns:
            bool: 下载是否成功
        """
        try:
            with self.connection() as client:
                # 确保本地目录存在
                os.makedirs(os.path.dirname(local_path), exist_ok=True)
                # 下载文件
                client.download_file(remote_path, local_path, callback)
            return True
        except Exception as e:
            log_error(f"Failed to download file {remote_path} from {self.node.host_name}: {str(e)}")
            return False

    def pause_transfer(self):
        """暂停传输并恢复配置"""
        try:
            with self.connection() as client:
                client.restore_sshd_config()
            return True
        except Exception as e:
            log_error(f"Failed to pause transfer for {self.node.host_name}: {str(e)}")
            return False
