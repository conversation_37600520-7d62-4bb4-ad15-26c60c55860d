{"version": 3, "sources": ["webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/components/common/NodeSelector.vue?b64e", "webpack:///./src/components/common/TaskProgressCard.vue?1c56", "webpack:///./src/mixins/NotificationMixin.js", "webpack:///./src/mixins/TaskPollingMixin.js", "webpack:///./src/components/common/ProxySelector.vue", "webpack:///src/components/common/ProxySelector.vue", "webpack:///./src/components/common/ProxySelector.vue?a770", "webpack:///./src/components/common/ProxySelector.vue?1de0", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.every.js", "webpack:///./src/components/common/TaskProgressCard.vue", "webpack:///src/components/common/TaskProgressCard.vue", "webpack:///./src/components/common/TaskProgressCard.vue?8ca7", "webpack:///./src/components/common/TaskProgressCard.vue?7c76", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./src/components/common/NodeSelector.vue", "webpack:///src/components/common/NodeSelector.vue", "webpack:///./src/components/common/NodeSelector.vue?f294", "webpack:///./src/components/common/NodeSelector.vue?2855", "webpack:///./node_modules/core-js/internals/array-reduce.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "methods", "addTaskCompletionNotification", "taskId", "taskType", "nodes", "projectId", "titles", "templates", "statusMapping", "notificationSent<PERSON>ey", "localStorage", "getItem", "defaultStatusMapping", "success", "failure", "finalStatusMapping", "successNodes", "filter", "node", "includes", "status", "error_detail", "failedNodes", "hasFailures", "notificationTitle", "notificationMessage", "error", "getDefaultErrorTitle", "getDefaultSuccessTitle", "addNotification", "title", "message", "type", "setItem", "clearTaskNotificationMark", "removeItem", "computed", "mapState", "_this$activeTask", "activeTask", "task_id", "progressPercentage", "_this$activeTask2", "Object", "values", "totalProgress", "sum", "parseInt", "progress", "Math", "round", "taskInProgress", "isProcessing", "pollInterval", "data", "<PERSON><PERSON><PERSON><PERSON>", "apiEndpoint", "storageKey", "currentProject", "completed<PERSON><PERSON>", "taskInfo", "taskCompleted", "projectFile", "JSON", "parse", "Error", "response", "axios", "get", "$store", "dispatch", "allCompleted", "every", "startPolling", "console", "clearInterval", "pollStatus", "async", "log", "stopPolling", "anyFailed", "some", "getNotificationTemplates", "$forceUpdate", "$nextTick", "taskProgressCard", "$children", "find", "child", "$options", "name", "_error$response", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "deactivated", "render", "_vm", "_c", "_self", "staticClass", "attrs", "isDetecting", "disabled", "on", "fetchReachableIps", "_v", "_s", "$t", "reachableIps", "staticStyle", "model", "value", "selectedIpValue", "callback", "$$v", "expression", "_l", "ip", "key", "_e", "staticRenderFns", "props", "Boolean", "default", "String", "watch", "newValue", "$emit", "reachable_ips", "$notify", "component", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "fn", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped", "slot", "clearTask", "hasNodes", "progressStatus", "nodeProgressData", "progressColumns", "scopedSlots", "_u", "text", "record", "getStatusColor", "truncateError", "Empty", "PRESENTED_IMAGE_SIMPLE", "currentTask", "activeToolTask", "activeUploadTask", "activeDownloadTask", "_this$currentTask", "keys", "_this$currentTask2", "_this$currentTask3", "_this$currentTask4", "entries", "map", "host_name", "dataIndex", "customRender", "width", "<PERSON><PERSON><PERSON><PERSON>", "colors", "processing", "substring", "commit", "reducer", "noInitial", "accumulator", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "count", "columns", "pageSize", "total", "showSizeChanger", "rowSelection", "handleRowClick", "Array", "required", "set", "onChange", "getCheckboxProps", "handler", "newVal", "fetchNodes", "immediate", "encodeURIComponent", "items", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "i", "right"], "mappings": "kHACA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,oCChB7F,W,2DCAA,W,wDCIe,QACbC,QAAS,CAYPC,+BAA8B,OAC5BC,EAAM,SACNC,EAAQ,MACRC,EAAK,UACLC,EAAS,OACTC,EAAS,GAAE,UACXC,EAAY,GAAE,cACdC,EAAgB,KAGhB,MAAMC,EAAsB,GAAGN,aAAoBE,KAAaH,IAChE,GAAIQ,aAAaC,QAAQF,GACvB,OAIF,MAAMG,EAAuB,CAC3BC,QAAS,CAAC,UAAW,aACrBC,QAAS,CAAC,WAINC,EAAqB,CACzBF,QAAS,IAAKL,EAAcK,SAAW,MAAQD,EAAqBC,SACpEC,QAAS,IAAKN,EAAcM,SAAW,MAAQF,EAAqBE,UAIhEE,EAAeZ,EAAMa,OAAOC,GAChCH,EAAmBF,QAAQM,SAASD,EAAKE,UAAYF,EAAKG,cAC1DvB,OACIwB,EAAclB,EAAMa,OAAOC,GAC/BH,EAAmBD,QAAQK,SAASD,EAAKE,SAAWF,EAAKG,cACzDvB,OACIyB,EAAcD,EAAc,EAGlC,IAAIE,EAQAC,EANFD,EADED,EACkBjB,EAAOoB,OAAS9B,KAAK+B,qBAAqBxB,GAE1CG,EAAOO,SAAWjB,KAAKgC,uBAAuBzB,GAMlEsB,EADEF,EACoBhB,EAAUmB,OAC9B,GAAGV,mCAA8CM,kBAE7Bf,EAAUM,SAC9B,OAAOT,EAAMN,uCAIjBF,KAAKiC,gBAAgB,CACnBC,MAAON,EACPO,QAASN,EACTO,KAAMT,EAAc,QAAU,UAC9BrB,OAAQA,IAIVQ,aAAauB,QAAQxB,EAAqB,SAQ5CmB,uBAAuBzB,GACrB,MAAMG,EAAS,CACb,KAAQ,iBACR,OAAU,wBACV,SAAY,0BACZ,KAAQ,4BAEV,OAAOA,EAAOH,IAAa,uBAQ7BwB,qBAAqBxB,GACnB,MAAMG,EAAS,CACb,KAAQ,6BACR,OAAU,oCACV,SAAY,sCACZ,KAAQ,wCAEV,OAAOA,EAAOH,IAAa,mCAS7B+B,0BAA0BhC,EAAQC,EAAUE,GAC1C,MAAMI,EAAsB,GAAGN,aAAoBE,KAAaH,IAChEQ,aAAayB,WAAW1B,O,sIClHf,QACb2B,SAAU,IACLC,eAAS,CAAC,aAAc,mBAG3BnC,SAAS,IAAAoC,EACP,OAAsB,QAAtBA,EAAO1C,KAAK2C,kBAAU,IAAAD,OAAA,EAAfA,EAAiBE,SAI1BC,qBAAqB,IAAAC,EACnB,GAAoB,QAAhBA,EAAC9C,KAAK2C,kBAAU,IAAAG,IAAfA,EAAiBtC,MAAO,OAAO,EACpC,MAAMA,EAAQuC,OAAOC,OAAOhD,KAAK2C,WAAWnC,OAC5C,GAAqB,IAAjBA,EAAMN,OAAc,OAAO,EAC/B,MAAM+C,EAAgBzC,EAAMV,OAAO,CAACoD,EAAK5B,IAAS4B,GAAOC,SAAS7B,EAAK8B,WAAa,GAAI,GACxF,OAAOC,KAAKC,MAAML,EAAgBzC,EAAMN,SAI1CqD,iBACE,OAAOvD,KAAKwD,cAAgBxD,KAAKyD,eAIrCC,OACE,MAAO,CACLF,cAAc,EACdC,aAAc,KACdE,UAAW,MAIfvD,QAAS,CAMP,sBAAsBG,EAAUqD,GAC9B,IACE,MAAMC,EAAa,GAAGtD,SAAgBP,KAAK8D,iBACrCC,EAAe,GAAGxD,cAAqBP,KAAK8D,iBAC5CE,EAAWlD,aAAaC,QAAQ8C,GAChCI,EAAgBnD,aAAaC,QAAQgD,GAE3C,GAAIC,EAAU,CACZ,MAAM,OAAE1D,EAAM,YAAE4D,GAAgBC,KAAKC,MAAMJ,GAE3C,GAAIE,IAAgBlE,KAAK8D,eACvB,MAAM,IAAIO,MAAM,qCAGlB,MAAMC,QAAiBC,OAAMC,IAAI,QAAQZ,KAAetD,KAExD,GAAIgE,EAASZ,OACX1D,KAAKyE,OAAOC,SAAS,aAAcJ,EAASZ,MAExCY,EAASZ,KAAKlD,OAAO,CACvB,MAAMA,EAAQuC,OAAOC,OAAOsB,EAASZ,KAAKlD,OACpCmE,EAAenE,EAAMoE,MAAMtD,GAC/B,CAAC,UAAW,UAAUC,SAASD,EAAKE,SAGjCmD,GAAiBV,EAGXU,IACT3E,KAAKwD,cAAe,EACpB1C,aAAauB,QAAQ0B,EAAc,UAJnC/D,KAAKwD,cAAe,EACpBxD,KAAK6E,aAAavE,EAAQC,EAAUqD,MAQ5C,MAAO9B,GACPgD,QAAQhD,MAAM,8BAA+BA,GAC7ChB,aAAayB,WAAW,GAAGhC,SAAgBP,KAAK8D,kBAChDhD,aAAayB,WAAW,GAAGhC,cAAqBP,KAAK8D,oBAUzDe,aAAavE,EAAQC,EAAUqD,GACzB5D,KAAKyD,eACPsB,cAAc/E,KAAKyD,cACnBzD,KAAKyD,aAAe,MAGtB,MAAMuB,EAAaC,UACjB,IAEE,IAAKjF,KAAKyD,aAER,YADAqB,QAAQI,IAAI,gBAKd,MAAMnB,EAAe,GAAGxD,cAAqBP,KAAK8D,iBAC5CG,EAAgBnD,aAAaC,QAAQgD,GAC3C,GAAsB,SAAlBE,EAIF,OAHAa,QAAQI,IAAI,iBACZlF,KAAKmF,mBACLnF,KAAKwD,cAAe,GAItB,IAAKxD,KAAK8D,eACR,MAAM,IAAIO,MAAM,gCAGlB,MAAMC,QAAiBC,OAAMC,IAAI,QAAQZ,KAAetD,KAWxD,GAVAwE,QAAQI,IAAI,UAAWZ,EAASZ,MAG5BY,EAASZ,OAASY,EAASZ,KAAKd,UAClC0B,EAASZ,KAAKd,QAAUtC,GAI1BN,KAAKyE,OAAOC,SAAS,aAAcJ,EAASZ,MAExCY,EAASZ,MAAQY,EAASZ,KAAKlD,MAAO,CACxC,MAAMA,EAAQuC,OAAOC,OAAOsB,EAASZ,KAAKlD,OAGpCmE,EAAenE,EAAMoE,MAAMtD,GAC/B,CAAC,UAAW,UAAUC,SAASD,EAAKE,SAIhC4D,EAAY5E,EAAM6E,KAAK/D,GAAwB,WAAhBA,EAAKE,QAAuBF,EAAKG,cAElEkD,GACFG,QAAQI,IAAI,kBACZlF,KAAKmF,cACLnF,KAAKwD,cAAe,EACpB1C,aAAauB,QAAQ0B,EAAc,QAGnC/D,KAAKK,8BAA8B,CACjCC,SACAC,WACAC,QACAC,UAAWT,KAAK8D,eAChBnD,UAAWX,KAAKsF,yBAAyB/E,EAAUC,MAE5C4E,GAAsC,WAAzBd,EAASZ,KAAKlC,SAEpCsD,QAAQI,IAAI,gBACZlF,KAAKmF,cACLnF,KAAKwD,cAAe,EACpB1C,aAAauB,QAAQ0B,EAAc,QAGnC/D,KAAKK,8BAA8B,CACjCC,SACAC,WACAC,QACAC,UAAWT,KAAK8D,eAChBnD,UAAW,CACTmB,MAAO,gBAAgBtB,EAAMa,OAAOC,GAAwB,WAAhBA,EAAKE,QAAqBtB,qCAM5EF,KAAKuF,eAGLvF,KAAKwF,UAAU,KACb,MAAMC,EAAmBzF,KAAK0F,UAAUC,KAAKC,GAAiC,qBAAxBA,EAAMC,SAASC,MACjEL,GACFA,EAAiBF,sBAGZjB,EAASZ,MAAiC,WAAzBY,EAASZ,KAAKlC,SAExCsD,QAAQI,IAAI,gBACZlF,KAAKmF,cACLnF,KAAKwD,cAAe,EACpB1C,aAAauB,QAAQ0B,EAAc,QAGnC/D,KAAKK,8BAA8B,CACjCC,SACAC,WACAC,MAAO,GACPC,UAAWT,KAAK8D,eAChBnD,UAAW,CACTmB,MAAO,yDAIb,MAAOA,GAAO,IAAAiE,EACdjB,QAAQhD,MAAM,wBAAyBA,GACR,OAAb,QAAdiE,EAAAjE,EAAMwC,gBAAQ,IAAAyB,OAAA,EAAdA,EAAgBvE,UAClBxB,KAAKmF,cACLnF,KAAKwD,cAAe,KAM1BwB,IAGAhF,KAAKyD,aAAeuC,YAAYhB,EAAYhF,KAAK2D,WAGjDmB,QAAQI,IAAI,UAAU5E,WAAgBN,KAAK2D,gBAM7CwB,cACMnF,KAAKyD,eACPsB,cAAc/E,KAAKyD,cACnBzD,KAAKyD,aAAe,OAUxB6B,yBAAyB/E,EAAUC,GACjC,MAAiB,SAAbD,EACK,CACLU,QAAS,qCAAqCT,EAAMN,gBACpD4B,MAAO,GAAGtB,EAAMa,OAAOC,GAAwB,YAAhBA,EAAKE,QAAsBtB,uDAAuDM,EAAMa,OAAOC,GAAwB,WAAhBA,EAAKE,QAAqBtB,wBAG3J,CACLe,QAAS,sCAAsCT,EAAMN,gBACrD4B,MAAO,GAAGtB,EAAMa,OAAOC,GAAwB,YAAhBA,EAAKE,QAAsBtB,wCAAwCM,EAAMa,OAAOC,GAAwB,WAAhBA,EAAKE,QAAqBtB,yBAQvJ+F,gBACEjG,KAAKmF,eAMPe,cACElG,KAAKmF,kB,oCCxQX,IAAIgB,EAAS,WAAkB,IAAIC,EAAIpG,KAAKqG,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,WAAW,CAACE,YAAY,mBAAmBC,MAAM,CAAC,QAAUJ,EAAIK,YAAY,SAAWL,EAAIM,UAAUC,GAAG,CAAC,MAAQP,EAAIQ,oBAAoB,CAACR,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIW,GAAG,8BAAgC,UAAU,OAAQX,EAAIY,aAAa9G,OAAQmG,EAAG,WAAW,CAACY,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQT,MAAM,CAAC,YAAcJ,EAAIW,GAAG,2BAA6B,SAAS,SAAWX,EAAIM,UAAUQ,MAAM,CAACC,MAAOf,EAAIgB,gBAAiBC,SAAS,SAAUC,GAAMlB,EAAIgB,gBAAgBE,GAAKC,WAAW,oBAAoBnB,EAAIoB,GAAIpB,EAAIY,cAAc,SAASS,GAAI,OAAOpB,EAAG,kBAAkB,CAACqB,IAAID,EAAGjB,MAAM,CAAC,MAAQiB,IAAK,CAACrB,EAAIS,GAAG,IAAIT,EAAIU,GAAGW,GAAI,UAAS,GAAGrB,EAAIuB,MAAM,IAEpvBC,EAAkB,G,YC6BP,GACf9B,KAAA,gBACA+B,MAAA,CAEAnB,SAAA,CACAtE,KAAA0F,QACAC,SAAA,GAGAZ,MAAA,CACA/E,KAAA4F,OACAD,QAAA,OAGArE,OACA,OACA+C,aAAA,EACAO,aAAA,GACAI,gBAAA,KAAAD,QAGA3E,SAAA,GAEAyF,MAAA,CAEAd,MAAAe,GACA,KAAAd,gBAAAc,GAGAd,gBAAAc,GACA,KAAAC,MAAA,QAAAD,GACA,KAAAC,MAAA,SAAAD,KAGA9H,QAAA,CACA,0BACA,KAAAqG,aAAA,EACA,IACA,MAAAnC,QAAAC,OAAAC,IAAA,qBACA,KAAAwC,aAAA1C,EAAAZ,KAAA0E,cACA,KAAApB,aAAA9G,SACA,KAAAkH,gBAAA,KAAAJ,aAAA,IAEA,MAAAlF,GACAgD,QAAAhD,MAAA,iCAAAA,GACA,KAAAuG,QAAAvG,MAAA,CACAI,MAAA,QACAC,QAAA,mCAEA,QACA,KAAAsE,aAAA,MCjFqW,I,YCOjW6B,EAAY,eACd,EACAnC,EACAyB,GACA,EACA,KACA,WACA,MAIa,OAAAU,E,gCClBf,IAAIC,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCDhC,IAAIxJ,EAAI,EAAQ,QACZyJ,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3J,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmJ,MAAM,GAAQ,CACjDnE,MAAO,SAAeoE,GAGpB,OAFAF,EAAS9I,MACT6I,EAAUG,IACFJ,EAAQ5I,MAAM,SAAUmH,EAAO8B,GACrC,IAAKD,EAAG7B,GAAQ,OAAO8B,MACtB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,2DCbjD,IAAIjD,EAAS,WAAkB,IAAIC,EAAIpG,KAAKqG,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACY,YAAY,CAAC,aAAa,QAAQT,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAIW,GAAG,yBAAyB,CAACV,EAAG,WAAW,CAACgD,KAAK,SAAS,CAAChD,EAAG,WAAW,CAACE,YAAY,eAAeC,MAAM,CAAC,KAAO,OAAO,KAAO,SAASG,GAAG,CAAC,MAAQP,EAAIkD,YAAY,CAACjD,EAAG,SAAS,CAACG,MAAM,CAAC,KAAO,WAAWJ,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIW,GAAG,iBAAiB,MAAM,IAAI,GAAIX,EAAI9F,QAAU8F,EAAImD,SAAUlD,EAAG,MAAM,CAACA,EAAG,aAAa,CAACY,YAAY,CAAC,gBAAgB,QAAQT,MAAM,CAAC,QAAUJ,EAAIvD,mBAAmB,OAASuD,EAAIoD,kBAAkBnD,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAU,CAACG,MAAM,CAAC,WAAaJ,EAAIqD,iBAAiB,QAAUrD,EAAIsD,gBAAgB,YAAa,GAAOC,YAAYvD,EAAIwD,GAAG,CAAC,CAAClC,IAAI,WAAWsB,GAAG,SAASa,EAAMC,GAAQ,MAAO,CAACzD,EAAG,aAAa,CAACG,MAAM,CAAC,QAAUsD,EAAO1G,SAAS,OAA2B,WAAlB0G,EAAOtI,OAAsB,YACr0B,YAAlBsI,EAAOtI,QAAyBsI,EAAOrI,aAA2B,SAAZ,UAAsB,KAAO,cAAc,CAACiG,IAAI,SAASsB,GAAG,SAASa,EAAMC,GAAQ,MAAO,CAACzD,EAAG,QAAQ,CAACG,MAAM,CAAC,MAAQJ,EAAI2D,eAAeF,EAAMC,EAAOrI,gBAAgB,CAAC2E,EAAIS,GAAG,IAAIT,EAAIU,GAAGgD,EAAOrI,aAAe2E,EAAIW,GAAG,sBAAwB8C,GAAM,UAAU,CAACnC,IAAI,QAAQsB,GAAG,SAASa,EAAMC,GAAQ,MAAO,CAAEA,EAAOrI,aAAc4E,EAAG,MAAM,CAACY,YAAY,CAAC,YAAY,UAAU,CAACZ,EAAG,YAAY,CAACG,MAAM,CAAC,UAAY,YAAY,CAACH,EAAG,WAAW,CAACgD,KAAK,SAAS,CAAChD,EAAG,MAAM,CAACY,YAAY,CAAC,cAAc,aAAa,CAACb,EAAIS,GAAGT,EAAIU,GAAGgD,EAAOrI,mBAAmB4E,EAAG,OAAO,CAACY,YAAY,CAAC,MAAQ,UAAU,OAAS,YAAY,CAACZ,EAAG,SAAS,CAACY,YAAY,CAAC,eAAe,OAAOT,MAAM,CAAC,KAAO,UAAU,MAAQ,YAAYJ,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAI4D,cAAcF,EAAOrI,eAAe,MAAM,IAAI,IAAI,GAAG2E,EAAIuB,SAAS,MAAK,EAAM,eAAe,IAAI,GAAGtB,EAAG,MAAM,CAACA,EAAG,UAAU,CAACG,MAAM,CAAC,MAAQJ,EAAI6D,MAAMC,2BAA2B,IAAI,IAEp8BtC,EAAkB,G,4ECoEP,GACf9B,KAAA,mBACApC,OACA,OACAuG,eAGApC,MAAA,CAEAtH,SAAA,CACA6B,KAAA4F,OACAD,QAAA,QAGAvE,aAAA,CACApB,KAAA0F,QACAC,SAAA,IAGAvF,SAAA,IACAC,eAAA,0FAGA0H,cACA,oBAAA5J,SACA,KAAA6J,eACA,gBAAA7J,SACA,KAAA8J,iBACA,kBAAA9J,SACA,KAAA+J,mBAGA,KAAA3H,YAKArC,SAAA,IAAAiK,EACA,eAAAA,EAAA,KAAAJ,mBAAA,IAAAI,OAAA,EAAAA,EAAA3H,SAIA2G,WACA,YAAAY,aAAA,KAAAA,YAAA3J,OAAAuC,OAAAyH,KAAA,KAAAL,YAAA3J,OAAAN,OAAA,GAMA2C,qBAAA,IAAA4H,EACA,WAAAA,EAAA,KAAAN,mBAAA,IAAAM,MAAAjK,MAAA,SACA,MAAAA,EAAAuC,OAAAC,OAAA,KAAAmH,YAAA3J,OACA,OAAAA,EAAAN,OAAA,SACA,MAAA+C,EAAAzC,EAAAV,OAAA,CAAAoD,EAAA5B,IAAA4B,GAAAC,SAAA7B,EAAA8B,WAAA,MACA,OAAAC,KAAAC,MAAAL,EAAAzC,EAAAN,SAIAsJ,iBAAA,IAAAkB,EACA,WAAAA,EAAA,KAAAP,mBAAA,IAAAO,MAAAlK,MAAA,eACA,MAAAA,EAAAuC,OAAAC,OAAA,KAAAmH,YAAA3J,OACAmB,EAAAnB,EAAA6E,KAAA/D,GAAA,WAAAA,EAAAE,QAAAF,EAAAG,cACA,OAAAE,EAAA,YACA,WAAAkB,mBAAA,oBAIA4G,mBAAA,IAAAkB,EACA,eAAAA,EAAA,KAAAR,mBAAA,IAAAQ,KAAAnK,MACAuC,OAAA6H,QAAA,KAAAT,YAAA3J,OAAAqK,IAAA,EAAApD,EAAAnG,MAAA,CACAoG,IAAAD,EACAA,KACAqD,UAAAxJ,EAAAwJ,UACAtJ,OAAAF,EAAAG,aAAA,SAAAH,EAAAE,OACA4B,SAAA9B,EAAA8B,UAAA,EACA3B,aAAAH,EAAAG,gBAPA,IAYAiI,kBACA,OACA,CACAxH,MAAA,KAAA6E,GAAA,yBACAgE,UAAA,YACArD,IAAA,aAEA,CACAxF,MAAA,KAAA6E,GAAA,mBACAgE,UAAA,KACArD,IAAA,MAEA,CACAxF,MAAA,KAAA6E,GAAA,uBACAgE,UAAA,SACArD,IAAA,SACAiC,YAAA,CAAAqB,aAAA,WAEA,CACA9I,MAAA,KAAA6E,GAAA,yBACAgE,UAAA,WACArD,IAAA,WACAiC,YAAA,CAAAqB,aAAA,aAEA,CACA9I,MAAA,KAAA6E,GAAA,6BACAW,IAAA,QACAuD,MAAA,IACAtB,YAAA,CAAAqB,aAAA,aAKA5K,QAAA,CAEA2J,eAAAvI,EAAA0J,GACA,MAAAC,EAAA,CACAlK,QAAA,UACAa,MAAA,UACAsJ,WAAA,WAEA,OAAAF,EAAAC,EAAArJ,MAAAqJ,EAAA3J,IAAA,WAIAwI,cAAAlI,GACA,OAAAA,EACAA,EAAA5B,OAAA,GAAA4B,EAAAuJ,UAAA,YAAAvJ,EADA,IAKAwH,YAEA,cAAA/I,SACA,KAAAkE,OAAAC,SAAA,uBACA,gBAAAnE,SACA,KAAAkE,OAAA6G,OAAA,4BACA,kBAAA/K,SACA,KAAAkE,OAAA6G,OAAA,8BAGA,KAAA7G,OAAAC,SAAA,mBAIA,KAAAZ,iBACA,cAAAvD,UACAO,aAAAyB,WAAA,qBAAAuB,gBACAhD,aAAAyB,WAAA,0BAAAuB,iBACA,gBAAAvD,UACAO,aAAAyB,WAAA,uBAAAuB,gBACAhD,aAAAyB,WAAA,4BAAAuB,iBACA,kBAAAvD,UACAO,aAAAyB,WAAA,yBAAAuB,gBACAhD,aAAAyB,WAAA,8BAAAuB,kBAEAhD,aAAAyB,WAAA,iBAAAuB,gBACAhD,aAAAyB,WAAA,sBAAAuB,qBCpOwW,I,wBCQpWwE,EAAY,eACd,EACAnC,EACAyB,GACA,EACA,KACA,WACA,MAIa,OAAAU,E,6CCjBf,IAAInJ,EAAI,EAAQ,QACZyJ,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3J,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmJ,MAAM,GAAQ,CACjD1D,KAAM,SAAc2D,GAGlB,OAFAF,EAAS9I,MACT6I,EAAUG,GACHJ,EAAQ5I,MAAM,SAAUmH,EAAO8B,GACpC,GAAID,EAAG7B,GAAQ,OAAO8B,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAIjK,EAAI,EAAQ,QACZyJ,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB3J,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMmJ,MAAM,GAAQ,CACjDjJ,OAAQ,SAAgByL,GACtBzC,EAAS9I,MACT6I,EAAU0C,GACV,IAAIC,EAAYvL,UAAUC,OAAS,EAC/BuL,EAAcD,OAAYrL,EAAYF,UAAU,GASpD,GARA2I,EAAQ5I,MAAM,SAAUmH,GAClBqE,GACFA,GAAY,EACZC,EAActE,GAEdsE,EAAcF,EAAQE,EAAatE,KAEpC,CAAE+B,aAAa,IACdsC,EAAW,MAAME,UAAU,kDAC/B,OAAOD,M,kCCrBX,IAAIE,EAAQ,EAAQ,QAEpBlD,EAAOC,QAAU,SAAUkD,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,kCCP5D,IAAI1F,EAAS,WAAkB,IAAIC,EAAIpG,KAAKqG,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAAEH,EAAI4F,gBAAgB9L,OAAS,EAAGmG,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACH,EAAIS,GAAG,IAAIT,EAAIU,GAAGV,EAAIW,GAAG,uBAAwB,CAAEkF,MAAO7F,EAAI4F,gBAAgB9L,UAAW,OAAOkG,EAAIuB,KAAKtB,EAAG,UAAU,CAACG,MAAM,CAAC,WAAaJ,EAAI5F,MAAM,QAAU4F,EAAI8F,QAAQ,OAAS,KAAK,KAAO,SAAS,WAAa,CACpYC,SAAU,GACVC,MAAOhG,EAAI5F,MAAMN,OACjBmM,iBAAiB,GACjB,aAAejG,EAAIkG,cAAc3F,GAAG,CAAC,SAAWP,EAAImG,mBAAmB,IAEzE3E,EAAkB,G,YCoBP,GACf9B,KAAA,eACA+B,MAAA,CACAV,MAAA,CACA/E,KAAAoK,MACAzE,YAAA,IAEA7D,YAAA,CACA9B,KAAA4F,OACAyE,UAAA,GAEA/F,SAAA,CACAtE,KAAA0F,QACAC,SAAA,IAGArE,OACA,OACAlD,MAAA,KAGAgC,SAAA,CACAwJ,gBAAA,CACAxH,MACA,YAAA2C,OAEAuF,IAAAvF,GACA,KAAAgB,MAAA,QAAAhB,KAGAmF,eACA,OACAN,gBAAA,KAAAA,gBACAW,SAAAX,IACA,KAAAA,mBAEAY,sBAAA,CACAlG,SAAA,KAAAA,aAIAwF,UACA,OACA,CACAhK,MAAA,KAAA6E,GAAA,yBACAgE,UAAA,YACArD,IAAA,aAEA,CACAxF,MAAA,KAAA6E,GAAA,mBACAgE,UAAA,KACArD,IAAA,SAKAO,MAAA,CACA/D,YAAA,CACA2I,QAAAC,GACAA,GACA,KAAAC,cAGAC,WAAA,IAGA5M,QAAA,CACA,mBACA,IACA,MAAAkE,QAAAC,OAAAC,IAAA,0BAAAyI,mBAAA,KAAA/I,cACA,KAAA1D,MAAA8D,EAAAZ,KAAAwJ,MACA,MAAApL,GACAgD,QAAAhD,MAAA,wBAAAA,GACA,KAAAuG,QAAAvG,MAAA,CACAI,MAAA,QACAC,QAAA,4BAIAoK,qBCzGoW,I,wBCQhWjE,EAAY,eACd,EACAnC,EACAyB,GACA,EACA,KACA,WACA,MAIa,OAAAU,E,8BCnBf,IAAIO,EAAY,EAAQ,QACpBsE,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMzN,EAAY0N,EAAiBC,GAClD7E,EAAU9I,GACV,IAAI4N,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrBzN,EAASmN,EAASM,EAAEzN,QACpB2N,EAAQN,EAAWrN,EAAS,EAAI,EAChC4N,EAAIP,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAASC,EACT,MAGF,GADAD,GAASC,EACLP,EAAWM,EAAQ,EAAI3N,GAAU2N,EACnC,MAAMnC,UAAU,+CAGpB,KAAM6B,EAAWM,GAAS,EAAI3N,EAAS2N,EAAOA,GAASC,EAAOD,KAASD,IACrEF,EAAO3N,EAAW2N,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIXjF,EAAOC,QAAU,CAGfrJ,KAAMiO,GAAa,GAGnBS,MAAOT,GAAa", "file": "static/js/chunk-29a53b76.a2cb3882.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NodeSelector.vue?vue&type=style&index=0&id=1bd9e7ce&prod&scoped=true&lang=css\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskProgressCard.vue?vue&type=style&index=0&id=7dd5f704&prod&scoped=true&lang=scss\"", "/**\r\n * 通知混入 - 提供通用的通知处理逻辑\r\n * 用于任务完成、文件上传/下载完成等场景\r\n */\r\nexport default {\r\n  methods: {\r\n    /**\r\n     * 添加任务完成通知\r\n     * @param {Object} options - 通知选项\r\n     * @param {string} options.taskId - 任务ID\r\n     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)\r\n     * @param {Array} options.nodes - 节点数组\r\n     * @param {string} options.projectId - 项目ID\r\n     * @param {Object} options.titles - 自定义标题 {success, error}\r\n     * @param {Object} options.templates - 自定义消息模板 {success, error}\r\n     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}\r\n     */\r\n    addTaskCompletionNotification({\r\n      taskId,\r\n      taskType,\r\n      nodes,\r\n      projectId,\r\n      titles = {},\r\n      templates = {},\r\n      statusMapping = {}\r\n    }) {\r\n      // 检查是否已经发送过通知\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      if (localStorage.getItem(notificationSentKey)) {\r\n        return; // 已经发送过通知，不再重复发送\r\n      }\r\n\r\n      // 设置默认状态映射\r\n      const defaultStatusMapping = {\r\n        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'\r\n        failure: ['failed']                // 失败状态通常是'failed'\r\n      };\r\n\r\n      // 合并自定义状态映射\r\n      const finalStatusMapping = {\r\n        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],\r\n        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]\r\n      };\r\n\r\n      // 计算成功和失败的节点数量\r\n      const successNodes = nodes.filter(node =>\r\n        finalStatusMapping.success.includes(node.status) && !node.error_detail\r\n      ).length;\r\n      const failedNodes = nodes.filter(node =>\r\n        finalStatusMapping.failure.includes(node.status) || node.error_detail\r\n      ).length;\r\n      const hasFailures = failedNodes > 0;\r\n\r\n      // 准备通知标题\r\n      let notificationTitle;\r\n      if (hasFailures) {\r\n        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);\r\n      } else {\r\n        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);\r\n      }\r\n\r\n      // 准备通知内容\r\n      let notificationMessage;\r\n      if (hasFailures) {\r\n        notificationMessage = templates.error ||\r\n          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;\r\n      } else {\r\n        notificationMessage = templates.success ||\r\n          `All ${nodes.length} nodes completed successfully.`;\r\n      }\r\n\r\n      // 添加到全局通知中心\r\n      this.addNotification({\r\n        title: notificationTitle,\r\n        message: notificationMessage,\r\n        type: hasFailures ? 'error' : 'success',\r\n        taskId: taskId\r\n      });\r\n\r\n      // 标记已发送通知\r\n      localStorage.setItem(notificationSentKey, 'true');\r\n    },\r\n\r\n    /**\r\n     * 获取默认的成功标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultSuccessTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed',\r\n        'upload': 'File Upload Completed',\r\n        'download': 'File Download Completed',\r\n        'tool': 'Tool Execution Completed'\r\n      };\r\n      return titles[taskType] || 'Operation Completed';\r\n    },\r\n\r\n    /**\r\n     * 获取默认的错误标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultErrorTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed with Errors',\r\n        'upload': 'File Upload Completed with Errors',\r\n        'download': 'File Download Completed with Errors',\r\n        'tool': 'Tool Execution Completed with Errors'\r\n      };\r\n      return titles[taskType] || 'Operation Completed with Errors';\r\n    },\r\n\r\n    /**\r\n     * 清除任务通知标记\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型\r\n     * @param {string} projectId - 项目ID\r\n     */\r\n    clearTaskNotificationMark(taskId, taskType, projectId) {\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      localStorage.removeItem(notificationSentKey);\r\n    }\r\n  }\r\n};\r\n", "/**\r\n * 任务轮询混入\r\n * 提供任务轮询相关的通用方法，用于TaskPanel和ToolsPanel组件\r\n */\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n  computed: {\r\n    ...mapState(['activeTask', 'currentProject']),\r\n    \r\n    // 任务ID\r\n    taskId() {\r\n      return this.activeTask?.task_id;\r\n    },\r\n    \r\n    // 任务进度百分比\r\n    progressPercentage() {\r\n      if (!this.activeTask?.nodes) return 0;\r\n      const nodes = Object.values(this.activeTask.nodes);\r\n      if (nodes.length === 0) return 0;\r\n      const totalProgress = nodes.reduce((sum, node) => sum + (parseInt(node.progress) || 0), 0);\r\n      return Math.round(totalProgress / nodes.length);\r\n    },\r\n    \r\n    // 任务是否正在进行中\r\n    taskInProgress() {\r\n      return this.isProcessing && this.pollInterval;\r\n    },\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      isProcessing: false,\r\n      pollInterval: null,\r\n      pollDelay: 10000, // 轮询间隔，默认10秒\r\n    };\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 检查活动任务\r\n     * @param {string} taskType - 任务类型，'task'或'tool'\r\n     * @param {string} apiEndpoint - API端点，例如'task'或'script'\r\n     */\r\n    async checkActiveTask(taskType, apiEndpoint) {\r\n      try {\r\n        const storageKey = `${taskType}Info_${this.currentProject}`;\r\n        const completedKey = `${taskType}Completed_${this.currentProject}`;\r\n        const taskInfo = localStorage.getItem(storageKey);\r\n        const taskCompleted = localStorage.getItem(completedKey);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/${apiEndpoint}/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, taskType, apiEndpoint);\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(completedKey, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`${taskType}Info_${this.currentProject}`);\r\n        localStorage.removeItem(`${taskType}Completed_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 开始轮询任务状态\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型，'task'或'tool'\r\n     * @param {string} apiEndpoint - API端点，例如'task'或'script'\r\n     */\r\n    startPolling(taskId, taskType, apiEndpoint) {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n        this.pollInterval = null;\r\n      }\r\n\r\n      const pollStatus = async () => {\r\n        try {\r\n          // 检查轮询是否已停止\r\n          if (!this.pollInterval) {\r\n            console.log('轮询已停止，不再请求数据');\r\n            return;\r\n          }\r\n\r\n          // 检查任务是否已标记为完成\r\n          const completedKey = `${taskType}Completed_${this.currentProject}`;\r\n          const taskCompleted = localStorage.getItem(completedKey);\r\n          if (taskCompleted === 'true') {\r\n            console.log('任务已标记为完成，停止轮询');\r\n            this.stopPolling();\r\n            this.isProcessing = false;\r\n            return;\r\n          }\r\n\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(`/api/${apiEndpoint}/${taskId}`);\r\n          console.log('轮询任务状态:', response.data);\r\n\r\n          // 确保任务ID存在\r\n          if (response.data && !response.data.task_id) {\r\n            response.data.task_id = taskId;\r\n          }\r\n\r\n          // 更新Vuex中的任务状态\r\n          this.$store.dispatch('updateTask', response.data);\r\n\r\n          if (response.data && response.data.nodes) {\r\n            const nodes = Object.values(response.data.nodes);\r\n            \r\n            // 检查是否所有节点都已完成（成功或失败）\r\n            const allCompleted = nodes.every(node =>\r\n              ['success', 'failed'].includes(node.status)\r\n            );\r\n            \r\n            // 检查是否有任何节点失败\r\n            const anyFailed = nodes.some(node => node.status === 'failed' || node.error_detail);\r\n\r\n            if (allCompleted) {\r\n              console.log('所有节点已完成处理，停止轮询');\r\n              this.stopPolling();\r\n              this.isProcessing = false;\r\n              localStorage.setItem(completedKey, 'true');\r\n\r\n              // 使用混入中的方法添加任务完成通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType,\r\n                nodes,\r\n                projectId: this.currentProject,\r\n                templates: this.getNotificationTemplates(taskType, nodes)\r\n              });\r\n            } else if (anyFailed && response.data.status === 'failed') {\r\n              // 如果整个任务状态为失败，也停止轮询\r\n              console.log('任务状态为失败，停止轮询');\r\n              this.stopPolling();\r\n              this.isProcessing = false;\r\n              localStorage.setItem(completedKey, 'true');\r\n              \r\n              // 添加失败通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType,\r\n                nodes,\r\n                projectId: this.currentProject,\r\n                templates: {\r\n                  error: `Task failed: ${nodes.filter(node => node.status === 'failed').length} nodes reported failure.`\r\n                }\r\n              });\r\n            }\r\n            \r\n            // 强制更新视图\r\n            this.$forceUpdate();\r\n            \r\n            // 确保TaskProgressCard组件也更新\r\n            this.$nextTick(() => {\r\n              const taskProgressCard = this.$children.find(child => child.$options.name === 'TaskProgressCard');\r\n              if (taskProgressCard) {\r\n                taskProgressCard.$forceUpdate();\r\n              }\r\n            });\r\n          } else if (response.data && response.data.status === 'failed') {\r\n            // 如果响应中只有任务状态且为失败，也停止轮询\r\n            console.log('任务状态为失败，停止轮询');\r\n            this.stopPolling();\r\n            this.isProcessing = false;\r\n            localStorage.setItem(completedKey, 'true');\r\n            \r\n            // 添加失败通知\r\n            this.addTaskCompletionNotification({\r\n              taskId,\r\n              taskType,\r\n              nodes: [],\r\n              projectId: this.currentProject,\r\n              templates: {\r\n                error: `Task failed: The server reported a failure status.`\r\n              }\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Error polling status:', error);\r\n          if (error.response?.status === 404) {\r\n            this.stopPolling();\r\n            this.isProcessing = false;\r\n          }\r\n        }\r\n      };\r\n\r\n      // 立即执行一次\r\n      pollStatus();\r\n      \r\n      // 设置定期轮询\r\n      this.pollInterval = setInterval(pollStatus, this.pollDelay);\r\n      \r\n      // 记录轮询开始时间，用于调试\r\n      console.log(`开始轮询任务 ${taskId}，轮询间隔: ${this.pollDelay}ms`);\r\n    },\r\n\r\n    /**\r\n     * 停止轮询\r\n     */\r\n    stopPolling() {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n        this.pollInterval = null;\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 获取通知模板\r\n     * @param {string} taskType - 任务类型\r\n     * @param {Array} nodes - 节点数组\r\n     * @returns {Object} 通知模板\r\n     */\r\n    getNotificationTemplates(taskType, nodes) {\r\n      if (taskType === 'tool') {\r\n        return {\r\n          success: `Tool executed successfully on all ${nodes.length} nodes.`,\r\n          error: `${nodes.filter(node => node.status === 'success').length} nodes completed tool execution successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n        };\r\n      } else {\r\n        return {\r\n          success: `Task completed successfully on all ${nodes.length} nodes.`,\r\n          error: `${nodes.filter(node => node.status === 'success').length} nodes completed successfully, ${nodes.filter(node => node.status === 'failed').length} nodes failed.`\r\n        };\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 组件销毁前停止轮询\r\n     */\r\n    beforeDestroy() {\r\n      this.stopPolling();\r\n    },\r\n    \r\n    /**\r\n     * 组件停用时停止轮询\r\n     */\r\n    deactivated() {\r\n      this.stopPolling();\r\n    }\r\n  }\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"proxy-selector\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.isDetecting,\"disabled\":_vm.disabled},on:{\"click\":_vm.fetchReachableIps}},[_vm._v(\" \"+_vm._s(_vm.$t('common.detectReachableIps') || '检测可达IP')+\" \")]),(_vm.reachableIps.length)?_c('a-select',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"16px\"},attrs:{\"placeholder\":_vm.$t('tool.selectReachableIp') || '选择可达IP',\"disabled\":_vm.disabled},model:{value:(_vm.selectedIpValue),callback:function ($$v) {_vm.selectedIpValue=$$v},expression:\"selectedIpValue\"}},_vm._l((_vm.reachableIps),function(ip){return _c('a-select-option',{key:ip,attrs:{\"value\":ip}},[_vm._v(\" \"+_vm._s(ip)+\" \")])}),1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"proxy-selector\">\r\n    <!-- 检测IP按钮 -->\r\n    <a-button\r\n      class=\"nav-style-button\"\r\n      @click=\"fetchReachableIps\"\r\n      :loading=\"isDetecting\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      {{ $t('common.detectReachableIps') || '检测可达IP' }}\r\n    </a-button>\r\n\r\n    <!-- IP选择下拉框 -->\r\n    <a-select\r\n      v-if=\"reachableIps.length\"\r\n      v-model=\"selectedIpValue\"\r\n      style=\"width: 100%; margin-top: 16px;\"\r\n      :placeholder=\"$t('tool.selectReachableIp') || '选择可达IP'\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <a-select-option v-for=\"ip in reachableIps\" :key=\"ip\" :value=\"ip\">\r\n        {{ ip }}\r\n      </a-select-option>\r\n    </a-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProxySelector',\r\n  props: {\r\n    // 是否禁用控件\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 初始选中的IP\r\n    value: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isDetecting: false,\r\n      reachableIps: [],\r\n      selectedIpValue: this.value\r\n    };\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {\r\n    // 监听外部传入的value变化\r\n    value(newValue) {\r\n      this.selectedIpValue = newValue;\r\n    },\r\n    // 监听内部selectedIpValue变化，向外发送事件\r\n    selectedIpValue(newValue) {\r\n      this.$emit('input', newValue);\r\n      this.$emit('change', newValue);\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchReachableIps() {\r\n      this.isDetecting = true;\r\n      try {\r\n        const response = await axios.get('/api/proxy/detect');\r\n        this.reachableIps = response.data.reachable_ips;\r\n        if (this.reachableIps.length) {\r\n          this.selectedIpValue = this.reachableIps[0];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error detecting reachable IPs:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to detect reachable IPs'\r\n        });\r\n      } finally {\r\n        this.isDetecting = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProxySelector.vue?vue&type=template&id=46f0b65a&scoped=true\"\nimport script from \"./ProxySelector.vue?vue&type=script&lang=js\"\nexport * from \"./ProxySelector.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46f0b65a\",\n  null\n  \n)\n\nexport default component.exports", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  every: function every(fn) {\n    anObject(this);\n    aFunction(fn);\n    return !iterate(this, function (value, stop) {\n      if (!fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticStyle:{\"margin-top\":\"16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.taskProgress')}},[_c('template',{slot:\"extra\"},[_c('a-button',{staticClass:\"clear-button\",attrs:{\"type\":\"link\",\"size\":\"small\"},on:{\"click\":_vm.clearTask}},[_c('a-icon',{attrs:{\"type\":\"close\"}}),_vm._v(\" \"+_vm._s(_vm.$t('common.clear'))+\" \")],1)],1),(_vm.taskId || _vm.hasNodes)?_c('div',[_c('a-progress',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"percent\":_vm.progressPercentage,\"status\":_vm.progressStatus}}),_c('div',{staticClass:\"result-table\"},[_c('a-table',{attrs:{\"dataSource\":_vm.nodeProgressData,\"columns\":_vm.progressColumns,\"pagination\":false},scopedSlots:_vm._u([{key:\"progress\",fn:function(text, record){return [_c('a-progress',{attrs:{\"percent\":record.progress,\"status\":record.status === 'failed' ? 'exception' :\n                  (record.status === 'success' && !record.error_detail ? 'success' : 'active'),\"size\":\"small\"}})]}},{key:\"status\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(text, record.error_detail)}},[_vm._v(\" \"+_vm._s(record.error_detail ? _vm.$t('tool.status.failed') : text)+\" \")])]}},{key:\"error\",fn:function(text, record){return [(record.error_detail)?_c('div',{staticStyle:{\"max-width\":\"300px\"}},[_c('a-tooltip',{attrs:{\"placement\":\"topLeft\"}},[_c('template',{slot:\"title\"},[_c('div',{staticStyle:{\"white-space\":\"pre-wrap\"}},[_vm._v(_vm._s(record.error_detail))])]),_c('span',{staticStyle:{\"color\":\"#ff4d4f\",\"cursor\":\"pointer\"}},[_c('a-icon',{staticStyle:{\"margin-right\":\"4px\"},attrs:{\"type\":\"warning\",\"theme\":\"filled\"}}),_vm._v(\" \"+_vm._s(_vm.truncateError(record.error_detail))+\" \")],1)],2)],1):_vm._e()]}}],null,false,3266794743)})],1)],1):_c('div',[_c('a-empty',{attrs:{\"image\":_vm.Empty.PRESENTED_IMAGE_SIMPLE}})],1)],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card style=\"margin-top: 16px;\" size=\"small\" :title=\"$t('common.taskProgress')\">\r\n    <template slot=\"extra\">\r\n      <a-button\r\n        type=\"link\"\r\n        size=\"small\"\r\n        @click=\"clearTask\"\r\n        class=\"clear-button\"\r\n      >\r\n        <a-icon type=\"close\" />\r\n        {{ $t('common.clear') }}\r\n      </a-button>\r\n    </template>\r\n    <div v-if=\"taskId || hasNodes\">\r\n      <!-- Overall progress -->\r\n      <a-progress\r\n        :percent=\"progressPercentage\"\r\n        :status=\"progressStatus\"\r\n        style=\"margin-bottom: 16px;\"\r\n      />\r\n\r\n      <!-- Node-level progress table -->\r\n      <div class=\"result-table\">\r\n        <a-table\r\n          :dataSource=\"nodeProgressData\"\r\n          :columns=\"progressColumns\"\r\n          :pagination=\"false\"\r\n        >\r\n        <template slot=\"progress\" slot-scope=\"text, record\">\r\n          <a-progress\r\n            :percent=\"record.progress\"\r\n            :status=\"record.status === 'failed' ? 'exception' :\r\n                    (record.status === 'success' && !record.error_detail ? 'success' : 'active')\"\r\n            size=\"small\"\r\n          />\r\n        </template>\r\n\r\n        <template slot=\"status\" slot-scope=\"text, record\">\r\n          <a-tag :color=\"getStatusColor(text, record.error_detail)\">\r\n            {{ record.error_detail ? $t('tool.status.failed') : text }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template slot=\"error\" slot-scope=\"text, record\">\r\n          <div v-if=\"record.error_detail\" style=\"max-width: 300px;\">\r\n            <a-tooltip placement=\"topLeft\">\r\n              <template slot=\"title\">\r\n                <div style=\"white-space: pre-wrap;\">{{ record.error_detail }}</div>\r\n              </template>\r\n              <span style=\"color: #ff4d4f; cursor: pointer;\">\r\n                <a-icon type=\"warning\" theme=\"filled\" style=\"margin-right: 4px;\" />\r\n                {{ truncateError(record.error_detail) }}\r\n              </span>\r\n            </a-tooltip>\r\n          </div>\r\n        </template>\r\n        </a-table>\r\n      </div>\r\n    </div>\r\n    <div v-else>\r\n      <a-empty\r\n        :image=\"Empty.PRESENTED_IMAGE_SIMPLE\"\r\n      />\r\n    </div>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n  name: 'TaskProgressCard',\r\n  data() {\r\n    return {\r\n      Empty\r\n    };\r\n  },\r\n  props: {\r\n    // 任务类型，用于区分不同类型的任务（如 'task' 或 'tool'）\r\n    taskType: {\r\n      type: String,\r\n      default: 'task'\r\n    },\r\n    // 是否正在处理中\r\n    isProcessing: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['activeTask', 'activeToolTask', 'activeUploadTask', 'activeDownloadTask', 'currentProject']),\r\n\r\n    // 根据任务类型获取对应的任务数据\r\n    currentTask() {\r\n      if (this.taskType === 'tool') {\r\n        return this.activeToolTask;\r\n      } else if (this.taskType === 'upload') {\r\n        return this.activeUploadTask;\r\n      } else if (this.taskType === 'download') {\r\n        return this.activeDownloadTask;\r\n      } else {\r\n        // task 使用 activeTask\r\n        return this.activeTask;\r\n      }\r\n    },\r\n\r\n    // 任务ID\r\n    taskId() {\r\n      return this.currentTask?.task_id;\r\n    },\r\n\r\n    // 是否有节点数据\r\n    hasNodes() {\r\n      return this.currentTask && this.currentTask.nodes && Object.keys(this.currentTask.nodes).length > 0;\r\n    },\r\n\r\n\r\n\r\n    // 进度百分比\r\n    progressPercentage() {\r\n      if (!this.currentTask?.nodes) return 0;\r\n      const nodes = Object.values(this.currentTask.nodes);\r\n      if (nodes.length === 0) return 0;\r\n      const totalProgress = nodes.reduce((sum, node) => sum + (parseInt(node.progress) || 0), 0);\r\n      return Math.round(totalProgress / nodes.length);\r\n    },\r\n\r\n    // 进度状态\r\n    progressStatus() {\r\n      if (!this.currentTask?.nodes) return 'active';\r\n      const nodes = Object.values(this.currentTask.nodes);\r\n      const hasFailures = nodes.some(node => node.status === 'failed' || node.error_detail);\r\n      return hasFailures ? 'exception' :\r\n             (this.progressPercentage === 100 ? 'success' : 'active');\r\n    },\r\n\r\n    // 节点进度数据\r\n    nodeProgressData() {\r\n      if (!this.currentTask?.nodes) return [];\r\n      return Object.entries(this.currentTask.nodes).map(([ip, node]) => ({\r\n        key: ip,\r\n        ip: ip,\r\n        host_name: node.host_name,\r\n        status: node.error_detail ? 'failed' : node.status,\r\n        progress: node.progress || 0,\r\n        error_detail: node.error_detail\r\n      }));\r\n    },\r\n\r\n    // 进度列定义\r\n    progressColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('tool.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.ip'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          scopedSlots: { customRender: 'status' }\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.progress'),\r\n          dataIndex: 'progress',\r\n          key: 'progress',\r\n          scopedSlots: { customRender: 'progress' }\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.errorDetails'),\r\n          key: 'error',\r\n          width: 300,\r\n          scopedSlots: { customRender: 'error' }\r\n        }\r\n      ];\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取状态颜色\r\n    getStatusColor(status, hasError) {\r\n      const colors = {\r\n        success: '#52c41a',\r\n        error: '#ff4d4f',\r\n        processing: '#1890ff'\r\n      }\r\n      return hasError ? colors.error : colors[status] || '#d9d9d9'\r\n    },\r\n\r\n    // 截断错误信息\r\n    truncateError(error) {\r\n      if (!error) return '';\r\n      return error.length > 50 ? error.substring(0, 47) + '...' : error;\r\n    },\r\n\r\n    // 清除任务\r\n    clearTask() {\r\n      // 根据任务类型清除对应的 store 状态\r\n      if (this.taskType === 'tool') {\r\n        this.$store.dispatch('clearActiveToolTask');\r\n      } else if (this.taskType === 'upload') {\r\n        this.$store.commit('setActiveUploadTask', null);\r\n      } else if (this.taskType === 'download') {\r\n        this.$store.commit('setActiveDownloadTask', null);\r\n      } else {\r\n        // task 清除 activeTask\r\n        this.$store.dispatch('clearActiveTask');\r\n      }\r\n      \r\n      // 清除 localStorage 中的任务相关信息\r\n      if (this.currentProject) {\r\n        if (this.taskType === 'tool') {\r\n          localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);\r\n          localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\r\n        } else if (this.taskType === 'upload') {\r\n          localStorage.removeItem(`uploadTaskInfo_${this.currentProject}`);\r\n          localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);\r\n        } else if (this.taskType === 'download') {\r\n          localStorage.removeItem(`downloadTaskInfo_${this.currentProject}`);\r\n          localStorage.removeItem(`downloadTaskCompleted_${this.currentProject}`);\r\n        } else {\r\n          localStorage.removeItem(`taskInfo_${this.currentProject}`);\r\n          localStorage.removeItem(`taskCompleted_${this.currentProject}`);\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.clear-button {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n\r\n::v-deep {\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskProgressCard.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskProgressCard.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TaskProgressCard.vue?vue&type=template&id=7dd5f704&scoped=true\"\nimport script from \"./TaskProgressCard.vue?vue&type=script&lang=js\"\nexport * from \"./TaskProgressCard.vue?vue&type=script&lang=js\"\nimport style0 from \"./TaskProgressCard.vue?vue&type=style&index=0&id=7dd5f704&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7dd5f704\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"config-table\"},[(_vm.selectedRowKeys.length > 0)?_c('div',{staticClass:\"selected-count\"},[_vm._v(\" \"+_vm._s(_vm.$t('common.selectedNodes', { count: _vm.selectedRowKeys.length }))+\" \")]):_vm._e(),_c('a-table',{attrs:{\"dataSource\":_vm.nodes,\"columns\":_vm.columns,\"rowKey\":\"ip\",\"size\":\"middle\",\"pagination\":{\n      pageSize: 10,\n      total: _vm.nodes.length,\n      showSizeChanger: false\n    },\"rowSelection\":_vm.rowSelection},on:{\"rowClick\":_vm.handleRowClick}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"config-table\">\r\n    <!-- 选中节点数量显示 -->\r\n    <div v-if=\"selectedRowKeys.length > 0\" class=\"selected-count\">\r\n      {{ $t('common.selectedNodes', { count: selectedRowKeys.length }) }}\r\n    </div>\r\n\r\n    <a-table\r\n      :dataSource=\"nodes\"\r\n      :columns=\"columns\"\r\n      rowKey=\"ip\"\r\n      size=\"middle\"\r\n      :pagination=\"{\r\n        pageSize: 10,\r\n        total: nodes.length,\r\n        showSizeChanger: false\r\n      }\"\r\n      :rowSelection=\"rowSelection\"\r\n      @rowClick=\"handleRowClick\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'NodeSelector',\r\n  props: {\r\n    value: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    projectFile: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      nodes: []\r\n    };\r\n  },\r\n  computed: {\r\n    selectedRowKeys: {\r\n      get() {\r\n        return this.value;\r\n      },\r\n      set(value) {\r\n        this.$emit('input', value);\r\n      }\r\n    },\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedRowKeys,\r\n        onChange: (selectedRowKeys) => {\r\n          this.selectedRowKeys = selectedRowKeys;\r\n        },\r\n        getCheckboxProps: () => ({\r\n          disabled: this.disabled\r\n        })\r\n      };\r\n    },\r\n    columns() {\r\n      return [\r\n        {\r\n          title: this.$t('tool.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.ip'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        }\r\n      ];\r\n    }\r\n  },\r\n  watch: {\r\n    projectFile: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.fetchNodes();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchNodes() {\r\n      try {\r\n        const response = await axios.get(`/api/node/nodes?dbFile=${encodeURIComponent(this.projectFile)}`);\r\n        this.nodes = response.data.items;\r\n      } catch (error) {\r\n        console.error('Error fetching nodes:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to fetch nodes'\r\n        });\r\n      }\r\n    },\r\n    handleRowClick() {\r\n      // 可根据需要添加单击行的逻辑\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.selected-count {\r\n  margin-bottom: 12px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(82, 196, 26, 0.1) !important;\r\n  border-color: rgba(82, 196, 26, 0.3) !important;\r\n  border-radius: 4px;\r\n  color: #52c41a !important;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NodeSelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./NodeSelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./NodeSelector.vue?vue&type=template&id=1bd9e7ce&scoped=true\"\nimport script from \"./NodeSelector.vue?vue&type=script&lang=js\"\nexport * from \"./NodeSelector.vue?vue&type=script&lang=js\"\nimport style0 from \"./NodeSelector.vue?vue&type=style&index=0&id=1bd9e7ce&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1bd9e7ce\",\n  null\n  \n)\n\nexport default component.exports", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n"], "sourceRoot": ""}