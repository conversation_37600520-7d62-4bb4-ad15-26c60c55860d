"""
会话管理器
"""

from typing import Dict, Any, Optional


class SessionManager:
    """简单的会话管理器"""
    
    def __init__(self):
        """初始化会话管理器"""
        self._sessions = {}
    
    def create_session(self, session_id: str, data: Dict[str, Any] = None) -> None:
        """创建会话"""
        self._sessions[session_id] = data or {}
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        return self._sessions.get(session_id)
    
    def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """更新会话数据"""
        if session_id in self._sessions:
            self._sessions[session_id].update(data)
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if session_id in self._sessions:
            del self._sessions[session_id]
            return True
        return False 