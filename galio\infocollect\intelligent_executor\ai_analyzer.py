"""
AI分析器 - 使用大模型分析测试用例并生成具体测试步骤和命令
"""

from typing import Dict, List, Any, Optional, Tuple
import json
import re
from langchain_ollama import ChatOllama
import base64
import os

from infocollect.log.logger import log_info, log_error, log_debug


class AIAnalyzer:
    """AI分析器，使用大模型分析测试用例"""
    
    def __init__(self):
        self.base_url = "http://**************:9034"
        self.username = "xxx"
        self.password = "xxxxxxx"
        self.model = "qwen2.5-coder:7b"
        self.timeout = 360.0
        
        # 设置环境变量
        os.environ['NO_PROXY'] = 'localhost,127.0.0.1,**************'
        
        self._llm = None

    @property
    def llm(self):
        """获取LLM实例"""
        if self._llm is None:
            # base64 编码认证信息
            auth_str = f"{self.username}:{self.password}"
            auth_bytes = auth_str.encode("ascii")
            encoded_auth = base64.b64encode(auth_bytes).decode("ascii")
            
            # 设置 HTTP 头
            headers = {
                "Authorization": f"Basic {encoded_auth}"
            }
            
            self._llm = ChatOllama(
                model=self.model,
                base_url=self.base_url,
                request_timeout=self.timeout,
                temperature=0.1,  # 降低温度以获得更一致的结果
                client_kwargs={"headers": headers}
            )
        return self._llm

    def analyze_testcase(self, testcase: Dict[str, Any]) -> Dict[str, Any]:
        """分析测试用例，生成详细的测试步骤"""
        try:
            log_info(f"Analyzing testcase: {testcase.get('Testcase_Number', 'Unknown')}")
            
            prompt = self._build_testcase_analysis_prompt(testcase)
            
            messages = [
                {"role": "system", "content": self._get_testcase_analysis_system_prompt()},
                {"role": "user", "content": prompt}
            ]
            
            response = self._make_request(messages)
            
            if response.get('success', False):
                analysis_result = self._parse_testcase_analysis(response['content'])
                analysis_result['original_testcase'] = testcase
                return {
                    "success": True,
                    "analysis": analysis_result
                }
            else:
                return {
                    "success": False,
                    "error": response.get('error', 'Unknown error')
                }
                
        except Exception as e:
            log_error(f"Error analyzing testcase: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def generate_commands_for_steps(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """为测试步骤生成具体的命令和工具建议"""
        try:
            steps = analysis_result.get('detailed_steps', [])
            if not steps:
                return {
                    "success": False,
                    "error": "No steps found in analysis result"
                }
            
            log_info(f"Generating commands for {len(steps)} steps")
            
            commands_result = []
            for i, step in enumerate(steps):
                step_commands = self._generate_step_commands(step, i + 1)
                commands_result.append(step_commands)
            
            return {
                "success": True,
                "commands": commands_result,
                "total_steps": len(steps)
            }
            
        except Exception as e:
            log_error(f"Error generating commands: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_step_commands(self, step: Dict[str, Any], step_number: int) -> Dict[str, Any]:
        """为单个步骤生成命令"""
        try:
            prompt = self._build_command_generation_prompt(step, step_number)
            
            messages = [
                {"role": "system", "content": self._get_command_generation_system_prompt()},
                {"role": "user", "content": prompt}
            ]
            
            response = self._make_request(messages)
            
            if response.get('success', False):
                commands = self._parse_command_response(response['content'])
                return {
                    "step_number": step_number,
                    "step_description": step.get('description', ''),
                    "commands": commands,
                    "success": True
                }
            else:
                return {
                    "step_number": step_number,
                    "step_description": step.get('description', ''),
                    "commands": [],
                    "success": False,
                    "error": response.get('error', 'Failed to generate commands')
                }
                
        except Exception as e:
            log_error(f"Error generating commands for step {step_number}: {e}")
            return {
                "step_number": step_number,
                "step_description": step.get('description', ''),
                "commands": [],
                "success": False,
                "error": str(e)
            }

    def _make_request(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """发送请求到大模型"""
        try:
            response = self.llm.invoke(messages)
            return {
                "success": True,
                "content": response.content
            }
        except Exception as e:
            log_error(f"Error making LLM request: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _build_testcase_analysis_prompt(self, testcase: Dict[str, Any]) -> str:
        """构建测试用例分析提示词"""
        testcase_info = f"""
测试用例编号: {testcase.get('Testcase_Number', 'N/A')}
测试用例名称: {testcase.get('Testcase_Name', 'N/A')}
测试等级: {testcase.get('Testcase_Level', 'N/A')}
前提条件: {testcase.get('Testcase_PrepareCondition', 'N/A')}
测试步骤: {testcase.get('Testcase_TestSteps', 'N/A')}
预期结果: {testcase.get('Testcase_ExpectedResult', 'N/A')}
测试类型: {testcase.get('Testcase_TestType', 'N/A')}
标签: {testcase.get('Testcase_Tags', 'N/A')}
"""
        
        prompt = f"""
请分析以下安全测试用例，并提供详细的测试步骤分解：

{testcase_info}

请按照以下要求进行分析：

1. **测试用例理解**：深入理解该测试用例的目的和测试目标
2. **详细步骤分解**：将原始测试步骤分解为更详细、可执行的子步骤
3. **技术要点识别**：识别每个步骤涉及的技术要点和安全风险点
4. **前置条件细化**：细化前置条件，明确测试环境需求
5. **验证点提取**：明确每个步骤的验证点和成功标准

请以JSON格式返回分析结果，包含以下字段：
- purpose: 测试目的
- security_focus: 安全关注点
- environment_requirements: 环境要求
- detailed_steps: 详细步骤列表，每个步骤包含description、technical_points、verification_points
- risk_assessment: 风险评估
"""
        return prompt

    def _build_command_generation_prompt(self, step: Dict[str, Any], step_number: int) -> str:
        """构建命令生成提示词"""
        prompt = f"""
针对以下测试步骤，请生成具体的执行命令和工具建议：

步骤 {step_number}: {step.get('description', '')}
技术要点: {step.get('technical_points', [])}
验证点: {step.get('verification_points', [])}

请提供：

1. **执行命令**：提供具体的命令行命令（如kubectl、docker、curl等）
2. **工具建议**：推荐使用的安全测试工具
3. **参数说明**：解释重要参数的作用
4. **安全注意事项**：执行过程中需要注意的安全事项
5. **结果验证**：如何验证命令执行结果

请以JSON格式返回，包含以下字段：
- commands: 命令列表，每个命令包含command、description、parameters、safety_notes
- tools: 推荐工具列表
- verification_methods: 验证方法
- expected_outputs: 预期输出示例
"""
        return prompt

    def _get_testcase_analysis_system_prompt(self) -> str:
        """获取测试用例分析的系统提示词"""
        return """
你是一位专业的安全测试专家，专门负责分析安全测试用例并生成详细的执行方案。

你的职责：
1. 深入理解测试用例的安全测试目标
2. 将抽象的测试步骤转换为具体可执行的操作
3. 识别每个步骤的技术实现要点
4. 提供清晰的验证标准

分析要求：
- 专业准确，基于安全测试最佳实践
- 步骤详细，具有可操作性
- 重点关注安全风险点和验证方法
- 考虑实际测试环境的复杂性

请严格按照要求的JSON格式返回结果。
"""

    def _get_command_generation_system_prompt(self) -> str:
        """获取命令生成的系统提示词"""
        return """
你是一位精通各种安全测试工具和命令的专家，专门负责为安全测试步骤生成具体的执行命令。

你的专长包括：
- Kubernetes/Docker容器安全测试
- 网络安全测试工具（nmap、ncat、curl等）
- 系统安全检查命令
- 安全扫描工具（如nuclei、nessus等）
- 渗透测试工具

生成命令的原则：
1. 命令必须具体可执行
2. 提供必要的参数说明
3. 考虑安全性，避免破坏性操作
4. 提供结果验证方法
5. 包含错误处理建议

请严格按照要求的JSON格式返回结果。
"""

    def _parse_testcase_analysis(self, content: str) -> Dict[str, Any]:
        """解析测试用例分析结果"""
        try:
            # 尝试提取JSON内容
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = content.strip()
            
            result = json.loads(json_str)
            
            # 验证必要字段
            required_fields = ['purpose', 'detailed_steps']
            for field in required_fields:
                if field not in result:
                    result[field] = f"未提供{field}信息"
            
            return result
            
        except json.JSONDecodeError as e:
            log_error(f"Failed to parse JSON analysis result: {e}")
            # 返回解析失败时的默认结构
            return {
                "purpose": "解析失败",
                "security_focus": ["解析失败"],
                "environment_requirements": ["解析失败"],
                "detailed_steps": [
                    {
                        "description": "原始内容解析失败，请查看原始响应",
                        "technical_points": ["解析失败"],
                        "verification_points": ["解析失败"]
                    }
                ],
                "risk_assessment": "解析失败",
                "original_response": content
            }

    def _parse_command_response(self, content: str) -> List[Dict[str, Any]]:
        """解析命令生成结果"""
        try:
            # 尝试提取JSON内容
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = content.strip()
            
            result = json.loads(json_str)
            
            # 提取命令列表
            commands = result.get('commands', [])
            if not isinstance(commands, list):
                commands = []
            
            return commands
            
        except json.JSONDecodeError as e:
            log_error(f"Failed to parse JSON command result: {e}")
            # 返回解析失败时的默认结构
            return [
                {
                    "command": "echo 'Command parsing failed'",
                    "description": "命令解析失败，请查看原始响应",
                    "parameters": [],
                    "safety_notes": ["解析失败"],
                    "original_response": content
                }
            ] 