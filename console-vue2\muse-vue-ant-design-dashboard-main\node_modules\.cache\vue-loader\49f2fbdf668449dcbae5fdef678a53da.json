{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue?vue&type=style&index=0&id=57e66c6a&scoped=true&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue", "mtime": 1751513794205}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DockerInfo.vue"], "names": [], "mappings": ";AAkuBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "DockerInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full docker-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"25\" height=\"25\"\r\n                viewBox=\"0 0 24 24\"\r\n                :class=\"`text-${sidebarColor}`\"\r\n            >\r\n              <path fill=\"currentColor\" d=\"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.docker') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <div class=\"container-runtime-tabs\">\r\n      <a-tabs v-model:activeKey=\"activeEngine\">\r\n        <a-tab-pane key=\"docker\" tab=\"Docker\">\r\n          <a-tabs v-model:activeKey=\"activeTab\" @change=\"handleTabChange\">\r\n            <a-tab-pane key=\"docker_host_config\" tab=\"Host Config\">\r\n              <div v-if=\"activeTab === 'docker_host_config'\" class=\"host-config-container\">\r\n                <a-row :gutter=\"[16, 16]\">\r\n                  <!-- Basic Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Basic Information\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Docker Version:</strong> {{ hostConfigData[0]?.docker_version }}</p>\r\n                      <p class=\"info-item\"><strong>Operating System:</strong> {{ getDaemonConfig('OperatingSystem') }}</p>\r\n                      <p class=\"info-item\"><strong>Architecture:</strong> {{ getDaemonConfig('Architecture') }}</p>\r\n                      <p class=\"info-item\"><strong>Kernel Version:</strong> {{ getDaemonConfig('KernelVersion') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Resource Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Resources\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>CPU Cores:</strong> {{ getDaemonConfig('NCPU') }}</p>\r\n                      <p class=\"info-item\"><strong>Total Memory:</strong> {{ formatBytes(getDaemonConfig('MemTotal')) }}</p>\r\n                      <p class=\"info-item\"><strong>Docker Root Dir:</strong> {{ getDaemonConfig('DockerRootDir') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Security Settings -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Security\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>User in Docker Group:</strong> {{ hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Root User:</strong> {{ hostConfigData[0]?.is_root_user ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Security Options:</strong> {{ getDaemonConfig('SecurityOptions')?.join(', ') || 'None' }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Container Statistics -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Container Statistics\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Total Containers:</strong> {{ getDaemonConfig('Containers') }}</p>\r\n                      <p class=\"info-item\"><strong>Running:</strong> {{ getDaemonConfig('ContainersRunning') }}</p>\r\n                      <p class=\"info-item\"><strong>Stopped:</strong> {{ getDaemonConfig('ContainersStopped') }}</p>\r\n                      <p class=\"info-item\"><strong>Images:</strong> {{ getDaemonConfig('Images') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Driver Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Storage Driver\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Driver:</strong> {{ getDaemonConfig('Driver') }}</p>\r\n                      <p class=\"info-item\"><strong>Logging Driver:</strong> {{ getDaemonConfig('LoggingDriver') }}</p>\r\n                      <p class=\"info-item\"><strong>Cgroup Driver:</strong> {{ getDaemonConfig('CgroupDriver') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Registry Configuration -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Registry Configuration\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Index Server:</strong> {{ getDaemonConfig('IndexServerAddress') }}</p>\r\n                      <p class=\"info-item\"><strong>Registry Mirrors:</strong></p>\r\n                      <ul>\r\n                        <li v-for=\"mirror in getRegistryMirrors()\" :key=\"mirror\" class=\"info-item\">{{ mirror }}</li>\r\n                      </ul>\r\n                    </a-card>\r\n                  </a-col>\r\n                </a-row>\r\n              </div>\r\n            </a-tab-pane>\r\n            <a-tab-pane key=\"docker_network\" tab=\"Networks\">\r\n              <a-table\r\n                :columns=\"networkColumns\"\r\n                :data-source=\"networkData\"\r\n                :rowKey=\"(record) => record.network_id\"\r\n                v-if=\"activeTab === 'docker_network'\"\r\n                :loading=\"loadingNetworks\"\r\n                :pagination=\"pagination\"\r\n              >\r\n              </a-table>\r\n            </a-tab-pane>\r\n\r\n            <a-tab-pane key=\"docker_container\" tab=\"Containers\">\r\n              <a-table\r\n                :columns=\"containerColumns\"\r\n                :data-source=\"containerData\"\r\n                :scroll=\"{ x: 1500 }\"\r\n                :pagination=\"{ pageSize: 20 }\"\r\n                :row-key=\"record => record.container_id\"\r\n                v-if=\"activeTab === 'docker_container'\"\r\n                :loading=\"loadingContainers\"\r\n              >\r\n                <template #mountsColumn=\"{ record }\">\r\n                  <div v-if=\"record\">\r\n                    <template v-if=\"record.mounts && record.mounts.length\">\r\n                      <div v-for=\"(mount, index) in record.mounts\" :key=\"index\">\r\n                        {{ mount.Source }} → {{ mount.Destination }}\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>N/A</span>\r\n                  </div>\r\n                </template>\r\n              </a-table>\r\n\r\n\r\n\r\n              <network-listening-modal\r\n                :visible=\"networkDetailsVisible\"\r\n                :network-data=\"selectedNetwork\"\r\n                @update:visible=\"networkDetailsVisible = $event\"\r\n                @close=\"handleNetworkDetailsClose\"\r\n              />\r\n            </a-tab-pane>\r\n          </a-tabs>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"crictl\" tab=\"CRI\">\r\n          <crictl-info :node-ip=\"selectedNodeIp\" />\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </div>\r\n\r\n    <a-modal\r\n      v-model:visible=\"mountDetailsVisible\"\r\n      title=\"Mount Details\"\r\n      width=\"800px\"\r\n      @cancel=\"handleMountDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleMountDetailsClose\">Cancel</a-button>\r\n      </template>\r\n      <template v-if=\"selectedMountContainer\">\r\n        <div class=\"mounts-container\">\r\n          <div v-for=\"(mount, index) in getAllMounts()\" :key=\"index\" class=\"mount-item\">\r\n            <div class=\"mount-path\">\r\n              <span class=\"mount-source\">{{ mount.Source }}</span>\r\n              <span class=\"mount-arrow\">→</span>\r\n              <span class=\"mount-dest\">{{ mount.Destination }}</span>\r\n            </div>\r\n            <div class=\"mount-details\">\r\n              <span v-if=\"mount.Mode\" class=\"mount-tag\">{{ mount.Mode }}</span>\r\n              <span class=\"mount-tag\">{{ mount.RW ? 'RW' : 'RO' }}</span>\r\n              <span v-if=\"mount.Propagation\" class=\"mount-tag\">{{ mount.Propagation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <a-modal\r\n      v-model:visible=\"environmentDetailsVisible\"\r\n      title=\"Environment Variables\"\r\n      width=\"800px\"\r\n      @cancel=\"handleEnvironmentDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleEnvironmentDetailsClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"selectedEnvironment\">\r\n        <div class=\"env-container\">\r\n          <div v-for=\"(env, index) in getAllEnvironmentVars()\" :key=\"index\" class=\"env-item\">\r\n            {{ env }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-table\r\n      :visible=\"processDetailsVisible\"\r\n      :process-container=\"selectedProcessContainer\"\r\n      user-field=\"uid\"\r\n      :include-tty=\"true\"\r\n      @update:visible=\"processDetailsVisible = $event\"\r\n      @close=\"handleProcessDetailsClose\"\r\n    />\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      user-field=\"uid\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n\r\n\r\n\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport CrictlInfo from './CrictlInfo.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\nimport { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index';\r\nimport { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index';\r\nimport { MountCell } from '../Widgets/Mount/mount_index';\r\n// 不再使用单独的环境变量组件\r\n// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    CrictlInfo,\r\n    JsonDetailModal,\r\n    ProcessDetailModal,\r\n    ProcessTable,\r\n    NetworkListeningModal,\r\n    NetworkListeningCell,\r\n    MountCell,\r\n    // EnvironmentCell,\r\n    // EnvironmentModal\r\n  },\r\n  name: 'DockerInfo',\r\n  props: {\r\n    // Using selectedNodeIp from Vuex store instead of prop\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'docker_host_config',\r\n      hostConfigData: [],\r\n      containerData: [],\r\n      networkData: [],\r\n      loadingHostConfig: false,\r\n      loadingContainers: false,\r\n      loadingNetworks: false,\r\n      hostConfigColumns: [\r\n        { title: 'Docker Version', dataIndex: 'docker_version', key: 'docker_version' },\r\n        {\r\n          title: 'Daemon Config',\r\n          dataIndex: 'daemon_config',\r\n          key: 'daemon_config',\r\n        },\r\n        {\r\n          title: 'In Docker Group',\r\n          dataIndex: 'user_in_docker_group',\r\n          key: 'user_in_docker_group',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n        {\r\n          title: 'Is Root',\r\n          dataIndex: 'is_root_user',\r\n          key: 'is_root_user',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n      ],\r\n      containerColumns: [\r\n        {\r\n          title: 'Container ID',\r\n          dataIndex: 'container_id',\r\n          key: 'container_id',\r\n          width: '150px',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'inspect_data',\r\n          key: 'name',\r\n          width: '15%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              return {\r\n                children: inspectData.Name || 'N/A',\r\n                props: {\r\n                  style: {\r\n                    whiteSpace: 'normal',\r\n                    wordBreak: 'break-word'\r\n                  }\r\n                }\r\n              };\r\n            } catch (e) {\r\n              console.warn('Failed to render container name:', e);\r\n              return 'N/A';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Privileged',\r\n          dataIndex: 'inspect_data',\r\n          key: 'privileged',\r\n          width: '100px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              const privileged = inspectData.HostConfig?.Privileged;\r\n              // 将undefined、null或非布尔值视为false\r\n              return privileged === true ? 'Yes' : 'No';\r\n            } catch (e) {\r\n              console.warn('Failed to render privileged status:', e);\r\n              return 'No'; // 出错时默认显示为No\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Environment',\r\n          dataIndex: 'env',\r\n          key: 'env',\r\n          width: '200px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const envVars = this.parseJsonField(record.env, []);\r\n              if (!envVars.length) return 'No environment variables';\r\n\r\n              return (\r\n                <div style=\"font-size: 12px;\">\r\n                  {envVars.slice(0, 1).map((env, index) => (\r\n                    <div key={index} style=\"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">\r\n                      {env.length > 200 ? env.substring(0, 200) + '...' : env}\r\n                    </div>\r\n                  ))}\r\n                  {envVars.length > 1 && (\r\n                    <a-button\r\n                      type=\"link\"\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        this.showEnvironmentDetails(record);\r\n                      }}\r\n                    >\r\n                      +{envVars.length - 1} more...\r\n                    </a-button>\r\n                  )}\r\n                </div>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render env vars:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Mounts',\r\n          dataIndex: 'mounts',\r\n          key: 'mounts',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <mount-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showMountDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Network Listening',\r\n          dataIndex: 'exposures',\r\n          key: 'exposures',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <network-listening-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showNetworkDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Processes',\r\n          dataIndex: 'processes',\r\n          key: 'processes',\r\n          width: '120px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const processData = this.parseJsonField(record.processes, {});\r\n              if (!processData?.process_list?.length) return 'N/A';\r\n\r\n              return (\r\n                <a onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetails(record);\r\n                }}>\r\n                  {processData.process_list.length} processes\r\n                </a>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render processes:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Inspect Data',\r\n          dataIndex: 'inspect_data',\r\n          key: 'inspect_data',\r\n          width: '150px',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <span style=\"display: inline-block; line-height: 22px;\">\r\n              <a\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showInspectDetails(record);\r\n                }}\r\n                style=\"color: #1890ff; cursor: pointer;\"\r\n              >\r\n                View Inspect\r\n              </a>\r\n            </span>\r\n          )\r\n        }\r\n      ],\r\n      networkColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '120px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Network ID',\r\n          dataIndex: 'network_id',\r\n          key: 'network_id',\r\n          width: '180px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Driver',\r\n          dataIndex: 'driver',\r\n          key: 'driver',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Scope',\r\n          dataIndex: 'scope',\r\n          key: 'scope',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'IPv6',\r\n          dataIndex: 'ipv6',\r\n          key: 'ipv6',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Internal',\r\n          dataIndex: 'internal',\r\n          key: 'internal',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Labels',\r\n          dataIndex: 'labels',\r\n          key: 'labels',\r\n          width: '120px',\r\n          customRender: (labels) => {\r\n            if (!labels || Object.keys(labels).length === 0) return 'None';\r\n            return Object.entries(labels).map(([key, value]) => `${key}=${value}`).join(', ');\r\n          }\r\n        },\r\n        {\r\n          title: 'Created',\r\n          dataIndex: 'created_at',\r\n          key: 'created_at',\r\n          width: '160px',\r\n          customRender: (text) => text || 'None'\r\n        }\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      resourceMapping: {\r\n        'docker_host_config': 'get_docker_host_config',\r\n        'docker_container': 'get_docker_containers',\r\n        'docker_network': 'get_docker_networks',\r\n      },\r\n\r\n\r\n\r\n      networkDetailsVisible: false,\r\n      selectedNetwork: null,\r\n      mountDetailsVisible: false,\r\n      selectedMountContainer: null,\r\n      environmentDetailsVisible: false,\r\n      selectedEnvironment: null,\r\n      processDetailsVisible: false,\r\n      selectedProcessContainer: null,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null,\r\n      activeEngine: 'docker'\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.resetData();\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      const dataName = this.camelCaseToDataName(resourceType);\r\n      const loadingKey = `loading${this.capitalizeFirstLetter(dataName.replace('Data', ''))}`;\r\n\r\n      this[loadingKey] = true;\r\n\r\n      try {\r\n        const response = await axios.get(`/api/docker/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[dataName] = resourceType === 'docker_host_config' && data ? [data] : data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[dataName] = [];\r\n      } finally {\r\n        this[loadingKey] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutDocker = camelCase.replace('docker_', '');\r\n      return withoutDocker.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.hostConfigData = [];\r\n      this.containerData = [];\r\n      this.networkData = [];\r\n    },\r\n    getDaemonConfig(key) {\r\n      if (!this.hostConfigData[0]?.daemon_config) return null;\r\n      return this.hostConfigData[0].daemon_config[key];\r\n    },\r\n    getRegistryMirrors() {\r\n      const registryConfig = this.getDaemonConfig('RegistryConfig');\r\n      return registryConfig?.Mirrors || [];\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    parseJsonField(field, defaultValue) {\r\n      try {\r\n        if (Array.isArray(field)) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        if (typeof field === 'object' && field !== null) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n      } catch (e) {\r\n        console.warn('Failed to parse JSON field:', e);\r\n        return defaultValue;\r\n      }\r\n    },\r\n\r\n    showInspectDetails(container) {\r\n      try {\r\n        const inspectData = this.parseJsonField(container.inspect_data, {});\r\n        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);\r\n      } catch (e) {\r\n        console.error('Failed to parse inspect data:', e);\r\n        this.$message.error('Failed to parse inspect data');\r\n      }\r\n    },\r\n    renderPreviewWithMore(items, renderItem, moreText, onClickMore) {\r\n      if (!items || !items.length) return 'None';\r\n\r\n      return (\r\n        <div style=\"font-size: 12px;\">\r\n          {items.slice(0, 2).map((item, index) => renderItem(item, index))}\r\n          {items.length > 2 && (\r\n            <a-button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onClickMore();\r\n              }}\r\n            >\r\n              +{items.length - 2} {moreText}\r\n            </a-button>\r\n          )}\r\n        </div>\r\n      );\r\n    },\r\n    cleanReactiveObject(obj) {\r\n      if (!obj) return null;\r\n      if (Array.isArray(obj)) {\r\n        return obj.map(item => this.cleanReactiveObject(item));\r\n      }\r\n      if (typeof obj === 'object') {\r\n        const cleaned = {};\r\n        for (const key in obj) {\r\n          if (!key.startsWith('_') && !key.startsWith('$') && key !== '__ob__') {\r\n            cleaned[key] = this.cleanReactiveObject(obj[key]);\r\n          }\r\n        }\r\n        return cleaned;\r\n      }\r\n      return obj;\r\n    },\r\n    getProcessList() {\r\n      if (!this.selectedProcessContainer?.processes?.process_list) return [];\r\n      return this.selectedProcessContainer.processes.process_list;\r\n    },\r\n    getNetworkListening() {\r\n      if (!this.selectedContainer?.exposures) return [];\r\n      const exposedServices = this.parseJsonField(this.selectedContainer.exposures, {});\r\n      return exposedServices.listening_ports || [];\r\n    },\r\n    showNetworkDetails(container) {\r\n      this.selectedNetwork = {\r\n        ...container,\r\n        exposures: this.parseJsonField(container.exposures, {})\r\n      };\r\n      this.networkDetailsVisible = true;\r\n    },\r\n    handleNetworkDetailsClose() {\r\n      this.networkDetailsVisible = false;\r\n      this.selectedNetwork = null;\r\n    },\r\n\r\n    showMountDetails(container) {\r\n      this.selectedMountContainer = {\r\n        ...container,\r\n        mounts: this.parseJsonField(container.mounts, [])\r\n      };\r\n      this.mountDetailsVisible = true;\r\n    },\r\n    handleMountDetailsClose() {\r\n      this.mountDetailsVisible = false;\r\n      this.selectedMountContainer = null;\r\n    },\r\n    getAllMounts() {\r\n      if (!this.selectedMountContainer?.mounts) return [];\r\n      return this.selectedMountContainer.mounts;\r\n    },\r\n    showEnvironmentDetails(container) {\r\n      this.selectedEnvironment = {\r\n        ...container,\r\n        env: this.parseJsonField(container.env, [])\r\n      };\r\n      this.environmentDetailsVisible = true;\r\n    },\r\n    handleEnvironmentDetailsClose() {\r\n      this.environmentDetailsVisible = false;\r\n      this.selectedEnvironment = null;\r\n    },\r\n    getAllEnvironmentVars() {\r\n      if (!this.selectedEnvironment?.env) return [];\r\n      return this.selectedEnvironment.env;\r\n    },\r\n    showProcessDetails(container) {\r\n      this.selectedProcessContainer = {\r\n        ...container,\r\n        processes: this.parseJsonField(container.processes, {})\r\n      };\r\n      this.processDetailsVisible = true;\r\n    },\r\n    handleProcessDetailsClose() {\r\n      this.processDetailsVisible = false;\r\n      this.selectedProcessContainer = null;\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.docker-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.docker-title {\r\n  color: #333;\r\n  font-size: 16px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n// 表格样式会从全局样式继承，不需要在这里硬编码\r\n\r\n.host-config-container {\r\n  padding: 24px;\r\n}\r\n\r\n.ant-card {\r\n  height: 100%;\r\n}\r\n\r\n.ant-card-head {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\nul {\r\n  padding-left: 20px;\r\n  margin: 0;\r\n}\r\n\r\nli {\r\n  word-break: break-all;\r\n}\r\n\r\n.ant-descriptions-bordered .ant-descriptions-item-label {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n\r\npre {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  margin: 4px 0;\r\n}\r\n\r\n/* 添加mount项的样式 */\r\n.mount-item {\r\n  padding: 2px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.mounts-container {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mount-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.mount-path {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mount-source {\r\n  color: #1890ff;\r\n}\r\n\r\n.mount-arrow {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.mount-dest {\r\n  color: #52c41a;\r\n}\r\n\r\n.mount-details {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mount-tag {\r\n  background: #f5f5f5;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.ant-collapse-panel {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.ant-tag {\r\n  margin: 2px;\r\n}\r\n\r\n.ant-collapse-content {\r\n  background: #fafafa;\r\n}\r\n\r\n.env-container {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.env-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  word-break: break-all;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.ant-descriptions {\r\n  .ant-descriptions-item-label {\r\n    width: 180px;\r\n    background-color: #fafafa;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .ant-descriptions-item-content {\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.details-container {\r\n  width: 100%;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 4px;\r\n}\r\n\r\n.details-row {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.details-header {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-item {\r\n  padding: 12px 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.details-content {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n}\r\n\r\n.content-item {\r\n  padding: 12px 16px;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n  word-break: break-word;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.container-runtime-tabs {\r\n  padding: 24px;\r\n}\r\n\r\n/* 确保标签紧跟在标题下方 */\r\n:deep(.ant-tabs) {\r\n  margin-top: 0;\r\n}\r\n\r\n:deep(.ant-card-head) {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.inspect-data-pre, .detail-pre {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n}\r\n\r\n/* 确保Tab内容区域没有背景色 */\r\n.ant-tabs-tabpane {\r\n  background-color: transparent !important;\r\n}\r\n\r\n\r\n\r\n/* 信息项样式 */\r\n.info-item {\r\n  margin-bottom: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px dashed #f0f0f0;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    padding-bottom: 0;\r\n    border-bottom: none;\r\n  }\r\n\r\n  strong {\r\n    margin-right: 8px;\r\n    color: #555;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}