{"version": 3, "sources": ["webpack:///./src/components/Cards/TestCaseInfo.vue?0a0f", "webpack:///./src/views/TestCase.vue", "webpack:///./src/components/Cards/TestCaseInfo.vue", "webpack:///src/components/Cards/TestCaseInfo.vue", "webpack:///./src/components/Cards/TestCaseInfo.vue?0a77", "webpack:///./src/components/Cards/TestCaseInfo.vue?3468", "webpack:///src/views/TestCase.vue", "webpack:///./src/views/TestCase.vue?9fb7", "webpack:///./src/views/TestCase.vue?7989", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "$event", "fetchTestcases", "currentPage", "proxy", "preventDefault", "handleSearch", "apply", "arguments", "model", "value", "searchForm", "name", "callback", "$$v", "$set", "expression", "staticStyle", "level", "prepare_condition", "test_steps", "expected_result", "loading", "resetSearch", "testcases", "length", "total", "_e", "columns", "pageSize", "current", "showSizeChanger", "showQuickJumper", "onChange", "handlePageChange", "x", "text", "getResultColor", "getLevelColor", "formatDate", "record", "viewDetails", "detailsVisible", "selectedTestcase", "Testcase_Number", "Testcase_Name", "Testcase_Level", "Testcase_PrepareCondition", "Testcase_TestSteps", "Testcase_ExpectedResult", "components", "RefreshButton", "data", "h", "$createElement", "undefined", "title", "dataIndex", "width", "align", "customRender", "index", "ellipsis", "click", "slots", "created", "computed", "mapState", "methods", "page", "params", "page_size", "response", "axios", "get", "error", "console", "$message", "date", "moment", "format", "result", "colors", "component", "TestCaseInfo", "$emit", "props", "type", "String", "default"], "mappings": "kHAAA,W,gECAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,iBAAiB,IAAI,IAAI,IAEzMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,UAAW,GAAOG,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACR,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACS,MAAM,QAAQX,EAAIY,aAAeR,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,8PAA8PF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIa,GAAGb,EAAIc,GAAGd,EAAIe,GAAG,4BAA4Bb,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACc,GAAG,CAAC,QAAU,SAASC,GAAQ,OAAOjB,EAAIkB,eAAelB,EAAImB,kBAAkB,OAAOC,OAAM,MAAS,CAAClB,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,UAAUY,GAAG,CAAC,OAAS,SAASC,GAAgC,OAAxBA,EAAOI,iBAAwBrB,EAAIsB,aAAaC,MAAM,KAAMC,cAAc,CAACtB,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,iBAAiB,WAAa,IAAIqB,MAAM,CAACC,MAAO1B,EAAI2B,WAAWC,KAAMC,SAAS,SAAUC,GAAM9B,EAAI+B,KAAK/B,EAAI2B,WAAY,OAAQG,IAAME,WAAW,sBAAsB,GAAG9B,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,WAAW,CAAC+B,YAAY,CAAC,MAAQ,SAAS7B,MAAM,CAAC,YAAc,eAAe,WAAa,IAAIqB,MAAM,CAACC,MAAO1B,EAAI2B,WAAWO,MAAOL,SAAS,SAAUC,GAAM9B,EAAI+B,KAAK/B,EAAI2B,WAAY,QAASG,IAAME,WAAW,qBAAqB,CAAC9B,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIa,GAAG,aAAaX,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIa,GAAG,aAAaX,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIa,GAAG,aAAaX,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIa,GAAG,aAAaX,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACJ,EAAIa,GAAG,cAAc,IAAI,GAAGX,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,sBAAsB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,8BAA8B,WAAa,IAAIqB,MAAM,CAACC,MAAO1B,EAAI2B,WAAWQ,kBAAmBN,SAAS,SAAUC,GAAM9B,EAAI+B,KAAK/B,EAAI2B,WAAY,oBAAqBG,IAAME,WAAW,mCAAmC,GAAG9B,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,eAAe,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,uBAAuB,WAAa,IAAIqB,MAAM,CAACC,MAAO1B,EAAI2B,WAAWS,WAAYP,SAAS,SAAUC,GAAM9B,EAAI+B,KAAK/B,EAAI2B,WAAY,aAAcG,IAAME,WAAW,4BAA4B,GAAG9B,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,oBAAoB,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,4BAA4B,WAAa,IAAIqB,MAAM,CAACC,MAAO1B,EAAI2B,WAAWU,gBAAiBR,SAAS,SAAUC,GAAM9B,EAAI+B,KAAK/B,EAAI2B,WAAY,kBAAmBG,IAAME,WAAW,iCAAiC,GAAG9B,EAAG,cAAc,CAACA,EAAG,WAAW,CAACS,MAAM,MAAMX,EAAIY,aAAeqB,YAAY,CAAC,MAAQ,SAAS7B,MAAM,CAAC,YAAY,SAAS,QAAUJ,EAAIsC,UAAU,CAACpC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIa,GAAG,aAAa,GAAGX,EAAG,WAAW,CAAC+B,YAAY,CAAC,cAAc,OAAOjB,GAAG,CAAC,MAAQhB,EAAIuC,cAAc,CAACrC,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAIa,GAAG,YAAY,IAAI,IAAI,GAAIb,EAAIwC,UAAUC,OAAS,EAAGvC,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIa,GAAG,UAAUb,EAAIc,GAAGd,EAAI0C,OAAO,kBAAkB,GAAG1C,EAAI2C,MAAM,GAAGzC,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI4C,QAAQ,cAAc5C,EAAIwC,UAAU,QAAUxC,EAAIsC,QAAQ,WAAa,CACnhHI,MAAO1C,EAAI0C,MACXG,SAAU,IACVC,QAAS9C,EAAImB,YACb4B,iBAAiB,EACjBC,iBAAiB,EACjBC,SAAUjD,EAAIkD,kBACd,OAAS,CAAEC,EAAG,OAAQ5C,YAAYP,EAAIQ,GAAG,CAAC,CAACC,IAAI,sBAAsBC,GAAG,UAAS,KAAE0C,IAAQ,MAAO,CAAClD,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIqD,eAAeD,KAAQ,CAACpD,EAAIa,GAAG,IAAIb,EAAIc,GAAGsC,GAAQ,OAAO,UAAU,CAAC3C,IAAI,iBAAiBC,GAAG,UAAS,KAAE0C,IAAQ,MAAO,CAAClD,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIsD,cAAcF,KAAQ,CAACpD,EAAIa,GAAG,IAAIb,EAAIc,GAAGsC,GAAQ,OAAO,UAAU,CAAC3C,IAAI,eAAeC,GAAG,UAAS,KAAE0C,IAAQ,MAAO,CAACpD,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAIuD,WAAWH,IAAO,QAAQ,CAAC3C,IAAI,SAASC,GAAG,UAAS,OAAE8C,IAAU,MAAO,CAACtD,EAAG,UAAU,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOjB,EAAIyD,YAAYD,MAAW,CAACxD,EAAIa,GAAG,qBAAqB,UAAUX,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,oBAAoB,MAAQ,QAAQ,OAAS,MAAMqB,MAAM,CAACC,MAAO1B,EAAI0D,eAAgB7B,SAAS,SAAUC,GAAM9B,EAAI0D,eAAe5B,GAAKE,WAAW,mBAAmB,CAAEhC,EAAI2D,iBAAkBzD,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAW,KAAK,CAACF,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,mBAAmB,KAAO,MAAM,CAACF,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACL,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBC,iBAAiB,SAAS1D,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,MAAM,CAACF,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACL,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBE,eAAe,SAAS3D,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,QAAQ,KAAO,MAAM,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIsD,cAActD,EAAI2D,iBAAiBG,kBAAkB,CAAC9D,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBG,gBAAgB,QAAQ,GAAG5D,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,oBAAoB,KAAO,MAAM,CAACF,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACL,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBI,2BAA2B,SAAS7D,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,aAAa,KAAO,MAAM,CAACF,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACL,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBK,oBAAoB,SAAS9D,EAAG,sBAAsB,CAACE,MAAM,CAAC,MAAQ,kBAAkB,KAAO,MAAM,CAACF,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACL,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAI2D,iBAAiBM,yBAAyB,UAAU,GAAGjE,EAAI2C,MAAM,IAAI,IAAI,IAExgErC,EAAkB,G,yDC6IP,GACf4D,WAAA,CACAC,sBAEAvC,KAAA,YACAwC,OAAA,MAAAC,EAAA,KAAAC,eACA,OACAhC,SAAA,EACAE,UAAA,GACAE,MAAA,EACAvB,YAAA,EACAuC,gBAAA,EACAC,iBAAA,KACAhC,WAAA,CACAC,KAAA,GACAM,WAAAqC,EACApC,kBAAA,GACAC,WAAA,GACAC,gBAAA,IAEAO,QAAA,CACA,CACA4B,MAAA,IACAC,UAAA,QACAhE,IAAA,QACAiE,MAAA,IACAC,MAAA,SACAC,cAAAxB,EAAAI,EAAAqB,IACA,UAAA1D,YAAA,GAAA0D,EAAA,GAGA,CACAL,MAAA,eACAC,UAAA,kBACAhE,IAAA,kBACAiE,MAAA,IACAI,UAAA,EACAF,cAAAxB,EAAAI,IACAa,EAAA,eAAAU,IAAA,KAAAtB,YAAAD,IAAA,2CAAAJ,KAGA,CACAoB,MAAA,OACAC,UAAA,gBACAhE,IAAA,gBACAiE,MAAA,KAGA,CACAF,MAAA,QACAC,UAAA,iBACAhE,IAAA,iBACAiE,MAAA,IACAM,MAAA,CAAAJ,aAAA,mBAEA,CACAJ,MAAA,oBACAC,UAAA,4BACAhE,IAAA,4BACAiE,MAAA,IACAI,UAAA,GAEA,CACAN,MAAA,aACAC,UAAA,qBACAhE,IAAA,qBACAiE,MAAA,IACAI,UAAA,GAEA,CACAN,MAAA,kBACAC,UAAA,0BACAhE,IAAA,0BACAiE,MAAA,IACAI,UAAA,MAYAG,UACA,KAAA/D,kBAEAgE,SAAA,IACAC,eAAA,qDAEAC,QAAA,CACA,qBAAAC,EAAA,GACA,KAAA/C,SAAA,EACA,IAEA,MAAAgD,EAAA,CACAD,OACAE,UAAA,KAIA,KAAA5D,WAAAC,OAAA0D,EAAA1D,KAAA,KAAAD,WAAAC,MACA,KAAAD,WAAAO,QAAAoD,EAAApD,MAAA,KAAAP,WAAAO,OACA,KAAAP,WAAAQ,oBAAAmD,EAAAnD,kBAAA,KAAAR,WAAAQ,mBACA,KAAAR,WAAAS,aAAAkD,EAAAlD,WAAA,KAAAT,WAAAS,YACA,KAAAT,WAAAU,kBAAAiD,EAAAjD,gBAAA,KAAAV,WAAAU,iBAEA,MAAAmD,QAAAC,OAAAC,IAAA,kBAAAJ,WACA,KAAA9C,UAAAgD,EAAApB,UACA,KAAA1B,MAAA8C,EAAApB,KAAA1B,MACA,MAAAiD,GACAC,QAAAD,MAAA,4BAAAA,GACA,KAAAE,SAAAF,MAAA,6BACA,QACA,KAAArD,SAAA,IAKAhB,eACA,KAAAH,YAAA,EACA,KAAAD,eAAA,IAIAqB,cACA,KAAAZ,WAAA,CACAC,KAAA,GACAM,WAAAqC,EACApC,kBAAA,GACAC,WAAA,GACAC,gBAAA,IAEA,KAAAlB,YAAA,EACA,KAAAD,eAAA,IAEAqC,WAAAuC,GACA,OAAAA,EAAAC,IAAAD,GAAAE,OAAA,2BAEA3C,eAAA4C,GACA,MAAAC,EAAA,CACA,eACA,aACA,kBACA,qBAEA,OAAAA,EAAAD,IAAA,WAEA3C,cAAApB,GACA,MAAAgE,EAAA,CACA,gBACA,mBACA,kBACA,iBACA,oBAEA,OAAAA,EAAAhE,IAAA,WAEA,kBAAAsB,GACA,IACA,MAAAgC,QAAAC,OAAAC,IAAA,iBAAAlC,EAAAI,iBACA,KAAAD,iBAAA6B,EAAApB,KACA,KAAAV,gBAAA,EACA,MAAAiC,GACAC,QAAAD,MAAA,mCAAAA,GACA,KAAAE,SAAAF,MAAA,sCAGAzC,iBAAAmC,GACA,KAAAlE,YAAAkE,EACA,KAAAnE,eAAAmE,MClUoW,I,wBCQhWc,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACfjC,WAAA,CACAkC,iBCjBiV,ICO7U,EAAY,eACd,EACArG,EACAO,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACS,MAAM,CAAC,iBAAkB,QAAQX,EAAIY,cAAgBR,MAAM,CAAC,KAAO,UAAUY,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOjB,EAAIqG,MAAM,cAAc,CAACrG,EAAIa,GAAG,IAAIb,EAAIc,GAAGd,EAAIoD,MAAQpD,EAAIe,GAAG,mBAAmB,QAEhRT,EAAkB,G,YCWP,GACf4E,SAAA,IACAC,eAAA,mBAEAvD,KAAA,gBACA0E,MAAA,CACAlD,KAAA,CACAmD,KAAAC,OACAC,QAAA,MCrBqW,I,YCOjWN,EAAY,eACd,EACApG,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAA6F,E", "file": "static/js/chunk-efc8f8a6.ab2cc752.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=style&index=0&id=6e3c2172&prod&lang=scss&scoped=true\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('TestCaseInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"layout-content\"},[_c('a-card',{staticClass:\"criclebox\",attrs:{\"bordered\":false},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 448 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.testcase')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":function($event){return _vm.fetchTestcases(_vm.currentPage)}}})],1)])]},proxy:true}])},[_c('div',{staticClass:\"search-form\"},[_c('a-form',{attrs:{\"layout\":\"inline\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.handleSearch.apply(null, arguments)}}},[_c('a-form-item',{attrs:{\"label\":\"Name\"}},[_c('a-input',{attrs:{\"placeholder\":\"Search by name\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.name),callback:function ($$v) {_vm.$set(_vm.searchForm, \"name\", $$v)},expression:\"searchForm.name\"}})],1),_c('a-form-item',{attrs:{\"label\":\"Level\"}},[_c('a-select',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"Select level\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.level),callback:function ($$v) {_vm.$set(_vm.searchForm, \"level\", $$v)},expression:\"searchForm.level\"}},[_c('a-select-option',{attrs:{\"value\":\"level 0\"}},[_vm._v(\"Level 0\")]),_c('a-select-option',{attrs:{\"value\":\"level 1\"}},[_vm._v(\"Level 1\")]),_c('a-select-option',{attrs:{\"value\":\"level 2\"}},[_vm._v(\"Level 2\")]),_c('a-select-option',{attrs:{\"value\":\"level 3\"}},[_vm._v(\"Level 3\")]),_c('a-select-option',{attrs:{\"value\":\"level 4\"}},[_vm._v(\"Level 4\")])],1)],1),_c('a-form-item',{attrs:{\"label\":\"Prepare Condition\"}},[_c('a-input',{attrs:{\"placeholder\":\"Search in prepare condition\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.prepare_condition),callback:function ($$v) {_vm.$set(_vm.searchForm, \"prepare_condition\", $$v)},expression:\"searchForm.prepare_condition\"}})],1),_c('a-form-item',{attrs:{\"label\":\"Test Steps\"}},[_c('a-input',{attrs:{\"placeholder\":\"Search in test steps\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.test_steps),callback:function ($$v) {_vm.$set(_vm.searchForm, \"test_steps\", $$v)},expression:\"searchForm.test_steps\"}})],1),_c('a-form-item',{attrs:{\"label\":\"Expected Result\"}},[_c('a-input',{attrs:{\"placeholder\":\"Search in expected result\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.expected_result),callback:function ($$v) {_vm.$set(_vm.searchForm, \"expected_result\", $$v)},expression:\"searchForm.expected_result\"}})],1),_c('a-form-item',[_c('a-button',{class:`bg-${_vm.sidebarColor}`,staticStyle:{\"color\":\"white\"},attrs:{\"html-type\":\"submit\",\"loading\":_vm.loading}},[_c('a-icon',{attrs:{\"type\":\"search\"}}),_vm._v(\" Search \")],1),_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},on:{\"click\":_vm.resetSearch}},[_c('a-icon',{attrs:{\"type\":\"reload\"}}),_vm._v(\" Reset \")],1)],1)],1),(_vm.testcases.length > 0)?_c('div',{staticClass:\"search-result-count\"},[_c('a-tag',{attrs:{\"color\":\"blue\"}},[_vm._v(\"Found: \"+_vm._s(_vm.total)+\" test cases\")])],1):_vm._e()],1),_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.testcases,\"loading\":_vm.loading,\"pagination\":{\n        total: _vm.total,\n        pageSize: 100,\n        current: _vm.currentPage,\n        showSizeChanger: false,\n        showQuickJumper: true,\n        onChange: _vm.handlePageChange\n      },\"scroll\":{ x: 1500 }},scopedSlots:_vm._u([{key:\"Testcase_LastResult\",fn:function({ text }){return [_c('a-tag',{attrs:{\"color\":_vm.getResultColor(text)}},[_vm._v(\" \"+_vm._s(text || 'N/A')+\" \")])]}},{key:\"Testcase_Level\",fn:function({ text }){return [_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(text)}},[_vm._v(\" \"+_vm._s(text || 'N/A')+\" \")])]}},{key:\"lastModified\",fn:function({ text }){return [_vm._v(\" \"+_vm._s(_vm.formatDate(text))+\" \")]}},{key:\"action\",fn:function({ record }){return [_c('a-space',[_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":function($event){return _vm.viewDetails(record)}}},[_vm._v(\" View Details \")])],1)]}}])}),_c('a-modal',{attrs:{\"title\":\"Test Case Details\",\"width\":\"800px\",\"footer\":null},model:{value:(_vm.detailsVisible),callback:function ($$v) {_vm.detailsVisible=$$v},expression:\"detailsVisible\"}},[(_vm.selectedTestcase)?_c('a-descriptions',{attrs:{\"bordered\":\"\"}},[_c('a-descriptions-item',{attrs:{\"label\":\"Test Case Number\",\"span\":\"3\"}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_Number)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":\"Name\",\"span\":\"3\"}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_Name)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":\"Level\",\"span\":\"3\"}},[_c('a-tag',{attrs:{\"color\":_vm.getLevelColor(_vm.selectedTestcase.Testcase_Level)}},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_Level)+\" \")])],1),_c('a-descriptions-item',{attrs:{\"label\":\"Prepare Condition\",\"span\":\"3\"}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_PrepareCondition)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":\"Test Steps\",\"span\":\"3\"}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_TestSteps)+\" \")])]),_c('a-descriptions-item',{attrs:{\"label\":\"Expected Result\",\"span\":\"3\"}},[_c('div',{staticClass:\"testcase-content\"},[_vm._v(\" \"+_vm._s(_vm.selectedTestcase.Testcase_ExpectedResult)+\" \")])])],1):_vm._e()],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item label=\"Name\">\r\n            <a-input v-model=\"searchForm.name\" placeholder=\"Search by name\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Level\">\r\n            <a-select v-model=\"searchForm.level\" placeholder=\"Select level\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item label=\"Prepare Condition\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" placeholder=\"Search in prepare condition\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Test Steps\">\r\n            <a-input v-model=\"searchForm.test_steps\" placeholder=\"Search in test steps\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Expected Result\">\r\n            <a-input v-model=\"searchForm.expected_result\" placeholder=\"Search in expected result\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              Search\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              Reset\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <a-modal\r\n        v-model:visible=\"detailsVisible\"\r\n        title=\"Test Case Details\"\r\n        width=\"800px\"\r\n        :footer=\"null\"\r\n      >\r\n        <a-descriptions v-if=\"selectedTestcase\" bordered>\r\n          <a-descriptions-item label=\"Test Case Number\" span=\"3\">\r\n            <div class=\"testcase-content\">\r\n              {{ selectedTestcase.Testcase_Number }}\r\n            </div>\r\n          </a-descriptions-item>\r\n          <a-descriptions-item label=\"Name\" span=\"3\">\r\n            <div class=\"testcase-content\">\r\n              {{ selectedTestcase.Testcase_Name }}\r\n            </div>\r\n          </a-descriptions-item>\r\n          <a-descriptions-item label=\"Level\" span=\"3\">\r\n            <a-tag :color=\"getLevelColor(selectedTestcase.Testcase_Level)\">\r\n              {{ selectedTestcase.Testcase_Level }}\r\n            </a-tag>\r\n          </a-descriptions-item>\r\n          <a-descriptions-item label=\"Prepare Condition\" span=\"3\">\r\n            <div class=\"testcase-content\">\r\n              {{ selectedTestcase.Testcase_PrepareCondition }}\r\n            </div>\r\n          </a-descriptions-item>\r\n          <a-descriptions-item label=\"Test Steps\" span=\"3\">\r\n            <div class=\"testcase-content\">\r\n              {{ selectedTestcase.Testcase_TestSteps }}\r\n            </div>\r\n          </a-descriptions-item>\r\n          <a-descriptions-item label=\"Expected Result\" span=\"3\">\r\n            <div class=\"testcase-content\">\r\n              {{ selectedTestcase.Testcase_ExpectedResult }}\r\n            </div>\r\n          </a-descriptions-item>\r\n        </a-descriptions>\r\n      </a-modal>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: 'Test Case ID',\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Level',\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: 'Prepare Condition',\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Test Steps',\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Expected Result',\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        // {\r\n        //   title: 'Action',\r\n        //   key: 'action',\r\n        //   fixed: 'right',\r\n        //   width: 120,\r\n        //   slots: { customRender: 'action' },\r\n        // },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n    computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    async viewDetails(record) {\r\n      try {\r\n        const response = await axios.get(`/api/testcase/${record.Testcase_Number}`);\r\n        this.selectedTestcase = response.data;\r\n        this.detailsVisible = true;\r\n      } catch (error) {\r\n        console.error('Error fetching testcase details:', error);\r\n        this.$message.error('Failed to load test case details');\r\n      }\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n.testcase-content {\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  padding: 12px;\r\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: rgba(0, 0, 0, 0.75);\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #1890ff;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCaseInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCaseInfo.vue?vue&type=template&id=6e3c2172&scoped=true\"\nimport script from \"./TestCaseInfo.vue?vue&type=script&lang=js\"\nexport * from \"./TestCaseInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./TestCaseInfo.vue?vue&type=style&index=0&id=6e3c2172&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e3c2172\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<TestCaseInfo></TestCaseInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport TestCaseInfo from \"@/components/Cards/TestCaseInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        TestCaseInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCase.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestCase.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./TestCase.vue?vue&type=template&id=e7339f32\"\nimport script from \"./TestCase.vue?vue&type=script&lang=js\"\nexport * from \"./TestCase.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}