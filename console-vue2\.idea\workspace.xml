<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c4764e69-8a17-4f35-89bf-992033578a8a" name="Changes" comment="fix">
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HostConfig.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/HostConfig.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/SmartOrchestrationInfo.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Cards/SmartOrchestrationInfo.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSettingsDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSettingsDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSidebar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Sidebars/DashboardSidebar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Widgets/JsonDetailModal.vue" beforeDir="false" afterPath="$PROJECT_DIR$/muse-vue-ant-design-dashboard-main/src/components/Widgets/JsonDetailModal.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2z5IXw2cdzZMQIyfLf76bN1dirg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/_Projects_python/vue3&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c4764e69-8a17-4f35-89bf-992033578a8a" name="Changes" comment="" />
      <created>1751014550491</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751014550491</updated>
      <workItem from="1751014552976" duration="21393000" />
      <workItem from="1751619588116" duration="2209000" />
    </task>
    <task id="LOCAL-00001" summary="fix">
      <created>1751248871666</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751248871666</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix" />
    <option name="LAST_COMMIT_MESSAGE" value="fix" />
  </component>
</project>