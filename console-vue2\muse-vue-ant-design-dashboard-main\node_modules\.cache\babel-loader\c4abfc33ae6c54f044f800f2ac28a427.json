{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue", "mtime": 1751522841423}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "methods", "showDetailModal", "title", "data", "options", "h", "$createElement", "modalWidth", "Math", "min", "width", "window", "innerWidth", "contentHeight", "innerHeight", "ids", "search", "Date", "now", "counter", "theme", "isDarkTheme", "header", "contentElement", "String", "$root", "$confirm", "content", "okText", "icon", "cancelButtonProps", "style", "display", "class", "maskClosable", "getContainer", "document", "body", "append<PERSON><PERSON><PERSON>", "createElement", "setTimeout", "container", "getElementById", "JsonViewer", "render", "props", "deep", "Infinity", "showDoubleQuotes", "showLength", "showLineNumbers", "height", "overflow", "$mount", "$el", "searchInput", "counterElement", "matches", "currentMatchIndex", "highlightMatches", "searchTerm", "textContent", "jsonNodes", "querySelectorAll", "regex", "RegExp", "replace", "for<PERSON>ach", "el", "classList", "remove", "node", "text", "match", "lastIndex", "exec", "push", "index", "length", "add", "error", "console", "navigateToMatch", "max", "currentMatch", "parent", "parentElement", "contains", "expandBtn", "querySelector", "click", "scrollIntoView", "behavior", "block", "searchTimeout", "addEventListener", "e", "clearTimeout", "target", "value", "trim", "key", "preventDefault", "shift<PERSON>ey", "themeButton", "backgroundColor", "copyButton", "textToCopy", "JSON", "stringify", "navigator", "clipboard", "isSecureContext", "writeText", "then", "$message", "success", "$t", "catch", "err", "textArea", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/Widgets/JsonDetailModal.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 组件不直接渲染模态框，而是提供方法 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport VueJsonPretty from 'vue-json-pretty';\r\n\r\nexport default {\r\n  name: 'JsonDetailModal',\r\n  methods: {\r\n    /**\r\n     * 显示JSON详情模态框\r\n     * @param {string} title 模态框标题\r\n     * @param {object|string} data 要显示的数据\r\n     * @param {object} options 配置选项\r\n     */\r\n    showDetailModal(title, data, options = {}) {\r\n      // 计算响应式尺寸\r\n      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);\r\n      const contentHeight = Math.min(700, window.innerHeight * 0.6);\r\n\r\n      // 生成唯一ID\r\n      const ids = {\r\n        search: `search-${Date.now()}`,\r\n        counter: `counter-${Date.now()}`,\r\n        theme: `theme-${Date.now()}`\r\n      };\r\n\r\n      // 默认使用暗色主题\r\n      let isDarkTheme = true;\r\n\r\n      // 创建标题、搜索框和复制按钮\r\n      const header = (\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <a-icon type=\"code\" style=\"margin-right: 8px; font-size: 16px;\" />\r\n            <span style=\"font-weight: 500;\">{title}</span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <div id={ids.counter} style=\"margin-right: 10px; min-width: 60px; text-align: right; color: #666;\"></div>\r\n            <a-input\r\n              id={ids.search}\r\n              placeholder=\"搜索 (Enter: ↓  Shift+Enter: ↑)\"\r\n              allowClear\r\n              prefix={<a-icon type=\"search\" style=\"color: rgba(0,0,0,.25)\" />}\r\n              style=\"width: 250px;\"\r\n            />\r\n            <a-button\r\n              id={ids.theme}\r\n              type=\"link\"\r\n              icon=\"bg-colors\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"切换主题\"\r\n            />\r\n            <a-button\r\n              id=\"copy-btn\"\r\n              type=\"link\"\r\n              icon=\"copy\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"复制内容\"\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n      // 准备内容元素\r\n      const contentElement = typeof data === 'object' ? (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class=\"json-container theme-dark\" id=\"json-container\">\r\n        </div>\r\n      ) : (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>\r\n          {String(data)}\r\n        </div>\r\n      );\r\n\r\n      // 创建模态框\r\n      this.$root.$confirm({\r\n        title: header,\r\n        content: contentElement,\r\n        width: modalWidth,\r\n        okText: options.okText || '关闭',\r\n        icon: null,\r\n        cancelButtonProps: { style: { display: 'none' } },\r\n        class: 'detail-modal',\r\n        maskClosable: false, // 防止点击外部关闭\r\n        getContainer: () => document.body.appendChild(document.createElement('div'))\r\n      });\r\n\r\n      // 在模态框内容区域渲染VueJsonPretty组件\r\n      setTimeout(() => {\r\n        if (typeof data === 'object') {\r\n          // 查找JSON容器\r\n          const container = document.getElementById('json-container');\r\n          if (container) {\r\n            // 创建VueJsonPretty组件实例\r\n            const JsonViewer = new Vue({\r\n              render: h => h(VueJsonPretty, {\r\n                props: {\r\n                  data: data,\r\n                  deep: Infinity, // 设置为Infinity以默认展开所有节点\r\n                  showDoubleQuotes: true,\r\n                  showLength: true,\r\n                  showLineNumbers: true,  // 添加行号显示\r\n                },\r\n                style: {\r\n                  height: '100%',\r\n                  overflow: 'auto'\r\n                }\r\n              })\r\n            });\r\n\r\n            // 挂载组件\r\n            JsonViewer.$mount();\r\n            container.appendChild(JsonViewer.$el);\r\n          }\r\n        }\r\n\r\n        // 获取搜索相关元素\r\n        const searchInput = document.getElementById(ids.search);\r\n        const counterElement = document.getElementById(ids.counter);\r\n\r\n        // 搜索功能变量\r\n        let matches = [];\r\n        let currentMatchIndex = -1;\r\n\r\n        // 如果有搜索框，添加搜索功能\r\n        if (searchInput && counterElement) {\r\n          // 高亮匹配项函数\r\n          const highlightMatches = (searchTerm) => {\r\n            // 重置匹配\r\n            matches = [];\r\n            currentMatchIndex = -1;\r\n\r\n            // 清除计数器\r\n            counterElement.textContent = '';\r\n\r\n            if (!searchTerm) return;\r\n\r\n            try {\r\n              // 查找所有键和值节点\r\n              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');\r\n\r\n              // 创建正则表达式\r\n              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\r\n\r\n              // 移除之前的高亮\r\n              document.querySelectorAll('.vjs-search-match').forEach(el => {\r\n                el.classList.remove('vjs-search-match');\r\n              });\r\n\r\n              document.querySelectorAll('.vjs-search-current').forEach(el => {\r\n                el.classList.remove('vjs-search-current');\r\n              });\r\n\r\n              // 查找匹配项\r\n              jsonNodes.forEach(node => {\r\n                const text = node.textContent;\r\n                let match;\r\n                regex.lastIndex = 0;\r\n\r\n                while ((match = regex.exec(text)) !== null) {\r\n                  matches.push({\r\n                    node: node,\r\n                    text: match[0]\r\n                  });\r\n\r\n                  // 防止无限循环\r\n                  if (match.index === regex.lastIndex) {\r\n                    regex.lastIndex++;\r\n                  }\r\n                }\r\n              });\r\n\r\n              // 更新计数器\r\n              if (matches.length === 0) {\r\n                counterElement.textContent = '无匹配项';\r\n                return;\r\n              }\r\n\r\n              counterElement.textContent = `0/${matches.length}`;\r\n\r\n              // 为所有匹配项添加高亮类\r\n              matches.forEach(match => {\r\n                match.node.classList.add('vjs-search-match');\r\n              });\r\n            } catch (error) {\r\n              console.error('搜索错误:', error);\r\n            }\r\n          };\r\n\r\n          // 导航到特定匹配项\r\n          const navigateToMatch = (index) => {\r\n            if (matches.length === 0) return;\r\n\r\n            // 确保索引在有效范围内\r\n            index = Math.max(0, Math.min(matches.length - 1, index));\r\n\r\n            // 移除之前匹配项的当前高亮\r\n            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {\r\n              matches[currentMatchIndex].node.classList.remove('vjs-search-current');\r\n            }\r\n\r\n            // 更新当前索引和计数器\r\n            currentMatchIndex = index;\r\n            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;\r\n\r\n            // 高亮当前匹配项\r\n            const currentMatch = matches[currentMatchIndex];\r\n            if (currentMatch) {\r\n              currentMatch.node.classList.add('vjs-search-current');\r\n\r\n              // 确保父节点是展开的\r\n              let parent = currentMatch.node.parentElement;\r\n              while (parent) {\r\n                if (parent.classList && parent.classList.contains('vjs-tree-node')) {\r\n                  // 如果节点是折叠的，点击展开按钮\r\n                  if (!parent.classList.contains('is-expanded')) {\r\n                    const expandBtn = parent.querySelector('.vjs-tree-brackets');\r\n                    if (expandBtn) expandBtn.click();\r\n                  }\r\n                }\r\n                parent = parent.parentElement;\r\n              }\r\n\r\n              // 滚动到匹配项\r\n              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          };\r\n\r\n          // 添加搜索输入事件\r\n          let searchTimeout;\r\n          searchInput.addEventListener('input', (e) => {\r\n            // 使用防抖优化性能\r\n            if (searchTimeout) clearTimeout(searchTimeout);\r\n            searchTimeout = setTimeout(() => {\r\n              highlightMatches(e.target.value.trim());\r\n            }, 300);\r\n          });\r\n\r\n          // 添加键盘导航事件\r\n          searchInput.addEventListener('keydown', (e) => {\r\n            if (e.key === 'Enter') {\r\n              e.preventDefault();\r\n              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加主题切换功能\r\n        const themeButton = document.getElementById(ids.theme);\r\n        if (themeButton) {\r\n          themeButton.addEventListener('click', () => {\r\n            const container = document.querySelector('.json-container');\r\n            if (container) {\r\n              // 切换主题类\r\n              if (container.classList.contains('theme-light')) {\r\n                container.classList.remove('theme-light');\r\n                container.classList.add('theme-dark');\r\n                container.style.backgroundColor = '#1e1e1e';\r\n                isDarkTheme = true;\r\n              } else {\r\n                container.classList.remove('theme-dark');\r\n                container.classList.add('theme-light');\r\n                container.style.backgroundColor = '#fff';\r\n                isDarkTheme = false;\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加复制功能\r\n        const copyButton = document.getElementById('copy-btn');\r\n        if (copyButton) {\r\n          copyButton.addEventListener('click', () => {\r\n            try {\r\n              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);\r\n\r\n              // 使用现代Clipboard API\r\n              if (navigator.clipboard && window.isSecureContext) {\r\n                navigator.clipboard.writeText(textToCopy)\r\n                  .then(() => {\r\n                    this.$message.success(this.$t('common.copiedToClipboard'));\r\n                  })\r\n                  .catch(err => {\r\n                    console.error('复制失败:', err);\r\n                    this.$message.error('复制失败');\r\n                  });\r\n              } else {\r\n                // 备用方法\r\n                const textArea = document.createElement('textarea');\r\n                textArea.value = textToCopy;\r\n                document.body.appendChild(textArea);\r\n                textArea.select();\r\n                const successful = document.execCommand('copy');\r\n                document.body.removeChild(textArea);\r\n\r\n                if (successful) {\r\n                  this.$message.success(this.$t('common.copiedToClipboard'));\r\n                } else {\r\n                  this.$message.error(this.$t('common.copyFailed'));\r\n                }\r\n              }\r\n            } catch (err) {\r\n              this.$message.error(this.$t('common.copyFailed'));\r\n            }\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 覆盖详情弹窗的样式 */\r\n.detail-modal {\r\n  .ant-modal-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-content {\r\n    margin-top: 12px !important;\r\n    margin-bottom: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-btns {\r\n    margin-top: 16px !important;\r\n    margin-bottom: 8px !important;\r\n  }\r\n\r\n  /* 复制按钮样式 */\r\n  #copy-btn {\r\n    transition: all 0.3s;\r\n\r\n    &:hover {\r\n      color: #40a9ff !important;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    &:active {\r\n      color: #096dd9 !important;\r\n    }\r\n  }\r\n\r\n  /* 搜索匹配项样式 */\r\n  .vjs-search-match {\r\n    background-color: rgba(255, 255, 0, 0.3);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .vjs-search-current {\r\n    background-color: rgba(255, 165, 0, 0.6);\r\n    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);\r\n  }\r\n\r\n  /* 主题样式 */\r\n  .json-container.theme-dark {\r\n    .vjs-tree {\r\n      background-color: #1e1e1e !important;\r\n      color: #d4d4d4 !important;\r\n\r\n      .vjs-key {\r\n        color: #9cdcfe !important; /* 浅蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-string {\r\n        color: #ce9178 !important; /* 橙红色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-number {\r\n        color: #b5cea8 !important; /* 浅绿色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-boolean {\r\n        color: #569cd6 !important; /* 蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-null {\r\n        color: #c586c0 !important; /* 紫色 */\r\n      }\r\n\r\n      .vjs-tree-brackets {\r\n        color: #d4d4d4 !important; /* 浅灰色 */\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 调整内容区域的左侧边距 */\r\n.ant-modal-confirm-body {\r\n  padding-left: 12px !important;\r\n}\r\n\r\n/* 调整内容区域的图标和文本间距 */\r\n.ant-modal-confirm-body > .anticon {\r\n  display: none !important;\r\n}\r\n\r\n/* 调整标题和内容的左侧边距 */\r\n.ant-modal-confirm-body > .ant-modal-confirm-title,\r\n.ant-modal-confirm-body > .ant-modal-confirm-content {\r\n  margin-left: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n/* 调整内容区域的下边距 */\r\n.detail-modal .ant-modal-confirm-content {\r\n  margin-bottom: 8px !important; /* 适当的下边距 */\r\n}\r\n\r\n/* 调整底部按钮区域的上下边距 */\r\n.detail-modal .ant-modal-confirm-btns {\r\n  margin-top: 8px !important; /* 减小上边距 */\r\n  margin-bottom: 4px !important;\r\n  padding-top: 0 !important; /* 移除上内边距 */\r\n  padding-bottom: 0 !important;\r\n  border-top: none !important; /* 移除分隔线 */\r\n}\r\n\r\n/* 调整底部按钮本身的样式 */\r\n.detail-modal .ant-modal-confirm-btns button {\r\n  margin-top: 0 !important;\r\n  margin-bottom: 0 !important;\r\n  padding: 6px 16px !important;\r\n  height: auto !important;\r\n}\r\n</style>\r\n"], "mappings": ";;AAOA,OAAAA,GAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,OAAA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC,gBAAAC,KAAA,EAAAC,IAAA,EAAAC,OAAA;MAAA,MAAAC,CAAA,QAAAC,cAAA;MACA;MACA,MAAAC,UAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAL,OAAA,CAAAM,KAAA,UAAAC,MAAA,CAAAC,UAAA;MACA,MAAAC,aAAA,GAAAL,IAAA,CAAAC,GAAA,MAAAE,MAAA,CAAAG,WAAA;;MAEA;MACA,MAAAC,GAAA;QACAC,MAAA,YAAAC,IAAA,CAAAC,GAAA;QACAC,OAAA,aAAAF,IAAA,CAAAC,GAAA;QACAE,KAAA,WAAAH,IAAA,CAAAC,GAAA;MACA;;MAEA;MACA,IAAAG,WAAA;;MAEA;MACA,MAAAC,MAAA,GAAAjB,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA;UAAA,QACA;QAAA;QAAA;MAAA,IAAAA,CAAA;QAAA,SACA;MAAA,IAAAH,KAAA,MAAAG,CAAA;QAAA,SAEA;MAAA,IAAAA,CAAA;QAAA;UAAA,MACAU,GAAA,CAAAI;QAAA;QAAA;MAAA,IAAAd,CAAA;QAAA;UAAA,MAEAU,GAAA,CAAAC,MAAA;UAAA,eACA;UAAA;UAAA,UAAAX,CAAA;YAAA;cAAA,QAEA;YAAA;YAAA;UAAA;QAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA;UAAA,MAGAU,GAAA,CAAAK,KAAA;UAAA,QACA;UAAA,QACA;UAAA,SAEA;QAAA;QAAA,SADA;MAAA,IAAAf,CAAA;QAAA;UAAA,MAIA;UAAA,QACA;UAAA,QACA;UAAA,SAEA;QAAA;QAAA,SADA;MAAA,MAKA;;MAEA;MACA,MAAAkB,cAAA,UAAApB,IAAA,gBAAAE,CAAA;QAAA,SACA,WAAAQ,aAAA;QAAA;QAAA;UAAA;QAAA;MAAA,KAAAR,CAAA;QAAA,SAGA,WAAAQ,aAAA;MAAA,IACAW,MAAA,CAAArB,IAAA,GAEA;;MAEA;MACA,KAAAsB,KAAA,CAAAC,QAAA;QACAxB,KAAA,EAAAoB,MAAA;QACAK,OAAA,EAAAJ,cAAA;QACAb,KAAA,EAAAH,UAAA;QACAqB,MAAA,EAAAxB,OAAA,CAAAwB,MAAA;QACAC,IAAA;QACAC,iBAAA;UAAAC,KAAA;YAAAC,OAAA;UAAA;QAAA;QACAC,KAAA;QACAC,YAAA;QAAA;QACAC,YAAA,EAAAA,CAAA,KAAAC,QAAA,CAAAC,IAAA,CAAAC,WAAA,CAAAF,QAAA,CAAAG,aAAA;MACA;;MAEA;MACAC,UAAA;QACA,WAAArC,IAAA;UACA;UACA,MAAAsC,SAAA,GAAAL,QAAA,CAAAM,cAAA;UACA,IAAAD,SAAA;YACA;YACA,MAAAE,UAAA,OAAA9C,GAAA;cACA+C,MAAA,EAAAvC,CAAA,IAAAA,CAAA,CAAAP,aAAA;gBACA+C,KAAA;kBACA1C,IAAA,EAAAA,IAAA;kBACA2C,IAAA,EAAAC,QAAA;kBAAA;kBACAC,gBAAA;kBACAC,UAAA;kBACAC,eAAA;gBACA;gBACAnB,KAAA;kBACAoB,MAAA;kBACAC,QAAA;gBACA;cACA;YACA;;YAEA;YACAT,UAAA,CAAAU,MAAA;YACAZ,SAAA,CAAAH,WAAA,CAAAK,UAAA,CAAAW,GAAA;UACA;QACA;;QAEA;QACA,MAAAC,WAAA,GAAAnB,QAAA,CAAAM,cAAA,CAAA3B,GAAA,CAAAC,MAAA;QACA,MAAAwC,cAAA,GAAApB,QAAA,CAAAM,cAAA,CAAA3B,GAAA,CAAAI,OAAA;;QAEA;QACA,IAAAsC,OAAA;QACA,IAAAC,iBAAA;;QAEA;QACA,IAAAH,WAAA,IAAAC,cAAA;UACA;UACA,MAAAG,gBAAA,GAAAC,UAAA;YACA;YACAH,OAAA;YACAC,iBAAA;;YAEA;YACAF,cAAA,CAAAK,WAAA;YAEA,KAAAD,UAAA;YAEA;cACA;cACA,MAAAE,SAAA,GAAA1B,QAAA,CAAA2B,gBAAA;;cAEA;cACA,MAAAC,KAAA,OAAAC,MAAA,CAAAL,UAAA,CAAAM,OAAA;;cAEA;cACA9B,QAAA,CAAA2B,gBAAA,sBAAAI,OAAA,CAAAC,EAAA;gBACAA,EAAA,CAAAC,SAAA,CAAAC,MAAA;cACA;cAEAlC,QAAA,CAAA2B,gBAAA,wBAAAI,OAAA,CAAAC,EAAA;gBACAA,EAAA,CAAAC,SAAA,CAAAC,MAAA;cACA;;cAEA;cACAR,SAAA,CAAAK,OAAA,CAAAI,IAAA;gBACA,MAAAC,IAAA,GAAAD,IAAA,CAAAV,WAAA;gBACA,IAAAY,KAAA;gBACAT,KAAA,CAAAU,SAAA;gBAEA,QAAAD,KAAA,GAAAT,KAAA,CAAAW,IAAA,CAAAH,IAAA;kBACAf,OAAA,CAAAmB,IAAA;oBACAL,IAAA,EAAAA,IAAA;oBACAC,IAAA,EAAAC,KAAA;kBACA;;kBAEA;kBACA,IAAAA,KAAA,CAAAI,KAAA,KAAAb,KAAA,CAAAU,SAAA;oBACAV,KAAA,CAAAU,SAAA;kBACA;gBACA;cACA;;cAEA;cACA,IAAAjB,OAAA,CAAAqB,MAAA;gBACAtB,cAAA,CAAAK,WAAA;gBACA;cACA;cAEAL,cAAA,CAAAK,WAAA,QAAAJ,OAAA,CAAAqB,MAAA;;cAEA;cACArB,OAAA,CAAAU,OAAA,CAAAM,KAAA;gBACAA,KAAA,CAAAF,IAAA,CAAAF,SAAA,CAAAU,GAAA;cACA;YACA,SAAAC,KAAA;cACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;YACA;UACA;;UAEA;UACA,MAAAE,eAAA,GAAAL,KAAA;YACA,IAAApB,OAAA,CAAAqB,MAAA;;YAEA;YACAD,KAAA,GAAArE,IAAA,CAAA2E,GAAA,IAAA3E,IAAA,CAAAC,GAAA,CAAAgD,OAAA,CAAAqB,MAAA,MAAAD,KAAA;;YAEA;YACA,IAAAnB,iBAAA,SAAAA,iBAAA,GAAAD,OAAA,CAAAqB,MAAA;cACArB,OAAA,CAAAC,iBAAA,EAAAa,IAAA,CAAAF,SAAA,CAAAC,MAAA;YACA;;YAEA;YACAZ,iBAAA,GAAAmB,KAAA;YACArB,cAAA,CAAAK,WAAA,MAAAH,iBAAA,QAAAD,OAAA,CAAAqB,MAAA;;YAEA;YACA,MAAAM,YAAA,GAAA3B,OAAA,CAAAC,iBAAA;YACA,IAAA0B,YAAA;cACAA,YAAA,CAAAb,IAAA,CAAAF,SAAA,CAAAU,GAAA;;cAEA;cACA,IAAAM,MAAA,GAAAD,YAAA,CAAAb,IAAA,CAAAe,aAAA;cACA,OAAAD,MAAA;gBACA,IAAAA,MAAA,CAAAhB,SAAA,IAAAgB,MAAA,CAAAhB,SAAA,CAAAkB,QAAA;kBACA;kBACA,KAAAF,MAAA,CAAAhB,SAAA,CAAAkB,QAAA;oBACA,MAAAC,SAAA,GAAAH,MAAA,CAAAI,aAAA;oBACA,IAAAD,SAAA,EAAAA,SAAA,CAAAE,KAAA;kBACA;gBACA;gBACAL,MAAA,GAAAA,MAAA,CAAAC,aAAA;cACA;;cAEA;cACAF,YAAA,CAAAb,IAAA,CAAAoB,cAAA;gBAAAC,QAAA;gBAAAC,KAAA;cAAA;YACA;UACA;;UAEA;UACA,IAAAC,aAAA;UACAvC,WAAA,CAAAwC,gBAAA,UAAAC,CAAA;YACA;YACA,IAAAF,aAAA,EAAAG,YAAA,CAAAH,aAAA;YACAA,aAAA,GAAAtD,UAAA;cACAmB,gBAAA,CAAAqC,CAAA,CAAAE,MAAA,CAAAC,KAAA,CAAAC,IAAA;YACA;UACA;;UAEA;UACA7C,WAAA,CAAAwC,gBAAA,YAAAC,CAAA;YACA,IAAAA,CAAA,CAAAK,GAAA;cACAL,CAAA,CAAAM,cAAA;cACApB,eAAA,CAAAc,CAAA,CAAAO,QAAA,GAAA7C,iBAAA,OAAAA,iBAAA;YACA;UACA;QACA;;QAEA;QACA,MAAA8C,WAAA,GAAApE,QAAA,CAAAM,cAAA,CAAA3B,GAAA,CAAAK,KAAA;QACA,IAAAoF,WAAA;UACAA,WAAA,CAAAT,gBAAA;YACA,MAAAtD,SAAA,GAAAL,QAAA,CAAAqD,aAAA;YACA,IAAAhD,SAAA;cACA;cACA,IAAAA,SAAA,CAAA4B,SAAA,CAAAkB,QAAA;gBACA9C,SAAA,CAAA4B,SAAA,CAAAC,MAAA;gBACA7B,SAAA,CAAA4B,SAAA,CAAAU,GAAA;gBACAtC,SAAA,CAAAV,KAAA,CAAA0E,eAAA;gBACApF,WAAA;cACA;gBACAoB,SAAA,CAAA4B,SAAA,CAAAC,MAAA;gBACA7B,SAAA,CAAA4B,SAAA,CAAAU,GAAA;gBACAtC,SAAA,CAAAV,KAAA,CAAA0E,eAAA;gBACApF,WAAA;cACA;YACA;UACA;QACA;;QAEA;QACA,MAAAqF,UAAA,GAAAtE,QAAA,CAAAM,cAAA;QACA,IAAAgE,UAAA;UACAA,UAAA,CAAAX,gBAAA;YACA;cACA,MAAAY,UAAA,UAAAxG,IAAA,gBAAAyG,IAAA,CAAAC,SAAA,CAAA1G,IAAA,aAAAqB,MAAA,CAAArB,IAAA;;cAEA;cACA,IAAA2G,SAAA,CAAAC,SAAA,IAAApG,MAAA,CAAAqG,eAAA;gBACAF,SAAA,CAAAC,SAAA,CAAAE,SAAA,CAAAN,UAAA,EACAO,IAAA;kBACA,KAAAC,QAAA,CAAAC,OAAA,MAAAC,EAAA;gBACA,GACAC,KAAA,CAAAC,GAAA;kBACAtC,OAAA,CAAAD,KAAA,UAAAuC,GAAA;kBACA,KAAAJ,QAAA,CAAAnC,KAAA;gBACA;cACA;gBACA;gBACA,MAAAwC,QAAA,GAAApF,QAAA,CAAAG,aAAA;gBACAiF,QAAA,CAAArB,KAAA,GAAAQ,UAAA;gBACAvE,QAAA,CAAAC,IAAA,CAAAC,WAAA,CAAAkF,QAAA;gBACAA,QAAA,CAAAC,MAAA;gBACA,MAAAC,UAAA,GAAAtF,QAAA,CAAAuF,WAAA;gBACAvF,QAAA,CAAAC,IAAA,CAAAuF,WAAA,CAAAJ,QAAA;gBAEA,IAAAE,UAAA;kBACA,KAAAP,QAAA,CAAAC,OAAA,MAAAC,EAAA;gBACA;kBACA,KAAAF,QAAA,CAAAnC,KAAA,MAAAqC,EAAA;gBACA;cACA;YACA,SAAAE,GAAA;cACA,KAAAJ,QAAA,CAAAnC,KAAA,MAAAqC,EAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}