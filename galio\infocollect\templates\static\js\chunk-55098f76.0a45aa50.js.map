{"version": 3, "sources": ["webpack:///./src/views/FileUpload.vue?6356", "webpack:///./node_modules/core-js/modules/es.array.reduce.js", "webpack:///./src/views/FileUpload.vue", "webpack:///src/views/FileUpload.vue", "webpack:///./src/views/FileUpload.vue?874f", "webpack:///./src/views/FileUpload.vue?44ef", "webpack:///./src/mixins/NotificationMixin.js", "webpack:///./src/components/common/ProxySelector.vue", "webpack:///src/components/common/ProxySelector.vue", "webpack:///./src/components/common/ProxySelector.vue?a770", "webpack:///./src/components/common/ProxySelector.vue?1de0", "webpack:///./node_modules/core-js/internals/engine-is-node.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.every.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.some.js", "webpack:///./node_modules/core-js/modules/esnext.iterator.reduce.js", "webpack:///./node_modules/core-js/internals/array-method-is-strict.js", "webpack:///./node_modules/core-js/internals/array-reduce.js"], "names": ["$", "$reduce", "left", "arrayMethodIsStrict", "CHROME_VERSION", "IS_NODE", "STRICT_METHOD", "CHROME_BUG", "target", "proto", "forced", "reduce", "callbackfn", "this", "arguments", "length", "undefined", "render", "_vm", "_c", "_self", "staticStyle", "staticClass", "class", "sidebarColor", "attrs", "_v", "_s", "$t", "uploading", "on", "handleUpload", "$event", "preventDefault", "apply", "beforeUpload", "fileName", "_e", "model", "value", "uploadDir", "callback", "$$v", "expression", "handleProxyChange", "selectedProxyIp", "nodes", "columns", "pageSize", "total", "showSizeChanger", "rowSelection", "uploadProgress", "progressBarStatus", "progressTableData", "progressColumns", "scopedSlots", "_u", "key", "fn", "text", "record", "error_detail", "slot", "time", "type", "message", "uploadResults", "uploadResultsData", "resultColumns", "staticRenderFns", "mixins", "NotificationMixin", "components", "ProxySelector", "data", "file", "selectedNodes", "activeTask", "pollInterval", "isProcessing", "computed", "mapState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "title", "dataIndex", "h", "$createElement", "width", "ellipsis", "customRender", "color", "status", "formatBytes", "bytes_transferred", "activeUploadTask", "metadata", "file_size", "Object", "keys", "map", "ip", "node", "host_name", "progress", "speed", "values", "totalProgress", "sum", "Math", "round", "some", "every", "formatSpeed", "totalSpeed", "parseSpeed", "formatOverallProgress", "_this$activeUploadTas", "totalTransferred", "totalSize", "results", "success", "for<PERSON>ach", "push", "failed", "error", "errors", "created", "checkDatabaseStatus", "$store", "dispatch", "taskInfo", "localStorage", "getItem", "currentProject", "taskId", "projectFile", "JSON", "parse", "checkActiveUploadTask", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "mapActions", "maxSize", "size", "$message", "name", "console", "log", "validateUploadPath", "path", "startsWith", "includes", "currentDbFile", "$router", "previousTaskInfo", "clearTaskNotificationMark", "e", "formData", "FormData", "append", "stringify", "response", "axios", "post", "encodeURIComponent", "headers", "task_id", "setItem", "removeItem", "startPolling", "_error$response", "messageType", "pollStatus", "Error", "get", "allCompleted", "addTaskCompletionNotification", "taskType", "projectId", "_error$response2", "setInterval", "taskCompleted", "bytes", "sizes", "i", "floor", "pow", "toFixed", "unit", "split", "multiplier", "parseFloat", "$notify", "watch", "handler", "newProject", "oldProject", "immediate", "component", "titles", "templates", "statusMapping", "notificationSent<PERSON>ey", "defaultStatusMapping", "failure", "finalStatusMapping", "successNodes", "filter", "failedNodes", "hasFailures", "notificationTitle", "notificationMessage", "getDefaultErrorTitle", "getDefaultSuccessTitle", "addNotification", "isDetecting", "disabled", "fetchReachableIps", "reachableIps", "selectedIpValue", "_l", "props", "Boolean", "default", "String", "newValue", "$emit", "reachable_ips", "classof", "global", "module", "exports", "process", "iterate", "aFunction", "anObject", "real", "stop", "IS_ITERATOR", "INTERRUPTED", "stopped", "reducer", "noInitial", "accumulator", "TypeError", "fails", "METHOD_NAME", "argument", "method", "call", "toObject", "IndexedObject", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_RIGHT", "that", "<PERSON><PERSON><PERSON><PERSON>", "memo", "O", "self", "index", "right"], "mappings": "kHAAA,W,oCCCA,IAAIA,EAAI,EAAQ,QACZC,EAAU,EAAQ,QAA6BC,KAC/CC,EAAsB,EAAQ,QAC9BC,EAAiB,EAAQ,QACzBC,EAAU,EAAQ,QAElBC,EAAgBH,EAAoB,UAGpCI,GAAcF,GAAWD,EAAiB,IAAMA,EAAiB,GAIrEJ,EAAE,CAAEQ,OAAQ,QAASC,OAAO,EAAMC,QAASJ,GAAiBC,GAAc,CACxEI,OAAQ,SAAgBC,GACtB,OAAOX,EAAQY,KAAMD,EAAYE,UAAUC,OAAQD,UAAUC,OAAS,EAAID,UAAU,QAAKE,O,2CChB7F,IAAIC,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,YAAY,CAAC,QAAU,QAAQ,CAACF,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACI,MAAM,QAAQL,EAAIM,aAAeC,MAAM,CAAC,MAAQ,6BAA6B,QAAU,cAAc,OAAS,KAAK,MAAQ,OAAO,CAACN,EAAG,OAAO,CAACM,MAAM,CAAC,KAAO,eAAe,EAAI,8cAA8cN,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,GAAG,8BAA8BT,EAAG,WAAW,CAACG,YAAY,mBAAmBG,MAAM,CAAC,QAAUP,EAAIW,WAAWC,GAAG,CAAC,MAAQZ,EAAIa,eAAe,CAACb,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,2BAA2B,QAAQ,GAAGT,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,SAAS,CAACG,YAAY,eAAeG,MAAM,CAAC,KAAO,UAAU,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,YAAYK,GAAG,CAAC,OAAS,SAASE,GAAgC,OAAxBA,EAAOC,iBAAwBf,EAAIa,aAAaG,MAAM,KAAMpB,cAAc,CAACK,EAAG,cAAc,CAACE,YAAY,CAAC,gBAAgB,OAAOI,MAAM,CAAC,MAAQP,EAAIU,GAAG,2BAA2B,CAACT,EAAG,WAAW,CAACM,MAAM,CAAC,gBAAgBP,EAAIiB,aAAa,oBAAmB,IAAQ,CAAChB,EAAG,WAAW,CAACG,YAAY,oBAAoB,CAACH,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,YAAYP,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,6BAA6B,MAAM,IAAI,GAAIV,EAAIkB,SAAUjB,EAAG,OAAO,CAACE,YAAY,CAAC,cAAc,QAAQ,CAACH,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIkB,aAAalB,EAAImB,MAAM,GAAGlB,EAAG,cAAc,CAACE,YAAY,CAAC,gBAAgB,OAAOI,MAAM,CAAC,MAAQP,EAAIU,GAAG,2BAA2B,CAACT,EAAG,UAAU,CAACM,MAAM,CAAC,YAAcP,EAAIU,GAAG,+BAA+BU,MAAM,CAACC,MAAOrB,EAAIsB,UAAWC,SAAS,SAAUC,GAAMxB,EAAIsB,UAAUE,GAAKC,WAAW,gBAAgB,IAAI,IAAI,GAAGxB,EAAG,SAAS,CAACG,YAAY,eAAeG,MAAM,CAAC,KAAO,QAAQ,MAAQP,EAAIU,GAAG,2BAA2B,CAACT,EAAG,iBAAiB,CAACM,MAAM,CAAC,SAAWP,EAAIW,WAAWC,GAAG,CAAC,OAASZ,EAAI0B,mBAAmBN,MAAM,CAACC,MAAOrB,EAAI2B,gBAAiBJ,SAAS,SAAUC,GAAMxB,EAAI2B,gBAAgBH,GAAKC,WAAW,sBAAsB,IAAI,GAAGxB,EAAG,MAAM,CAACG,YAAY,8BAA8B,CAACH,EAAG,SAAS,CAACG,YAAY,eAAeG,MAAM,CAAC,KAAO,QAAQ,MAAQP,EAAIU,GAAG,2BAA2B,CAACT,EAAG,UAAU,CAACG,YAAY,uBAAuBG,MAAM,CAAC,WAAaP,EAAI4B,MAAM,QAAU5B,EAAI6B,QAAQ,OAAS,KAAK,WAAa,CAC/sFC,SAAU,GACVC,MAAO/B,EAAI4B,MAAM/B,OACjBmC,iBAAiB,GACjB,aAAehC,EAAIiC,iBAAiB,IAAI,KAAKhC,EAAG,SAAS,CAACG,YAAY,eAAeD,YAAY,CAAC,aAAa,QAAQI,MAAM,CAAC,MAAQP,EAAIU,GAAG,+BAA+B,CAACT,EAAG,aAAa,CAACE,YAAY,CAAC,gBAAgB,QAAQI,MAAM,CAAC,QAAUP,EAAIkC,eAAe,OAASlC,EAAImC,qBAAqBlC,EAAG,UAAU,CAACM,MAAM,CAAC,WAAaP,EAAIoC,kBAAkB,QAAUpC,EAAIqC,gBAAgB,OAAS,KAAK,YAAa,GAAOC,YAAYtC,EAAIuC,GAAG,CAAC,CAACC,IAAI,cAAcC,GAAG,SAASC,EAAMC,GAAQ,MAAO,CAAEA,GAAUA,EAAOC,aAAc3C,EAAG,YAAY,CAACM,MAAM,CAAC,UAAY,YAAY,CAACN,EAAG,WAAW,CAAC4C,KAAK,WAAW,CAAC5C,EAAG,IAAI,CAACD,EAAIQ,GAAG,SAASR,EAAIS,GAAGkC,EAAOC,aAAaE,SAAS7C,EAAG,IAAI,CAACD,EAAIQ,GAAG,SAASR,EAAIS,GAAGkC,EAAOC,aAAaG,SAAS9C,EAAG,IAAI,CAACD,EAAIQ,GAAG,YAAYR,EAAIS,GAAGkC,EAAOC,aAAaI,cAAc/C,EAAG,SAAS,CAACE,YAAY,CAAC,MAAQ,WAAWI,MAAM,CAAC,KAAO,kBAAkB,GAAGP,EAAImB,aAAa,GAAInB,EAAIiD,cAAehD,EAAG,SAAS,CAACE,YAAY,CAAC,aAAa,QAAQI,MAAM,CAAC,MAAQP,EAAIU,GAAG,8BAA8B,CAACT,EAAG,UAAU,CAACM,MAAM,CAAC,WAAaP,EAAIkD,kBAAkB,QAAUlD,EAAImD,cAAc,OAAS,KAAK,YAAa,MAAU,GAAGnD,EAAImB,MAAM,IAEhoCiC,EAAkB,G,wHC8GP,GACfC,OAAA,CAAAC,QACAC,WAAA,CACAC,sBAEAC,OACA,OACAC,KAAA,KACAxC,SAAA,GACAyC,cAAA,GACArC,UAAA,sBACAX,WAAA,EACAgB,gBAAA,KACAiC,WAAA,KACAC,aAAA,KACAC,cAAA,EACAb,cAAA,OAGAc,SAAA,IACAC,eAAA,8DACA/B,eACA,OACAgC,gBAAA,KAAAN,cACAO,SAAAD,IACA,KAAAN,cAAAM,KAIApC,UACA,OACA,CACAsC,MAAA,KAAAzD,GAAA,+BACA0D,UAAA,YACA5B,IAAA,aAEA,CACA2B,MAAA,KAAAzD,GAAA,gCACA0D,UAAA,KACA5B,IAAA,QAIAH,kBAAA,MAAAgC,EAAA,KAAAC,eACA,OACA,CACAH,MAAA,KAAAzD,GAAA,gCACA0D,UAAA,KACA5B,IAAA,KACA+B,MAAA,SAEA,CACAJ,MAAA,KAAAzD,GAAA,+BACA0D,UAAA,YACA5B,IAAA,YACA+B,MAAA,QACAC,UAAA,GAEA,CACAL,MAAA,KAAAzD,GAAA,uBACA0D,UAAA,SACA5B,IAAA,SACA+B,MAAA,QACAE,aAAA/B,IACA,MAAAgC,EAAA,CACA,kBACA,sBACA,iBACA,kBACA,kBACAhC,IAAA,OACA,OAAA2B,EAAA,eAAAK,UAAA,CAAAhC,MAGA,CACAyB,MAAA,KAAAzD,GAAA,yBACA0D,UAAA,WACA5B,IAAA,WACA+B,MAAA,QACAE,cAAA/B,EAAAC,IAAA0B,EAAA,OAAAA,EAAA,6BAGA3B,GAAA,OACA,eACA,WAAAC,EAAAgC,OAAA,YACA,WAAAhC,EAAAgC,OAAA,cAAA7E,KAAAuE,EAAA,aAEA,iCACA,KAAAO,YAAAjC,EAAAkC,mBAAA,WAAAD,YAAA,KAAAE,iBAAAC,SAAAC,gBAKA,CACAb,MAAA,KAAAzD,GAAA,sBACA0D,UAAA,QACA5B,IAAA,QACA+B,MAAA,SAEA,CACAJ,MAAA,KAAAzD,GAAA,6BACA0D,UAAA,eACA5B,IAAA,eACA+B,MAAA,OACAjC,YAAA,CAAAmC,aAAA,kBAIArC,oBACA,YAAA0C,kBAAA,KAAAA,iBAAAlD,MACAqD,OAAAC,KAAA,KAAAJ,iBAAAlD,OAAAuD,IAAAC,IACA,MAAAC,EAAA,KAAAP,iBAAAlD,MAAAwD,GACA,OACAA,KACAE,UAAAD,EAAAC,UACAX,OAAAU,EAAAV,OACAY,SAAAF,EAAAE,UAAA,EACAC,MAAAH,EAAAG,OAAA,IACAX,kBAAAQ,EAAAR,kBACAjC,aAAAyC,EAAAzC,gBAVA,IAcAV,iBACA,SAAA4C,mBAAA,KAAAA,iBAAAlD,MAAA,SACA,MAAAA,EAAAqD,OAAAQ,OAAA,KAAAX,iBAAAlD,OACA,OAAAA,EAAA/B,OAAA,SAEA,MAAA6F,EAAA9D,EAAAnC,OAAA,CAAAkG,EAAAN,IAAAM,GAAAN,EAAAE,UAAA,MACA,OAAAK,KAAAC,MAAAH,EAAA9D,EAAA/B,SAEAsC,oBACA,SAAA2C,mBAAA,KAAAA,iBAAAlD,MAAA,eAEA,MAAAA,EAAAqD,OAAAQ,OAAA,KAAAX,iBAAAlD,OAAA,IACA,WAAAA,EAAA/B,OAAA,SAEA+B,EAAAkE,KAAAT,GAAA,WAAAA,EAAAV,QAAA,YACA/C,EAAAmE,MAAAV,GAAA,cAAAA,EAAAV,QAAA,UACA,UAEAqB,cACA,SAAAlB,mBAAA,KAAAA,iBAAAlD,MAAA,SACA,MAAAqE,EAAAhB,OAAAQ,OAAA,KAAAX,iBAAAlD,OAAA,IACAnC,OAAA,CAAAkG,EAAAN,IACA,gBAAAA,EAAAV,QAAAU,EAAAG,MACAG,EAAA,KAAAO,WAAAb,EAAAG,OAEAG,EACA,GACA,YAAAf,YAAAqB,GAAA,MAEAE,wBAAA,IAAAC,EACA,SAAAtB,mBAAA,KAAAA,iBAAAlD,MAAA,SACA,MAAAA,EAAAqD,OAAAQ,OAAA,KAAAX,iBAAAlD,OAAA,IACAyE,EAAAzE,EAAAnC,OAAA,CAAAkG,EAAAN,IAAAM,GAAAN,EAAAR,mBAAA,MACAyB,IAAA,QAAAF,EAAA,KAAAtB,iBAAAC,gBAAA,IAAAqB,OAAA,EAAAA,EAAApB,YAAA,GAAApD,EAAA/B,OACA,cAAA+E,YAAAyB,QAAA,KAAAzB,YAAA0B,MAEApD,oBACA,SAAAD,cAAA,SAEA,MAAAsD,EAAA,GAWA,OAVA,KAAAtD,cAAAuD,QAAAC,QAAArB,IACAmB,EAAAG,KAAA,CAAAtB,KAAAT,OAAA,cAEA,KAAA1B,cAAA0D,OAAAF,QAAArB,IACAmB,EAAAG,KAAA,CACAtB,KACAT,OAAA,SACAiC,MAAA,KAAA3D,cAAA4D,OAAAzB,OAGAmB,GAEApD,gBAAA,MAAAkB,EAAA,KAAAC,eACA,OACA,CAAAH,MAAA,KAAAzD,GAAA,gCAAA0D,UAAA,MACA,CACAD,MAAA,KAAAzD,GAAA,uBACA0D,UAAA,SACAK,aAAA/B,GAAA2B,EAAA,cACA,CAAAK,MAAA,YAAAhC,EAAA,uBACAA,KAIA,CAAAyB,MAAA,KAAAzD,GAAA,6BAAA0D,UAAA,YAIA0C,UACA,SAAAC,sBACA,OAEA,KAAAC,OAAAC,SAAA,cAEA,MAAAC,EAAAC,aAAAC,QAAA,mBAAAC,gBACA,GAAAH,EAAA,CACA,aAAAI,EAAA,YAAAC,GAAAC,KAAAC,MAAAP,GACAK,IAAA,KAAAF,gBACA,KAAAK,0BAIAC,gBACA,KAAA9D,cACA+D,cAAA,KAAA/D,eAGAgE,QAAA,IACAC,eAAA,qBACA7G,aAAAyC,GACA,MAAAqE,EAAA,WACA,OAAArE,EAAAsE,KAAAD,GACA,KAAAE,SAAArB,MAAA,oCACA,IAEA,KAAAlD,OACA,KAAAxC,SAAAwC,EAAAwE,MACA,IAGAxG,kBAAA0D,GACA+C,QAAAC,IAAA,oBAAAhD,GACA,KAAAzD,gBAAAyD,GAEAiD,mBAAAC,GACA,OAAAA,EAAAC,WAAA,OAIAD,EAAAE,SAAA,OAAAF,EAAAE,SAAA,OAAAF,EAAAE,SAAA,QACA,KAAAP,SAAArB,MAAA,iDACA,IALA,KAAAqB,SAAArB,MAAA,qCACA,IAQA,qBACA,SAAAlD,OAAA,KAAAC,cAAA9D,SAAA,KAAA8B,gBAEA,YADA,KAAAsG,SAAArB,MAAA,+CAIA,SAAAyB,mBAAA,KAAA/G,WACA,OAIA,MAAAmH,EAAAtB,aAAAC,QAAA,kBACA,IAAAqB,EAGA,OAFA,KAAAR,SAAArB,MAAA,4DACA,KAAA8B,QAAAhC,KAAA,aAIA,IACA,KAAA/F,WAAA,EAGA,MAAAgI,EAAAxB,aAAAC,QAAA,mBAAAC,gBACA,GAAAsB,EACA,IACA,aAAArB,GAAAE,KAAAC,MAAAkB,GACArB,GACA,KAAAsB,0BAAAtB,EAAA,cAAAD,gBAEA,MAAAwB,GACAV,QAAAvB,MAAA,+CAAAiC,GAIA,MAAAC,EAAA,IAAAC,SACAD,EAAAE,OAAA,YAAAtF,MACAoF,EAAAE,OAAA,UAAAxB,KAAAyB,UAAA,KAAAtF,gBACAmF,EAAAE,OAAA,kBAAA1H,WACAwH,EAAAE,OAAA,eAAArH,iBAEA,MAAAuH,QAAAC,OAAAC,KACA,oCAAAC,mBAAA,KAAAhC,gBACAyB,EACA,CACAQ,QAAA,CACA,wCAKAhC,EAAA4B,EAAAzF,KAAA8F,QACApC,aAAAqC,QAAA,mBAAAnC,eAAAG,KAAAyB,UAAA,CACA3B,SACAC,YAAA,KAAAF,kBAEAF,aAAAsC,WAAA,4BAAApC,gBAEA,KAAAqC,aAAApC,GAEA,MAAAV,GAAA,IAAA+C,EACAxB,QAAAvB,MAAA,gBAAAA,GACA,KAAA5D,QAAA,QAAAtC,GAAA,iCAAAiJ,EAAA/C,EAAAsC,gBAAA,IAAAS,GAAA,QAAAA,IAAAlG,YAAA,IAAAkG,OAAA,EAAAA,EAAA/C,UAAA5D,UACA,KAAA4G,YAAA,QACA,KAAAjJ,WAAA,IAGA,mBAAA2G,GACA,KAAAzD,cACA+D,cAAA,KAAA/D,cAGA,MAAAgG,EAAA,UACA,IACA,SAAAxC,eACA,UAAAyC,MAAA,gCAGA,MAAAZ,QAAAC,OAAAY,IACA,6BAAAzC,YAAA+B,mBAAA,KAAAhC,mBAGA,KAAAL,OAAAC,SAAA,mBAAAiC,EAAAzF,MAEA,MAAAuG,EAAA/E,OAAAQ,OAAAyD,EAAAzF,KAAA7B,OAAAmE,MACAV,GAAA,qBAAAmD,SAAAnD,EAAAV,SAGA,GAAAqF,EAAA,CACApC,cAAA,KAAA/D,cACA,KAAAlD,WAAA,EACAwG,aAAAqC,QAAA,4BAAAnC,eAAA,QAEA,MAAAzF,EAAAqD,OAAAQ,OAAAyD,EAAAzF,KAAA7B,OAGA,KAAAqI,8BAAA,CACA3C,SACA4C,SAAA,SACAtI,QACAuI,UAAA,KAAA9C,kBAGA,MAAAT,GAAA,IAAAwD,EACAjC,QAAAvB,MAAA,wBAAAA,GACA,eAAAwD,EAAAxD,EAAAsC,gBAAA,IAAAkB,OAAA,EAAAA,EAAAzF,UACAiD,cAAA,KAAA/D,cACA,KAAAlD,WAAA,EACAwG,aAAAsC,WAAA,mBAAApC,gBACAF,aAAAsC,WAAA,4BAAApC,yBAKAwC,IACA,KAAAhG,aAAAwG,YAAAR,EAAA,MAEA,8BACA,IACA,MAAA3C,EAAAC,aAAAC,QAAA,mBAAAC,gBACAiD,EAAAnD,aAAAC,QAAA,4BAAAC,gBAEA,GAAAH,EAAA,CACA,aAAAI,EAAA,YAAAC,GAAAC,KAAAC,MAAAP,GAEA,GAAAK,IAAA,KAAAF,eACA,OAGA,MAAA6B,QAAAC,OAAAY,IACA,6BAAAzC,YAAA+B,mBAAA,KAAAhC,mBAGA,GAAA6B,EAAAzF,KAAA,CACA,KAAAuD,OAAAC,SAAA,mBAAAiC,EAAAzF,MAEA,MAAAuG,EAAA/E,OAAAQ,OAAAyD,EAAAzF,KAAA7B,OAAAmE,MACAV,GAAA,qBAAAmD,SAAAnD,EAAAV,SAGA,GAAAqF,GAAAM,GAGA,GAAAN,EAAA,CACA,KAAArJ,WAAA,EACAwG,aAAAqC,QAAA,4BAAAnC,eAAA,QAEA,MAAAzF,EAAAqD,OAAAQ,OAAAyD,EAAAzF,KAAA7B,OAGA,KAAAqI,8BAAA,CACA3C,SACA4C,SAAA,SACAtI,QACAuI,UAAA,KAAA9C,uBAbA,KAAA1G,WAAA,EACA,KAAA+I,aAAApC,KAiBA,MAAAV,GACAuB,QAAAvB,MAAA,qCAAAA,KAGAhC,YAAA2F,GACA,IAAAA,EAAA,YACA,MAAAC,EAAA,qBACAC,EAAA7E,KAAA8E,MAAA9E,KAAAwC,IAAAmC,GAAA3E,KAAAwC,IAAA,OACA,UAAAmC,EAAA3E,KAAA+E,IAAA,KAAAF,IAAAG,QAAA,MAAAJ,EAAAC,MAEAvE,WAAAV,GACA,IAAAA,GAAA,MAAAA,EAAA,SACA,MAAAnE,EAAAwJ,GAAArF,EAAAsF,MAAA,KACAC,EAAA,CACA,QACA,YACA,gBACAF,IAAA,EACA,OAAAG,WAAA3J,GAAA0J,GAEAhE,sBACA,MAAA0B,EAAAtB,aAAAC,QAAA,kBACA,QAAAqB,IACA,KAAAwC,QAAArE,MAAA,CACAzC,MAAA,KAAAzD,GAAA,oBACAsC,QAAA,KAAAtC,GAAA,kCAEA,KAAAgI,QAAAhC,KAAA,cACA,KAKAwE,MAAA,CAEA7D,eAAA,CACA8D,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAArE,OAAAC,SAAA,yBACA,KAAApD,cACA+D,cAAA,KAAA/D,cAGA,KAAA6D,0BAGA4D,WAAA,KC/iBmV,I,wBCQ/UC,EAAY,eACd,EACAxL,EACAqD,GACA,EACA,KACA,WACA,MAIa,aAAAmI,E,iECfA,QACb1D,QAAS,CAYPoC,+BAA8B,OAC5B3C,EAAM,SACN4C,EAAQ,MACRtI,EAAK,UACLuI,EAAS,OACTqB,EAAS,GAAE,UACXC,EAAY,GAAE,cACdC,EAAgB,KAGhB,MAAMC,EAAsB,GAAGzB,aAAoBC,KAAa7C,IAChE,GAAIH,aAAaC,QAAQuE,GACvB,OAIF,MAAMC,EAAuB,CAC3BpF,QAAS,CAAC,UAAW,aACrBqF,QAAS,CAAC,WAINC,EAAqB,CACzBtF,QAAS,IAAKkF,EAAclF,SAAW,MAAQoF,EAAqBpF,SACpEqF,QAAS,IAAKH,EAAcG,SAAW,MAAQD,EAAqBC,UAIhEE,EAAenK,EAAMoK,OAAO3G,GAChCyG,EAAmBtF,QAAQgC,SAASnD,EAAKV,UAAYU,EAAKzC,cAC1D/C,OACIoM,EAAcrK,EAAMoK,OAAO3G,GAC/ByG,EAAmBD,QAAQrD,SAASnD,EAAKV,SAAWU,EAAKzC,cACzD/C,OACIqM,EAAcD,EAAc,EAGlC,IAAIE,EAQAC,EANFD,EADED,EACkBV,EAAO5E,OAASjH,KAAK0M,qBAAqBnC,GAE1CsB,EAAOhF,SAAW7G,KAAK2M,uBAAuBpC,GAMlEkC,EADEF,EACoBT,EAAU7E,OAC9B,GAAGmF,mCAA8CE,kBAE7BR,EAAUjF,SAC9B,OAAO5E,EAAM/B,uCAIjBF,KAAK4M,gBAAgB,CACnBpI,MAAOgI,EACPnJ,QAASoJ,EACTrJ,KAAMmJ,EAAc,QAAU,UAC9B5E,OAAQA,IAIVH,aAAaqC,QAAQmC,EAAqB,SAQ5CW,uBAAuBpC,GACrB,MAAMsB,EAAS,CACb,KAAQ,iBACR,OAAU,wBACV,SAAY,0BACZ,KAAQ,4BAEV,OAAOA,EAAOtB,IAAa,uBAQ7BmC,qBAAqBnC,GACnB,MAAMsB,EAAS,CACb,KAAQ,6BACR,OAAU,oCACV,SAAY,sCACZ,KAAQ,wCAEV,OAAOA,EAAOtB,IAAa,mCAS7BtB,0BAA0BtB,EAAQ4C,EAAUC,GAC1C,MAAMwB,EAAsB,GAAGzB,aAAoBC,KAAa7C,IAChEH,aAAasC,WAAWkC,O,oCCzH9B,IAAI5L,EAAS,WAAkB,IAAIC,EAAIL,KAAKM,EAAGD,EAAIE,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,WAAW,CAACG,YAAY,mBAAmBG,MAAM,CAAC,QAAUP,EAAIwM,YAAY,SAAWxM,EAAIyM,UAAU7L,GAAG,CAAC,MAAQZ,EAAI0M,oBAAoB,CAAC1M,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIU,GAAG,8BAAgC,UAAU,OAAQV,EAAI2M,aAAa9M,OAAQI,EAAG,WAAW,CAACE,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQI,MAAM,CAAC,YAAcP,EAAIU,GAAG,2BAA6B,SAAS,SAAWV,EAAIyM,UAAUrL,MAAM,CAACC,MAAOrB,EAAI4M,gBAAiBrL,SAAS,SAAUC,GAAMxB,EAAI4M,gBAAgBpL,GAAKC,WAAW,oBAAoBzB,EAAI6M,GAAI7M,EAAI2M,cAAc,SAASvH,GAAI,OAAOnF,EAAG,kBAAkB,CAACuC,IAAI4C,EAAG7E,MAAM,CAAC,MAAQ6E,IAAK,CAACpF,EAAIQ,GAAG,IAAIR,EAAIS,GAAG2E,GAAI,UAAS,GAAGpF,EAAImB,MAAM,IAEpvBiC,EAAkB,G,YC6BP,GACf8E,KAAA,gBACA4E,MAAA,CAEAL,SAAA,CACA1J,KAAAgK,QACAC,SAAA,GAGA3L,MAAA,CACA0B,KAAAkK,OACAD,QAAA,OAGAvJ,OACA,OACA+I,aAAA,EACAG,aAAA,GACAC,gBAAA,KAAAvL,QAGA0C,SAAA,GAEAmH,MAAA,CAEA7J,MAAA6L,GACA,KAAAN,gBAAAM,GAGAN,gBAAAM,GACA,KAAAC,MAAA,QAAAD,GACA,KAAAC,MAAA,SAAAD,KAGArF,QAAA,CACA,0BACA,KAAA2E,aAAA,EACA,IACA,MAAAtD,QAAAC,OAAAY,IAAA,qBACA,KAAA4C,aAAAzD,EAAAzF,KAAA2J,cACA,KAAAT,aAAA9M,SACA,KAAA+M,gBAAA,KAAAD,aAAA,IAEA,MAAA/F,GACAuB,QAAAvB,MAAA,iCAAAA,GACA,KAAAqE,QAAArE,MAAA,CACAzC,MAAA,QACAnB,QAAA,mCAEA,QACA,KAAAwJ,aAAA,MCjFqW,I,YCOjWjB,EAAY,eACd,EACAxL,EACAqD,GACA,EACA,KACA,WACA,MAIa,OAAAmI,E,gCClBf,IAAI8B,EAAU,EAAQ,QAClBC,EAAS,EAAQ,QAErBC,EAAOC,QAAqC,WAA3BH,EAAQC,EAAOG,U,oCCDhC,IAAI3O,EAAI,EAAQ,QACZ4O,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsO,MAAM,GAAQ,CACjD9H,MAAO,SAAetD,GAGpB,OAFAmL,EAASjO,MACTgO,EAAUlL,IACFiL,EAAQ/N,MAAM,SAAU0B,EAAOyM,GACrC,IAAKrL,EAAGpB,GAAQ,OAAOyM,MACtB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAInP,EAAI,EAAQ,QACZ4O,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsO,MAAM,GAAQ,CACjD/H,KAAM,SAAcrD,GAGlB,OAFAmL,EAASjO,MACTgO,EAAUlL,GACHiL,EAAQ/N,MAAM,SAAU0B,EAAOyM,GACpC,GAAIrL,EAAGpB,GAAQ,OAAOyM,MACrB,CAAEC,aAAa,EAAMC,aAAa,IAAQC,Y,oCCXjD,IAAInP,EAAI,EAAQ,QACZ4O,EAAU,EAAQ,QAClBC,EAAY,EAAQ,QACpBC,EAAW,EAAQ,QAEvB9O,EAAE,CAAEQ,OAAQ,WAAYC,OAAO,EAAMsO,MAAM,GAAQ,CACjDpO,OAAQ,SAAgByO,GACtBN,EAASjO,MACTgO,EAAUO,GACV,IAAIC,EAAYvO,UAAUC,OAAS,EAC/BuO,EAAcD,OAAYrO,EAAYF,UAAU,GASpD,GARA8N,EAAQ/N,MAAM,SAAU0B,GAClB8M,GACFA,GAAY,EACZC,EAAc/M,GAEd+M,EAAcF,EAAQE,EAAa/M,KAEpC,CAAE0M,aAAa,IACdI,EAAW,MAAME,UAAU,kDAC/B,OAAOD,M,kCCrBX,IAAIE,EAAQ,EAAQ,QAEpBf,EAAOC,QAAU,SAAUe,EAAaC,GACtC,IAAIC,EAAS,GAAGF,GAChB,QAASE,GAAUH,GAAM,WAEvBG,EAAOC,KAAK,KAAMF,GAAY,WAAc,MAAM,GAAM,Q,qBCP5D,IAAIb,EAAY,EAAQ,QACpBgB,EAAW,EAAQ,QACnBC,EAAgB,EAAQ,QACxBC,EAAW,EAAQ,QAGnBC,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAMtP,EAAYuP,EAAiBC,GAClDvB,EAAUjO,GACV,IAAIyP,EAAIR,EAASK,GACbI,EAAOR,EAAcO,GACrBtP,EAASgP,EAASM,EAAEtP,QACpBwP,EAAQN,EAAWlP,EAAS,EAAI,EAChC4K,EAAIsE,GAAY,EAAI,EACxB,GAAIE,EAAkB,EAAG,MAAO,EAAM,CACpC,GAAII,KAASD,EAAM,CACjBF,EAAOE,EAAKC,GACZA,GAAS5E,EACT,MAGF,GADA4E,GAAS5E,EACLsE,EAAWM,EAAQ,EAAIxP,GAAUwP,EACnC,MAAMhB,UAAU,+CAGpB,KAAMU,EAAWM,GAAS,EAAIxP,EAASwP,EAAOA,GAAS5E,EAAO4E,KAASD,IACrEF,EAAOxP,EAAWwP,EAAME,EAAKC,GAAQA,EAAOF,IAE9C,OAAOD,IAIX3B,EAAOC,QAAU,CAGfxO,KAAM8P,GAAa,GAGnBQ,MAAOR,GAAa,K", "file": "static/js/chunk-55098f76.0a45aa50.js", "sourcesContent": ["export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=style&index=0&id=014974c6&prod&scoped=true&lang=css\"", "'use strict';\nvar $ = require('../internals/export');\nvar $reduce = require('../internals/array-reduce').left;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\nvar CHROME_VERSION = require('../internals/engine-v8-version');\nvar IS_NODE = require('../internals/engine-is-node');\n\nvar STRICT_METHOD = arrayMethodIsStrict('reduce');\n// Chrome 80-82 has a critical bug\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1049982\nvar CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;\n\n// `Array.prototype.reduce` method\n// https://tc39.es/ecma262/#sec-array.prototype.reduce\n$({ target: 'Array', proto: true, forced: !STRICT_METHOD || CHROME_BUG }, {\n  reduce: function reduce(callbackfn /* , initialValue */) {\n    return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticStyle:{\"padding\":\"2px\"}},[_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 640 512\",\"height\":\"20\",\"width\":\"20\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"d\":\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.fileUpload')))])]),_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.uploading},on:{\"click\":_vm.handleUpload}},[_vm._v(\" \"+_vm._s(_vm.$t('fileUpload.startUpload'))+\" \")])],1),_c('div',{staticClass:\"main-content\"},[_c('div',{staticClass:\"left-section\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\"}},[_c('a-form',{attrs:{\"layout\":\"vertical\"},on:{\"submit\":function($event){$event.preventDefault();return _vm.handleUpload.apply(null, arguments)}}},[_c('a-form-item',{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{\"label\":_vm.$t('fileUpload.selectFile')}},[_c('a-upload',{attrs:{\"before-upload\":_vm.beforeUpload,\"show-upload-list\":false}},[_c('a-button',{staticClass:\"nav-style-button\"},[_c('a-icon',{attrs:{\"type\":\"upload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('fileUpload.clickToSelect'))+\" \")],1)],1),(_vm.fileName)?_c('span',{staticStyle:{\"margin-left\":\"8px\"}},[_vm._v(_vm._s(_vm.fileName))]):_vm._e()],1),_c('a-form-item',{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{\"label\":_vm.$t('fileUpload.uploadPath')}},[_c('a-input',{attrs:{\"placeholder\":_vm.$t('fileUpload.enterUploadPath')},model:{value:(_vm.uploadDir),callback:function ($$v) {_vm.uploadDir=$$v},expression:\"uploadDir\"}})],1)],1)],1),_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.uploading},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedProxyIp),callback:function ($$v) {_vm.selectedProxyIp=$$v},expression:\"selectedProxyIp\"}})],1)],1),_c('div',{staticClass:\"right-section config-table\"},[_c('a-card',{staticClass:\"compact-card\",attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('a-table',{staticClass:\"bordered-nodes-table\",attrs:{\"dataSource\":_vm.nodes,\"columns\":_vm.columns,\"rowKey\":\"ip\",\"pagination\":{\n            pageSize: 10,\n            total: _vm.nodes.length,\n            showSizeChanger: false\n          },\"rowSelection\":_vm.rowSelection}})],1)],1)]),_c('a-card',{staticClass:\"compact-card\",staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.$t('fileUpload.uploadProgress')}},[_c('a-progress',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"percent\":_vm.uploadProgress,\"status\":_vm.progressBarStatus}}),_c('a-table',{attrs:{\"dataSource\":_vm.progressTableData,\"columns\":_vm.progressColumns,\"rowKey\":\"ip\",\"pagination\":false},scopedSlots:_vm._u([{key:\"errorDetail\",fn:function(text, record){return [(record && record.error_detail)?_c('a-popover',{attrs:{\"placement\":\"topLeft\"}},[_c('template',{slot:\"content\"},[_c('p',[_vm._v(\"Time: \"+_vm._s(record.error_detail.time))]),_c('p',[_vm._v(\"Type: \"+_vm._s(record.error_detail.type))]),_c('p',[_vm._v(\"Message: \"+_vm._s(record.error_detail.message))])]),_c('a-icon',{staticStyle:{\"color\":\"#ff4d4f\"},attrs:{\"type\":\"info-circle\"}})],2):_vm._e()]}}])})],1),(_vm.uploadResults)?_c('a-card',{staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":_vm.$t('fileUpload.uploadResults')}},[_c('a-table',{attrs:{\"dataSource\":_vm.uploadResultsData,\"columns\":_vm.resultColumns,\"rowKey\":\"ip\",\"pagination\":false}})],1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"padding: 2px;\">\r\n    <div class=\"card-header-wrapper\">\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"logo-wrapper\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" d=\"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z\"/>\r\n          </svg>\r\n        </div>\r\n        <h6 class=\"font-semibold m-0\">{{ $t('headTopic.fileUpload') }}</h6>\r\n      </div>\r\n      <a-button\r\n        class=\"nav-style-button\"\r\n        @click=\"handleUpload\"\r\n        :loading=\"uploading\"\r\n      >\r\n        {{ $t('fileUpload.startUpload') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <div class=\"left-section\">\r\n        <a-card size=\"small\" class=\"compact-card\">\r\n          <a-form layout=\"vertical\" @submit.prevent=\"handleUpload\">\r\n            <a-form-item :label=\"$t('fileUpload.selectFile')\" style=\"margin-bottom: 8px;\">\r\n              <a-upload :before-upload=\"beforeUpload\" :show-upload-list=\"false\">\r\n                <a-button class=\"nav-style-button\">\r\n                  <a-icon type=\"upload\" /> {{ $t('fileUpload.clickToSelect') }}\r\n                </a-button>\r\n              </a-upload>\r\n              <span v-if=\"fileName\" style=\"margin-left: 8px;\">{{ fileName }}</span>\r\n            </a-form-item>\r\n\r\n            <a-form-item :label=\"$t('fileUpload.uploadPath')\" style=\"margin-bottom: 8px;\">\r\n              <a-input v-model=\"uploadDir\" :placeholder=\"$t('fileUpload.enterUploadPath')\" />\r\n            </a-form-item>\r\n          </a-form>\r\n        </a-card>\r\n\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureProxy')\">\r\n          <proxy-selector\r\n            v-model=\"selectedProxyIp\"\r\n            :disabled=\"uploading\"\r\n            @change=\"handleProxyChange\"\r\n          />\r\n        </a-card>\r\n      </div>\r\n\r\n      <div class=\"right-section config-table\">\r\n        <a-card size=\"small\" class=\"compact-card\" :title=\"$t('common.configureNodes')\">\r\n          <a-table\r\n            :dataSource=\"nodes\"\r\n            :columns=\"columns\"\r\n            rowKey=\"ip\"\r\n            :pagination=\"{\r\n              pageSize: 10,\r\n              total: nodes.length,\r\n              showSizeChanger: false\r\n            }\"\r\n            :rowSelection=\"rowSelection\"\r\n            class=\"bordered-nodes-table\"\r\n            >\r\n          </a-table>\r\n        </a-card>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <a-card\r\n      :title=\"$t('fileUpload.uploadProgress')\"\r\n      style=\"margin-top: 16px;\"\r\n      class=\"compact-card\"\r\n    >\r\n      <a-progress\r\n        :percent=\"uploadProgress\"\r\n        :status=\"progressBarStatus\"\r\n        style=\"margin-bottom: 16px;\"\r\n      />\r\n\r\n      <a-table\r\n        :dataSource=\"progressTableData\"\r\n        :columns=\"progressColumns\"\r\n        rowKey=\"ip\"\r\n        :pagination=\"false\"\r\n      >\r\n        <template slot=\"errorDetail\" slot-scope=\"text, record\">\r\n          <a-popover v-if=\"record && record.error_detail\" placement=\"topLeft\">\r\n            <template slot=\"content\">\r\n              <p>Time: {{ record.error_detail.time }}</p>\r\n              <p>Type: {{ record.error_detail.type }}</p>\r\n              <p>Message: {{ record.error_detail.message }}</p>\r\n            </template>\r\n            <a-icon type=\"info-circle\" style=\"color: #ff4d4f\" />\r\n          </a-popover>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n\r\n    <a-card v-if=\"uploadResults\" :title=\"$t('fileUpload.uploadResults')\" style=\"margin-top: 16px;\">\r\n      <a-table\r\n        :dataSource=\"uploadResultsData\"\r\n        :columns=\"resultColumns\"\r\n        rowKey=\"ip\"\r\n        :pagination=\"false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport axios from '@/api/axiosInstance';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin],\r\n  components: {\r\n    ProxySelector\r\n  },\r\n  data() {\r\n    return {\r\n      file: null,\r\n      fileName: '',\r\n      selectedNodes: [],\r\n      uploadDir: '/root/.test/Uploads',\r\n      uploading: false,\r\n      selectedProxyIp: null,\r\n      activeTask: null,\r\n      pollInterval: null,\r\n      isProcessing: false,\r\n      uploadResults: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['nodes', 'activeUploadTask', 'currentProject', 'sidebarColor']),\r\n    rowSelection() {\r\n      return {\r\n        selectedRowKeys: this.selectedNodes,\r\n        onChange: (selectedRowKeys) => {\r\n          this.selectedNodes = selectedRowKeys;\r\n        },\r\n      };\r\n    },\r\n    columns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip'\r\n        }\r\n      ];\r\n    },\r\n    progressColumns() {\r\n      return [\r\n        {\r\n          title: this.$t('hostConfig.columns.ipAddress'),\r\n          dataIndex: 'ip',\r\n          key: 'ip',\r\n          width: '120px'\r\n        },\r\n        {\r\n          title: this.$t('hostConfig.columns.hostName'),\r\n          dataIndex: 'host_name',\r\n          key: 'host_name',\r\n          width: '150px',\r\n          ellipsis: true\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          key: 'status',\r\n          width: '100px',\r\n          customRender: (text) => {\r\n            const color = {\r\n              'pending': '#1890ff',\r\n              'in_progress': '#1890ff',\r\n              'paused': '#faad14',\r\n              'success': '#52c41a',\r\n              'failed': '#f5222d'\r\n            }[text] || '#000';\r\n            return <span style={{ color }}>{text}</span>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.progress'),\r\n          dataIndex: 'progress',\r\n          key: 'progress',\r\n          width: '200px',\r\n          customRender: (text, record) => (\r\n            <div>\r\n              <a-progress\r\n                percent={text || 0}\r\n                size=\"small\"\r\n                status={record.status === 'failed' ? 'exception' :\r\n                       record.status === 'paused' ? 'normal' : undefined}\r\n              />\r\n              <div style=\"font-size: 12px; color: #999\">\r\n                {this.formatBytes(record.bytes_transferred)} / {this.formatBytes(this.activeUploadTask.metadata.file_size)}\r\n              </div>\r\n            </div>\r\n          )\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.speed'),\r\n          dataIndex: 'speed',\r\n          key: 'speed',\r\n          width: '100px'\r\n        },\r\n        {\r\n          title: this.$t('tool.columns.errorDetails'),\r\n          dataIndex: 'error_detail',\r\n          key: 'error_detail',\r\n          width: '60px',\r\n          scopedSlots: { customRender: 'errorDetail' }\r\n        }\r\n      ];\r\n    },\r\n    progressTableData() {\r\n      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return [];\r\n      return Object.keys(this.activeUploadTask.nodes).map(ip => {\r\n        const node = this.activeUploadTask.nodes[ip];\r\n        return {\r\n          ip,\r\n          host_name: node.host_name,\r\n          status: node.status,\r\n          progress: node.progress || 0,\r\n          speed: node.speed || '-',\r\n          bytes_transferred: node.bytes_transferred,\r\n          error_detail: node.error_detail\r\n        };\r\n      });\r\n    },\r\n    uploadProgress() {\r\n      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return 0;\r\n      const nodes = Object.values(this.activeUploadTask.nodes);\r\n      if (nodes.length === 0) return 0;\r\n\r\n      const totalProgress = nodes.reduce((sum, node) => sum + (node.progress || 0), 0);\r\n      return Math.round(totalProgress / nodes.length);\r\n    },\r\n    progressBarStatus() {\r\n      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return 'normal';\r\n\r\n      const nodes = Object.values(this.activeUploadTask.nodes || {});\r\n      if (nodes.length === 0) return 'normal';\r\n\r\n      if (nodes.some(node => node.status === 'failed')) return 'exception';\r\n      if (nodes.every(node => node.status === 'completed')) return 'success';\r\n      return 'active';\r\n    },\r\n    formatSpeed() {\r\n      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return '';\r\n      const totalSpeed = Object.values(this.activeUploadTask.nodes || {})\r\n        .reduce((sum, node) => {\r\n          if (node.status === 'in_progress' && node.speed) {\r\n            return sum + this.parseSpeed(node.speed);\r\n          }\r\n          return sum;\r\n        }, 0);\r\n      return this.formatBytes(totalSpeed) + '/s';\r\n    },\r\n    formatOverallProgress() {\r\n      if (!this.activeUploadTask || !this.activeUploadTask.nodes) return '';\r\n      const nodes = Object.values(this.activeUploadTask.nodes || {});\r\n      const totalTransferred = nodes.reduce((sum, node) => sum + (node.bytes_transferred || 0), 0);\r\n      const totalSize = (this.activeUploadTask.metadata?.file_size || 0) * nodes.length;\r\n      return `${this.formatBytes(totalTransferred)} / ${this.formatBytes(totalSize)}`;\r\n    },\r\n    uploadResultsData() {\r\n      if (!this.uploadResults) return [];\r\n\r\n      const results = [];\r\n      this.uploadResults.success.forEach(ip => {\r\n        results.push({ ip, status: 'success' });\r\n      });\r\n      this.uploadResults.failed.forEach(ip => {\r\n        results.push({\r\n          ip,\r\n          status: 'failed',\r\n          error: this.uploadResults.errors[ip]\r\n        });\r\n      });\r\n      return results;\r\n    },\r\n    resultColumns() {\r\n      return [\r\n        { title: this.$t('hostConfig.columns.ipAddress'), dataIndex: 'ip' },\r\n        {\r\n          title: this.$t('tool.columns.status'),\r\n          dataIndex: 'status',\r\n          customRender: (text) => (\r\n            <span style={{ color: text === 'success' ? '#52c41a' : '#f5222d' }}>\r\n              {text}\r\n            </span>\r\n          )\r\n        },\r\n        { title: this.$t('tool.columns.errorDetails'), dataIndex: 'error' }\r\n      ];\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.checkDatabaseStatus()) {\r\n      return;\r\n    }\r\n    this.$store.dispatch('fetchNodes');\r\n    // 只检查当前项目的活动上传任务\r\n    const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { taskId, projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveUploadTask();\r\n      }\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    if (this.pollInterval) {\r\n      clearInterval(this.pollInterval);\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n    beforeUpload(file) {\r\n      const maxSize = 5 * 1024 * 1024 * 1024; // 5GB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('File size exceeds the 5GB limit');\r\n        return false;\r\n      }\r\n      this.file = file;\r\n      this.fileName = file.name;\r\n      return false;\r\n    },\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      console.log('Proxy IP changed:', ip);\r\n      this.selectedProxyIp = ip;\r\n    },\r\n    validateUploadPath(path) {\r\n      if (!path.startsWith('/')) {\r\n        this.$message.error('Path must start with a slash (/)');\r\n        return false;\r\n      }\r\n      if (path.includes('..') || path.includes('./') || path.includes('~')) {\r\n        this.$message.error('Path cannot contain relative path components');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    async handleUpload() {\r\n      if (!this.file || !this.selectedNodes.length || !this.selectedProxyIp) {\r\n        this.$message.error('Please select a file, nodes, and a proxy IP');\r\n        return;\r\n      }\r\n\r\n      if (!this.validateUploadPath(this.uploadDir)) {\r\n        return;\r\n      }\r\n\r\n      // 获取当前项目的数据库文件\r\n      const currentDbFile = localStorage.getItem('currentProject');\r\n      if (!currentDbFile) {\r\n        this.$message.error('No project selected. Please select a project first.');\r\n        this.$router.push('/projects');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.uploading = true;\r\n\r\n        // 清除之前的上传任务通知记录\r\n        const previousTaskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n        if (previousTaskInfo) {\r\n          try {\r\n            const { taskId } = JSON.parse(previousTaskInfo);\r\n            if (taskId) {\r\n              this.clearTaskNotificationMark(taskId, 'upload', this.currentProject);\r\n            }\r\n          } catch (e) {\r\n            console.error('Error clearing previous upload notification:', e);\r\n          }\r\n        }\r\n\r\n        const formData = new FormData();\r\n        formData.append('file', this.file);\r\n        formData.append('targets', JSON.stringify(this.selectedNodes));\r\n        formData.append('upload_dir', this.uploadDir);\r\n        formData.append('proxyIp', this.selectedProxyIp);\r\n\r\n        const response = await axios.post(\r\n          `/api/file_transfer/upload?dbFile=${encodeURIComponent(this.currentProject)}`,\r\n          formData,\r\n          {\r\n            headers: {\r\n              'Content-Type': 'multipart/form-data'\r\n            }\r\n          }\r\n        );\r\n\r\n        const taskId = response.data.task_id;\r\n        localStorage.setItem(`uploadTask_${this.currentProject}`, JSON.stringify({\r\n          taskId,\r\n          projectFile: this.currentProject\r\n        }));\r\n        localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);\r\n\r\n        this.startPolling(taskId);\r\n\r\n      } catch (error) {\r\n        console.error('Upload error:', error);\r\n        this.message = `${this.$t('fileUpload.error')}: ${error.response?.data?.error || error.message}`;\r\n        this.messageType = 'error';\r\n        this.uploading = false;\r\n      }\r\n    },\r\n    async startPolling(taskId) {\r\n      if (this.pollInterval) {\r\n        clearInterval(this.pollInterval);\r\n      }\r\n\r\n      const pollStatus = async () => {\r\n        try {\r\n          if (!this.currentProject) {\r\n            throw new Error('No project database selected');\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          this.$store.dispatch('updateUploadTask', response.data);\r\n\r\n          const allCompleted = Object.values(response.data.nodes).every(\r\n            node => ['success', 'failed'].includes(node.status)\r\n          );\r\n\r\n          if (allCompleted) {\r\n            clearInterval(this.pollInterval);\r\n            this.uploading = false;\r\n            localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n            const nodes = Object.values(response.data.nodes);\r\n\r\n            // 使用混入中的方法添加上传完成通知\r\n            this.addTaskCompletionNotification({\r\n              taskId,\r\n              taskType: 'upload',\r\n              nodes,\r\n              projectId: this.currentProject\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error('Error polling status:', error);\r\n          if (error.response?.status === 404) {\r\n            clearInterval(this.pollInterval);\r\n            this.uploading = false;\r\n            localStorage.removeItem(`uploadTask_${this.currentProject}`);\r\n            localStorage.removeItem(`uploadTaskCompleted_${this.currentProject}`);\r\n          }\r\n        }\r\n      };\r\n\r\n      await pollStatus();\r\n      this.pollInterval = setInterval(pollStatus, 5000);\r\n    },\r\n    async checkActiveUploadTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`uploadTask_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`uploadTaskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            return;\r\n          }\r\n\r\n          const response = await axios.get(\r\n            `/api/file_transfer/status/${taskId}?dbFile=${encodeURIComponent(this.currentProject)}`\r\n          );\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateUploadTask', response.data);\r\n\r\n            const allCompleted = Object.values(response.data.nodes).every(\r\n              node => ['success', 'failed'].includes(node.status)\r\n            );\r\n\r\n            if (!allCompleted && !taskCompleted) {\r\n              this.uploading = true;\r\n              this.startPolling(taskId);\r\n            } else if (allCompleted) {\r\n              this.uploading = false;\r\n              localStorage.setItem(`uploadTaskCompleted_${this.currentProject}`, 'true');\r\n\r\n              const nodes = Object.values(response.data.nodes);\r\n\r\n              // 使用混入中的方法添加上传完成通知\r\n              this.addTaskCompletionNotification({\r\n                taskId,\r\n                taskType: 'upload',\r\n                nodes,\r\n                projectId: this.currentProject\r\n              });\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active upload task:', error);\r\n      }\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n      return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;\r\n    },\r\n    parseSpeed(speed) {\r\n      if (!speed || speed === '-') return 0;\r\n      const [value, unit] = speed.split(' ');\r\n      const multiplier = {\r\n        'B/s': 1,\r\n        'KB/s': 1024,\r\n        'MB/s': 1024 * 1024\r\n      }[unit] || 1;\r\n      return parseFloat(value) * multiplier;\r\n    },\r\n    checkDatabaseStatus() {\r\n      const currentDbFile = localStorage.getItem('currentProject');\r\n      if (!currentDbFile) {\r\n        this.$notify.error({\r\n          title: this.$t('fileUpload.error'),\r\n          message: this.$t('fileUpload.noProjectSelected')\r\n        });\r\n        this.$router.push('/projects');\r\n        return false;\r\n      }\r\n      return true;\r\n    }\r\n  },\r\n  watch: {\r\n    // 添加对 currentProject 的监听\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除当前的上传任务状态\r\n          this.$store.dispatch('updateUploadTask', null);\r\n          if (this.pollInterval) {\r\n            clearInterval(this.pollInterval);\r\n          }\r\n          // 检查新项目的活动任务\r\n          this.checkActiveUploadTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.card-header-wrapper {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo-wrapper {\r\n  margin-right: 8px;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n}\r\n\r\n/* 页面整体布局 - 使用CSS Grid将页面分为左右两部分 */\r\n.main-content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 16px;\r\n}\r\n\r\n/* 左右两侧的Flexbox布局 */\r\n.left-section, .right-section {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 左侧卡片样式 */\r\n.left-section .compact-card {\r\n  flex: 1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.left-section .compact-card:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 右侧卡片样式 */\r\n.right-section .compact-card {\r\n  flex: 1;\r\n}\r\n\r\n/* 响应式布局 - 在移动设备上切换为单列布局 */\r\n@media (max-width: 768px) {\r\n  .main-content {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n/* 卡片标题样式 - 使其更紧凑 */\r\n.compact-card >>> .ant-card-head {\r\n  min-height: 40px;\r\n  padding: 0 12px;\r\n}\r\n\r\n.compact-card >>> .ant-card-head-title {\r\n  padding: 8px 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 表格样式 - 使用全局样式，不再硬编码边框颜色 */\r\n</style>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FileUpload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FileUpload.vue?vue&type=template&id=014974c6&scoped=true\"\nimport script from \"./FileUpload.vue?vue&type=script&lang=js\"\nexport * from \"./FileUpload.vue?vue&type=script&lang=js\"\nimport style0 from \"./FileUpload.vue?vue&type=style&index=0&id=014974c6&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"014974c6\",\n  null\n  \n)\n\nexport default component.exports", "/**\r\n * 通知混入 - 提供通用的通知处理逻辑\r\n * 用于任务完成、文件上传/下载完成等场景\r\n */\r\nexport default {\r\n  methods: {\r\n    /**\r\n     * 添加任务完成通知\r\n     * @param {Object} options - 通知选项\r\n     * @param {string} options.taskId - 任务ID\r\n     * @param {string} options.taskType - 任务类型 (task, upload, download, tool)\r\n     * @param {Array} options.nodes - 节点数组\r\n     * @param {string} options.projectId - 项目ID\r\n     * @param {Object} options.titles - 自定义标题 {success, error}\r\n     * @param {Object} options.templates - 自定义消息模板 {success, error}\r\n     * @param {Object} options.statusMapping - 状态映射 {success: ['success'], failure: ['failed']}\r\n     */\r\n    addTaskCompletionNotification({\r\n      taskId,\r\n      taskType,\r\n      nodes,\r\n      projectId,\r\n      titles = {},\r\n      templates = {},\r\n      statusMapping = {}\r\n    }) {\r\n      // 检查是否已经发送过通知\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      if (localStorage.getItem(notificationSentKey)) {\r\n        return; // 已经发送过通知，不再重复发送\r\n      }\r\n\r\n      // 设置默认状态映射\r\n      const defaultStatusMapping = {\r\n        success: ['success', 'completed'],  // 成功状态可能是'success'或'completed'\r\n        failure: ['failed']                // 失败状态通常是'failed'\r\n      };\r\n\r\n      // 合并自定义状态映射\r\n      const finalStatusMapping = {\r\n        success: [...(statusMapping.success || []), ...defaultStatusMapping.success],\r\n        failure: [...(statusMapping.failure || []), ...defaultStatusMapping.failure]\r\n      };\r\n\r\n      // 计算成功和失败的节点数量\r\n      const successNodes = nodes.filter(node =>\r\n        finalStatusMapping.success.includes(node.status) && !node.error_detail\r\n      ).length;\r\n      const failedNodes = nodes.filter(node =>\r\n        finalStatusMapping.failure.includes(node.status) || node.error_detail\r\n      ).length;\r\n      const hasFailures = failedNodes > 0;\r\n\r\n      // 准备通知标题\r\n      let notificationTitle;\r\n      if (hasFailures) {\r\n        notificationTitle = titles.error || this.getDefaultErrorTitle(taskType);\r\n      } else {\r\n        notificationTitle = titles.success || this.getDefaultSuccessTitle(taskType);\r\n      }\r\n\r\n      // 准备通知内容\r\n      let notificationMessage;\r\n      if (hasFailures) {\r\n        notificationMessage = templates.error ||\r\n          `${successNodes} nodes completed successfully, ${failedNodes} nodes failed.`;\r\n      } else {\r\n        notificationMessage = templates.success ||\r\n          `All ${nodes.length} nodes completed successfully.`;\r\n      }\r\n\r\n      // 添加到全局通知中心\r\n      this.addNotification({\r\n        title: notificationTitle,\r\n        message: notificationMessage,\r\n        type: hasFailures ? 'error' : 'success',\r\n        taskId: taskId\r\n      });\r\n\r\n      // 标记已发送通知\r\n      localStorage.setItem(notificationSentKey, 'true');\r\n    },\r\n\r\n    /**\r\n     * 获取默认的成功标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultSuccessTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed',\r\n        'upload': 'File Upload Completed',\r\n        'download': 'File Download Completed',\r\n        'tool': 'Tool Execution Completed'\r\n      };\r\n      return titles[taskType] || 'Operation Completed';\r\n    },\r\n\r\n    /**\r\n     * 获取默认的错误标题\r\n     * @param {string} taskType - 任务类型\r\n     * @returns {string} 默认标题\r\n     */\r\n    getDefaultErrorTitle(taskType) {\r\n      const titles = {\r\n        'task': 'Task Completed with Errors',\r\n        'upload': 'File Upload Completed with Errors',\r\n        'download': 'File Download Completed with Errors',\r\n        'tool': 'Tool Execution Completed with Errors'\r\n      };\r\n      return titles[taskType] || 'Operation Completed with Errors';\r\n    },\r\n\r\n    /**\r\n     * 清除任务通知标记\r\n     * @param {string} taskId - 任务ID\r\n     * @param {string} taskType - 任务类型\r\n     * @param {string} projectId - 项目ID\r\n     */\r\n    clearTaskNotificationMark(taskId, taskType, projectId) {\r\n      const notificationSentKey = `${taskType}Notified_${projectId}_${taskId}`;\r\n      localStorage.removeItem(notificationSentKey);\r\n    }\r\n  }\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"proxy-selector\"},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"loading\":_vm.isDetecting,\"disabled\":_vm.disabled},on:{\"click\":_vm.fetchReachableIps}},[_vm._v(\" \"+_vm._s(_vm.$t('common.detectReachableIps') || '检测可达IP')+\" \")]),(_vm.reachableIps.length)?_c('a-select',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"16px\"},attrs:{\"placeholder\":_vm.$t('tool.selectReachableIp') || '选择可达IP',\"disabled\":_vm.disabled},model:{value:(_vm.selectedIpValue),callback:function ($$v) {_vm.selectedIpValue=$$v},expression:\"selectedIpValue\"}},_vm._l((_vm.reachableIps),function(ip){return _c('a-select-option',{key:ip,attrs:{\"value\":ip}},[_vm._v(\" \"+_vm._s(ip)+\" \")])}),1):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"proxy-selector\">\r\n    <!-- 检测IP按钮 -->\r\n    <a-button\r\n      class=\"nav-style-button\"\r\n      @click=\"fetchReachableIps\"\r\n      :loading=\"isDetecting\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      {{ $t('common.detectReachableIps') || '检测可达IP' }}\r\n    </a-button>\r\n\r\n    <!-- IP选择下拉框 -->\r\n    <a-select\r\n      v-if=\"reachableIps.length\"\r\n      v-model=\"selectedIpValue\"\r\n      style=\"width: 100%; margin-top: 16px;\"\r\n      :placeholder=\"$t('tool.selectReachableIp') || '选择可达IP'\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <a-select-option v-for=\"ip in reachableIps\" :key=\"ip\" :value=\"ip\">\r\n        {{ ip }}\r\n      </a-select-option>\r\n    </a-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n  name: 'ProxySelector',\r\n  props: {\r\n    // 是否禁用控件\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 初始选中的IP\r\n    value: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isDetecting: false,\r\n      reachableIps: [],\r\n      selectedIpValue: this.value\r\n    };\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {\r\n    // 监听外部传入的value变化\r\n    value(newValue) {\r\n      this.selectedIpValue = newValue;\r\n    },\r\n    // 监听内部selectedIpValue变化，向外发送事件\r\n    selectedIpValue(newValue) {\r\n      this.$emit('input', newValue);\r\n      this.$emit('change', newValue);\r\n    }\r\n  },\r\n  methods: {\r\n    async fetchReachableIps() {\r\n      this.isDetecting = true;\r\n      try {\r\n        const response = await axios.get('/api/proxy/detect');\r\n        this.reachableIps = response.data.reachable_ips;\r\n        if (this.reachableIps.length) {\r\n          this.selectedIpValue = this.reachableIps[0];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error detecting reachable IPs:', error);\r\n        this.$notify.error({\r\n          title: 'Error',\r\n          message: 'Failed to detect reachable IPs'\r\n        });\r\n      } finally {\r\n        this.isDetecting = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProxySelector.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProxySelector.vue?vue&type=template&id=46f0b65a&scoped=true\"\nimport script from \"./ProxySelector.vue?vue&type=script&lang=js\"\nexport * from \"./ProxySelector.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46f0b65a\",\n  null\n  \n)\n\nexport default component.exports", "var classof = require('../internals/classof-raw');\nvar global = require('../internals/global');\n\nmodule.exports = classof(global.process) == 'process';\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  every: function every(fn) {\n    anObject(this);\n    aFunction(fn);\n    return !iterate(this, function (value, stop) {\n      if (!fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  some: function some(fn) {\n    anObject(this);\n    aFunction(fn);\n    return iterate(this, function (value, stop) {\n      if (fn(value)) return stop();\n    }, { IS_ITERATOR: true, INTERRUPTED: true }).stopped;\n  }\n});\n", "'use strict';\n// https://github.com/tc39/proposal-iterator-helpers\nvar $ = require('../internals/export');\nvar iterate = require('../internals/iterate');\nvar aFunction = require('../internals/a-function');\nvar anObject = require('../internals/an-object');\n\n$({ target: 'Iterator', proto: true, real: true }, {\n  reduce: function reduce(reducer /* , initialValue */) {\n    anObject(this);\n    aFunction(reducer);\n    var noInitial = arguments.length < 2;\n    var accumulator = noInitial ? undefined : arguments[1];\n    iterate(this, function (value) {\n      if (noInitial) {\n        noInitial = false;\n        accumulator = value;\n      } else {\n        accumulator = reducer(accumulator, value);\n      }\n    }, { IS_ITERATOR: true });\n    if (noInitial) throw TypeError('Reduce of empty iterator with no initial value');\n    return accumulator;\n  }\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var aFunction = require('../internals/a-function');\nvar toObject = require('../internals/to-object');\nvar IndexedObject = require('../internals/indexed-object');\nvar toLength = require('../internals/to-length');\n\n// `Array.prototype.{ reduce, reduceRight }` methods implementation\nvar createMethod = function (IS_RIGHT) {\n  return function (that, callbackfn, argumentsLength, memo) {\n    aFunction(callbackfn);\n    var O = toObject(that);\n    var self = IndexedObject(O);\n    var length = toLength(O.length);\n    var index = IS_RIGHT ? length - 1 : 0;\n    var i = IS_RIGHT ? -1 : 1;\n    if (argumentsLength < 2) while (true) {\n      if (index in self) {\n        memo = self[index];\n        index += i;\n        break;\n      }\n      index += i;\n      if (IS_RIGHT ? index < 0 : length <= index) {\n        throw TypeError('Reduce of empty array with no initial value');\n      }\n    }\n    for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {\n      memo = callbackfn(memo, self[index], index, O);\n    }\n    return memo;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.reduce` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduce\n  left: createMethod(false),\n  // `Array.prototype.reduceRight` method\n  // https://tc39.es/ecma262/#sec-array.prototype.reduceright\n  right: createMethod(true)\n};\n"], "sourceRoot": ""}