from obs import ObsClient


class ObsClientManager:
    _instance = None
    _bucket_name = None

    @classmethod
    def get_instance(cls, access_key_id, secret_access_key, server):
        if cls._instance is None:
            cls._instance = ObsClient(
                access_key_id=access_key_id,
                secret_access_key=secret_access_key,
                server=server
            )

        return cls._instance

