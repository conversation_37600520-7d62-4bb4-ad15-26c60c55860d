(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55098f76"],{"08a6":function(t,e,s){"use strict";s("db3d")},"13d5":function(t,e,s){"use strict";var a=s("23e7"),o=s("d58f").left,r=s("a640"),l=s("2d00"),i=s("605d"),c=r("reduce"),n=!i&&l>79&&l<83;a({target:"Array",proto:!0,forced:!c||n},{reduce:function(t){return o(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"2d74":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"2px"}},[e("div",{staticClass:"card-header-wrapper"},[e("div",{staticClass:"header-wrapper"},[e("div",{staticClass:"logo-wrapper"},[e("svg",{class:"text-"+t.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[e("path",{attrs:{fill:"currentColor",d:"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"}})])]),e("h6",{staticClass:"font-semibold m-0"},[t._v(t._s(t.$t("headTopic.fileUpload")))])]),e("a-button",{staticClass:"nav-style-button",attrs:{loading:t.uploading},on:{click:t.handleUpload}},[t._v(" "+t._s(t.$t("fileUpload.startUpload"))+" ")])],1),e("div",{staticClass:"main-content"},[e("div",{staticClass:"left-section"},[e("a-card",{staticClass:"compact-card",attrs:{size:"small"}},[e("a-form",{attrs:{layout:"vertical"},on:{submit:function(e){return e.preventDefault(),t.handleUpload.apply(null,arguments)}}},[e("a-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:t.$t("fileUpload.selectFile")}},[e("a-upload",{attrs:{"before-upload":t.beforeUpload,"show-upload-list":!1}},[e("a-button",{staticClass:"nav-style-button"},[e("a-icon",{attrs:{type:"upload"}}),t._v(" "+t._s(t.$t("fileUpload.clickToSelect"))+" ")],1)],1),t.fileName?e("span",{staticStyle:{"margin-left":"8px"}},[t._v(t._s(t.fileName))]):t._e()],1),e("a-form-item",{staticStyle:{"margin-bottom":"8px"},attrs:{label:t.$t("fileUpload.uploadPath")}},[e("a-input",{attrs:{placeholder:t.$t("fileUpload.enterUploadPath")},model:{value:t.uploadDir,callback:function(e){t.uploadDir=e},expression:"uploadDir"}})],1)],1)],1),e("a-card",{staticClass:"compact-card",attrs:{size:"small",title:t.$t("common.configureProxy")}},[e("proxy-selector",{attrs:{disabled:t.uploading},on:{change:t.handleProxyChange},model:{value:t.selectedProxyIp,callback:function(e){t.selectedProxyIp=e},expression:"selectedProxyIp"}})],1)],1),e("div",{staticClass:"right-section config-table"},[e("a-card",{staticClass:"compact-card",attrs:{size:"small",title:t.$t("common.configureNodes")}},[e("a-table",{staticClass:"bordered-nodes-table",attrs:{dataSource:t.nodes,columns:t.columns,rowKey:"ip",pagination:{pageSize:10,total:t.nodes.length,showSizeChanger:!1},rowSelection:t.rowSelection}})],1)],1)]),e("a-card",{staticClass:"compact-card",staticStyle:{"margin-top":"16px"},attrs:{title:t.$t("fileUpload.uploadProgress")}},[e("a-progress",{staticStyle:{"margin-bottom":"16px"},attrs:{percent:t.uploadProgress,status:t.progressBarStatus}}),e("a-table",{attrs:{dataSource:t.progressTableData,columns:t.progressColumns,rowKey:"ip",pagination:!1},scopedSlots:t._u([{key:"errorDetail",fn:function(s,a){return[a&&a.error_detail?e("a-popover",{attrs:{placement:"topLeft"}},[e("template",{slot:"content"},[e("p",[t._v("Time: "+t._s(a.error_detail.time))]),e("p",[t._v("Type: "+t._s(a.error_detail.type))]),e("p",[t._v("Message: "+t._s(a.error_detail.message))])]),e("a-icon",{staticStyle:{color:"#ff4d4f"},attrs:{type:"info-circle"}})],2):t._e()]}}])})],1),t.uploadResults?e("a-card",{staticStyle:{"margin-top":"16px"},attrs:{title:t.$t("fileUpload.uploadResults")}},[e("a-table",{attrs:{dataSource:t.uploadResultsData,columns:t.resultColumns,rowKey:"ip",pagination:!1}})],1):t._e()],1)},o=[],r=(s("13d5"),s("0643"),s("76d6"),s("4e3e"),s("a573"),s("9d4a"),s("9a9a"),s("2f62")),l=s("4f4d"),i=s("fec3"),c=s("5a3b"),n={mixins:[l["a"]],components:{ProxySelector:c["a"]},data(){return{file:null,fileName:"",selectedNodes:[],uploadDir:"/root/.test/Uploads",uploading:!1,selectedProxyIp:null,activeTask:null,pollInterval:null,isProcessing:!1,uploadResults:null}},computed:{...Object(r["e"])(["nodes","activeUploadTask","currentProject","sidebarColor"]),rowSelection(){return{selectedRowKeys:this.selectedNodes,onChange:t=>{this.selectedNodes=t}}},columns(){return[{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name"},{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip"}]},progressColumns(){const t=this.$createElement;return[{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip",width:"120px"},{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name",width:"150px",ellipsis:!0},{title:this.$t("tool.columns.status"),dataIndex:"status",key:"status",width:"100px",customRender:e=>{const s={pending:"#1890ff",in_progress:"#1890ff",paused:"#faad14",success:"#52c41a",failed:"#f5222d"}[e]||"#000";return t("span",{style:{color:s}},[e])}},{title:this.$t("tool.columns.progress"),dataIndex:"progress",key:"progress",width:"200px",customRender:(e,s)=>t("div",[t("a-progress",{attrs:{percent:e||0,size:"small",status:"failed"===s.status?"exception":"paused"===s.status?"normal":void 0}}),t("div",{style:"font-size: 12px; color: #999"},[this.formatBytes(s.bytes_transferred)," / ",this.formatBytes(this.activeUploadTask.metadata.file_size)])])},{title:this.$t("tool.columns.speed"),dataIndex:"speed",key:"speed",width:"100px"},{title:this.$t("tool.columns.errorDetails"),dataIndex:"error_detail",key:"error_detail",width:"60px",scopedSlots:{customRender:"errorDetail"}}]},progressTableData(){return this.activeUploadTask&&this.activeUploadTask.nodes?Object.keys(this.activeUploadTask.nodes).map(t=>{const e=this.activeUploadTask.nodes[t];return{ip:t,host_name:e.host_name,status:e.status,progress:e.progress||0,speed:e.speed||"-",bytes_transferred:e.bytes_transferred,error_detail:e.error_detail}}):[]},uploadProgress(){if(!this.activeUploadTask||!this.activeUploadTask.nodes)return 0;const t=Object.values(this.activeUploadTask.nodes);if(0===t.length)return 0;const e=t.reduce((t,e)=>t+(e.progress||0),0);return Math.round(e/t.length)},progressBarStatus(){if(!this.activeUploadTask||!this.activeUploadTask.nodes)return"normal";const t=Object.values(this.activeUploadTask.nodes||{});return 0===t.length?"normal":t.some(t=>"failed"===t.status)?"exception":t.every(t=>"completed"===t.status)?"success":"active"},formatSpeed(){if(!this.activeUploadTask||!this.activeUploadTask.nodes)return"";const t=Object.values(this.activeUploadTask.nodes||{}).reduce((t,e)=>"in_progress"===e.status&&e.speed?t+this.parseSpeed(e.speed):t,0);return this.formatBytes(t)+"/s"},formatOverallProgress(){var t;if(!this.activeUploadTask||!this.activeUploadTask.nodes)return"";const e=Object.values(this.activeUploadTask.nodes||{}),s=e.reduce((t,e)=>t+(e.bytes_transferred||0),0),a=((null===(t=this.activeUploadTask.metadata)||void 0===t?void 0:t.file_size)||0)*e.length;return`${this.formatBytes(s)} / ${this.formatBytes(a)}`},uploadResultsData(){if(!this.uploadResults)return[];const t=[];return this.uploadResults.success.forEach(e=>{t.push({ip:e,status:"success"})}),this.uploadResults.failed.forEach(e=>{t.push({ip:e,status:"failed",error:this.uploadResults.errors[e]})}),t},resultColumns(){const t=this.$createElement;return[{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip"},{title:this.$t("tool.columns.status"),dataIndex:"status",customRender:e=>t("span",{style:{color:"success"===e?"#52c41a":"#f5222d"}},[e])},{title:this.$t("tool.columns.errorDetails"),dataIndex:"error"}]}},created(){if(!this.checkDatabaseStatus())return;this.$store.dispatch("fetchNodes");const t=localStorage.getItem("uploadTask_"+this.currentProject);if(t){const{taskId:e,projectFile:s}=JSON.parse(t);s===this.currentProject&&this.checkActiveUploadTask()}},beforeDestroy(){this.pollInterval&&clearInterval(this.pollInterval)},methods:{...Object(r["b"])(["addNotification"]),beforeUpload(t){const e=5368709120;return t.size>e?(this.$message.error("File size exceeds the 5GB limit"),!1):(this.file=t,this.fileName=t.name,!1)},handleProxyChange(t){console.log("Proxy IP changed:",t),this.selectedProxyIp=t},validateUploadPath(t){return t.startsWith("/")?!(t.includes("..")||t.includes("./")||t.includes("~"))||(this.$message.error("Path cannot contain relative path components"),!1):(this.$message.error("Path must start with a slash (/)"),!1)},async handleUpload(){if(!this.file||!this.selectedNodes.length||!this.selectedProxyIp)return void this.$message.error("Please select a file, nodes, and a proxy IP");if(!this.validateUploadPath(this.uploadDir))return;const t=localStorage.getItem("currentProject");if(!t)return this.$message.error("No project selected. Please select a project first."),void this.$router.push("/projects");try{this.uploading=!0;const t=localStorage.getItem("uploadTask_"+this.currentProject);if(t)try{const{taskId:e}=JSON.parse(t);e&&this.clearTaskNotificationMark(e,"upload",this.currentProject)}catch(s){console.error("Error clearing previous upload notification:",s)}const e=new FormData;e.append("file",this.file),e.append("targets",JSON.stringify(this.selectedNodes)),e.append("upload_dir",this.uploadDir),e.append("proxyIp",this.selectedProxyIp);const a=await i["a"].post("/api/file_transfer/upload?dbFile="+encodeURIComponent(this.currentProject),e,{headers:{"Content-Type":"multipart/form-data"}}),o=a.data.task_id;localStorage.setItem("uploadTask_"+this.currentProject,JSON.stringify({taskId:o,projectFile:this.currentProject})),localStorage.removeItem("uploadTaskCompleted_"+this.currentProject),this.startPolling(o)}catch(a){var e;console.error("Upload error:",a),this.message=`${this.$t("fileUpload.error")}: ${(null===(e=a.response)||void 0===e||null===(e=e.data)||void 0===e?void 0:e.error)||a.message}`,this.messageType="error",this.uploading=!1}},async startPolling(t){this.pollInterval&&clearInterval(this.pollInterval);const e=async()=>{try{if(!this.currentProject)throw new Error("No project database selected");const e=await i["a"].get(`/api/file_transfer/status/${t}?dbFile=${encodeURIComponent(this.currentProject)}`);this.$store.dispatch("updateUploadTask",e.data);const s=Object.values(e.data.nodes).every(t=>["success","failed"].includes(t.status));if(s){clearInterval(this.pollInterval),this.uploading=!1,localStorage.setItem("uploadTaskCompleted_"+this.currentProject,"true");const s=Object.values(e.data.nodes);this.addTaskCompletionNotification({taskId:t,taskType:"upload",nodes:s,projectId:this.currentProject})}}catch(s){var e;console.error("Error polling status:",s),404===(null===(e=s.response)||void 0===e?void 0:e.status)&&(clearInterval(this.pollInterval),this.uploading=!1,localStorage.removeItem("uploadTask_"+this.currentProject),localStorage.removeItem("uploadTaskCompleted_"+this.currentProject))}};await e(),this.pollInterval=setInterval(e,5e3)},async checkActiveUploadTask(){try{const t=localStorage.getItem("uploadTask_"+this.currentProject),e=localStorage.getItem("uploadTaskCompleted_"+this.currentProject);if(t){const{taskId:s,projectFile:a}=JSON.parse(t);if(a!==this.currentProject)return;const o=await i["a"].get(`/api/file_transfer/status/${s}?dbFile=${encodeURIComponent(this.currentProject)}`);if(o.data){this.$store.dispatch("updateUploadTask",o.data);const t=Object.values(o.data.nodes).every(t=>["success","failed"].includes(t.status));if(t||e){if(t){this.uploading=!1,localStorage.setItem("uploadTaskCompleted_"+this.currentProject,"true");const t=Object.values(o.data.nodes);this.addTaskCompletionNotification({taskId:s,taskType:"upload",nodes:t,projectId:this.currentProject})}}else this.uploading=!0,this.startPolling(s)}}}catch(t){console.error("Error checking active upload task:",t)}},formatBytes(t){if(!t)return"0 B";const e=["B","KB","MB","GB"],s=Math.floor(Math.log(t)/Math.log(1024));return`${(t/Math.pow(1024,s)).toFixed(2)} ${e[s]}`},parseSpeed(t){if(!t||"-"===t)return 0;const[e,s]=t.split(" "),a={"B/s":1,"KB/s":1024,"MB/s":1048576}[s]||1;return parseFloat(e)*a},checkDatabaseStatus(){const t=localStorage.getItem("currentProject");return!!t||(this.$notify.error({title:this.$t("fileUpload.error"),message:this.$t("fileUpload.noProjectSelected")}),this.$router.push("/projects"),!1)}},watch:{currentProject:{handler(t,e){t!==e&&(this.$store.dispatch("updateUploadTask",null),this.pollInterval&&clearInterval(this.pollInterval),this.checkActiveUploadTask())},immediate:!0}}},d=n,p=(s("08a6"),s("2877")),u=Object(p["a"])(d,a,o,!1,null,"014974c6",null);e["default"]=u.exports},"4f4d":function(t,e,s){"use strict";s("0643"),s("2382");e["a"]={methods:{addTaskCompletionNotification({taskId:t,taskType:e,nodes:s,projectId:a,titles:o={},templates:r={},statusMapping:l={}}){const i=`${e}Notified_${a}_${t}`;if(localStorage.getItem(i))return;const c={success:["success","completed"],failure:["failed"]},n={success:[...l.success||[],...c.success],failure:[...l.failure||[],...c.failure]},d=s.filter(t=>n.success.includes(t.status)&&!t.error_detail).length,p=s.filter(t=>n.failure.includes(t.status)||t.error_detail).length,u=p>0;let h,f;h=u?o.error||this.getDefaultErrorTitle(e):o.success||this.getDefaultSuccessTitle(e),f=u?r.error||`${d} nodes completed successfully, ${p} nodes failed.`:r.success||`All ${s.length} nodes completed successfully.`,this.addNotification({title:h,message:f,type:u?"error":"success",taskId:t}),localStorage.setItem(i,"true")},getDefaultSuccessTitle(t){const e={task:"Task Completed",upload:"File Upload Completed",download:"File Download Completed",tool:"Tool Execution Completed"};return e[t]||"Operation Completed"},getDefaultErrorTitle(t){const e={task:"Task Completed with Errors",upload:"File Upload Completed with Errors",download:"File Download Completed with Errors",tool:"Tool Execution Completed with Errors"};return e[t]||"Operation Completed with Errors"},clearTaskNotificationMark(t,e,s){const a=`${e}Notified_${s}_${t}`;localStorage.removeItem(a)}}}},"5a3b":function(t,e,s){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"proxy-selector"},[e("a-button",{staticClass:"nav-style-button",attrs:{loading:t.isDetecting,disabled:t.disabled},on:{click:t.fetchReachableIps}},[t._v(" "+t._s(t.$t("common.detectReachableIps")||"检测可达IP")+" ")]),t.reachableIps.length?e("a-select",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{placeholder:t.$t("tool.selectReachableIp")||"选择可达IP",disabled:t.disabled},model:{value:t.selectedIpValue,callback:function(e){t.selectedIpValue=e},expression:"selectedIpValue"}},t._l(t.reachableIps,(function(s){return e("a-select-option",{key:s,attrs:{value:s}},[t._v(" "+t._s(s)+" ")])})),1):t._e()],1)},o=[],r=s("fec3"),l={name:"ProxySelector",props:{disabled:{type:Boolean,default:!1},value:{type:String,default:null}},data(){return{isDetecting:!1,reachableIps:[],selectedIpValue:this.value}},computed:{},watch:{value(t){this.selectedIpValue=t},selectedIpValue(t){this.$emit("input",t),this.$emit("change",t)}},methods:{async fetchReachableIps(){this.isDetecting=!0;try{const t=await r["a"].get("/api/proxy/detect");this.reachableIps=t.data.reachable_ips,this.reachableIps.length&&(this.selectedIpValue=this.reachableIps[0])}catch(t){console.error("Error detecting reachable IPs:",t),this.$notify.error({title:"Error",message:"Failed to detect reachable IPs"})}finally{this.isDetecting=!1}}}},i=l,c=s("2877"),n=Object(c["a"])(i,a,o,!1,null,"46f0b65a",null);e["a"]=n.exports},"605d":function(t,e,s){var a=s("c6b6"),o=s("da84");t.exports="process"==a(o.process)},"76d6":function(t,e,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{every:function(t){return l(this),r(t),!o(this,(function(e,s){if(!t(e))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9a9a":function(t,e,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{some:function(t){return l(this),r(t),o(this,(function(e,s){if(t(e))return s()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9d4a":function(t,e,s){"use strict";var a=s("23e7"),o=s("2266"),r=s("1c0b"),l=s("825a");a({target:"Iterator",proto:!0,real:!0},{reduce:function(t){l(this),r(t);var e=arguments.length<2,s=e?void 0:arguments[1];if(o(this,(function(a){e?(e=!1,s=a):s=t(s,a)}),{IS_ITERATOR:!0}),e)throw TypeError("Reduce of empty iterator with no initial value");return s}})},a640:function(t,e,s){"use strict";var a=s("d039");t.exports=function(t,e){var s=[][t];return!!s&&a((function(){s.call(null,e||function(){throw 1},1)}))}},d58f:function(t,e,s){var a=s("1c0b"),o=s("7b0b"),r=s("44ad"),l=s("50c4"),i=function(t){return function(e,s,i,c){a(s);var n=o(e),d=r(n),p=l(n.length),u=t?p-1:0,h=t?-1:1;if(i<2)while(1){if(u in d){c=d[u],u+=h;break}if(u+=h,t?u<0:p<=u)throw TypeError("Reduce of empty array with no initial value")}for(;t?u>=0:p>u;u+=h)u in d&&(c=s(c,d[u],u,n));return c}};t.exports={left:i(!1),right:i(!0)}},db3d:function(t,e,s){}}]);
//# sourceMappingURL=chunk-55098f76.0a45aa50.js.map