from flask import Blueprint, jsonify
from services.os_package_service import PackageService
from routes.database_wraps import with_database_session

bp = Blueprint('package', __name__)


@bp.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_packages(node_ip, db):
    package_service = PackageService(db)
    node = package_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    result = package_service.get_packages(node.id)
    if not result:
        return jsonify([]), 200

    return jsonify(result), 200
