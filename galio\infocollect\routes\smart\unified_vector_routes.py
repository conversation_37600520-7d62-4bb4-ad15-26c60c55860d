"""
统一向量搜索路由
整合ChromaDB向量数据库的管理和搜索功能
"""

from flask import Blueprint, jsonify, request
import asyncio
from datetime import datetime
from typing import Dict, Any

from intelligent_executor.vectordb.manager import VectorDBManager
from log.logger import log_info, log_error, log_debug

bp = Blueprint('unified_vector', __name__)

# 全局向量数据库管理器实例
vector_manager = None


def get_vector_manager() -> VectorDBManager:
    """获取向量数据库管理器实例"""
    global vector_manager
    if vector_manager is None:
        vector_manager = VectorDBManager()
    return vector_manager


@bp.route('/status', methods=['GET'])
def get_vector_status():
    """获取向量数据库状态"""
    try:
        manager = get_vector_manager()
        status = manager.get_status()
        return jsonify(status), 200
    except Exception as e:
        log_error(f"Error getting vector status: {e}")
        return jsonify({'error': str(e), 'status': 'error'}), 500


@bp.route('/search', methods=['GET', 'POST'])
def search_testcases():
    """
    搜索测试用例

    GET参数或POST JSON体:
    - query: 查询文本 (必需)
    - search_type: 搜索类型，可选值为 'comprehensive'(默认), 'name', 'steps'
    - top_k: 返回结果数量，默认为3

    返回:
    - 相似测试用例列表，包含相似度分数
    """
    try:
        # 获取参数，支持GET和POST请求
        if request.method == 'GET':
            query = request.args.get('query', '').strip()
            search_type = request.args.get('search_type', 'comprehensive')
            top_k = request.args.get('top_k', 3, type=int)
        else:  # POST
            data = request.get_json()
            if not data:
                return jsonify({'error': '请求体必须是JSON格式'}), 400

            query = data.get('query', '').strip()
            search_type = data.get('search_type', 'comprehensive')
            top_k = data.get('top_k', 3)

        # 验证参数
        if not query:
            return jsonify({'error': '查询文本不能为空'}), 400

        if search_type not in ['comprehensive', 'name', 'steps']:
            return jsonify({'error': '搜索类型必须是 "comprehensive", "name" 或 "steps"'}), 400

        if not isinstance(top_k, int) or top_k < 1 or top_k > 20:
            return jsonify({'error': 'top_k 必须是1-20之间的整数'}), 400

        # 执行搜索
        manager = get_vector_manager()
        results = manager.search_similar_testcases(
            query=query,
            top_k=top_k,
            search_type=search_type
        )

        # 构建响应
        response = {
            'query': query,
            'search_type': search_type,
            'top_k': top_k,
            'total': len(results),
            'results': results,
            'success': True
        }

        log_info(f"Vector search completed: {len(results)} results for '{query}'")
        return jsonify(response), 200

    except Exception as e:
        log_error(f"Error in vector search: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@bp.route('/initialize', methods=['POST'])
def initialize_vector_database():
    """
    初始化/重建向量数据库

    POST JSON体:
    - force_rebuild: 是否强制重建 (可选，默认false)

    返回:
    - 初始化结果
    """
    try:
        data = request.get_json() or {}
        force_rebuild = data.get('force_rebuild', False)

        manager = get_vector_manager()
        
        # 创建新的事件循环来运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            if force_rebuild:
                result = loop.run_until_complete(manager.rebuild_index())
                message = "向量数据库重建完成"
            else:
                result = loop.run_until_complete(manager.sync_from_sqlite(force_rebuild=False))
                message = "向量数据库同步完成"
        finally:
            loop.close()

        if result.get('success'):
            response = {
                'status': 'success',
                'message': message,
                'synced_count': result.get('synced_count', 0),
                'total_in_collection': result.get('total_in_collection', 0)
            }
            return jsonify(response), 200
        else:
            return jsonify({
                'status': 'error',
                'message': f'向量数据库初始化失败: {result.get("error", "未知错误")}'
            }), 500

    except Exception as e:
        log_error(f"Error initializing vector database: {e}")
        return jsonify({'error': str(e), 'status': 'error'}), 500


@bp.route('/stats', methods=['GET'])
def get_collection_stats():
    """获取向量集合统计信息"""
    try:
        manager = get_vector_manager()
        stats = manager.get_collection_stats()
        return jsonify(stats), 200
    except Exception as e:
        log_error(f"Error getting collection stats: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/check', methods=['GET'])
def check_vector_database():
    """检查向量数据库是否需要同步"""
    try:
        manager = get_vector_manager()
        status = manager.check_vector_db_status()
        return jsonify(status), 200
    except Exception as e:
        log_error(f"Error checking vector database: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        manager = get_vector_manager()
        status = manager.get_status()
        
        is_healthy = (
            status.get('status') == 'connected' and
            status.get('vector_count', 0) > 0
        )
        
        response = {
            'healthy': is_healthy,
            'timestamp': datetime.now().isoformat(),
            'status': status
        }
        
        return jsonify(response), 200 if is_healthy else 503
        
    except Exception as e:
        log_error(f"Health check failed: {e}")
        return jsonify({
            'healthy': False,
            'error': str(e)
        }), 503 