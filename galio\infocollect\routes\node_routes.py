"""
节点路由模块 - 提供节点相关的API接口
"""
from flask import Blueprint, jsonify
from services.node_service import NodeService
from routes.database_wraps import with_database_session

bp = Blueprint('node', __name__)


@bp.route('/nodes', methods=['GET'])
@with_database_session
def get_nodes(db):
    """
    获取所有节点信息
    :return: 节点信息列表
    """
    node_service = NodeService(db)
    nodes = node_service.get_all_nodes()
    return jsonify({"items": nodes, "total": len(nodes)}), 200
