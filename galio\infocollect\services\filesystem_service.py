from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.filesystem_datamodel import FilesystemSnapshot


class FilesystemService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_filesystem(self, node_id: int) -> List[Dict]:
        """获取指定节点的所有文件系统信息"""
        filesystem_items = self.db.query(FilesystemSnapshot).filter_by(node_id=node_id).all()
        return [fs.to_dict() for fs in filesystem_items] if filesystem_items else []

    @staticmethod
    def get_insert_objects(mount_info, node_id: int) -> List[FilesystemSnapshot]:
        """获取要插入的文件系统对象列表"""
        insert_objects = []
        for mount in mount_info:
            filesystem_snapshot = FilesystemSnapshot(
                node_id=node_id,
                device=mount['device'],
                mount_point=mount['mount_point'],
                fs_type=mount['fs_type'],
                mount_options=mount.get('mount_options', None)
            )
            insert_objects.append(filesystem_snapshot)
        return insert_objects
