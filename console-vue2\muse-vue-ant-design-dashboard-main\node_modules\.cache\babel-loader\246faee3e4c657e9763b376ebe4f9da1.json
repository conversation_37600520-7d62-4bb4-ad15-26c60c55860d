{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue", "mtime": 1751513794205}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "axios", "RefreshButton", "CrictlInfo", "JsonDetailModal", "ProcessDetailModal", "ProcessTable", "NetworkListeningModal", "NetworkListeningCell", "MountCell", "components", "name", "props", "data", "h", "$createElement", "activeTab", "hostConfigData", "containerData", "networkData", "loadingHostConfig", "loadingContainers", "loadingNetworks", "hostConfigColumns", "title", "dataIndex", "key", "render", "text", "containerColumns", "width", "ellipsis", "customRender", "_", "record", "inspectData", "parseJsonField", "inspect_data", "children", "Name", "style", "whiteSpace", "wordBreak", "e", "console", "warn", "_inspectData$HostConf", "privileged", "HostConfig", "Privileged", "envVars", "env", "length", "slice", "map", "index", "substring", "stopPropagation", "showEnvironmentDetails", "showMountDetails", "showNetworkDetails", "align", "_processData$process_", "processData", "processes", "process_list", "showProcessDetails", "showInspectDetails", "networkColumns", "undefined", "labels", "Object", "keys", "entries", "value", "join", "pagination", "pageSize", "resourceMapping", "networkDetailsVisible", "selectedNetwork", "mountDetailsVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentDetailsVisible", "selectedEnvironment", "processDetailsVisible", "selectedProcessContainer", "processDetailInfoVisible", "selectedProcessInfo", "activeEngine", "computed", "watch", "selectedNodeIp", "newIp", "resetData", "fetchActiveTabData", "mounted", "methods", "handleTabChange", "error", "resourceType", "dataName", "camelCaseToDataName", "loadingKey", "capitalizeFirstLetter", "replace", "response", "get", "params", "dbFile", "currentProject", "camelCase", "<PERSON><PERSON><PERSON><PERSON>", "letter", "toUpperCase", "string", "char<PERSON>t", "getDaemonConfig", "_this$hostConfigData$", "daemon_config", "getRegistryMirrors", "registryConfig", "Mirrors", "formatBytes", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "field", "defaultValue", "Array", "isArray", "cleanReactiveObject", "JSON", "parse", "container", "$refs", "jsonDetailModal", "showDetailModal", "$message", "renderPreviewWithMore", "items", "renderItem", "moreText", "onClickMore", "item", "obj", "cleaned", "startsWith", "getProcessList", "_this$selectedProcess", "getNetworkListening", "_this$selectedContain", "<PERSON><PERSON><PERSON><PERSON>", "exposures", "exposedServices", "listening_ports", "handleNetworkDetailsClose", "mounts", "handleMountDetailsClose", "getAllMounts", "_this$selectedMountCo", "handleEnvironmentDetailsClose", "getAllEnvironmentVars", "_this$selectedEnviron", "handleProcessDetailsClose", "showProcessDetailInfo", "process", "handleProcessDetailInfoClose"], "sources": ["src/components/Cards/DockerInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full docker-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"25\" height=\"25\"\r\n                viewBox=\"0 0 24 24\"\r\n                :class=\"`text-${sidebarColor}`\"\r\n            >\r\n              <path fill=\"currentColor\" d=\"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.docker') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <div class=\"container-runtime-tabs\">\r\n      <a-tabs v-model:activeKey=\"activeEngine\">\r\n        <a-tab-pane key=\"docker\" tab=\"Docker\">\r\n          <a-tabs v-model:activeKey=\"activeTab\" @change=\"handleTabChange\">\r\n            <a-tab-pane key=\"docker_host_config\" tab=\"Host Config\">\r\n              <div v-if=\"activeTab === 'docker_host_config'\" class=\"host-config-container\">\r\n                <a-row :gutter=\"[16, 16]\">\r\n                  <!-- Basic Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Basic Information\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Docker Version:</strong> {{ hostConfigData[0]?.docker_version }}</p>\r\n                      <p class=\"info-item\"><strong>Operating System:</strong> {{ getDaemonConfig('OperatingSystem') }}</p>\r\n                      <p class=\"info-item\"><strong>Architecture:</strong> {{ getDaemonConfig('Architecture') }}</p>\r\n                      <p class=\"info-item\"><strong>Kernel Version:</strong> {{ getDaemonConfig('KernelVersion') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Resource Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Resources\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>CPU Cores:</strong> {{ getDaemonConfig('NCPU') }}</p>\r\n                      <p class=\"info-item\"><strong>Total Memory:</strong> {{ formatBytes(getDaemonConfig('MemTotal')) }}</p>\r\n                      <p class=\"info-item\"><strong>Docker Root Dir:</strong> {{ getDaemonConfig('DockerRootDir') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Security Settings -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Security\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>User in Docker Group:</strong> {{ hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Root User:</strong> {{ hostConfigData[0]?.is_root_user ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Security Options:</strong> {{ getDaemonConfig('SecurityOptions')?.join(', ') || 'None' }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Container Statistics -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Container Statistics\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Total Containers:</strong> {{ getDaemonConfig('Containers') }}</p>\r\n                      <p class=\"info-item\"><strong>Running:</strong> {{ getDaemonConfig('ContainersRunning') }}</p>\r\n                      <p class=\"info-item\"><strong>Stopped:</strong> {{ getDaemonConfig('ContainersStopped') }}</p>\r\n                      <p class=\"info-item\"><strong>Images:</strong> {{ getDaemonConfig('Images') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Driver Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Storage Driver\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Driver:</strong> {{ getDaemonConfig('Driver') }}</p>\r\n                      <p class=\"info-item\"><strong>Logging Driver:</strong> {{ getDaemonConfig('LoggingDriver') }}</p>\r\n                      <p class=\"info-item\"><strong>Cgroup Driver:</strong> {{ getDaemonConfig('CgroupDriver') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Registry Configuration -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Registry Configuration\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Index Server:</strong> {{ getDaemonConfig('IndexServerAddress') }}</p>\r\n                      <p class=\"info-item\"><strong>Registry Mirrors:</strong></p>\r\n                      <ul>\r\n                        <li v-for=\"mirror in getRegistryMirrors()\" :key=\"mirror\" class=\"info-item\">{{ mirror }}</li>\r\n                      </ul>\r\n                    </a-card>\r\n                  </a-col>\r\n                </a-row>\r\n              </div>\r\n            </a-tab-pane>\r\n            <a-tab-pane key=\"docker_network\" tab=\"Networks\">\r\n              <a-table\r\n                :columns=\"networkColumns\"\r\n                :data-source=\"networkData\"\r\n                :rowKey=\"(record) => record.network_id\"\r\n                v-if=\"activeTab === 'docker_network'\"\r\n                :loading=\"loadingNetworks\"\r\n                :pagination=\"pagination\"\r\n              >\r\n              </a-table>\r\n            </a-tab-pane>\r\n\r\n            <a-tab-pane key=\"docker_container\" tab=\"Containers\">\r\n              <a-table\r\n                :columns=\"containerColumns\"\r\n                :data-source=\"containerData\"\r\n                :scroll=\"{ x: 1500 }\"\r\n                :pagination=\"{ pageSize: 20 }\"\r\n                :row-key=\"record => record.container_id\"\r\n                v-if=\"activeTab === 'docker_container'\"\r\n                :loading=\"loadingContainers\"\r\n              >\r\n                <template #mountsColumn=\"{ record }\">\r\n                  <div v-if=\"record\">\r\n                    <template v-if=\"record.mounts && record.mounts.length\">\r\n                      <div v-for=\"(mount, index) in record.mounts\" :key=\"index\">\r\n                        {{ mount.Source }} → {{ mount.Destination }}\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>N/A</span>\r\n                  </div>\r\n                </template>\r\n              </a-table>\r\n\r\n\r\n\r\n              <network-listening-modal\r\n                :visible=\"networkDetailsVisible\"\r\n                :network-data=\"selectedNetwork\"\r\n                @update:visible=\"networkDetailsVisible = $event\"\r\n                @close=\"handleNetworkDetailsClose\"\r\n              />\r\n            </a-tab-pane>\r\n          </a-tabs>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"crictl\" tab=\"CRI\">\r\n          <crictl-info :node-ip=\"selectedNodeIp\" />\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </div>\r\n\r\n    <a-modal\r\n      v-model:visible=\"mountDetailsVisible\"\r\n      title=\"Mount Details\"\r\n      width=\"800px\"\r\n      @cancel=\"handleMountDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleMountDetailsClose\">Cancel</a-button>\r\n      </template>\r\n      <template v-if=\"selectedMountContainer\">\r\n        <div class=\"mounts-container\">\r\n          <div v-for=\"(mount, index) in getAllMounts()\" :key=\"index\" class=\"mount-item\">\r\n            <div class=\"mount-path\">\r\n              <span class=\"mount-source\">{{ mount.Source }}</span>\r\n              <span class=\"mount-arrow\">→</span>\r\n              <span class=\"mount-dest\">{{ mount.Destination }}</span>\r\n            </div>\r\n            <div class=\"mount-details\">\r\n              <span v-if=\"mount.Mode\" class=\"mount-tag\">{{ mount.Mode }}</span>\r\n              <span class=\"mount-tag\">{{ mount.RW ? 'RW' : 'RO' }}</span>\r\n              <span v-if=\"mount.Propagation\" class=\"mount-tag\">{{ mount.Propagation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <a-modal\r\n      v-model:visible=\"environmentDetailsVisible\"\r\n      title=\"Environment Variables\"\r\n      width=\"800px\"\r\n      @cancel=\"handleEnvironmentDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleEnvironmentDetailsClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"selectedEnvironment\">\r\n        <div class=\"env-container\">\r\n          <div v-for=\"(env, index) in getAllEnvironmentVars()\" :key=\"index\" class=\"env-item\">\r\n            {{ env }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-table\r\n      :visible=\"processDetailsVisible\"\r\n      :process-container=\"selectedProcessContainer\"\r\n      user-field=\"uid\"\r\n      :include-tty=\"true\"\r\n      @update:visible=\"processDetailsVisible = $event\"\r\n      @close=\"handleProcessDetailsClose\"\r\n    />\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      user-field=\"uid\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n\r\n\r\n\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport CrictlInfo from './CrictlInfo.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\nimport { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index';\r\nimport { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index';\r\nimport { MountCell } from '../Widgets/Mount/mount_index';\r\n// 不再使用单独的环境变量组件\r\n// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    CrictlInfo,\r\n    JsonDetailModal,\r\n    ProcessDetailModal,\r\n    ProcessTable,\r\n    NetworkListeningModal,\r\n    NetworkListeningCell,\r\n    MountCell,\r\n    // EnvironmentCell,\r\n    // EnvironmentModal\r\n  },\r\n  name: 'DockerInfo',\r\n  props: {\r\n    // Using selectedNodeIp from Vuex store instead of prop\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'docker_host_config',\r\n      hostConfigData: [],\r\n      containerData: [],\r\n      networkData: [],\r\n      loadingHostConfig: false,\r\n      loadingContainers: false,\r\n      loadingNetworks: false,\r\n      hostConfigColumns: [\r\n        { title: 'Docker Version', dataIndex: 'docker_version', key: 'docker_version' },\r\n        {\r\n          title: 'Daemon Config',\r\n          dataIndex: 'daemon_config',\r\n          key: 'daemon_config',\r\n        },\r\n        {\r\n          title: 'In Docker Group',\r\n          dataIndex: 'user_in_docker_group',\r\n          key: 'user_in_docker_group',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n        {\r\n          title: 'Is Root',\r\n          dataIndex: 'is_root_user',\r\n          key: 'is_root_user',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n      ],\r\n      containerColumns: [\r\n        {\r\n          title: 'Container ID',\r\n          dataIndex: 'container_id',\r\n          key: 'container_id',\r\n          width: '150px',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'inspect_data',\r\n          key: 'name',\r\n          width: '15%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              return {\r\n                children: inspectData.Name || 'N/A',\r\n                props: {\r\n                  style: {\r\n                    whiteSpace: 'normal',\r\n                    wordBreak: 'break-word'\r\n                  }\r\n                }\r\n              };\r\n            } catch (e) {\r\n              console.warn('Failed to render container name:', e);\r\n              return 'N/A';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Privileged',\r\n          dataIndex: 'inspect_data',\r\n          key: 'privileged',\r\n          width: '100px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              const privileged = inspectData.HostConfig?.Privileged;\r\n              // 将undefined、null或非布尔值视为false\r\n              return privileged === true ? 'Yes' : 'No';\r\n            } catch (e) {\r\n              console.warn('Failed to render privileged status:', e);\r\n              return 'No'; // 出错时默认显示为No\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Environment',\r\n          dataIndex: 'env',\r\n          key: 'env',\r\n          width: '200px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const envVars = this.parseJsonField(record.env, []);\r\n              if (!envVars.length) return 'No environment variables';\r\n\r\n              return (\r\n                <div style=\"font-size: 12px;\">\r\n                  {envVars.slice(0, 1).map((env, index) => (\r\n                    <div key={index} style=\"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">\r\n                      {env.length > 200 ? env.substring(0, 200) + '...' : env}\r\n                    </div>\r\n                  ))}\r\n                  {envVars.length > 1 && (\r\n                    <a-button\r\n                      type=\"link\"\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        this.showEnvironmentDetails(record);\r\n                      }}\r\n                    >\r\n                      +{envVars.length - 1} more...\r\n                    </a-button>\r\n                  )}\r\n                </div>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render env vars:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Mounts',\r\n          dataIndex: 'mounts',\r\n          key: 'mounts',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <mount-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showMountDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Network Listening',\r\n          dataIndex: 'exposures',\r\n          key: 'exposures',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <network-listening-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showNetworkDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Processes',\r\n          dataIndex: 'processes',\r\n          key: 'processes',\r\n          width: '120px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const processData = this.parseJsonField(record.processes, {});\r\n              if (!processData?.process_list?.length) return 'N/A';\r\n\r\n              return (\r\n                <a onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetails(record);\r\n                }}>\r\n                  {processData.process_list.length} processes\r\n                </a>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render processes:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Inspect Data',\r\n          dataIndex: 'inspect_data',\r\n          key: 'inspect_data',\r\n          width: '150px',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <span style=\"display: inline-block; line-height: 22px;\">\r\n              <a\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showInspectDetails(record);\r\n                }}\r\n                style=\"color: #1890ff; cursor: pointer;\"\r\n              >\r\n                View Inspect\r\n              </a>\r\n            </span>\r\n          )\r\n        }\r\n      ],\r\n      networkColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '120px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Network ID',\r\n          dataIndex: 'network_id',\r\n          key: 'network_id',\r\n          width: '180px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Driver',\r\n          dataIndex: 'driver',\r\n          key: 'driver',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Scope',\r\n          dataIndex: 'scope',\r\n          key: 'scope',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'IPv6',\r\n          dataIndex: 'ipv6',\r\n          key: 'ipv6',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Internal',\r\n          dataIndex: 'internal',\r\n          key: 'internal',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Labels',\r\n          dataIndex: 'labels',\r\n          key: 'labels',\r\n          width: '120px',\r\n          customRender: (labels) => {\r\n            if (!labels || Object.keys(labels).length === 0) return 'None';\r\n            return Object.entries(labels).map(([key, value]) => `${key}=${value}`).join(', ');\r\n          }\r\n        },\r\n        {\r\n          title: 'Created',\r\n          dataIndex: 'created_at',\r\n          key: 'created_at',\r\n          width: '160px',\r\n          customRender: (text) => text || 'None'\r\n        }\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      resourceMapping: {\r\n        'docker_host_config': 'get_docker_host_config',\r\n        'docker_container': 'get_docker_containers',\r\n        'docker_network': 'get_docker_networks',\r\n      },\r\n\r\n\r\n\r\n      networkDetailsVisible: false,\r\n      selectedNetwork: null,\r\n      mountDetailsVisible: false,\r\n      selectedMountContainer: null,\r\n      environmentDetailsVisible: false,\r\n      selectedEnvironment: null,\r\n      processDetailsVisible: false,\r\n      selectedProcessContainer: null,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null,\r\n      activeEngine: 'docker'\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.resetData();\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      const dataName = this.camelCaseToDataName(resourceType);\r\n      const loadingKey = `loading${this.capitalizeFirstLetter(dataName.replace('Data', ''))}`;\r\n\r\n      this[loadingKey] = true;\r\n\r\n      try {\r\n        const response = await axios.get(`/api/docker/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[dataName] = resourceType === 'docker_host_config' && data ? [data] : data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[dataName] = [];\r\n      } finally {\r\n        this[loadingKey] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutDocker = camelCase.replace('docker_', '');\r\n      return withoutDocker.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.hostConfigData = [];\r\n      this.containerData = [];\r\n      this.networkData = [];\r\n    },\r\n    getDaemonConfig(key) {\r\n      if (!this.hostConfigData[0]?.daemon_config) return null;\r\n      return this.hostConfigData[0].daemon_config[key];\r\n    },\r\n    getRegistryMirrors() {\r\n      const registryConfig = this.getDaemonConfig('RegistryConfig');\r\n      return registryConfig?.Mirrors || [];\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    parseJsonField(field, defaultValue) {\r\n      try {\r\n        if (Array.isArray(field)) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        if (typeof field === 'object' && field !== null) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n      } catch (e) {\r\n        console.warn('Failed to parse JSON field:', e);\r\n        return defaultValue;\r\n      }\r\n    },\r\n\r\n    showInspectDetails(container) {\r\n      try {\r\n        const inspectData = this.parseJsonField(container.inspect_data, {});\r\n        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);\r\n      } catch (e) {\r\n        console.error('Failed to parse inspect data:', e);\r\n        this.$message.error('Failed to parse inspect data');\r\n      }\r\n    },\r\n    renderPreviewWithMore(items, renderItem, moreText, onClickMore) {\r\n      if (!items || !items.length) return 'None';\r\n\r\n      return (\r\n        <div style=\"font-size: 12px;\">\r\n          {items.slice(0, 2).map((item, index) => renderItem(item, index))}\r\n          {items.length > 2 && (\r\n            <a-button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onClickMore();\r\n              }}\r\n            >\r\n              +{items.length - 2} {moreText}\r\n            </a-button>\r\n          )}\r\n        </div>\r\n      );\r\n    },\r\n    cleanReactiveObject(obj) {\r\n      if (!obj) return null;\r\n      if (Array.isArray(obj)) {\r\n        return obj.map(item => this.cleanReactiveObject(item));\r\n      }\r\n      if (typeof obj === 'object') {\r\n        const cleaned = {};\r\n        for (const key in obj) {\r\n          if (!key.startsWith('_') && !key.startsWith('$') && key !== '__ob__') {\r\n            cleaned[key] = this.cleanReactiveObject(obj[key]);\r\n          }\r\n        }\r\n        return cleaned;\r\n      }\r\n      return obj;\r\n    },\r\n    getProcessList() {\r\n      if (!this.selectedProcessContainer?.processes?.process_list) return [];\r\n      return this.selectedProcessContainer.processes.process_list;\r\n    },\r\n    getNetworkListening() {\r\n      if (!this.selectedContainer?.exposures) return [];\r\n      const exposedServices = this.parseJsonField(this.selectedContainer.exposures, {});\r\n      return exposedServices.listening_ports || [];\r\n    },\r\n    showNetworkDetails(container) {\r\n      this.selectedNetwork = {\r\n        ...container,\r\n        exposures: this.parseJsonField(container.exposures, {})\r\n      };\r\n      this.networkDetailsVisible = true;\r\n    },\r\n    handleNetworkDetailsClose() {\r\n      this.networkDetailsVisible = false;\r\n      this.selectedNetwork = null;\r\n    },\r\n\r\n    showMountDetails(container) {\r\n      this.selectedMountContainer = {\r\n        ...container,\r\n        mounts: this.parseJsonField(container.mounts, [])\r\n      };\r\n      this.mountDetailsVisible = true;\r\n    },\r\n    handleMountDetailsClose() {\r\n      this.mountDetailsVisible = false;\r\n      this.selectedMountContainer = null;\r\n    },\r\n    getAllMounts() {\r\n      if (!this.selectedMountContainer?.mounts) return [];\r\n      return this.selectedMountContainer.mounts;\r\n    },\r\n    showEnvironmentDetails(container) {\r\n      this.selectedEnvironment = {\r\n        ...container,\r\n        env: this.parseJsonField(container.env, [])\r\n      };\r\n      this.environmentDetailsVisible = true;\r\n    },\r\n    handleEnvironmentDetailsClose() {\r\n      this.environmentDetailsVisible = false;\r\n      this.selectedEnvironment = null;\r\n    },\r\n    getAllEnvironmentVars() {\r\n      if (!this.selectedEnvironment?.env) return [];\r\n      return this.selectedEnvironment.env;\r\n    },\r\n    showProcessDetails(container) {\r\n      this.selectedProcessContainer = {\r\n        ...container,\r\n        processes: this.parseJsonField(container.processes, {})\r\n      };\r\n      this.processDetailsVisible = true;\r\n    },\r\n    handleProcessDetailsClose() {\r\n      this.processDetailsVisible = false;\r\n      this.selectedProcessContainer = null;\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.docker-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.docker-title {\r\n  color: #333;\r\n  font-size: 16px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n// 表格样式会从全局样式继承，不需要在这里硬编码\r\n\r\n.host-config-container {\r\n  padding: 24px;\r\n}\r\n\r\n.ant-card {\r\n  height: 100%;\r\n}\r\n\r\n.ant-card-head {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\nul {\r\n  padding-left: 20px;\r\n  margin: 0;\r\n}\r\n\r\nli {\r\n  word-break: break-all;\r\n}\r\n\r\n.ant-descriptions-bordered .ant-descriptions-item-label {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n\r\npre {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  margin: 4px 0;\r\n}\r\n\r\n/* 添加mount项的样式 */\r\n.mount-item {\r\n  padding: 2px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.mounts-container {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mount-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.mount-path {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mount-source {\r\n  color: #1890ff;\r\n}\r\n\r\n.mount-arrow {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.mount-dest {\r\n  color: #52c41a;\r\n}\r\n\r\n.mount-details {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mount-tag {\r\n  background: #f5f5f5;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.ant-collapse-panel {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.ant-tag {\r\n  margin: 2px;\r\n}\r\n\r\n.ant-collapse-content {\r\n  background: #fafafa;\r\n}\r\n\r\n.env-container {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.env-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  word-break: break-all;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.ant-descriptions {\r\n  .ant-descriptions-item-label {\r\n    width: 180px;\r\n    background-color: #fafafa;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .ant-descriptions-item-content {\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.details-container {\r\n  width: 100%;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 4px;\r\n}\r\n\r\n.details-row {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.details-header {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-item {\r\n  padding: 12px 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.details-content {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n}\r\n\r\n.content-item {\r\n  padding: 12px 16px;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n  word-break: break-word;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.container-runtime-tabs {\r\n  padding: 24px;\r\n}\r\n\r\n/* 确保标签紧跟在标题下方 */\r\n:deep(.ant-tabs) {\r\n  margin-top: 0;\r\n}\r\n\r\n:deep(.ant-card-head) {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.inspect-data-pre, .detail-pre {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n}\r\n\r\n/* 确保Tab内容区域没有背景色 */\r\n.ant-tabs-tabpane {\r\n  background-color: transparent !important;\r\n}\r\n\r\n\r\n\r\n/* 信息项样式 */\r\n.info-item {\r\n  margin-bottom: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px dashed #f0f0f0;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    padding-bottom: 0;\r\n    border-bottom: none;\r\n  }\r\n\r\n  strong {\r\n    margin-right: 8px;\r\n    color: #555;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"], "mappings": ";;AAsNA,SAAAA,QAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AACA,OAAAC,UAAA;AACA,OAAAC,eAAA;AACA,SAAAC,kBAAA,EAAAC,YAAA;AACA,SAAAC,qBAAA,EAAAC,oBAAA;AACA,SAAAC,SAAA;AACA;AACA;;AAEA;EACAC,UAAA;IACAR,aAAA;IACAC,UAAA;IACAC,eAAA;IACAC,kBAAA;IACAC,YAAA;IACAC,qBAAA;IACAC,oBAAA;IACAC;IACA;IACA;EACA;EACAE,IAAA;EACAC,KAAA;IACA;EAAA,CACA;EACAC,KAAA;IAAA,MAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,SAAA;MACAC,cAAA;MACAC,aAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,iBAAA,GACA;QAAAC,KAAA;QAAAC,SAAA;QAAAC,GAAA;MAAA,GACA;QACAF,KAAA;QACAC,SAAA;QACAC,GAAA;MACA,GACA;QACAF,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,MAAA,EAAAC,IAAA,IAAAA,IAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAC,MAAA,EAAAC,IAAA,IAAAA,IAAA;MACA,EACA;MACAC,gBAAA,GACA;QACAL,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAC,QAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA;YACA,MAAAC,WAAA,QAAAC,cAAA,CAAAF,MAAA,CAAAG,YAAA;YACA;cACAC,QAAA,EAAAH,WAAA,CAAAI,IAAA;cACA3B,KAAA;gBACA4B,KAAA;kBACAC,UAAA;kBACAC,SAAA;gBACA;cACA;YACA;UACA,SAAAC,CAAA;YACAC,OAAA,CAAAC,IAAA,qCAAAF,CAAA;YACA;UACA;QACA;MACA,GACA;QACAnB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA;YAAA,IAAAY,qBAAA;YACA,MAAAX,WAAA,QAAAC,cAAA,CAAAF,MAAA,CAAAG,YAAA;YACA,MAAAU,UAAA,IAAAD,qBAAA,GAAAX,WAAA,CAAAa,UAAA,cAAAF,qBAAA,uBAAAA,qBAAA,CAAAG,UAAA;YACA;YACA,OAAAF,UAAA;UACA,SAAAJ,CAAA;YACAC,OAAA,CAAAC,IAAA,wCAAAF,CAAA;YACA;UACA;QACA;MACA,GACA;QACAnB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA;YACA,MAAAgB,OAAA,QAAAd,cAAA,CAAAF,MAAA,CAAAiB,GAAA;YACA,KAAAD,OAAA,CAAAE,MAAA;YAEA,OAAAtC,CAAA;cAAA,SACA;YAAA,IACAoC,OAAA,CAAAG,KAAA,OAAAC,GAAA,EAAAH,GAAA,EAAAI,KAAA,KAAAzC,CAAA;cAAA,OACAyC,KAAA;cAAA;YAAA,IACAJ,GAAA,CAAAC,MAAA,SAAAD,GAAA,CAAAK,SAAA,mBAAAL,GAAA,EAEA,GACAD,OAAA,CAAAE,MAAA,QAAAtC,CAAA;cAAA;gBAAA,QAEA;gBAAA,QACA;cAAA;cAAA;gBAAA,SACA6B,CAAA;kBACAA,CAAA,CAAAc,eAAA;kBACA,KAAAC,sBAAA,CAAAxB,MAAA;gBACA;cAAA;YAAA,SAEAgB,OAAA,CAAAE,MAAA,kBAEA;UAGA,SAAAT,CAAA;YACAC,OAAA,CAAAC,IAAA,+BAAAF,CAAA;YACA;UACA;QACA;MACA,GACA;QACAnB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA,OAAApB,CAAA;YAAA;cAAA,UAEAoB,MAAA;cAAA,kBACA,KAAAE;YAAA;YAAA;cAAA,gBACA,KAAAuB;YAAA;UAAA;QAGA;MACA,GACA;QACAnC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA,OAAApB,CAAA;YAAA;cAAA,UAEAoB,MAAA;cAAA,kBACA,KAAAE;YAAA;YAAA;cAAA,gBACA,KAAAwB;YAAA;UAAA;QAGA;MACA,GACA;QACApC,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACA+B,KAAA;QACA7B,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA;UACA;YAAA,IAAA4B,qBAAA;YACA,MAAAC,WAAA,QAAA3B,cAAA,CAAAF,MAAA,CAAA8B,SAAA;YACA,MAAAD,WAAA,aAAAA,WAAA,gBAAAD,qBAAA,GAAAC,WAAA,CAAAE,YAAA,cAAAH,qBAAA,eAAAA,qBAAA,CAAAV,MAAA;YAEA,OAAAtC,CAAA;cAAA;gBAAA,SACA6B,CAAA;kBACAA,CAAA,CAAAc,eAAA;kBACA,KAAAS,kBAAA,CAAAhC,MAAA;gBACA;cAAA;YAAA,IACA6B,WAAA,CAAAE,YAAA,CAAAb,MAAA;UAGA,SAAAT,CAAA;YACAC,OAAA,CAAAC,IAAA,gCAAAF,CAAA;YACA;UACA;QACA;MACA,GACA;QACAnB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACA+B,KAAA;QACA7B,YAAA,EAAAA,CAAAC,CAAA,EAAAC,MAAA,KAAApB,CAAA;UAAA,SACA;QAAA,IAAAA,CAAA;UAAA;YAAA,SAEA6B,CAAA;cACAA,CAAA,CAAAc,eAAA;cACA,KAAAU,kBAAA,CAAAjC,MAAA;YACA;UAAA;UAAA,SACA;QAAA;MAMA,EACA;MACAkC,cAAA,GACA;QACA5C,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA,IAAAA,IAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA,IAAAA,IAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA,IAAAA,IAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA,IAAAA,IAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA;UACA,IAAAA,IAAA,KAAAyC,SAAA,IAAAzC,IAAA;UACA,OAAAA,IAAA;QACA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA;UACA,IAAAA,IAAA,KAAAyC,SAAA,IAAAzC,IAAA;UACA,OAAAA,IAAA;QACA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAsC,MAAA;UACA,KAAAA,MAAA,IAAAC,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAlB,MAAA;UACA,OAAAmB,MAAA,CAAAE,OAAA,CAAAH,MAAA,EAAAhB,GAAA,GAAA5B,GAAA,EAAAgD,KAAA,SAAAhD,GAAA,IAAAgD,KAAA,IAAAC,IAAA;QACA;MACA,GACA;QACAnD,KAAA;QACAC,SAAA;QACAC,GAAA;QACAI,KAAA;QACAE,YAAA,EAAAJ,IAAA,IAAAA,IAAA;MACA,EACA;MACAgD,UAAA;QACAC,QAAA;MACA;MACAC,eAAA;QACA;QACA;QACA;MACA;MAIAC,qBAAA;MACAC,eAAA;MACAC,mBAAA;MACAC,sBAAA;MACAC,yBAAA;MACAC,mBAAA;MACAC,qBAAA;MACAC,wBAAA;MACAC,wBAAA;MACAC,mBAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,GAAA1F,QAAA;EACA;EACA2F,KAAA;IACAC,eAAAC,KAAA;MACA,KAAAC,SAAA;MACA,IAAAD,KAAA;QACA,KAAAE,kBAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,SAAAJ,cAAA;MACA,KAAAG,kBAAA;IACA;EACA;EACAE,OAAA;IACAC,gBAAAxE,GAAA;MACA,KAAAV,SAAA,GAAAU,GAAA;MACA,KAAAoE,SAAA;MACA,KAAAC,kBAAA;IACA;IACA,MAAAA,mBAAA;MACA,UAAAH,cAAA;QACAhD,OAAA,CAAAuD,KAAA;QACA,KAAAL,SAAA;QACA;MACA;MACA,MAAAM,YAAA,QAAApF,SAAA;MACA,MAAAqF,QAAA,QAAAC,mBAAA,CAAAF,YAAA;MACA,MAAAG,UAAA,kBAAAC,qBAAA,CAAAH,QAAA,CAAAI,OAAA;MAEA,KAAAF,UAAA;MAEA;QACA,MAAAG,QAAA,SAAAzG,KAAA,CAAA0G,GAAA,gBAAAP,YAAA,SAAAR,cAAA;UACAgB,MAAA;YACAC,MAAA,OAAAC;UACA;QACA;QACA,MAAAjG,IAAA,GAAA6F,QAAA,CAAA7F,IAAA;QACA,KAAAwF,QAAA,IAAAD,YAAA,6BAAAvF,IAAA,IAAAA,IAAA,IAAAA,IAAA;MACA,SAAAsF,KAAA;QACAvD,OAAA,CAAAuD,KAAA,mBAAAC,YAAA,KAAAD,KAAA;QACA,KAAAE,QAAA;MACA;QACA,KAAAE,UAAA;MACA;IACA;IACAD,oBAAAS,SAAA;MACA,MAAAC,aAAA,GAAAD,SAAA,CAAAN,OAAA;MACA,OAAAO,aAAA,CAAAP,OAAA,eAAAxE,CAAA,EAAAgF,MAAA,KAAAA,MAAA,CAAAC,WAAA;IACA;IACAV,sBAAAW,MAAA;MACA,OAAAA,MAAA,CAAAC,MAAA,IAAAF,WAAA,KAAAC,MAAA,CAAA9D,KAAA;IACA;IACAyC,UAAA;MACA,KAAA7E,cAAA;MACA,KAAAC,aAAA;MACA,KAAAC,WAAA;IACA;IACAkG,gBAAA3F,GAAA;MAAA,IAAA4F,qBAAA;MACA,OAAAA,qBAAA,QAAArG,cAAA,iBAAAqG,qBAAA,eAAAA,qBAAA,CAAAC,aAAA;MACA,YAAAtG,cAAA,IAAAsG,aAAA,CAAA7F,GAAA;IACA;IACA8F,mBAAA;MACA,MAAAC,cAAA,QAAAJ,eAAA;MACA,QAAAI,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAC,OAAA;IACA;IACAC,YAAAC,KAAA;MACA,KAAAA,KAAA;MACA,MAAAC,CAAA;MACA,MAAAC,KAAA;MACA,MAAAC,CAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,GAAA,CAAAN,KAAA,IAAAI,IAAA,CAAAE,GAAA,CAAAL,CAAA;MACA,UAAAM,UAAA,EAAAP,KAAA,GAAAI,IAAA,CAAAI,GAAA,CAAAP,CAAA,EAAAE,CAAA,GAAAM,OAAA,QAAAP,KAAA,CAAAC,CAAA;IACA;IACA3F,eAAAkG,KAAA,EAAAC,YAAA;MACA;QACA,IAAAC,KAAA,CAAAC,OAAA,CAAAH,KAAA;UACA,YAAAI,mBAAA,CAAAJ,KAAA;QACA;QACA,WAAAA,KAAA,iBAAAA,KAAA;UACA,YAAAI,mBAAA,CAAAJ,KAAA;QACA;QACA,cAAAA,KAAA,gBAAAK,IAAA,CAAAC,KAAA,CAAAN,KAAA,IAAAC,YAAA;MACA,SAAA5F,CAAA;QACAC,OAAA,CAAAC,IAAA,gCAAAF,CAAA;QACA,OAAA4F,YAAA;MACA;IACA;IAEApE,mBAAA0E,SAAA;MACA;QACA,MAAA1G,WAAA,QAAAC,cAAA,CAAAyG,SAAA,CAAAxG,YAAA;QACA,KAAAyG,KAAA,CAAAC,eAAA,CAAAC,eAAA,2BAAA7G,WAAA;MACA,SAAAQ,CAAA;QACAC,OAAA,CAAAuD,KAAA,kCAAAxD,CAAA;QACA,KAAAsG,QAAA,CAAA9C,KAAA;MACA;IACA;IACA+C,sBAAAC,KAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,WAAA;MAAA,MAAAxI,CAAA,QAAAC,cAAA;MACA,KAAAoI,KAAA,KAAAA,KAAA,CAAA/F,MAAA;MAEA,OAAAtC,CAAA;QAAA,SACA;MAAA,IACAqI,KAAA,CAAA9F,KAAA,OAAAC,GAAA,EAAAiG,IAAA,EAAAhG,KAAA,KAAA6F,UAAA,CAAAG,IAAA,EAAAhG,KAAA,IACA4F,KAAA,CAAA/F,MAAA,QAAAtC,CAAA;QAAA;UAAA,QAEA;UAAA,QACA;QAAA;QAAA;UAAA,SACA6B,CAAA;YACAA,CAAA,CAAAc,eAAA;YACA6F,WAAA;UACA;QAAA;MAAA,SAEAH,KAAA,CAAA/F,MAAA,WAAAiG,QAAA,EAEA;IAGA;IACAX,oBAAAc,GAAA;MACA,KAAAA,GAAA;MACA,IAAAhB,KAAA,CAAAC,OAAA,CAAAe,GAAA;QACA,OAAAA,GAAA,CAAAlG,GAAA,CAAAiG,IAAA,SAAAb,mBAAA,CAAAa,IAAA;MACA;MACA,WAAAC,GAAA;QACA,MAAAC,OAAA;QACA,WAAA/H,GAAA,IAAA8H,GAAA;UACA,KAAA9H,GAAA,CAAAgI,UAAA,UAAAhI,GAAA,CAAAgI,UAAA,SAAAhI,GAAA;YACA+H,OAAA,CAAA/H,GAAA,SAAAgH,mBAAA,CAAAc,GAAA,CAAA9H,GAAA;UACA;QACA;QACA,OAAA+H,OAAA;MACA;MACA,OAAAD,GAAA;IACA;IACAG,eAAA;MAAA,IAAAC,qBAAA;MACA,OAAAA,qBAAA,QAAAtE,wBAAA,cAAAsE,qBAAA,gBAAAA,qBAAA,GAAAA,qBAAA,CAAA5F,SAAA,cAAA4F,qBAAA,eAAAA,qBAAA,CAAA3F,YAAA;MACA,YAAAqB,wBAAA,CAAAtB,SAAA,CAAAC,YAAA;IACA;IACA4F,oBAAA;MAAA,IAAAC,qBAAA;MACA,OAAAA,qBAAA,QAAAC,iBAAA,cAAAD,qBAAA,eAAAA,qBAAA,CAAAE,SAAA;MACA,MAAAC,eAAA,QAAA7H,cAAA,MAAA2H,iBAAA,CAAAC,SAAA;MACA,OAAAC,eAAA,CAAAC,eAAA;IACA;IACAtG,mBAAAiF,SAAA;MACA,KAAA7D,eAAA;QACA,GAAA6D,SAAA;QACAmB,SAAA,OAAA5H,cAAA,CAAAyG,SAAA,CAAAmB,SAAA;MACA;MACA,KAAAjF,qBAAA;IACA;IACAoF,0BAAA;MACA,KAAApF,qBAAA;MACA,KAAAC,eAAA;IACA;IAEArB,iBAAAkF,SAAA;MACA,KAAA3D,sBAAA;QACA,GAAA2D,SAAA;QACAuB,MAAA,OAAAhI,cAAA,CAAAyG,SAAA,CAAAuB,MAAA;MACA;MACA,KAAAnF,mBAAA;IACA;IACAoF,wBAAA;MACA,KAAApF,mBAAA;MACA,KAAAC,sBAAA;IACA;IACAoF,aAAA;MAAA,IAAAC,qBAAA;MACA,OAAAA,qBAAA,QAAArF,sBAAA,cAAAqF,qBAAA,eAAAA,qBAAA,CAAAH,MAAA;MACA,YAAAlF,sBAAA,CAAAkF,MAAA;IACA;IACA1G,uBAAAmF,SAAA;MACA,KAAAzD,mBAAA;QACA,GAAAyD,SAAA;QACA1F,GAAA,OAAAf,cAAA,CAAAyG,SAAA,CAAA1F,GAAA;MACA;MACA,KAAAgC,yBAAA;IACA;IACAqF,8BAAA;MACA,KAAArF,yBAAA;MACA,KAAAC,mBAAA;IACA;IACAqF,sBAAA;MAAA,IAAAC,qBAAA;MACA,OAAAA,qBAAA,QAAAtF,mBAAA,cAAAsF,qBAAA,eAAAA,qBAAA,CAAAvH,GAAA;MACA,YAAAiC,mBAAA,CAAAjC,GAAA;IACA;IACAe,mBAAA2E,SAAA;MACA,KAAAvD,wBAAA;QACA,GAAAuD,SAAA;QACA7E,SAAA,OAAA5B,cAAA,CAAAyG,SAAA,CAAA7E,SAAA;MACA;MACA,KAAAqB,qBAAA;IACA;IACAsF,0BAAA;MACA,KAAAtF,qBAAA;MACA,KAAAC,wBAAA;IACA;IACAsF,sBAAAC,OAAA;MACA,KAAArF,mBAAA,GAAAqF,OAAA;MACA,KAAAtF,wBAAA;IACA;IACAuF,6BAAA;MACA,KAAAvF,wBAAA;MACA,KAAAC,mBAAA;IACA;EACA;AACA", "ignoreList": []}]}