import datetime
from .log_config import logger


class LogColors:
    DEBUG = "\033[0;37m"
    INFO = "\033[0;32m"
    WARNING = "\033[93m"
    ERROR = "\033[91m"
    RESET = "\033[0m"


def current_time():
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def log_debug(message):
    logger.debug(message)


def log_info(message):
    logger.info(message)


def log_warning(message):
    logger.warning(message)


def log_error(message):
    logger.error(message)
