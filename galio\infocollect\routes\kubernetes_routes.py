from flask import Blueprint, jsonify
from services.kubernetes_service import KubernetesService
from routes.database_wraps import with_database_session

bp = Blueprint('k8s', __name__)

resource_mapping = {
    "k8s_api_server": "get_k8s_api_server_list",
    "k8s_ingress": "get_k8s_ingress_list",
    "k8s_gateway": "get_k8s_gateway_list",
    "k8s_virtual_service": "get_k8s_virtual_service_list",
    "k8s_service": "get_k8s_service_list",
    "k8s_network_policy": "get_k8s_network_policy_list",
    "k8s_pod": "get_k8s_pod_list",
    "k8s_node": "get_k8s_node_list",
    "k8s_secret": "get_k8s_secret_list",
    "k8s_config_map": "get_k8s_config_map_list",
}


@bp.route('/<resource_type>/<string:node_ip>', methods=['GET'])
@with_database_session
def get_k8s_resource(resource_type, node_ip, db):
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    method_name = resource_mapping.get(resource_type)
    if not method_name:
        return jsonify({"error": "Resource type not supported"}), 400

    method = getattr(k8s_service, method_name, None)
    if not method:
        return jsonify({"error": "Service method not found"}), 500

    result = method(node.id)
    if not result:
        return jsonify([]), 200

    return jsonify(result), 200


@bp.route('/k8s_role/<node_ip>', methods=['GET'])
@with_database_session
def get_k8s_role(db, node_ip):
    """获取Role列表"""
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    roles = k8s_service.get_k8s_role_list(node.id)
    return jsonify(roles)


@bp.route('/k8s_role_binding/<node_ip>', methods=['GET'])
@with_database_session
def get_k8s_role_binding(db, node_ip):
    """获取RoleBinding列表"""
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    role_bindings = k8s_service.get_k8s_role_binding_list(node.id)
    return jsonify(role_bindings)


@bp.route('/k8s_cluster_role/<node_ip>', methods=['GET'])
@with_database_session
def get_k8s_cluster_role(db, node_ip):
    """获取ClusterRole列表"""
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    cluster_roles = k8s_service.get_k8s_cluster_role_list(node.id)
    return jsonify(cluster_roles)


@bp.route('/k8s_cluster_role_binding/<node_ip>', methods=['GET'])
@with_database_session
def get_k8s_cluster_role_binding(db, node_ip):
    """获取ClusterRoleBinding列表"""
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    cluster_role_bindings = k8s_service.get_k8s_cluster_role_binding_list(node.id)
    return jsonify(cluster_role_bindings)


@bp.route('/k8s_serviceaccount_permissions/<node_ip>', methods=['GET'])
@with_database_session
def get_k8s_serviceaccount_permissions(db, node_ip):
    """获取ServiceAccount权限列表"""
    k8s_service = KubernetesService(db)
    node = k8s_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    sa_permissions = k8s_service.get_k8s_serviceaccount_permissions_list(node.id)
    return jsonify(sa_permissions)
