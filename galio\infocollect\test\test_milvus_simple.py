#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import requests
import json

# 添加路径
current_dir = os.path.dirname(__file__)
sys.path.append(current_dir)

def test_milvus_service():
    """测试 Milvus 服务的基本功能"""
    
    # Milvus 服务地址
    base_url = "http://172.22.30.65:5001"
    
    print("🚀 开始测试 Milvus Lite 服务...")
    
    # 1. 健康检查
    print("\n1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 服务健康状态正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False
    
    # 2. 准备测试数据
    print("\n2. 准备测试数据...")
    test_testcase = {
        "id": "TEST_001",
        "Testcase_Number": "TEST_001", 
        "Testcase_Name": "用户登录功能测试",
        "Testcase_Level": "P1",
        "Testcase_PrepareCondition": "系统已启动，用户账号已创建",
        "Testcase_TestSteps": "1. 打开登录页面 2. 输入用户名和密码 3. 点击登录按钮",
        "Testcase_ExpectedResult": "用户成功登录，跳转到主页面"
    }
    
    print(f"   测试用例: {test_testcase['Testcase_Name']}")
    
    # 3. 插入单个测试用例
    print("\n3. 插入测试用例...")
    try:
        response = requests.post(
            f"{base_url}/insert_testcase",
            json={"testcase_data": test_testcase},
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ 测试用例插入成功")
                print(f"   插入数量: {result.get('inserted_count')}")
            else:
                print(f"❌ 插入失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 插入请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 插入测试用例失败: {e}")
        return False
    
    # 4. 搜索测试用例
    print("\n4. 搜索测试用例...")
    try:
        search_queries = [
            "登录功能",
            "用户认证",
            "密码验证"
        ]
        
        for query in search_queries:
            print(f"\n   搜索查询: '{query}'")
            response = requests.post(
                f"{base_url}/search_testcase",
                json={
                    "query_text": query,
                    "top_k": 5
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    results = result.get('results', [])
                    print(f"   ✅ 找到 {len(results)} 个相关测试用例")
                    for i, item in enumerate(results[:2]):  # 只显示前2个
                        print(f"      [{i+1}] ID: {item.get('id')}, 名称: {item.get('name')}, 相似度: {item.get('score'):.4f}")
                else:
                    print(f"   ❌ 搜索失败: {result.get('message')}")
            else:
                print(f"   ❌ 搜索请求失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        return False
    
    # 5. 获取状态信息
    print("\n5. 获取集合状态...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            result = response.json()
            print("✅ 状态信息获取成功")
            print(f"   集合信息: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 状态获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
    
    print("\n🎉 基本功能测试完成！")
    return True


def test_batch_insert():
    """测试批量插入功能"""
    
    base_url = "http://172.22.30.65:5001"
    
    print("\n🔄 测试批量插入功能...")
    
    # 准备批量测试数据
    batch_testcases = [
        {
            "id": "BATCH_001",
            "Testcase_Number": "BATCH_001",
            "Testcase_Name": "用户注册功能测试",
            "Testcase_Level": "P1", 
            "Testcase_PrepareCondition": "系统已启动",
            "Testcase_TestSteps": "1. 打开注册页面 2. 填写用户信息 3. 提交注册",
            "Testcase_ExpectedResult": "注册成功，返回成功提示"
        },
        {
            "id": "BATCH_002", 
            "Testcase_Number": "BATCH_002",
            "Testcase_Name": "密码修改功能测试",
            "Testcase_Level": "P2",
            "Testcase_PrepareCondition": "用户已登录",
            "Testcase_TestSteps": "1. 进入设置页面 2. 修改密码 3. 保存更改",
            "Testcase_ExpectedResult": "密码修改成功"
        },
        {
            "id": "BATCH_003",
            "Testcase_Number": "BATCH_003", 
            "Testcase_Name": "文件上传功能测试",
            "Testcase_Level": "P2",
            "Testcase_PrepareCondition": "用户已登录，有可上传文件",
            "Testcase_TestSteps": "1. 选择文件 2. 点击上传 3. 等待上传完成",
            "Testcase_ExpectedResult": "文件上传成功"
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/batch_insert_testcases",
            json={
                "testcases": batch_testcases,
                "batch_size": 3
            },
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print(f"✅ 批量插入成功，插入了 {result.get('inserted_count')} 条测试用例")
                return True
            else:
                print(f"❌ 批量插入失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 批量插入请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 批量插入测试失败: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 Milvus Lite 服务功能测试")
    print("=" * 60)
    
    # 基本功能测试
    if test_milvus_service():
        print("\n" + "=" * 40)
        # 批量插入测试
        test_batch_insert()
        
        print("\n" + "=" * 40)
        print("📊 最终测试结果:")
        print("✅ 如果以上测试都通过，说明 Milvus 服务工作正常")
        print("❌ 如果有测试失败，请检查服务端的错误日志")
        print("\n💡 提示: 你可以多次运行这个脚本来测试不同的查询")
    else:
        print("\n❌ 基本功能测试失败，请检查服务配置") 