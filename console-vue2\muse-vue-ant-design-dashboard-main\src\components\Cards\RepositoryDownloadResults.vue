<template>
  <a-card class="download-results-card" size="small" :title="$t('repositoryDownload.title')">
    <template #extra>
      <a-space>
        <a-tag v-if="downloadResults && downloadResults.status === 'running'" color="blue">
          {{ $t('repositoryDownload.downloading') }}
        </a-tag>
        <a-tag v-else-if="downloadResults && downloadResults.status === 'success'" color="green">
          {{ $t('repositoryDownload.completed') }}
        </a-tag>
        <a-tag v-else-if="downloadResults && downloadResults.status === 'partial_success'" color="orange">
          {{ $t('repositoryDownload.partialCompleted') }}
        </a-tag>
        <a-tag v-else-if="downloadResults && downloadResults.status === 'failed'" color="red">
          {{ $t('repositoryDownload.failed') }}
        </a-tag>
        
        <a-button
          type="link"
          size="small"
          @click="clearResults"
          class="clear-button"
        >
          <a-icon type="close" />
          {{ $t('repositoryDownload.clear') }}
        </a-button>
      </a-space>
    </template>

    <div class="stats-summary">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic
            :title="$t('repositoryDownload.total')"
            :value="totalCount"
            :value-style="{ color: '#1890ff' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            :title="$t('repositoryDownload.success')"
            :value="successCount"
            :value-style="{ color: '#52c41a' }"
          />
        </a-col>
        <a-col :span="8">
          <a-statistic
            :title="$t('repositoryDownload.failed')"
            :value="failedCount"
            :value-style="{ color: '#f5222d' }"
          />
        </a-col>
      </a-row>
    </div>

    <div class="result-table">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        size="small"
        :row-key="record => record.key"
      >
      <template slot="status" slot-scope="text, record">
        <a-tag v-if="record.status === 'success'" color="green">
          {{ $t('repositoryDownload.success') }}
        </a-tag>
        <a-tag v-else-if="record.status === 'failed'" color="red">
          {{ $t('repositoryDownload.failed') }}
        </a-tag>
        <a-tag v-else-if="record.status === 'downloading'" color="blue">
          {{ $t('repositoryDownload.downloading') }}
        </a-tag>
        <a-tag v-else color="orange">
          {{ $t('repositoryDownload.pending') }}
        </a-tag>
      </template>

      <template slot="progress" slot-scope="text, record">
        <div v-if="record.status === 'downloading'">
          <a-progress
            :percent="record.progress || 0"
            :size="'small'"
            status="active"
          />
        </div>
        <span v-else-if="record.status === 'success'" style="color: #52c41a; font-weight: 500;">
          100%
        </span>
        <span v-else-if="record.status === 'failed'" style="color: #f5222d; font-weight: 500;">
          {{ $t('repositoryDownload.failed') }}
        </span>
        <span v-else style="color: #fa8c16; font-weight: 500;">
          {{ $t('repositoryDownload.pending') }}
        </span>
      </template>

      <template slot="result" slot-scope="text, record">
        <span v-if="record.error" style="color: #f5222d;">
          <a-tooltip :title="record.error">
            <a-icon type="exclamation-circle" /> 
            {{ record.error.length > 50 ? record.error.substring(0, 50) + '...' : record.error }}
          </a-tooltip>
        </span>
        <span v-else-if="record.download_path" style="color: #52c41a;">
          <a-tooltip :title="record.download_path">
            <a-icon type="check-circle" />
            {{ record.download_path.length > 50 ? '...' + record.download_path.substring(record.download_path.length - 50) : record.download_path }}
          </a-tooltip>
        </span>
        <span v-else-if="record.status === 'downloading'" style="color: #1890ff;">
          <a-icon type="loading" spin />
          {{ $t('repositoryDownload.downloading') }}...
        </span>
        <span v-else style="color: #fa8c16; font-weight: 500;">
          {{ $t('repositoryDownload.pending') }}
        </span>
      </template>
      </a-table>
    </div>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'RepositoryDownloadResults',
  computed: {
    ...mapState(['repositoryDownloadResults']),

    downloadResults() {
      return this.repositoryDownloadResults;
    },

    totalCount() {
      if (!this.downloadResults || !this.downloadResults.repositories) return 0;
      return Object.keys(this.downloadResults.repositories).length;
    },

    successCount() {
      if (!this.downloadResults || !this.downloadResults.repositories) return 0;
      return Object.values(this.downloadResults.repositories).filter(repo => repo.status === 'success').length;
    },

    failedCount() {
      if (!this.downloadResults || !this.downloadResults.repositories) return 0;
      return Object.values(this.downloadResults.repositories).filter(repo => repo.status === 'failed').length;
    },

    tableData() {
      if (!this.downloadResults || !this.downloadResults.repositories) return [];

      const data = [];

      // 使用 repositories 对象中的实时状态数据
      Object.values(this.downloadResults.repositories).forEach((repo, index) => {
        data.push({
          key: `repo_${index}`,
          microservice_name: repo.microservice_name,
          repository_url: repo.repository_url,
          branch_name: repo.branch_name,
          status: repo.status,
          progress: repo.progress || 0,
          download_path: repo.download_path,
          error: repo.error_detail
        });
      });

      return data;
    },

    columns() {
      return [
        {
          title: '#',
          width: 100,
          customRender: (text, record, index) => index + 1
        },
        {
          title: this.$t('repositoryConfig.columns.microservice'),
          dataIndex: 'microservice_name',
          key: 'microservice_name',
          width: 150
        },
        {
          title: this.$t('repositoryConfig.columns.repositoryUrl'),
          dataIndex: 'repository_url',
          key: 'repository_url',
          ellipsis: true,
          width: 600
        },
        {
          title: this.$t('repositoryConfig.columns.branchName'),
          dataIndex: 'branch_name',
          key: 'branch_name',
          width: 150
        },
        {
          title: this.$t('tool.columns.status'),
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' },
          width: 150
        },
        {
          title: this.$t('repositoryDownload.progress'),
          dataIndex: 'progress',
          key: 'progress',
          scopedSlots: { customRender: 'progress' },
          width: 150
        },
        {
          title: this.$t('tool.columns.result'),
          dataIndex: 'result',
          key: 'result',
          scopedSlots: { customRender: 'result' }
        }
      ];
    }
  },

  methods: {
    clearResults() {
      this.$store.dispatch('clearRepositoryDownloadResults');
    }
  }
};
</script>

<style scoped lang="scss">
.download-results-card {
  margin: 24px;
}

.clear-button {
  padding: 0;
  height: auto;
}

.stats-summary {
  margin-bottom: 16px;
}
</style>
