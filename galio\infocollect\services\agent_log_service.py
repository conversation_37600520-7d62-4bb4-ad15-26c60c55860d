from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.agent_log_datamodel import Agent<PERSON>og
from log.logger import log_error, log_warning
import re
from datetime import datetime


class AgentLogService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_agent_logs(self, node_id: int, limit: int = 100) -> List[Dict]:
        """获取指定节点的agent日志信息"""
        agent_logs = (self.db.query(AgentLog)
                     .filter_by(node_id=node_id)
                     .order_by(AgentLog.timestamp.desc())
                     .limit(limit)
                     .all())
        return [log.to_dict() for log in agent_logs] if agent_logs else []

    @staticmethod
    def get_insert_objects(log_file_path: str, node_id: int) -> List[AgentLog]:
        """从日志文件获取要插入的AgentLog对象列表"""
        agent_logs = []
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    log_entry = AgentLogService.parse_log_line(line)
                    if log_entry:
                        agent_log = AgentLog(
                            node_id=node_id,
                            log_level=log_entry['level'],
                            timestamp=log_entry['timestamp'],
                            module=log_entry.get('module', "agent"),
                            log_content=log_entry['content']
                        )
                        agent_logs.append(agent_log)
                        
        except FileNotFoundError:
            log_error(f"日志文件未找到: {log_file_path}")
        except Exception as e:
            log_error(f"处理日志文件时发生错误 ({log_file_path}): {e}")
            
        return agent_logs

    @staticmethod
    def parse_log_line(line: str):
        """解析单行日志，提取时间戳、级别、模块和内容"""
        # 匹配日志格式: '2023-12-01 10:30:45 [INFO] message'
        # 或者: '2023-12-01 10:30:45 [INFO] [module] message'
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[([A-Z]+)\](?:\s*\[([^\]]+)\])?\s*(.*)'
        
        match = re.match(pattern, line)
        if match:
            timestamp_str, level, module, content = match.groups()
            
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                return {
                    'timestamp': timestamp,
                    'level': level,
                    'module': module,
                    'content': content
                }
            except ValueError as e:
                log_warning(f"无法解析时间戳 {timestamp_str}: {e}")
                return None
        else:
            # 如果无法解析，将整行作为内容，使用默认值
            log_warning(f"无法解析日志行格式: {line}")
            return {
                'timestamp': datetime.now(),
                'level': 'INFO',
                'module': None,
                'content': line
            } 