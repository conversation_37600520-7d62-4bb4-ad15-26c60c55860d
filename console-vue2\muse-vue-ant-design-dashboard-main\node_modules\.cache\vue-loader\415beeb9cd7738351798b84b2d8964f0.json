{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue?vue&type=style&index=0&id=17687e1a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue", "mtime": 1751874855810}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci50ZXN0Y2FzZS1jb250ZW50IHsKICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7CiAgd29yZC1icmVhazogYnJlYWstd29yZDsKICBtYXgtaGVpZ2h0OiAzMDBweDsKICBvdmVyZmxvdy15OiBhdXRvOwogIHBhZGRpbmc6IDEycHg7CiAgZm9udC1mYW1pbHk6ICdDb25zb2xhcycsICdNb25hY28nLCAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7CiAgZm9udC1zaXplOiAxNHB4OwogIGxpbmUtaGVpZ2h0OiAxLjY7CiAgY29sb3I6IHJnYmEoMCwgMCwgMCwgMC43NSk7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjMTg5MGZmOwp9Cg=="}, {"version": 3, "sources": ["TestCaseDetailModal.vue"], "names": [], "mappings": ";AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseDetailModal.vue", "sourceRoot": "src/components/Widgets", "sourcesContent": ["<template>\n  <a-modal\n    :visible=\"visible\"\n    title=\"测试用例详情\"\n    width=\"800px\"\n    :footer=\"null\"\n    @cancel=\"handleClose\"\n  >\n    <a-descriptions v-if=\"testcase\" bordered>\n      <a-descriptions-item label=\"用例编号\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_Number }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"用例名称\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_Name }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"用例级别\" :span=\"3\">\n        <a-tag :color=\"getLevelColor(testcase.Testcase_Level)\">\n          {{ testcase.Testcase_Level }}\n        </a-tag>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"相似度\" :span=\"3\" v-if=\"testcase.similarity !== undefined\">\n        <a-progress\n          :percent=\"Math.round(testcase.similarity * 100)\"\n          size=\"small\"\n          :stroke-color=\"getSimilarityColor(testcase.similarity)\"\n        />\n        <span style=\"margin-left: 8px;\">{{ (testcase.similarity * 100).toFixed(1) }}%</span>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"准备条件\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_PrepareCondition }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"测试步骤\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_TestSteps }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"预期结果\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_ExpectedResult }}\n        </div>\n      </a-descriptions-item>\n    </a-descriptions>\n  </a-modal>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\n\nexport default {\n  name: 'TestCaseDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    testcase: {\n      type: Object,\n      default: null\n    },\n    // 是否需要通过API获取详细信息（当传入的testcase只有基本信息时）\n    fetchDetails: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      detailedTestcase: null\n    };\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal && this.fetchDetails && this.testcase && !this.detailedTestcase) {\n        this.fetchTestcaseDetails();\n      }\n    },\n    testcase() {\n      this.detailedTestcase = null;\n    }\n  },\n  computed: {\n    currentTestcase() {\n      return this.detailedTestcase || this.testcase;\n    }\n  },\n  methods: {\n    async fetchTestcaseDetails() {\n      if (!this.testcase || !this.testcase.Testcase_Number) return;\n      \n      this.loading = true;\n      try {\n        const response = await axios.get(`/api/testcase/${this.testcase.Testcase_Number}`);\n        this.detailedTestcase = {\n          ...response.data,\n          // 保留原有的相似度信息（如果有的话）\n          similarity: this.testcase.similarity\n        };\n      } catch (error) {\n        console.error('获取测试用例详情失败:', error);\n        this.$message.error('获取测试用例详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    handleClose() {\n      this.$emit('close');\n      this.detailedTestcase = null;\n    },\n    \n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange', \n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n        'P0': 'red',\n        'P1': 'orange',\n        'P2': 'blue', \n        'P3': 'green',\n        'P4': 'gray'\n      };\n      return colors[level] || 'default';\n    },\n    \n    getSimilarityColor(similarity) {\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\n      if (similarity >= 0.6) return '#faad14'; // 橙色\n      return '#f5222d'; // 红色\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.testcase-content {\n  white-space: pre-wrap;\n  word-break: break-word;\n  max-height: 300px;\n  overflow-y: auto;\n  padding: 12px;\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.75);\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n}\n</style>\n"]}]}