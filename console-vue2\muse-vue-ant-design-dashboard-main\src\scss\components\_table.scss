// ===== 基础表格样式 =====
// 适用于所有表格的通用基础样式

.ant-table-thead > tr > th {
  color: $color-gray-7;
  font-weight: $fw-bold;
  background-color: transparent;
  font-size: 12px;
  padding: 16px 25px;
}

.ant-table-tbody > tr > td {
  padding: 16px 25px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.ant-table-tbody > tr:last-child > td {
  border-bottom: 0;
}

.ant-table-tbody > tr > td h6 {
  font-size: 14px;
  font-weight: $fw-semibold;
  color: $color-gray-12;
}

.ant-table-tbody .ant-progress-bg {
  height: 3px !important;
  border-radius: 3px;
}

.ant-table-tbody .ant-progress {
  line-height: 3px;
  margin-top: 3px;
  display: block;
}

.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background-color: rgba(0, 0, 0, 0.03);
}

.ant-table-body {
  overflow: auto;

  @media (min-width: $xl) {
    overflow: visible;
  }

  .btn-edit {
    font-weight: $fw-semibold;
    color: $color-muted;
  }
}

// ===== 辅助组件样式 =====

.table-upload-btn {
  margin: 20px;
  margin-top: 10px;

  .ant-btn {
    box-shadow: none;
    font-weight: $fw-semibold;

    &:hover svg path,
    &:focus svg path,
    &:active svg path {
      fill: $color-primary;
    }
  }

  svg {
    vertical-align: middle;
    margin-right: 5px;

    path {
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
  }
}

.table-avatar-info {
  display: flex;

  .ant-avatar {
    margin-right: 20px;
  }

  .avatar-info {
    h6 {
      font-size: 14px;
      line-height: 14px;
      margin-bottom: 3px;
    }

    p {
      font-size: 14px;
      font-weight: $fw-regular;
      color: $color-gray-7;
    }

    > * {
      margin: 0;
    }
  }
}

// ===== 配置类表格样式 =====
// 用于主机配置、代码仓配置等可编辑配置表格

.config-table {
  .ant-table {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 12px;
    }

    .ant-table-tbody {
      > tr {
        &:hover > td {
          background-color: #f5f5f5;
        }

        > td {
          padding: 12px;
          border-bottom: 1px solid #f0f0f0;
          vertical-align: middle;
        }

        &:last-child > td {
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }

    .ant-table-content {
      border-radius: 6px;
      overflow: hidden;
    }
  }

  // 编辑状态的输入框样式
  .ant-input {
    border-radius: 4px;
    border: 1px solid #d9d9d9;

    &:focus,
    &:hover {
      border-color: #40a9ff;
    }
  }

  // 操作按钮样式
  .editable-row-operations {
    display: flex;
    justify-content: center;
    gap: 8px;

    .ant-btn-link {
      padding: 0 4px;
      height: auto;
    }
  }
}

// ===== 结果类表格样式 =====
// 用于任务进度、下载结果等只读结果表格

.result-table {
  .ant-table {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;

    .ant-table-thead > tr > th {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.75);
      padding: 12px;
      font-size: 13px;
    }

    .ant-table-tbody {
      > tr {
        &:hover > td {
          background-color: rgba(24, 144, 255, 0.04);
        }

        > td {
          padding: 10px 12px;
          border-bottom: 1px solid #f0f0f0;
          vertical-align: middle;
          font-size: 13px;
        }

        &:last-child > td {
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }

    .ant-table-content {
      border-radius: 6px;
      overflow: hidden;
    }

    .ant-empty {
      padding: 24px 0;

      .ant-empty-description {
        color: rgba(0, 0, 0, 0.45);
        font-size: 13px;
      }
    }
  }

  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  .ant-progress {
    .ant-progress-bg {
      border-radius: 3px;
    }
  }

  .error-text {
    color: #ff4d4f;
    font-size: 12px;
    max-width: 300px;
    word-break: break-all;
    line-height: 1.4;
  }

  .success-text {
    color: #52c41a;
    font-size: 12px;
    max-width: 300px;
    word-break: break-all;
    line-height: 1.4;
  }
}

// ===== 响应式适配 =====

@media (max-width: 768px) {
  .config-table,
  .result-table {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 6px;
        font-size: 12px;
      }
    }
  }
}

// ===== 统计卡片样式 =====

.stats-summary {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;

  .ant-statistic {
    text-align: center;

    .ant-statistic-title {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 4px;
    }

    .ant-statistic-content {
      font-size: 20px;
      font-weight: 600;
    }
  }
}
