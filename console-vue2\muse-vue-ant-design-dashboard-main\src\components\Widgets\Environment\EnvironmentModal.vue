<template>
  <a-modal
    :visible="visible"
    title="Environment Variables"
    width="800px"
    @cancel="handleClose"
    @update:visible="$emit('update:visible', $event)"
  >
    <template v-slot:footer>
      <a-button @click="handleClose">Close</a-button>
    </template>
    <template v-if="environmentData">
      <div class="env-container">
        <div v-for="(env, index) in getAllEnvironmentVars()" :key="index" class="env-item">
          {{ env }}
        </div>
        <div v-if="!getAllEnvironmentVars().length" class="empty-message">
          No environment variables found
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'EnvironmentModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    environmentData: {
      type: Object,
      default: null
    },
    parseJsonField: {
      type: Function,
      default: null
    }
  },
  methods: {
    handleClose() {
      this.$emit('close');
      this.$emit('update:visible', false);
    },
    getAllEnvironmentVars() {
      if (!this.environmentData?.env) return [];

      try {
        // 处理不同的数据格式
        let env = this.environmentData.env;

        // 如果提供了parseJsonField函数，使用它来解析
        if (this.parseJsonField) {
          return this.parseJsonField(env, []);
        }

        // 否则尝试自己解析
        if (typeof env === 'string') {
          return JSON.parse(env) || [];
        }

        return env || [];
      } catch (e) {
        console.warn('Failed to parse environment variables:', e);
        return [];
      }
    }
  }
}
</script>

<style scoped>
.env-container {
  max-height: 500px;
  overflow-y: auto;
}

.env-item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.env-item:last-child {
  border-bottom: none;
}

.empty-message {
  padding: 16px;
  text-align: center;
  color: #999;
}
</style>
