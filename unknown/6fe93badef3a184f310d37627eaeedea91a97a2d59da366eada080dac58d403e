from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.os_package_datamodel import OsPackageSnapshot


class PackageService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_packages(self, node_id: int) -> List[Dict]:
        """获取指定节点的所有包信息"""
        packages = self.db.query(OsPackageSnapshot).filter_by(node_id=node_id).all()
        return [pkg.to_dict() for pkg in packages] if packages else []

    @staticmethod
    def get_insert_objects(package_info, node_id):
        insert_objects = []
        for package in package_info:
            os_package_snapshot = OsPackageSnapshot(
                node_id=node_id,
                package_name=package['package_name'],
                package_type=package['package_type']
            )
            insert_objects.append(os_package_snapshot)
        return insert_objects
