import time
import traceback

from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>ool<PERSON>xecutor, as_completed

from .generate_url import GenerateUrl
from .cbh_client import CbhClient
from .obs_client_manager import ObsClientManager
from .obs_config_utils import *
from app.obsclient.logger import log_debug, log_info, log_warning, log_error

from obs import PutObjectHeader, GetObjectHeader
from obs import CreateBucketHeader, HeadPermission

DARK_COLOR = "\033[90m"
RESET_COL = "\033[0m"


class OBSClient:
    def __init__(self, access_key_id, secret_access_key, server, bucket_name):
        self.client = ObsClientManager.get_instance(access_key_id, secret_access_key, server)
        self.bucket_name = bucket_name

    def check_bucket_exists(self, bucket_name, location=''):
        try:
            resp = self.client.headBucket(bucket_name)
            if resp.status < 300:
                log_debug(f"Bucket {bucket_name} exists")
                return True
            else:
                log_warning(f"Bucket {bucket_name} does not exist!")
                if self.create_bucket(location, bucket_name):
                    return True
        except Exception as e:
            log_error("Head Bucket Failed")
            log_debug(traceback.format_exc())
            return False

    def create_bucket(self, location, bucket_name):
        try:
            header = CreateBucketHeader(aclControl=HeadPermission.PRIVATE, storageClass="STANDARD", availableZone="3az")
            resp = self.client.createBucket(bucket_name, header, location)
            if resp.status < 300:
                log_info(f"Create bucket {bucket_name} success")
                return True
            else:
                log_error(f"Create bucket {bucket_name} failed!")
                log_debug(f"ErrorCode: {resp.errorCode}")
                return False
        except Exception as e:
            log_error("Create Bucket Failed")
            log_debug(traceback.format_exc())
            return False

    def delete_obs_files(self, bucket_name, directory_prefix):
        objects_to_delete = []
        marker = None

        while True:
            resp = self.client.listObjects(bucket_name, prefix=directory_prefix, marker=marker)

            if resp.status < 300:
                for obj in resp.body.contents:
                    objects_to_delete.append({'key': obj.key})

                if not resp.body.is_truncated:
                    break
                marker = resp.body.next_marker
            else:
                log_error(f"Error listing objects: {resp.errorMessage}")
                return False

        if objects_to_delete:
            delete_request = {'objects': objects_to_delete}
            delete_response = self.client.deleteObjects(bucket_name, delete_request)

            if delete_response.status < 300:
                log_info(f"Successfully deleted {len(objects_to_delete)} objects from {bucket_name}.")
                return True
            else:
                log_error(f"Error deleting objects: {delete_response.errorMessage}")
                return False
        else:
            log_info("No objects found to delete.")
            return True

    def upload_file(self, object_key, file_path, location):
        if not self.check_bucket_exists(self.bucket_name, location):
            log_error("upload operation canceled, bucket exception!")
            exit(1)

        try:
            headers = PutObjectHeader()
            resp = self.client.putFile(self.bucket_name, object_key, file_path, headers)
            if resp.status < 300:
                log_info(f"Local -> OBS: {file_path} upload success")
                if hasattr(resp.body, 'etag'):
                    log_debug(f"ETag: {resp.body.etag}")
                return True
            else:
                log_error(f"Local -> OBS: {file_path} upload failed!")
                log_debug(f"ErrorCode: {resp.errorCode}")
                log_debug(f"ErrorMessage: {resp.errorMessage}")
                return False
        except Exception as e:
            log_error(f"Local -> OBS: {file_path} upload failed!")
            log_debug(traceback.format_exc())

    def download_file(self, object_key, file_path, timeout):
        if not self.check_bucket_exists(self.bucket_name):
            log_error("Download operation canceled, bucket does not exist!")
            return False

        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            if attempt > 0:
                log_debug(f"Wait {timeout}s, attempting to download file... (Attempt {attempt + 1})")
                time.sleep(timeout)

            try:
                headers = GetObjectHeader()
                resp = self.client.getObject(self.bucket_name, object_key, file_path, headers=headers)

                if resp.status < 300:
                    log_info(f"OBS -> Local: {object_key} download success")
                    if hasattr(resp.body, 'etag'):
                        log_debug(f"ETag: {resp.body.etag}")
                    return True
                else:
                    log_error(f"OBS -> Local: {object_key} download failed!")
                    log_debug(f"ErrorCode: {resp.errorCode}, ErrorMessage: {resp.errorMessage}")

            except Exception as e:
                log_error(f"An error occurred during download: {e}")
                log_debug(f"Attempt {attempt + 1} failed due to exception.")

            attempt += 1

        log_error(f"All attempts to download {object_key} have failed.")
        return False

    @staticmethod
    def handle_response(resp, operation=""):
        if resp.status < 300:
            if hasattr(resp.body, 'etag'):
                log_debug(f"ETag: {resp.body.etag}")
            return True
        else:
            log_debug(f"ErrorCode: {resp.errorCode}")
            log_debug(f"ErrorMessage: {resp.errorMessage}")
            return False


def process_node(node, ak, sk, server, bucket_name,
                 bucket_store_dir, upload_object_key,
                 download_object_key, download_file_path, uploader, obs_config):
    print(f"{DARK_COLOR}{'.' * 70}{RESET_COL} "
          f"{RESET_COL} Current test environment: {node.ip} {DARK_COLOR}{'.' * 70}{RESET_COL}")
    download_high_risk_object_key = "high_risk_port_scan.log"

    try:
        generate_url = GenerateUrl(ak, sk, server, node)
        download_object_key_new = f"{bucket_store_dir}/{download_object_key}_{node.node_name}"
        upload_sign_url = generate_url.generate_upload_url(bucket_name, download_object_key_new)
        # high_risk_port_scan
        download_high_risk_port_scan_object_key = f"{bucket_store_dir}/{node.node_name}_{download_high_risk_object_key}"
        upload_high_risk_url = generate_url.generate_upload_url(bucket_name, download_high_risk_port_scan_object_key)

        # Build and execute a command to download a file from OBS to the node
        cmd = generate_url.build_command(bucket_name, bucket_store_dir, upload_object_key,
                                         upload_sign_url, upload_high_risk_url)

        cbh_client = CbhClient(
            obs_config["cbh_host"],
            obs_config["cbh_user_name"],
            obs_config["cbh_user_port"],
            obs_config["cbh_private_key_path"],
            obs_config["cbh_private_key_passwd"],
        )

        cbh_client.ssh_connect(node.ip, cmd, obs_config["cbh_switch_account"])

        # Download result file from OBS
        current_download_file_path = os.path.join(download_file_path,
                                                  f"{download_object_key}_{node.node_name}")
        current_download_high_risk_scan_file_path = os.path.join(download_file_path,
                                                                 f"{node.node_name}_{download_high_risk_object_key}")

        download_status = True
        try:
            if not uploader.download_file(download_object_key_new, current_download_file_path, 20):
                download_status = False
        except Exception as e:
            download_status = False
            log_error(f"Error downloading {download_object_key_new}: {e}")

        try:
            if not uploader.download_file(download_high_risk_port_scan_object_key,
                                          current_download_high_risk_scan_file_path, 20):
                download_status = False
        except Exception as e:
            download_status = False
            log_error(f"Error downloading {download_high_risk_port_scan_object_key}: {e}")

    except Exception as e:
        log_error(f"Node {node.ip} processing failed: {e}")
        return False

    return download_status


def main(nodes):
    obs_config = get_obs_config()
    if obs_config is None:
        return

    ak = obs_config["ak"]
    sk = obs_config["sk"]
    server = obs_config["server"]
    location = obs_config["location"]
    bucket_name = obs_config["bucket_name"]
    bucket_store_dir = obs_config["bucket_store_dir"]
    upload_object_key = obs_config["upload_object_key"]
    upload_file_path = obs_config["upload_file_path"]
    download_object_key = obs_config['download_object_key']
    download_file_path = obs_config["download_file_path"]

    uploader = OBSClient(ak, sk, server, bucket_name)

    # del obs object
    uploader.delete_obs_files(bucket_name, bucket_store_dir + "/")
    object_key = "/".join([bucket_store_dir, upload_object_key])
    # upload file to obs
    if not uploader.upload_file(object_key, upload_file_path, location):
        return

    success_count = 0
    failure_count = 0
    failed_nodes_ips = []

    log_info(f"[+] Start processing [{len(nodes)}] nodes.")

    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_node = {
            executor.submit(process_node,
                            node,
                            ak,
                            sk,
                            server,
                            bucket_name,
                            bucket_store_dir,
                            upload_object_key,
                            download_object_key,
                            download_file_path,
                            uploader,
                            obs_config): node for node in nodes
        }

        for future in as_completed(future_to_node):
            node = future_to_node[future]
            try:
                if future.result():
                    success_count += 1
                else:
                    failure_count += 1
                    failed_nodes_ips.append(node.ip)
            except Exception as e:
                failure_count += 1
                failed_nodes_ips.append(node.ip)
                log_error(f"Node {node.ip} processing failed: {e}")

    log_info(
        f"[+] All [{len(nodes)}] nodes processed. Success [{success_count}] nodes. Failed [{failure_count}] nodes.")

    if failed_nodes_ips:
        log_warning(f"Failed nodes: {', '.join(failed_nodes_ips)}")
