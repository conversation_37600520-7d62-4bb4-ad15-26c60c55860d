from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class OsPackageSnapshot(Base):
    __tablename__ = 'os_package_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'), nullable=False)
    package_name = Column(String, nullable=False)
    package_type = Column(String, nullable=False)

    host = relationship("HostConfig", back_populates="os_package_snapshot")

    def to_dict(self):
        return {
            'node_id': self.node_id,
            'package_name': self.package_name,
            'package_type': self.package_type,
        }
