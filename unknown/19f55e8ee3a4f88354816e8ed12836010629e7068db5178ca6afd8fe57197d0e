from flask import Blueprint, send_from_directory, current_app
import os

bp = Blueprint('vue', __name__)


@bp.route('/')
@bp.route('/<path:path>')
def serve_vue_app(path=''):
    try:
        template_dir = os.path.join(current_app.root_path, '../templates')
        if path and (path.startswith('static/') or path.endswith(('.js', '.css', '.ico'))):
            return send_from_directory(template_dir, path)

        return send_from_directory(template_dir, 'index.html')
    except Exception as e:
        current_app.logger.error(f"Error serving file: {str(e)}")
        return f"Error: {str(e)}", 500
