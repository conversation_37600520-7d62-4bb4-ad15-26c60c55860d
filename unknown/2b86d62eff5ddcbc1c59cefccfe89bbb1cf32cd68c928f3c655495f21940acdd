from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class FilesystemSnapshot(Base):
    __tablename__ = 'filesystem_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'), nullable=False)
    device = Column(String, nullable=False)
    mount_point = Column(String, nullable=False)
    fs_type = Column(String, nullable=False)
    mount_options = Column(String, nullable=True)

    host = relationship("HostConfig", back_populates="filesystem_snapshot")

    def to_dict(self):
        return {
            'id': self.id,
            'device': self.device,
            'mount_point': self.mount_point,
            'fs_type': self.fs_type,
            'mount_options': self.mount_options,
        }

