from functools import wraps
from flask import request, jsonify
from db.init_db import get_db


def with_database_session(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        db_file = (
            request.args.get('dbFile') or
            request.form.get('dbFile') or
            (request.get_json() or {}).get('dbFile')
        )

        if not db_file:
            return jsonify({"error": "No database file specified"}), 400

        try:
            with get_db(db_file) as db:
                return f(*args, db=db, **kwargs)
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    return decorated_function
