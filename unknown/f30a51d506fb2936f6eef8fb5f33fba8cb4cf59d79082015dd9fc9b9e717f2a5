from .obs_config_utils import *
from .obs_client import OBSClient
from .generate_url import GenerateUrl

from concurrent.futures import ThreadPoolExecutor, as_completed

from .cbh_file_client import CbhClient
from app.obsclient.logger import log_debug, log_info, log_warning, log_error

DARK_COLOR = "\033[90m"
RESET_COL = "\033[0m"


def upload_to_node(node, upload_object_key, cbh_client, obs_config):
    log_debug(f"{'.' * 70} current test environment: {node.ip} {'.' * 70}")

    # download file from obs to node
    generate_url = GenerateUrl(obs_config["ak"], obs_config["sk"], obs_config["server"], node)
    download_file_name = os.path.basename(upload_object_key)
    cmd = generate_url.build_obs2node_download_command(obs_config["bucket_name"], upload_object_key, download_file_name)

    try:
        cbh_client.ssh_connect(node.ip, cmd, obs_config["cbh_switch_account"])
        log_info(f"Upload to {node.ip} completed!")
        return True
    except Exception as e:
        log_error(f"Failed to upload to {node.ip}: {e}")
        return False


def main(action, file_path, nodes):
    obs_config = get_obs_config()
    if obs_config is None:
        return

    ak = obs_config["ak"]
    sk = obs_config["sk"]
    server = obs_config["server"]
    bucket_name = obs_config["bucket_name"]
    bucket_store_dir = obs_config['bucket_store_dir']
    download_file_path = obs_config['download_file_path']
    location = server.split('.')[1]

    client = OBSClient(ak, sk, server, bucket_name)
    upload_object_key = f"{bucket_store_dir}/transit/{os.path.basename(file_path)}"

    cbh_client = CbhClient(
        obs_config["cbh_host"],
        obs_config["cbh_user_name"],
        obs_config["cbh_user_port"],
        obs_config["cbh_private_key_path"],
        obs_config["cbh_private_key_passwd"],
    )

    if action == "upload":
        if not os.path.exists(file_path):
            log_error(f"{file_path} does not exist!")
            return

        if os.path.isdir(file_path):
            log_warning("Folder uploads are not supported. "
                        "Please compress the folder into a zip file and try again.")
            return

        # upload file to obs
        if not client.upload_file(upload_object_key, file_path, location):
            return

        success_count = 0
        failure_count = 0
        failed_nodes_ips = []

        log_info(f"[+] Start uploading to [{len(nodes)}] nodes.")

        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_node = {
                executor.submit(upload_to_node,
                                node,
                                upload_object_key,
                                cbh_client,
                                obs_config): node for node in nodes
            }

            for future in as_completed(future_to_node):
                node = future_to_node[future]
                try:
                    ret = future.result()
                    if ret:
                        success_count += 1
                    else:
                        failure_count += 1
                        failed_nodes_ips.append(node.ip)
                except Exception as e:
                    failure_count += 1
                    failed_nodes_ips.append(node.ip)
                    log_error(f"Node {node.ip} upload failed: {e}")

        log_info(
            f"[+] Upload to all [{len(nodes)}] nodes completed. "
            f"Success [{success_count}] nodes. Failed [{failure_count}] nodes.")

        if failed_nodes_ips:
            log_warning(f"Failed nodes: {', '.join(failed_nodes_ips)}")

    elif action == "download":
        for node in nodes:
            print(f"{DARK_COLOR}{'.' * 70}{RESET_COL} "
                  f"{RESET_COL} current test environment: {node.ip} {DARK_COLOR}{'.' * 70}{RESET_COL}")

            if not os.path.exists(download_file_path):
                log_error(f"Path {download_file_path} does not exist!")
                return
            # node to obs
            generate_url = GenerateUrl(ak, sk, server, node)
            upload_file = file_path
            cmd = generate_url.build_node2obs_upload_command(bucket_name, upload_object_key, upload_file)
            if not cmd:
                return

            cbh_client.ssh_connect(node.ip, cmd, obs_config["cbh_switch_account"])
            # obs to local
            download_object_key = upload_object_key
            current_download_file_path = os.path.join(download_file_path, os.path.basename(file_path))
            client.download_file(download_object_key, current_download_file_path, 60)

    else:
        return
