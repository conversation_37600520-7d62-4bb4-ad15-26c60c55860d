import zipfile

from app.obsclient.obs_config_utils import *
from app.obsclient.obs_client import OBSClient
from app.obsclient.generate_url import GenerateUrl

from concurrent.futures import ThreadPoolExecutor, as_completed

from app.obsclient.cbh_client import CbhClient
from app.obsclient.logger import log_info, log_warning, log_error

DARK_COLOR = "\033[90m"
RESET_COL = "\033[0m"


def process_node(bucket_name, bucket_store_dir, node, cbh_client, obs_config, upload_object_key, node_name,
                 client, download_result_path):
    print(f"{DARK_COLOR}{'.' * 70}{RESET_COL} "
          f"{RESET_COL} current test environment: {node.ip} {DARK_COLOR}{'.' * 70}{RESET_COL}")

    download_status = True
    try:
        # node_name = node_name.split("_")[0] if '_' in node_name else ""
        # download scan_tools.zip from obs to node and exec
        generate_url = GenerateUrl(obs_config["ak"], obs_config["sk"], obs_config["server"], node)

        # generate url for node to obs
        object_key = f"{bucket_store_dir}/{node_name}.tar"
        upload_sign_url = generate_url.generate_upload_url(bucket_name, object_key)
        if not upload_sign_url:
            return False

        cmd = generate_url.build_spider_command(obs_config["bucket_name"], upload_object_key, node_name, upload_sign_url)
        cbh_client.ssh_connect(node.ip, cmd, obs_config["cbh_switch_account"])

        try:
            download_result_path = os.path.join(download_result_path, os.path.basename(object_key))
            if not client.download_file(object_key, download_result_path, 30):
                download_status = False
        except Exception as e:
            download_status = False
            log_error(f"Error downloading {download_result_path}: {e}")

    except Exception as e:
        download_status = False
        log_error(f"Node {node.ip} processing failed: {e}")

    return download_status


def get_spider_config():
    config_file_path = os.path.join(os.path.dirname(__file__), '../../config/cbh_spider_config.ini')
    config = load_config(config_file_path)
    if config is None:
        return None

    spider_scan_pkg_path = config.get('DEFAULT', 'SPIDER_SCAN_PKG_PATH', fallback='')
    spider_scan_result_dir = config.get('DEFAULT', 'SPIDER_SCAN_RESULT_DIR', fallback='')

    return {
        "upload_path": spider_scan_pkg_path,
        "download_result_path": spider_scan_result_dir
    }


def main(nodes, spider_scan_pkg_path, spider_scan_result_dir):
    obs_config = get_obs_config()
    if obs_config is None:
        return

    ak = obs_config["ak"]
    sk = obs_config["sk"]
    bucket_name = obs_config["bucket_name"]
    server = obs_config["server"]
    bucket_store_dir = obs_config['bucket_store_dir']
    download_file_path = obs_config['download_file_path']
    location = server.split('.')[1]

    client = OBSClient(ak, sk, server, bucket_name)
    cbh_client = CbhClient(
        obs_config["cbh_host"],
        obs_config["cbh_user_name"],
        obs_config["cbh_user_port"],
        obs_config["cbh_private_key_path"],
        obs_config["cbh_private_key_passwd"],
    )

    # spider_config = get_spider_config()
    # upload_path = spider_config.get('upload_path')
    # download_result_path = spider_config.get('download_result_path')
    upload_path = spider_scan_pkg_path
    download_result_path = spider_scan_result_dir
    if not os.path.exists(upload_path):
        log_error(f"{upload_path} does not exist!")
        return

    if not zipfile.is_zipfile(upload_path):
        log_warning("The provided file is not a valid ZIP file. Please upload a valid ZIP file.")
        return

    upload_object_key = f"{bucket_store_dir}/transit/{os.path.basename(upload_path)}"

    # upload file to obs
    if not client.upload_file(upload_object_key, upload_path, location):
        return

    success_count = 0
    failure_count = 0
    failed_nodes_ips = []

    log_info(f"[+] Start uploading to [{len(nodes)}] nodes.")

    with ThreadPoolExecutor(max_workers=20) as executor:
        future_to_node = {
            executor.submit(process_node,
                            bucket_name,
                            bucket_store_dir,
                            node, cbh_client,
                            obs_config,
                            upload_object_key,
                            node.node_name,
                            client,
                            download_result_path
                            ): node for node in nodes
        }

        for future in as_completed(future_to_node):
            node = future_to_node[future]
            try:
                result = future.result()
                if result:
                    success_count += 1
                else:
                    failure_count += 1
                    failed_nodes_ips.append(node.ip)
            except Exception as e:
                failure_count += 1
                failed_nodes_ips.append(node.ip)

    log_info(
        f"[+] All [{len(nodes)}] nodes processed. Success [{success_count}] nodes. Failed [{failure_count}] nodes.")

    if failed_nodes_ips:
        log_warning(f"Failed nodes: {', '.join(failed_nodes_ips)}")
