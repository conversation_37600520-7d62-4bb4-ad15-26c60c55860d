--DROP TABLE IF EXISTSdocker_host_config;
CREATE TABLE IF NOT EXISTS docker_host_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER NOT NULL,
    docker_version TEXT,
    daemon_config TEXT,
    user_in_docker_group BOOLEAN,
    is_root_user BOOLEAN,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTSdocker_network;
CREATE TABLE IF NOT EXISTS docker_network (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER NOT NULL,
    network_id TEXT,
    name TEXT,
    driver TEXT,
    scope TEXT,
    created_at TEXT,
    ipv6 BOOLEAN,
    internal BOOLEAN,
    labels TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);

--DROP TABLE IF EXISTSdocker_container;
CREATE TABLE IF NOT EXISTS docker_container (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER,
    container_id TEXT,
    mounts TEXT,
    processes TEXT,
    exposures TEXT,
    env TEXT,
    inspect_data TEXT,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE,
    UNIQUE(node_id, container_id)
);
