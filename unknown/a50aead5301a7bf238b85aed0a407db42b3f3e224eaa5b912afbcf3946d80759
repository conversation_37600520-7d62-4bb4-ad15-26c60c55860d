import json

from typing import Dict, List, Optional
from log.logger import log_error
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.crictl_datamodel import CrictlHostConfig, CrictlPod, CrictlContainer


class CrictlService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_crictl_host_config(self, node_id: int) -> Optional[Dict]:
        host_config = self.db.query(CrictlHostConfig).filter_by(node_id=node_id).first()
        return host_config.to_dict() if host_config else None

    def get_crictl_containers(self, node_id: int) -> List[Dict]:
        containers = self.db.query(CrictlContainer).filter_by(node_id=node_id).all()
        return [container.to_dict() for container in containers]

    def get_crictl_pods(self, node_id: int) -> List[Dict]:
        pods = self.db.query(CrictlPod).filter_by(node_id=node_id).all()
        return [pod.to_dict() for pod in pods]

    @staticmethod
    def get_insert_objects_host_config(host_data: Dict, node_id: int) -> List[CrictlHostConfig]:
        return [CrictlHostConfig(
            node_id=node_id,
            version_info=json.dumps(host_data.get('version_info', {})),
            runtime_info=json.dumps(host_data.get('runtime_info', {})),
            is_root_user=host_data.get('user_privileges', {}).get('root_user', False)
        )]

    @staticmethod
    def get_insert_objects_pods(pods: List[Dict], node_id: int) -> List[CrictlPod]:
        insert_objects = []
        for pod in pods:
            try:
                new_pod = CrictlPod(
                    node_id=node_id,
                    pod_id=pod.get('pod_id', ''),
                    pod_metadata=json.dumps(pod.get('metadata', {})),
                    state=pod.get('state', ''),
                    created_at=pod.get('created_at', ''),
                    network=json.dumps(pod.get('network', {})),
                    labels=json.dumps(pod.get('labels', {})),
                    annotations=json.dumps(pod.get('annotations', {}))
                )
                insert_objects.append(new_pod)
            except Exception as e:
                log_error(f"Error creating pod object: {e}")
                continue
        return insert_objects

    @staticmethod
    def get_insert_objects_containers(containers: List[Dict], node_id: int) -> List[CrictlContainer]:
        insert_objects = []
        for container in containers:
            try:
                # 确保所有JSON字段都有有效的默认值
                mounts = json.dumps(container.get('mounts', []) or [])
                processes = json.dumps(container.get('processes', {}) or {})
                exposures = json.dumps(container.get('exposed_ports', {}) or {})
                env = json.dumps(container.get('env', []) or [])

                # 处理inspect_data
                inspect_data = json.dumps(container.get('inspect_data', {})) if container.get('inspect_data') else '{}'

                new_container = CrictlContainer(
                    node_id=node_id,
                    container_id=container.get('container_id', ''),
                    mounts=mounts,
                    processes=processes,
                    exposures=exposures,
                    env=env,
                    inspect_data=inspect_data
                )
                insert_objects.append(new_container)
            except Exception as e:
                log_error(f"Error creating container object: {e}")
                continue
        return insert_objects
