(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7155ac4e"],{"3d82":function(e,t,s){},4650:function(e,t,s){"use strict";s("3d82")},"7eb5":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("FilesystemInfo")],1)],1)],1)},o=[],a=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full filesystem-card",attrs:{bordered:!1,bodyStyle:{padding:0}},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 16 16"}},[t("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z","clip-rule":"evenodd"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.mount")))])]),t("div",[t("RefreshButton",{on:{refresh:e.fetchFilesystem}})],1)])]},proxy:!0}])},[t("a-table",{attrs:{columns:e.columns,dataSource:e.filesystemItems,rowKey:e=>e.key||e.device,pagination:e.pagination,loading:e.loading}})],1)},n=[],l=(s("0643"),s("a573"),s("2f62")),r=s("fec3"),c=s("f188"),d={components:{RefreshButton:c["a"]},name:"FilesystemInfo",data(){return{filesystemItems:[],loading:!1,columns:[{title:"Device",dataIndex:"device",key:"device",width:"20%",ellipsis:!0},{title:"Mount Point",dataIndex:"mount_point",key:"mount_point",width:"25%",ellipsis:!0},{title:"File System Type",dataIndex:"fs_type",key:"fs_type",width:"15%",ellipsis:!0},{title:"Mount Options",dataIndex:"mount_options",key:"mount_options",width:"40%",ellipsis:!0}],pagination:{pageSize:100}}},computed:{...Object(l["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(e){this.fetchFilesystem()}},mounted(){this.fetchFilesystem()},methods:{async fetchFilesystem(){if(!this.selectedNodeIp)return console.error("Node IP is not defined"),void(this.filesystemItems=[]);try{this.loading=!0;const e=await r["a"].get("/api/filesystem/"+this.selectedNodeIp,{params:{dbFile:this.currentProject}});this.filesystemItems=e.data.map((e,t)=>({...e,key:`${e.device}_${e.mount_point}_${t}`}))}catch(e){console.error("Error fetching filesystem:",e),this.filesystemItems=[]}finally{this.loading=!1}}}},u=d,p=(s("4650"),s("2877")),f=Object(p["a"])(u,a,n,!1,null,"659b42ba",null),h=f.exports,m={components:{FilesystemInfo:h}},y=m,v=Object(p["a"])(y,i,o,!1,null,null,null);t["default"]=v.exports},f188:function(e,t,s){"use strict";var i=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},o=[],a=s("2f62"),n={computed:{...Object(a["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},l=n,r=s("2877"),c=Object(r["a"])(l,i,o,!1,null,"80cb1374",null);t["a"]=c.exports}}]);
//# sourceMappingURL=chunk-7155ac4e.c5071787.js.map