import Vue from 'vue';
import VueI18n from 'vue-i18n';
import enUS from './locales/en-US';
import zhCN from './locales/zh-CN';

Vue.use(VueI18n);

const messages = {
  'en-US': enUS,
  'zh-CN': zhCN
};

// 从localStorage获取语言设置，如果没有则使用浏览器默认语言或英文
const getDefaultLanguage = () => {
  const savedLanguage = localStorage.getItem('language');
  if (savedLanguage && messages[savedLanguage]) {
    return savedLanguage;
  }
  
  // 检测浏览器语言
  const browserLang = navigator.language || navigator.userLanguage;
  const lang = browserLang.startsWith('zh') ? 'zh-CN' : 'en-US';
  
  return lang;
};

const i18n = new VueI18n({
  locale: getDefaultLanguage(),
  fallbackLocale: 'en-US',
  messages,
  silentTranslationWarn: true
});

export default i18n;
