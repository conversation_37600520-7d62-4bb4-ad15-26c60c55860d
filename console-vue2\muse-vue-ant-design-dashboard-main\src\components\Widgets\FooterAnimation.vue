<template>
  <div class="footer-animation-container">
    <div class="animal" ref="animal">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="animal-svg">
        <path :fill="currentColor" d="M226.5 92.9c14.3 42.9-.3 86.2-32.6 96.8s-70.1-15.6-84.4-58.5s.3-86.2 32.6-96.8s70.1 15.6 84.4 58.5zM100.4 198.6c18.9 32.4 14.3 70.1-10.2 84.1s-59.7-.9-78.5-33.3S-2.7 179.3 21.8 165.3s59.7 .9 78.5 33.3zM69.2 401.2C121.6 259.9 214.7 224 256 224s134.4 35.9 186.8 177.2c3.6 9.7 5.2 20.1 5.2 30.5v1.6c0 25.8-20.9 46.7-46.7 46.7c-11.5 0-22.9-1.4-34-4.2l-88-22c-15.3-3.8-31.3-3.8-46.6 0l-88 22c-11.1 2.8-22.5 4.2-34 4.2C84.9 480 64 459.1 64 433.3v-1.6c0-10.4 1.6-20.8 5.2-30.5zM421.8 282.7c-24.5-14-29.1-51.7-10.2-84.1s54-47.3 78.5-33.3s29.1 51.7 10.2 84.1s-54 47.3-78.5 33.3zM310.1 189.7c-32.3-10.6-46.9-53.9-32.6-96.8s52.1-69.1 84.4-58.5s46.9 53.9 32.6 96.8s-52.1 69.1-84.4 58.5z"/>
      </svg>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      position: 0,
      direction: 1, // 1 表示向右, -1 表示向左
      animationFrame: null,
      containerWidth: 0,
      animalWidth: 40,
      speed: 0.6, // 降低一半速度
      jumpHeight: 0,
      isJumping: false,
      jumpDirection: 1, // 1 表示上升, -1 表示下降
      maxJumpHeight: 15,
      jumpSpeed: 0.3, // 降低一半跳跃速度
      jumpProbability: 0.008, // 降低跳跃概率
      colorIndex: 0,
      colors: ['#818080', '#4096ff', '#ff7875', '#52c41a', '#faad14'],
      frameCount: 0,
      frameSkip: 1, // 每隔一帧才更新一次位置，这样可以降低动画速度
      currentColor: '#818080' // 当前颜色，初始为第一个颜色
    };
  },
  mounted() {
    this.containerWidth = this.$el.offsetWidth;
    this.updateAnimalColor(); // 初始化颜色
    this.initAnimation();
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    cancelAnimationFrame(this.animationFrame);
  },
  methods: {
    initAnimation() {
      // 初始化动画
      this.position = 0;
      this.animate();
    },
    animate() {
      this.frameCount++;

      // 每隔指定帧数才更新位置，这样可以降低动画速度
      if (this.frameCount % (this.frameSkip + 1) === 0) {
        // 更新水平位置
        this.position += this.speed * this.direction;

        // 检查边界并改变方向
        if (this.position > this.containerWidth - this.animalWidth) {
          this.direction = -1;
          this.$refs.animal.classList.remove('facing-right');
          this.$refs.animal.classList.add('facing-left');
        } else if (this.position < 0) {
          this.direction = 1;
          this.$refs.animal.classList.remove('facing-left');
          this.$refs.animal.classList.add('facing-right');
        }

        // 随机跳跃
        if (!this.isJumping && Math.random() < this.jumpProbability) {
          this.isJumping = true;
          this.jumpDirection = 1;
          this.jumpHeight = 0;
          // 改变颜色
          this.colorIndex = (this.colorIndex + 1) % this.colors.length;
          this.updateAnimalColor();
        }

        // 处理跳跃动画
        if (this.isJumping) {
          this.jumpHeight += this.jumpSpeed * this.jumpDirection;

          if (this.jumpHeight >= this.maxJumpHeight) {
            this.jumpDirection = -1; // 开始下降
          } else if (this.jumpHeight <= 0 && this.jumpDirection === -1) {
            this.isJumping = false;
            this.jumpHeight = 0;
          }
        }
      }

      // 应用新位置
      this.$refs.animal.style.left = `${this.position}px`;
      this.$refs.animal.style.bottom = `${5 + this.jumpHeight}px`;

      // 继续动画循环
      this.animationFrame = requestAnimationFrame(this.animate);
    },
    handleResize() {
      this.containerWidth = this.$el.offsetWidth;
      // 如果当前位置超出新的容器宽度，重置位置
      if (this.position > this.containerWidth - this.animalWidth) {
        this.position = this.containerWidth - this.animalWidth;
      }
    },
    updateAnimalColor() {
      // 更新SVG图标的颜色
      this.currentColor = this.colors[this.colorIndex];
    }
  }
}
</script>

<style scoped>
.footer-animation-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  overflow: hidden;
  z-index: 1;
}

.animal {
  position: absolute;
  bottom: 5px;
  width: 30px;
  height: 30px;
  transition: transform 0.2s;
}

.animal-svg {
  width: 100%;
  height: 100%;
}

.facing-right {
  transform: scaleX(1);
}

.facing-left {
  transform: scaleX(-1);
}
</style>
