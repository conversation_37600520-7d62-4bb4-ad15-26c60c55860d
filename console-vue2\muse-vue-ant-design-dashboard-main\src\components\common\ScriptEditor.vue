<template>
  <div>
    <!-- 脚本编辑按钮和确认按钮 -->
    <div style="display: flex; align-items: center; gap: 10px;">
      <a-button @click="showScriptModal" :disabled="disabled" class="nav-style-button">
        <a-icon type="code" /> {{ $t('tool.editShellScript') }}
      </a-button>
      <a-button
        class="nav-style-button"
        v-if="scriptContent"
        @click="confirmScript"
        :disabled="disabled"
      >
        <a-icon type="check-circle" /> {{ $t('tool.confirmScript') }}
      </a-button>
    </div>

    <!-- 脚本编辑模态框 -->
    <a-modal
      :title="$t('tool.editShellScript')"
      :visible="scriptModalVisible"
      :closable="false"
      :maskClosable="false"
      :keyboard="false"
      @ok="handleScriptOk"
      @cancel="handleScriptCancel"
      width="1200px"
      :bodyStyle="{ maxHeight: '90vh', overflow: 'auto', padding: '20px' }"
      :afterVisibleChange="handleModalVisibleChange"
    >
      <!-- 脚本选项卡 -->
      <a-tabs v-model="activeScriptTab" @change="handleTabChange" style="margin-bottom: 16px;">
        <a-tab-pane v-for="tab in scriptTabs" :key="tab.key" :tab="tab.name" />
      </a-tabs>

      <div style="background: #1e1e1e; padding: 16px; border-radius: 4px; min-height: 700px;">
        <a-textarea
          v-model="scriptContentInternal"
          :rows="30"
          style="font-family: 'Courier New', monospace; background: #1e1e1e; color: #d4d4d4; border: 1px solid #444; font-size: 14px; width: 100%; resize: none;"
          placeholder="#!/bin/bash"
          :auto-size="{ minRows: 30, maxRows: 40 }"
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
import axios from '@/api/axiosInstance';
import { mapState } from 'vuex';
import { getScriptContent } from '@/assets/scripts';

export default {
  name: 'ScriptEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    scriptType: {
      type: String,
      default: 'general' // 'general' 或 'spider'
    },
    scriptTabs: {
      type: Array,
      default: () => []
    },
    defaultTab: {
      type: String,
      default: 'default_command'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filename: {
      type: String,
      default: 'script.sh'
    },
    successMessage: {
      type: String,
      default: 'Script saved successfully'
    }
  },
  data() {
    return {
      scriptModalVisible: false,
      scriptContentInternal: this.value,
      activeScriptTab: this.defaultTab,
      scriptPath: ''
    };
  },
  computed: {
    scriptContent: {
      get() {
        return this.scriptContentInternal;
      },
      set(value) {
        this.scriptContentInternal = value;
        this.$emit('input', value);
      }
    }
  },
  watch: {
    value(newVal) {
      this.scriptContentInternal = newVal;
    }
  },
  methods: {
    showScriptModal() {
      // 确保脚本内容已初始化
      if (!this.scriptContentInternal) {
        this.loadScriptContent(this.activeScriptTab);
      }
      this.scriptModalVisible = true;
    },

    loadScriptContent(tabKey) {
      this.activeScriptTab = tabKey;
      this.scriptContent = getScriptContent(tabKey);
    },

    handleTabChange(tabKey) {
      this.loadScriptContent(tabKey);
    },

    handleScriptOk() {
      if (!this.scriptContent.trim()) {
        this.$message.warning('Script content cannot be empty');
        return;
      }

      // 保存脚本内容到文件
      this.saveScriptToFile();

      this.scriptModalVisible = false;
    },

    handleModalVisibleChange() {
      // 脚本内容已在data中初始化，不需要额外的处理
    },

    handleScriptCancel() {
      // 关闭模态框，不保存脚本内容
      this.scriptModalVisible = false;
    },

    // 直接确认脚本（不打开编辑对话框）
    async confirmScript() {
      if (!this.scriptContent.trim()) {
        this.$message.warning('Script content cannot be empty');
        return;
      }

      // 保存脚本内容到文件
      const path = await this.saveScriptToFile();

      if (path) {
        this.$message.success(this.successMessage);
      }
    },

    async saveScriptToFile() {
      try {
        // 创建一个包含脚本内容的文件对象
        const blob = new Blob([this.scriptContent], { type: 'text/plain' });
        const file = new File([blob], this.filename, { type: 'text/plain' });

        // 上传脚本文件
        const formData = new FormData();
        formData.append('script_content', file);

        const response = await axios.post('/api/script/save_script', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (response.data && response.data.path) {
          this.scriptPath = response.data.path;
          this.$emit('script-saved', response.data.path);
          return response.data.path;
        }
        return null;
      } catch (error) {
        console.error('Script save error:', error);
        this.$message.error(`Failed to save script: ${error.response?.data?.error || error.message}`);
        return null;
      }
    }
  }
};
</script>
