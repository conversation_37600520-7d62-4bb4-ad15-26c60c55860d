import os
import json
import base64
from flask import send_from_directory, abort
from log.logger import log_error, log_info

SCRIPT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DOWNLOAD_PATH = SCRIPT_DIR
UPLOAD_PATH = os.path.join(SCRIPT_DIR, 'cache\\uploads')


def download(agent_info_encoded):
    try:
        agent_info = json.loads(base64.b64decode(agent_info_encoded).decode())
    except Exception as e:
        log_error("Failed to decode agent_info: %s", e)
        abort(400, description="Invalid agent_info parameter.")

    filename = agent_info.get('file_name')
    if not filename:
        abort(400, description="Filename not provided.")

    # 先在DOWNLOAD_PATH中查找文件
    full_path = os.path.join(DOWNLOAD_PATH, filename)

    # 如果在DOWNLOAD_PATH中找不到，尝试在cache/uploads目录中查找
    if not os.path.exists(full_path):
        cache_uploads_path = os.path.join(DOWNLOAD_PATH, 'cache', 'uploads', filename)
        if os.path.exists(cache_uploads_path):
            log_info(f"File found in cache/uploads: {cache_uploads_path}")
            return send_from_directory(os.path.join(DOWNLOAD_PATH, 'cache', 'uploads'), filename, as_attachment=True)
        else:
            log_error(f"File not found: {full_path} or {cache_uploads_path}")
            abort(404, description=f"File {filename} not found in {DOWNLOAD_PATH} or cache/uploads")

    try:
        return send_from_directory(DOWNLOAD_PATH, filename, as_attachment=True)
    except Exception as e:
        log_error(f"Error sending file {filename}: {str(e)}")
        abort(500, description="Error while sending file")


def upload(agent_info_encoded, request):
    if not os.path.exists(UPLOAD_PATH):
        os.makedirs(UPLOAD_PATH)

    try:
        agent_info = json.loads(base64.b64decode(agent_info_encoded).decode())
    except Exception as e:
        log_error("Failed to decode agent_info: %s", e)
        abort(400, description="Invalid agent_info parameter.")

    host_ip = agent_info.get('host_ip')
    if not host_ip:
        abort(400, description="host_ip not provided.")

    if 'file' not in request.files:
        abort(400, description="No file provided.")

    f = request.files['file']
    log_info("upload=>{}:{}/{}".format(host_ip, UPLOAD_PATH, f.filename))

    f.save(os.path.join(UPLOAD_PATH, f.filename))
    return "upload success\n"
