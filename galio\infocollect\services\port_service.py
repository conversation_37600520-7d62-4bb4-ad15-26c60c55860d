import json

from log.logger import log_error
from typing import List, Optional, Dict, Tuple, Union
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.port_datamodel import Tcp<PERSON>ort<PERSON>napshot, UdpPortSnapshot, UnixSocketSnapshot


class PortService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    @staticmethod
    def _serialize_port(port: TcpPortSnapshot) -> dict:
        """Serialize a PortSnapshot object to dictionary with processed data"""
        protocols_data = json.loads(port.protocols) if port.protocols else {}
        cipher_suites_data = json.loads(port.cipher_suites) if port.cipher_suites else {}
        certificate_data = json.loads(port.certificate) if port.certificate else {}
        vulnerabilities_data = json.loads(port.vulnerabilities) if port.vulnerabilities else {}
        http_info_data = json.loads(port.http_info) if port.http_info else {}

        # Process protocols
        offered_protocols = [
            protocol for protocol, status in protocols_data.items()
            if status in ['offered', 'offered with final']
        ]

        # Process cipher suites
        cipher_details = []
        if cipher_suites_data.get('supported_ciphers'):
            for protocol, ciphers in cipher_suites_data['supported_ciphers'].items():
                cipher_details.extend([f"{protocol}: {cipher}" for cipher in ciphers])

        # Process certificate
        cert_summary = []
        if certificate_data:
            # 关键身份信息
            if certificate_data.get('cert_commonName'):
                cert_summary.append(f"CN: {certificate_data['cert_commonName']}")
            if certificate_data.get('cert_caIssuers'):
                cert_summary.append(f"Issuer: {certificate_data['cert_caIssuers']}")
            if certificate_data.get('cert_subjectAltName'):
                cert_summary.append(f"Subject Alt Names: {certificate_data['cert_subjectAltName']}")

            # 高危安全项
            if certificate_data.get('cert_chain_of_trust'):
                cert_summary.append(f"Chain Status: {certificate_data['cert_chain_of_trust']}")
            if certificate_data.get('cert_revocation'):
                cert_summary.append(f"Revocation: {certificate_data['cert_revocation']}")
            if certificate_data.get('cert_extlifeSpan'):
                cert_summary.append(f"Validity Period: {certificate_data['cert_extlifeSpan']}")
            if certificate_data.get('cert_expirationStatus'):
                cert_summary.append(f"Expiration Status: {certificate_data['cert_expirationStatus']}")

            # 重要技术参数
            if certificate_data.get('cert_keySize'):
                cert_summary.append(f"Key Size: {certificate_data['cert_keySize']}")
            if certificate_data.get('cert_sigAlg'):
                cert_summary.append(f"Signature Algorithm: {certificate_data['cert_sigAlg']}")
            if certificate_data.get('clientAuth'):
                cert_summary.append(f"Client Auth: {certificate_data['clientAuth']}")
            if certificate_data.get('cert_keyUsage'):
                cert_summary.append(f"Key Usage: {certificate_data['cert_keyUsage']}")

            # 其他辅助信息
            if certificate_data.get('cert_serialNumber'):
                cert_summary.append(f"Serial Number: {certificate_data['cert_serialNumber']}")
            if certificate_data.get('cert_fingerprintSHA256'):
                cert_summary.append(f"Fingerprint SHA256: {certificate_data['cert_fingerprintSHA256']}")
            if certificate_data.get('cert_notAfter'):
                cert_summary.append(f"Valid Until: {certificate_data['cert_notAfter']}")

        # Process vulnerabilities
        critical_vulns = []
        for vuln_id, vuln_info in vulnerabilities_data.items():
            if vuln_info['severity'] not in ['OK', 'INFO']:
                critical_vulns.append({
                    'name': vuln_id,
                    'status': vuln_info['status'],
                    'severity': vuln_info['severity'],
                    'cve': vuln_info['cve']
                })

        return {
            'id': port.id,
            'node_id': port.node_id,
            'ip': port.ip,
            'port': port.port,
            'pid': port.pid,
            'protocols': {
                'raw': protocols_data,
                'offered': offered_protocols
            },
            'cipher_suites': {
                'raw': cipher_suites_data,
                'details': cipher_details
            },
            'certificate': {
                'raw': certificate_data,
                'summary': cert_summary
            },
            'vulnerabilities': {
                'raw': vulnerabilities_data,
                'critical': critical_vulns,
                'count': len(critical_vulns)
            },
            'http_info': {
                'protocol': http_info_data.get('protocol', 'unknown'),
                'raw_output': http_info_data.get('raw_output', '')
            }
        }

    @staticmethod
    def _serialize_udp_port(port: UdpPortSnapshot) -> dict:
        """序列化UDP端口对象"""
        return {
            'id': port.id,
            'node_id': port.node_id,
            'proto': port.proto,
            'recv_q': port.recv_q,
            'send_q': port.send_q,
            'ip': port.ip,
            'port': port.port,
            'foreign_address': port.foreign_address,
            'state': port.state,
            'pid_program': port.pid_program,
            'port_type': 'udp',  # 固定为UDP类型
            'raw_data': port.raw_data
        }

    @staticmethod
    def _serialize_unix_socket(socket: UnixSocketSnapshot) -> dict:
        """序列化UNIX Socket对象"""
        return {
            'id': socket.id,
            'node_id': socket.node_id,
            'proto': socket.proto,
            'refcnt': socket.refcnt,
            'flags': socket.flags,
            'type': socket.type,
            'state': socket.state,
            'inode': socket.inode,
            'pid_program': socket.pid_program,
            'path': socket.path,
            'port_type': 'unix_socket',  # 固定为UNIX Socket类型
            'raw_data': socket.raw_data
        }

    def get_port(self, node_id: int, port: str, port_type: str = 'tcp') -> Optional[dict]:
        """获取单个端口信息"""
        if port_type == 'tcp':
            port_obj = self.db.query(TcpPortSnapshot).filter_by(node_id=node_id, port=port).first()
            return self._serialize_port(port_obj) if port_obj else None
        elif port_type == 'udp':
            port_obj = self.db.query(UdpPortSnapshot).filter_by(node_id=node_id, port=port).first()
            return self._serialize_udp_port(port_obj) if port_obj else None
        elif port_type == 'unix_socket':
            # 对于UNIX Socket，使用inode作为标识
            socket_obj = self.db.query(UnixSocketSnapshot).filter_by(node_id=node_id, inode=port).first()
            return self._serialize_unix_socket(socket_obj) if socket_obj else None
        return None

    def get_ports(self, node_id: int, page: int = 1, page_size: int = 50, port_type: str = 'tcp') -> Tuple[List[dict], int]:
        """获取端口列表（分页）"""
        if port_type == 'tcp':
            # 计算总数
            total = self.db.query(TcpPortSnapshot).filter_by(node_id=node_id).count()

            # 获取分页数据
            ports = (self.db.query(TcpPortSnapshot)
                    .filter_by(node_id=node_id)
                    .offset((page - 1) * page_size)
                    .limit(page_size)
                    .all())

            return [self._serialize_port(port) for port in ports], total
        elif port_type == 'udp':
            # 计算总数
            total = self.db.query(UdpPortSnapshot).filter_by(node_id=node_id).count()

            # 获取分页数据
            ports = (self.db.query(UdpPortSnapshot)
                    .filter_by(node_id=node_id)
                    .offset((page - 1) * page_size)
                    .limit(page_size)
                    .all())

            return [self._serialize_udp_port(port) for port in ports], total
        elif port_type == 'unix_socket':
            # 计算总数
            total = self.db.query(UnixSocketSnapshot).filter_by(node_id=node_id).count()

            # 获取分页数据
            sockets = (self.db.query(UnixSocketSnapshot)
                    .filter_by(node_id=node_id)
                    .offset((page - 1) * page_size)
                    .limit(page_size)
                    .all())

            return [self._serialize_unix_socket(socket) for socket in sockets], total

        return [], 0

    @staticmethod
    def get_insert_objects(port_snapshot: Dict, node_id: int) -> Dict[str, List]:
        """获取要插入的端口对象列表"""
        tcp_objects = []
        udp_objects = []
        unix_socket_objects = []

        # 处理TCP端口
        for port_key, port_data in port_snapshot.get('tcp_ports', {}).items():
            try:
                port_obj = TcpPortSnapshot(
                    node_id=node_id,
                    ip=port_data['ip'],
                    port=port_data['port'],
                    pid=port_data['pid'],
                    protocols=port_data.get('protocols', '{}'),
                    cipher_suites=port_data.get('cipher_suites', '{}'),
                    certificate=port_data.get('certificate', '{}'),
                    vulnerabilities=port_data.get('vulnerabilities', '{}'),
                    http_info=port_data.get('http_info', '{}')
                )
                tcp_objects.append(port_obj)
            except Exception as e:
                log_error(f"Error processing TCP port data: {str(e)}")
                continue

        # 处理UDP端口
        for udp_port in port_snapshot.get('udp_ports', []):
            try:
                udp_obj = UdpPortSnapshot(
                    node_id=node_id,
                    proto=udp_port.get('proto', ''),
                    recv_q=udp_port.get('recv_q', ''),
                    send_q=udp_port.get('send_q', ''),
                    ip=udp_port.get('ip', ''),
                    port=udp_port.get('port', ''),
                    foreign_address=udp_port.get('foreign_address', ''),
                    state=udp_port.get('state', ''),
                    pid_program=udp_port.get('pid_program', ''),
                    raw_data=udp_port.get('raw_data', '')
                )
                udp_objects.append(udp_obj)
            except Exception as e:
                log_error(f"Error processing UDP port data: {str(e)}")
                continue

        # 处理UNIX Socket
        for unix_socket in port_snapshot.get('unix_sockets', []):
            try:
                socket_obj = UnixSocketSnapshot(
                    node_id=node_id,
                    proto=unix_socket.get('proto', ''),
                    refcnt=unix_socket.get('refcnt', ''),
                    flags=unix_socket.get('flags', ''),
                    type=unix_socket.get('type', ''),
                    state=unix_socket.get('state', ''),
                    inode=unix_socket.get('inode', ''),
                    pid_program=unix_socket.get('pid_program', ''),
                    path=unix_socket.get('path', ''),
                    raw_data=unix_socket.get('raw_data', '')
                )
                unix_socket_objects.append(socket_obj)
            except Exception as e:
                log_error(f"Error processing UNIX socket data: {str(e)}")
                continue

        return {
            'tcp': tcp_objects,
            'udp': udp_objects,
            'unix_socket': unix_socket_objects
        }
