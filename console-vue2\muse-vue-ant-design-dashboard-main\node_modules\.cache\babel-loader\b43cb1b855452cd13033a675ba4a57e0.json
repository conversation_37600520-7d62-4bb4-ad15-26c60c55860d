{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\KubernetesInfo.vue", "mtime": 1751513794206}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapState", "axios", "RefreshButton", "JsonDetailModal", "components", "name", "data", "h", "$createElement", "activeTab", "apiServerData", "ingressData", "gatewayData", "virtualServiceData", "serviceData", "networkPolicyData", "podData", "nodeData", "secretData", "configMapData", "roleData", "roleBindingData", "clusterRoleData", "clusterRoleBindingData", "serviceAccountPermissionsData", "loadingApiServers", "loadingIngresses", "loadingGateways", "loadingVirtualServices", "loadingServices", "loadingNetworkPolicies", "loadingPods", "loadingNodes", "loadingSecrets", "loadingConfigMaps", "loadingRole", "loadingRoleBinding", "loadingClusterRole", "loadingClusterRoleBinding", "loadingServiceAccountPermissions", "apiServerColumns", "title", "dataIndex", "key", "ingressColumns", "width", "ellipsis", "customRender", "text", "renderComplexData", "gatewayColumns", "virtualServiceColumns", "serviceColumns", "networkPolicyColumns", "podColumns", "nodeColumns", "secretColumns", "configMapColumns", "roleColumns", "roleBindingColumns", "clusterRoleColumns", "clusterRoleBindingColumns", "serviceAccountPermissionsColumns", "permissions", "Array", "isArray", "JSON", "parse", "displayText", "length", "summary", "map", "p", "type", "join", "slice", "click", "showDetailModal", "error", "console", "pagination", "pageSize", "initialLoad", "computed", "watch", "selectedNodeIp", "newIp", "resetData", "fetchActiveTabData", "mounted", "methods", "handleTabChange", "resourceType", "capitalizeFirstLetter", "response", "get", "params", "dbFile", "currentProject", "camelCaseToDataName", "camelCase", "withoutK8s", "replace", "camelized", "_", "letter", "toUpperCase", "string", "char<PERSON>t", "Object", "entries", "k", "v", "stringify", "substring", "keys", "String", "$refs", "jsonDetailModal"], "sources": ["src/components/Cards/KubernetesInfo.vue"], "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full kubernetes-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.k8s') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-tabs default-active-key=\"k8s_api_server\" @change=\"handleTabChange\">\r\n      <a-tab-pane key=\"k8s_api_server\" tab=\"API Servers\">\r\n        <a-table\r\n          :columns=\"apiServerColumns\"\r\n          :data-source=\"apiServerData\"\r\n          :rowKey=\"(record) => record.address\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_api_server'\"\r\n          :loading=\"loadingApiServers\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_ingress\" tab=\"Ingresses\">\r\n        <a-table\r\n          :columns=\"ingressColumns\"\r\n          :data-source=\"ingressData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_ingress'\"\r\n          :loading=\"loadingIngresses\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_gateway\" tab=\"Gateways\">\r\n        <a-table\r\n          :columns=\"gatewayColumns\"\r\n          :data-source=\"gatewayData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_gateway'\"\r\n          :loading=\"loadingGateways\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_virtual_service\" tab=\"Virtual Services\">\r\n        <a-table\r\n          :columns=\"virtualServiceColumns\"\r\n          :data-source=\"virtualServiceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_virtual_service'\"\r\n          :loading=\"loadingVirtualServices\"\r\n        >\r\n\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_service\" tab=\"Services\">\r\n        <a-table\r\n          :columns=\"serviceColumns\"\r\n          :data-source=\"serviceData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_service'\"\r\n          :loading=\"loadingServices\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_network_policy\" tab=\"Network Policies\">\r\n        <a-table\r\n          :columns=\"networkPolicyColumns\"\r\n          :data-source=\"networkPolicyData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_network_policy'\"\r\n          :loading=\"loadingNetworkPolicies\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_pod\" tab=\"Pods\">\r\n        <a-table\r\n          :columns=\"podColumns\"\r\n          :data-source=\"podData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_pod'\"\r\n          :loading=\"loadingPods\"\r\n          :scroll=\"{ x: 1500 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_node\" tab=\"Nodes\">\r\n        <a-table\r\n          :columns=\"nodeColumns\"\r\n          :data-source=\"nodeData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_node'\"\r\n          :loading=\"loadingNodes\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_secret\" tab=\"Secrets\">\r\n        <a-table\r\n          :columns=\"secretColumns\"\r\n          :data-source=\"secretData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_secret'\"\r\n          :loading=\"loadingSecrets\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_config_map\" tab=\"ConfigMaps\">\r\n        <a-table\r\n          :columns=\"configMapColumns\"\r\n          :data-source=\"configMapData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_config_map'\"\r\n          :loading=\"loadingConfigMaps\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role\" tab=\"Roles\">\r\n        <a-table\r\n          :columns=\"roleColumns\"\r\n          :data-source=\"roleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role'\"\r\n          :loading=\"loadingRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_role_binding\" tab=\"Role Bindings\">\r\n        <a-table\r\n          :columns=\"roleBindingColumns\"\r\n          :data-source=\"roleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_role_binding'\"\r\n          :loading=\"loadingRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role\" tab=\"Cluster Roles\">\r\n        <a-table\r\n          :columns=\"clusterRoleColumns\"\r\n          :data-source=\"clusterRoleData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role'\"\r\n          :loading=\"loadingClusterRole\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_cluster_role_binding\" tab=\"Cluster Role Bindings\">\r\n        <a-table\r\n          :columns=\"clusterRoleBindingColumns\"\r\n          :data-source=\"clusterRoleBindingData\"\r\n          :rowKey=\"(record) => record.name\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_cluster_role_binding'\"\r\n          :loading=\"loadingClusterRoleBinding\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n      <a-tab-pane key=\"k8s_serviceaccount_permissions\" tab=\"ServiceAccount Perms\">\r\n        <a-table\r\n          :columns=\"serviceAccountPermissionsColumns\"\r\n          :data-source=\"serviceAccountPermissionsData\"\r\n          :rowKey=\"(record) => record.id\"\r\n          :pagination=\"pagination\"\r\n          v-if=\"activeTab === 'k8s_serviceaccount_permissions'\"\r\n          :loading=\"loadingServiceAccountPermissions\"\r\n          :scroll=\"{ x: 1200 }\"\r\n        >\r\n        </a-table>\r\n      </a-tab-pane>\r\n    </a-tabs>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    JsonDetailModal\r\n  },\r\n  name: 'KubernetesInfo',\r\n  data() {\r\n    return {\r\n      activeTab: 'k8s_api_server',\r\n      apiServerData: [],\r\n      ingressData: [],\r\n      gatewayData: [],\r\n      virtualServiceData: [],\r\n      serviceData: [],\r\n      networkPolicyData: [],\r\n      podData: [],\r\n      nodeData: [],\r\n      secretData: [],\r\n      configMapData: [],\r\n      roleData: [],\r\n      roleBindingData: [],\r\n      clusterRoleData: [],\r\n      clusterRoleBindingData: [],\r\n      serviceAccountPermissionsData: [],\r\n      loadingApiServers: false,\r\n      loadingIngresses: false,\r\n      loadingGateways: false,\r\n      loadingVirtualServices: false,\r\n      loadingServices: false,\r\n      loadingNetworkPolicies: false,\r\n      loadingPods: false,\r\n      loadingNodes: false,\r\n      loadingSecrets: false,\r\n      loadingConfigMaps: false,\r\n      loadingRole: false,\r\n      loadingRoleBinding: false,\r\n      loadingClusterRole: false,\r\n      loadingClusterRoleBinding: false,\r\n      loadingServiceAccountPermissions: false,\r\n      apiServerColumns: [\r\n        { title: 'Node ID', dataIndex: 'node_id', key: 'node_id' },\r\n        { title: 'Address', dataIndex: 'address', key: 'address' },\r\n      ],\r\n      ingressColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'ingress_status',\r\n          key: 'ingress_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Ingress Status')\r\n        },\r\n      ],\r\n      gatewayColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'gateway_status',\r\n          key: 'gateway_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Gateway Status')\r\n        },\r\n      ],\r\n      virtualServiceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'virtual_service_status',\r\n          key: 'virtual_service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Virtual Service Status')\r\n        },\r\n      ],\r\n      serviceColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'service_status',\r\n          key: 'service_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Service Status')\r\n        },\r\n      ],\r\n      networkPolicyColumns: [\r\n        { title: 'Namespace', dataIndex: 'namespace', key: 'namespace', width: 120 },\r\n        { title: 'Name', dataIndex: 'name', key: 'name', width: 150 },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n      ],\r\n      podColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          customRender: (text) => this.renderComplexData(text, 'Pod Status')\r\n        },\r\n      ],\r\n      nodeColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Spec',\r\n          dataIndex: 'spec',\r\n          key: 'spec',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Spec')\r\n        },\r\n        {\r\n          title: 'Status',\r\n          dataIndex: 'pod_status',\r\n          key: 'pod_status',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Node Status')\r\n        },\r\n      ],\r\n      secretColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Data')\r\n        },\r\n        {\r\n          title: 'Type',\r\n          dataIndex: 'secret_type',\r\n          key: 'secret_type',\r\n          width: 120\r\n        },\r\n      ],\r\n      configMapColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Data',\r\n          dataIndex: 'data',\r\n          key: 'data',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'ConfigMap Data')\r\n        },\r\n      ],\r\n      roleColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      roleBindingColumns: [\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      clusterRoleColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Aggregation Rule',\r\n          dataIndex: 'aggregationRule',\r\n          key: 'aggregationRule',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Aggregation Rule')\r\n        },\r\n        {\r\n          title: 'Rules',\r\n          dataIndex: 'rules',\r\n          key: 'rules',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Rules')\r\n        },\r\n      ],\r\n      clusterRoleBindingColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Metadata',\r\n          dataIndex: 'meta_data',\r\n          key: 'meta_data',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Metadata')\r\n        },\r\n        {\r\n          title: 'Role Ref',\r\n          dataIndex: 'roleRef',\r\n          key: 'roleRef',\r\n          width: 250,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'Role Reference')\r\n        },\r\n        {\r\n          title: 'Subjects',\r\n          dataIndex: 'subjects',\r\n          key: 'subjects',\r\n          width: 200,\r\n          ellipsis: true,\r\n          customRender: (text) => this.renderComplexData(text, 'RBAC Subjects')\r\n        },\r\n      ],\r\n      serviceAccountPermissionsColumns: [\r\n        {\r\n          title: 'Pod Name',\r\n          dataIndex: 'pod_name',\r\n          key: 'pod_name',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Namespace',\r\n          dataIndex: 'namespace',\r\n          key: 'namespace',\r\n          width: 120\r\n        },\r\n        {\r\n          title: 'Service Account',\r\n          dataIndex: 'service_account',\r\n          key: 'service_account',\r\n          width: 150\r\n        },\r\n        {\r\n          title: 'Permissions',\r\n          dataIndex: 'permissions',\r\n          key: 'permissions',\r\n          width: 300,\r\n          ellipsis: true,\r\n          customRender: (text) => {\r\n            try {\r\n              const permissions = Array.isArray(text) ? text : (text ? JSON.parse(text) : []);\r\n\r\n              let displayText = 'No permissions';\r\n              if (permissions && permissions.length > 0) {\r\n                const summary = permissions.map(p => `${p.type}: ${p.name}`).join(', ');\r\n                displayText = summary.length > 50 ? summary.slice(0, 50) + '...' : summary;\r\n              }\r\n\r\n              return (\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n                  <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">\r\n                    {displayText}\r\n                  </span>\r\n                  <a-button\r\n                    type=\"link\"\r\n                    style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n                    onClick={() => this.showDetailModal('ServiceAccount Permissions', permissions)}\r\n                  >\r\n                    View\r\n                  </a-button>\r\n                </div>\r\n              );\r\n            } catch (error) {\r\n              console.error('Error parsing permissions:', error);\r\n              return 'Error parsing data';\r\n            }\r\n          }\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      initialLoad: true,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      this.initialLoad = true;\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      this[`loading${this.capitalizeFirstLetter(resourceType)}`] = true;\r\n      try {\r\n        let response;\r\n        response = await axios.get(`/api/k8s/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[`${this.camelCaseToDataName(resourceType)}`] = [];\r\n      } finally {\r\n        this[`loading${this.capitalizeFirstLetter(resourceType)}`] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutK8s = camelCase.replace('k8s_', '');\r\n\r\n      // 特殊处理 serviceaccount_permissions\r\n      if (withoutK8s === 'serviceaccount_permissions') {\r\n        return 'serviceAccountPermissionsData';\r\n      }\r\n\r\n      const camelized = withoutK8s.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\r\n      return camelized + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.apiServerData = [];\r\n      this.ingressData = [];\r\n      this.gatewayData = [];\r\n      this.virtualServiceData = [];\r\n      this.serviceData = [];\r\n      this.networkPolicyData = [];\r\n      this.podData = [];\r\n      this.nodeData = [];\r\n      this.secretData = [];\r\n      this.configMapData = [];\r\n      this.roleData = [];\r\n      this.roleBindingData = [];\r\n      this.clusterRoleData = [];\r\n      this.clusterRoleBindingData = [];\r\n      this.serviceAccountPermissionsData = [];\r\n    },\r\n    renderComplexData(text, title) {\r\n      if (!text || text === 'None') return '-';\r\n\r\n      // 简化显示文本生成逻辑\r\n      const displayText = Array.isArray(text) ? `Array(${text.length})` :\r\n                         typeof text === 'object' && text !== null ?\r\n                           Object.entries(text).slice(0, 2).map(([k, v]) =>\r\n                             `${k}: ${typeof v === 'object' ? JSON.stringify(v).substring(0, 15) : v}`\r\n                           ).join(', ') + (Object.keys(text).length > 2 ? '...' : '') :\r\n                           String(text).length > 50 ? String(text).slice(0, 50) + '...' : String(text);\r\n\r\n      return (\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; padding-right: 8px;\">\r\n          <span style=\"flex: 1; overflow: hidden; text-overflow: ellipsis;\">{displayText}</span>\r\n          <a-button\r\n            type=\"link\"\r\n            style=\"flex-shrink: 0; padding: 0 8px; min-width: 50px;\"\r\n            onClick={() => this.showDetailModal(title, text)}\r\n          >\r\n            View\r\n          </a-button>\r\n        </div>\r\n      );\r\n    },\r\n\r\n    showDetailModal(title, data) {\r\n      // 使用JsonDetailModal组件的showDetailModal方法\r\n      this.$refs.jsonDetailModal.showDetailModal(title, data);\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.kubernetes-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;AA0MA,SAAAA,QAAA;AACA,OAAAC,KAAA;AACA,OAAAC,aAAA;AACA,OAAAC,eAAA;AAEA;EACAC,UAAA;IACAF,aAAA;IACAC;EACA;EACAE,IAAA;EACAC,KAAA;IAAA,MAAAC,CAAA,QAAAC,cAAA;IACA;MACAC,SAAA;MACAC,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,aAAA;MACAC,QAAA;MACAC,eAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,6BAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,eAAA;MACAC,sBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,cAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,yBAAA;MACAC,gCAAA;MACAC,gBAAA,GACA;QAAAC,KAAA;QAAAC,SAAA;QAAAC,GAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,SAAA;QAAAC,GAAA;MAAA,EACA;MACAC,cAAA,GACA;QAAAH,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAE,cAAA,GACA;QAAAT,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAG,qBAAA,GACA;QAAAV,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAI,cAAA,GACA;QAAAX,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAK,oBAAA,GACA;QAAAZ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QAAAJ,KAAA;QAAAC,SAAA;QAAAC,GAAA;QAAAE,KAAA;MAAA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAM,UAAA,GACA;QACAb,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAE,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAE,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAO,WAAA,GACA;QACAd,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAQ,aAAA,GACA;QACAf,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,EACA;MACAY,gBAAA,GACA;QACAhB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAU,WAAA,GACA;QACAjB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAW,kBAAA,GACA;QACAlB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAY,kBAAA,GACA;QACAnB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAa,yBAAA,GACA;QACApB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,GACA;QACAP,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA,SAAAC,iBAAA,CAAAD,IAAA;MACA,EACA;MACAc,gCAAA,GACA;QACArB,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;MACA,GACA;QACAJ,KAAA;QACAC,SAAA;QACAC,GAAA;QACAE,KAAA;QACAC,QAAA;QACAC,YAAA,EAAAC,IAAA;UACA;YACA,MAAAe,WAAA,GAAAC,KAAA,CAAAC,OAAA,CAAAjB,IAAA,IAAAA,IAAA,GAAAA,IAAA,GAAAkB,IAAA,CAAAC,KAAA,CAAAnB,IAAA;YAEA,IAAAoB,WAAA;YACA,IAAAL,WAAA,IAAAA,WAAA,CAAAM,MAAA;cACA,MAAAC,OAAA,GAAAP,WAAA,CAAAQ,GAAA,CAAAC,CAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAD,CAAA,CAAAnE,IAAA,IAAAqE,IAAA;cACAN,WAAA,GAAAE,OAAA,CAAAD,MAAA,QAAAC,OAAA,CAAAK,KAAA,kBAAAL,OAAA;YACA;YAEA,OAAA/D,CAAA;cAAA,SACA;YAAA,IAAAA,CAAA;cAAA,SACA;YAAA,IACA6D,WAAA,IAAA7D,CAAA;cAAA;gBAAA,QAGA;cAAA;cAAA,SACA;cAAA;gBAAA,SACAqE,CAAA,UAAAC,eAAA,+BAAAd,WAAA;cAAA;YAAA;UAMA,SAAAe,KAAA;YACAC,OAAA,CAAAD,KAAA,+BAAAA,KAAA;YACA;UACA;QACA;MACA,EACA;MACAE,UAAA;QACAC,QAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAnF,QAAA;EACA;EACAoF,KAAA;IACAC,eAAAC,KAAA;MACA,KAAAC,SAAA;MACA,KAAAL,WAAA;MACA,IAAAI,KAAA;QACA,KAAAE,kBAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,SAAAJ,cAAA;MACA,KAAAG,kBAAA;IACA;EACA;EACAE,OAAA;IACAC,gBAAAhD,GAAA;MACA,KAAAlC,SAAA,GAAAkC,GAAA;MACA,KAAA6C,kBAAA;IACA;IACA,MAAAA,mBAAA;MACA,UAAAH,cAAA;QACAN,OAAA,CAAAD,KAAA;QACA,KAAAS,SAAA;QACA;MACA;MACA,MAAAK,YAAA,QAAAnF,SAAA;MACA,oBAAAoF,qBAAA,CAAAD,YAAA;MACA;QACA,IAAAE,QAAA;QACAA,QAAA,SAAA7F,KAAA,CAAA8F,GAAA,aAAAH,YAAA,SAAAP,cAAA;UACAW,MAAA;YACAC,MAAA,OAAAC;UACA;QACA;QACA,MAAA5F,IAAA,GAAAwF,QAAA,CAAAxF,IAAA;QACA,aAAA6F,mBAAA,CAAAP,YAAA,OAAAtF,IAAA;MACA,SAAAwE,KAAA;QACAC,OAAA,CAAAD,KAAA,mBAAAc,YAAA,KAAAd,KAAA;QACA,aAAAqB,mBAAA,CAAAP,YAAA;MACA;QACA,oBAAAC,qBAAA,CAAAD,YAAA;MACA;IACA;IACAO,oBAAAC,SAAA;MACA,MAAAC,UAAA,GAAAD,SAAA,CAAAE,OAAA;;MAEA;MACA,IAAAD,UAAA;QACA;MACA;MAEA,MAAAE,SAAA,GAAAF,UAAA,CAAAC,OAAA,eAAAE,CAAA,EAAAC,MAAA,KAAAA,MAAA,CAAAC,WAAA;MACA,OAAAH,SAAA;IACA;IACAV,sBAAAc,MAAA;MACA,OAAAA,MAAA,CAAAC,MAAA,IAAAF,WAAA,KAAAC,MAAA,CAAAhC,KAAA;IACA;IACAY,UAAA;MACA,KAAA7E,aAAA;MACA,KAAAC,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,OAAA;MACA,KAAAC,QAAA;MACA,KAAAC,UAAA;MACA,KAAAC,aAAA;MACA,KAAAC,QAAA;MACA,KAAAC,eAAA;MACA,KAAAC,eAAA;MACA,KAAAC,sBAAA;MACA,KAAAC,6BAAA;IACA;IACAyB,kBAAAD,IAAA,EAAAP,KAAA;MAAA,MAAAlC,CAAA,QAAAC,cAAA;MACA,KAAAwC,IAAA,IAAAA,IAAA;;MAEA;MACA,MAAAoB,WAAA,GAAAJ,KAAA,CAAAC,OAAA,CAAAjB,IAAA,aAAAA,IAAA,CAAAqB,MAAA,MACA,OAAArB,IAAA,iBAAAA,IAAA,YACA6D,MAAA,CAAAC,OAAA,CAAA9D,IAAA,EAAA2B,KAAA,OAAAJ,GAAA,GAAAwC,CAAA,EAAAC,CAAA,MACA,GAAAD,CAAA,YAAAC,CAAA,gBAAA9C,IAAA,CAAA+C,SAAA,CAAAD,CAAA,EAAAE,SAAA,UAAAF,CAAA,EACA,EAAAtC,IAAA,UAAAmC,MAAA,CAAAM,IAAA,CAAAnE,IAAA,EAAAqB,MAAA,qBACA+C,MAAA,CAAApE,IAAA,EAAAqB,MAAA,QAAA+C,MAAA,CAAApE,IAAA,EAAA2B,KAAA,kBAAAyC,MAAA,CAAApE,IAAA;MAEA,OAAAzC,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA,SACA;MAAA,IAAA6D,WAAA,IAAA7D,CAAA;QAAA;UAAA,QAEA;QAAA;QAAA,SACA;QAAA;UAAA,SACAqE,CAAA,UAAAC,eAAA,CAAApC,KAAA,EAAAO,IAAA;QAAA;MAAA;IAMA;IAEA6B,gBAAApC,KAAA,EAAAnC,IAAA;MACA;MACA,KAAA+G,KAAA,CAAAC,eAAA,CAAAzC,eAAA,CAAApC,KAAA,EAAAnC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}