(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-364a09a0"],{"185c":function(s,e,t){"use strict";t("eb7e")},da71:function(s,e,t){"use strict";t.r(e);var i=function(){var s=this,e=s._self._c;return e("div",[e("a-row",{attrs:{type:"flex",gutter:24}},[e("a-col",{staticClass:"mb-24",attrs:{span:24}},[e("ProcessInfo")],1)],1)],1)},a=[],r=function(){var s=this,e=s._self._c;return e("a-card",{staticClass:"header-solid h-full process-card",attrs:{bordered:!1,bodyStyle:{padding:0}},scopedSlots:s._u([{key:"title",fn:function(){return[e("div",{staticClass:"card-header-wrapper"},[e("div",{staticClass:"header-wrapper"},[e("div",{staticClass:"logo-wrapper"},[e("svg",{class:"text-"+s.sidebarColor,attrs:{width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"}},[e("path",{attrs:{fill:"currentColor",d:"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"}})])]),e("h6",{staticClass:"font-semibold m-0"},[s._v(s._s(s.$t("headTopic.process")))])]),e("div",[e("RefreshButton",{on:{refresh:s.fetchProcesses}})],1)])]},proxy:!0}])},[e("a-table",{attrs:{columns:s.columns,"data-source":s.processes,rowKey:s=>s.pid,customRow:s.rowClick,pagination:s.pagination},scopedSlots:s._u([{key:"emptyText",fn:function(){return[e("a-empty",{attrs:{description:"No processes found"}})]},proxy:!0}])}),e("a-modal",{attrs:{title:"AI Security Analysis",width:"800px"},on:{cancel:s.handleAIAnalysisClose},scopedSlots:s._u([{key:"footer",fn:function(){return[e("a-button",{on:{click:s.handleAIAnalysisClose}},[s._v("Close")])]},proxy:!0}]),model:{value:s.aiAnalysisVisible,callback:function(e){s.aiAnalysisVisible=e},expression:"aiAnalysisVisible"}},[e("div",{staticClass:"ai-analysis-container"},[s.selectedProcess?e("div",{staticClass:"process-info"},[e("p",[e("strong",[s._v("PID:")]),s._v(" "+s._s(s.selectedProcess.pid))])]):s._e(),s.aiAnalysisLoading?e("a-skeleton",{attrs:{loading:s.aiAnalysisLoading,active:""}}):s._e(),!s.aiAnalysisLoading&&s.aiAnalysisResult?e("div",{staticClass:"analysis-results"},[e("div",{staticClass:"markdown-content",domProps:{innerHTML:s._s(s.formatMarkdown(s.aiAnalysisResult))}})]):s._e(),s.aiAnalysisLoading||s.aiAnalysisResult||s.aiAnalysisError?s._e():e("div",{staticClass:"no-analysis"},[e("a-empty",{attrs:{description:"Analyzing process..."}})],1),!s.aiAnalysisLoading&&s.aiAnalysisError?e("div",{staticClass:"analysis-error"},[e("a-alert",{attrs:{message:"Analysis Error",description:s.aiAnalysisError,type:"error","show-icon":""}})],1):s._e()],1)])],1)},o=[],l=t("2f62"),n=t("fec3"),c=t("f188"),d={components:{RefreshButton:c["a"]},data(){const s=this.$createElement;return{processes:[],columns:[{title:"PID",dataIndex:"pid",key:"pid",width:120},{title:"Process Name",dataIndex:"cmdline",key:"cmdline",width:"95%",ellipsis:!1},{title:"AI Analysis",key:"ai_analysis",width:"15%",align:"center",customRender:(e,t)=>s("a-button",{class:"bg-"+this.sidebarColor,style:"color: white",attrs:{size:"small"},on:{click:s=>{s.stopPropagation(),this.showAIAnalysis(t)}}},["Analyze"])}],pagination:{pageSize:100,current:1,onChange:s=>{this.$store.dispatch("processList/updateCurrentPage",s)}},aiAnalysisVisible:!1,aiAnalysisLoading:!1,aiAnalysisResult:null,aiAnalysisError:null,selectedProcess:null}},computed:{...Object(l["e"])(["selectedNodeIp","currentProject","sidebarColor"]),...Object(l["e"])("processList",["currentPage","scrollPosition","lastViewedPid"])},watch:{selectedNodeIp:{handler(){this.fetchProcesses()},immediate:!0},currentPage:{handler(s){s&&this.pagination.current!==s&&(this.pagination.current=s)},immediate:!0}},mounted(){this.fetchProcesses(),this.$nextTick(()=>{this.scrollPosition>0&&setTimeout(()=>{window.scrollTo({top:this.scrollPosition,behavior:"auto"})},100),this.lastViewedPid&&setTimeout(this.applyHighlight,500)})},updated(){this.lastViewedPid&&this.processes.length>0&&this.applyHighlight()},beforeRouteLeave(s,e,t){"ProcessDetail"!==s.name&&(this.$store.dispatch("processList/resetState"),this.$store.dispatch("processList/clearLastViewedPid")),t()},methods:{async fetchProcesses(){if(this.selectedNodeIp)try{const s=await n["a"].get("/api/processes/"+this.selectedNodeIp,{params:{fields:"pid,cmdline",dbFile:this.currentProject}});this.processes=s.data}catch(s){console.error("Error fetching processes:",s)}else console.error("Node IP is not defined")},rowClick(s,e){const t=this.lastViewedPid&&s.pid===this.lastViewedPid;return{on:{click:()=>{this.viewProcessDetails(s)}},class:{"last-viewed-row":t},style:{cursor:"pointer"}}},viewProcessDetails(s){const e=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;this.$store.dispatch("processList/updateScrollPosition",e),this.$store.dispatch("processList/updateLastViewedPid",s.pid),this.$router.push({name:"ProcessDetail",params:{pid:s.pid}})},showAIAnalysis(s){this.selectedProcess=s,this.aiAnalysisVisible=!0,this.aiAnalysisResult=null,this.aiAnalysisError=null,this.requestAIAnalysis()},applyHighlight(){if(!this.lastViewedPid)return;const s=document.querySelectorAll(".ant-table-row");for(let e=0;e<s.length;e++){const t=s[e],i=t.querySelectorAll("td");if(i.length>0&&i[0].textContent.includes(this.lastViewedPid)){t.classList.add("last-viewed-row");for(let s=0;s<i.length;s++){const e=i[s];e.style.backgroundColor="#f5f5f5",e.style.borderTop="1px solid #1890ff",e.style.borderBottom="1px solid #1890ff",0===s&&(e.style.borderLeft="3px solid #1890ff"),s===i.length-1&&(e.style.borderRight="3px solid #1890ff")}break}}},handleAIAnalysisClose(){this.aiAnalysisVisible=!1,this.aiAnalysisResult=null,this.aiAnalysisError=null},async requestAIAnalysis(){if(this.selectedProcess)if(this.selectedNodeIp){this.aiAnalysisLoading=!0,this.aiAnalysisError=null;try{const s=await n["a"].get("/api/processes/"+this.selectedNodeIp,{params:{pid:this.selectedProcess.pid,fields:"pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps",dbFile:this.currentProject||""}}),e=s.data,t=await n["a"].post("/api/ai/analyze/process",{process_data:{process_list:[e]}},{timeout:6e5});t.data.success?this.aiAnalysisResult=t.data.analysis:this.aiAnalysisError=t.data.error||"Failed to analyze process data"}catch(e){var s;console.error("AI analysis error:",e),this.aiAnalysisError=(null===(s=e.response)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.error)||e.message||"An error occurred during analysis"}finally{this.aiAnalysisLoading=!1}}else this.aiAnalysisError="No node selected";else this.aiAnalysisError="No process selected for analysis"},formatMarkdown(s){if(!s)return"";let e=s.replace(/^# (.*$)/gm,"<h1>$1</h1>").replace(/^## (.*$)/gm,"<h2>$1</h2>").replace(/^### (.*$)/gm,"<h3>$1</h3>").replace(/^#### (.*$)/gm,"<h4>$1</h4>").replace(/^##### (.*$)/gm,"<h5>$1</h5>").replace(/^###### (.*$)/gm,"<h6>$1</h6>");return e=e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/__(.*?)__/g,"<strong>$1</strong>").replace(/_(.*?)_/g,"<em>$1</em>"),e=e.replace(/```([\s\S]*?)```/g,"<pre><code>$1</code></pre>"),e=e.replace(/`([^`]+)`/g,"<code>$1</code>"),e=e.replace(/^\s*\d+\.\s+(.*$)/gm,"<li>$1</li>").replace(/^\s*[-*]\s+(.*$)/gm,"<li>$1</li>"),e=e.replace(/^(?!<[a-z])(.*$)/gm,(function(s){return s.trim()?"<p>"+s+"</p>":""})),e=e.replace(/\[(.*?)\]\((.*?)\)/g,'<a href="$2" target="_blank">$1</a>'),e}}},p=d,h=(t("185c"),t("2877")),u=Object(h["a"])(p,r,o,!1,null,"16962dbd",null),y=u.exports,f={components:{ProcessInfo:y}},m=f,g=Object(h["a"])(m,i,a,!1,null,null,null);e["default"]=g.exports},eb7e:function(s,e,t){},f188:function(s,e,t){"use strict";var i=function(){var s=this,e=s._self._c;return e("a-button",{class:["refresh-button","text-"+s.sidebarColor],attrs:{icon:"reload"},on:{click:function(e){return s.$emit("refresh")}}},[s._v(" "+s._s(s.text||s.$t("common.refresh"))+" ")])},a=[],r=t("2f62"),o={computed:{...Object(r["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},l=o,n=t("2877"),c=Object(n["a"])(l,i,a,!1,null,"80cb1374",null);e["a"]=c.exports}}]);
//# sourceMappingURL=chunk-364a09a0.ff89fb38.js.map