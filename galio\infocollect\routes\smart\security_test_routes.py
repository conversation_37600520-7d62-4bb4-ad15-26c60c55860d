"""
安全测试路由 - 提供SecurityTestAgent的API接口
"""

from flask import Blueprint, jsonify, request
import asyncio
from typing import Dict, Any

from intelligent_executor.agent import SecurityTestAgent
from infocollect.log.logger import log_info, log_error

bp = Blueprint('security_test', __name__)

# 全局Agent实例
security_agent = SecurityTestAgent()


@bp.route('/run', methods=['POST'])
def run_security_test():
    """运行安全测试"""
    try:
        data = request.get_json()
        
        # 验证请求数据
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        security_info = data.get('security_info', {})
        if not security_info:
            return jsonify({'error': '未提供安全信息'}), 400
        
        node_ip = data.get('node_ip')
        max_testcases = data.get('max_testcases', 5)
        auto_execute = data.get('auto_execute', True)
        
        log_info(f"Starting security test for node: {node_ip}")
        
        # 运行安全测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                security_agent.run_security_test(
                    security_info=security_info,
                    node_ip=node_ip,
                    max_testcases=max_testcases,
                    auto_execute=auto_execute
                )
            )
        finally:
            loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        log_error(f"Error in run_security_test: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/k8s-test', methods=['POST'])
def run_k8s_security_test():
    """专门针对K8s信息的安全测试"""
    try:
        data = request.get_json()
        
        # 构建K8s安全信息
        k8s_info = data.get('k8s_info', {})
        if not k8s_info:
            return jsonify({'error': '未提供K8s信息'}), 400
        
        # 构造安全信息格式
        security_info = {
            'k8s': k8s_info,
            'info_type': 'kubernetes'
        }
        
        node_ip = data.get('node_ip')
        max_testcases = data.get('max_testcases', 10)  # K8s测试可以更多用例
        auto_execute = data.get('auto_execute', False)  # K8s测试默认不自动执行
        
        log_info(f"Starting K8s security test for node: {node_ip}")
        
        # 运行安全测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                security_agent.run_security_test(
                    security_info=security_info,
                    node_ip=node_ip,
                    max_testcases=max_testcases,
                    auto_execute=auto_execute
                )
            )
        finally:
            loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        log_error(f"Error in run_k8s_security_test: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/status', methods=['GET'])
def get_current_session_status():
    """获取当前会话状态"""
    try:
        status = security_agent.get_current_session_status()
        
        if status:
            return jsonify({
                'success': True,
                'current_session': status
            })
        else:
            return jsonify({
                'success': True,
                'current_session': None,
                'message': '当前没有运行中的测试会话'
            })
            
    except Exception as e:
        log_error(f"Error getting session status: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/history', methods=['GET'])
def get_session_history():
    """获取会话历史"""
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 10, type=int)
        
        history = security_agent.get_session_history()
        
        # 分页处理
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_history = history[start_idx:end_idx]
        
        return jsonify({
            'success': True,
            'history': paginated_history,
            'total': len(history),
            'page': page,
            'page_size': page_size,
            'total_pages': (len(history) + page_size - 1) // page_size
        })
        
    except Exception as e:
        log_error(f"Error getting session history: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/demo', methods=['GET'])
def run_demo_test():
    """运行演示测试"""
    try:
        # 构造演示数据
        demo_security_info = {
            'k8s': {
                'cluster_info': {
                    'version': 'v1.25.0',
                    'nodes': 3
                },
                'pods': [
                    {'name': 'test-pod', 'namespace': 'default'},
                    {'name': 'app-pod', 'namespace': 'production'}
                ],
                'services': [
                    {'name': 'test-service', 'type': 'ClusterIP'}
                ]
            },
            'docker': {
                'containers': [
                    {'name': 'web-container', 'image': 'nginx:latest'},
                    {'name': 'app-container', 'image': 'app:v1.0'}
                ]
            }
        }
        
        log_info("Running demo security test")
        
        # 运行安全测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                security_agent.run_security_test(
                    security_info=demo_security_info,
                    node_ip='demo-node',
                    max_testcases=3,
                    auto_execute=False  # 演示模式不执行实际命令
                )
            )
        finally:
            loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        log_error(f"Error in demo test: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/analyze-only', methods=['POST'])
def analyze_testcases_only():
    """仅分析测试用例，不执行命令"""
    try:
        data = request.get_json()
        
        security_info = data.get('security_info', {})
        if not security_info:
            return jsonify({'error': '未提供安全信息'}), 400
        
        max_testcases = data.get('max_testcases', 5)
        
        log_info("Running analysis-only security test")
        
        # 运行安全测试（不执行命令）
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                security_agent.run_security_test(
                    security_info=security_info,
                    node_ip=None,
                    max_testcases=max_testcases,
                    auto_execute=False
                )
            )
        finally:
            loop.close()
        
        return jsonify(result)
        
    except Exception as e:
        log_error(f"Error in analyze-only test: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/report/<session_id>', methods=['GET'])
def get_session_report(session_id):
    """获取指定会话的报告"""
    try:
        # 从会话历史中查找指定会话
        history = security_agent.get_session_history()
        session = next((s for s in history if s['session_id'] == session_id), None)
        
        if not session:
            return jsonify({'error': '会话不存在'}), 404
        
        # 这里应该从存储中获取完整的会话数据和报告
        # 暂时返回基本信息
        return jsonify({
            'success': True,
            'session_id': session_id,
            'session_info': session,
            'message': '报告功能正在开发中，请使用完整的测试结果'
        })
        
    except Exception as e:
        log_error(f"Error getting session report: {e}")
        return jsonify({'error': str(e)}), 500


@bp.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        return jsonify({
            'success': True,
            'service': 'SecurityTestAgent',
            'status': 'healthy',
            'agent_initialized': security_agent is not None,
            'current_session': security_agent.get_current_session_status() is not None,
            'total_sessions': len(security_agent.get_session_history())
        })
        
    except Exception as e:
        log_error(f"Error in health check: {e}")
        return jsonify({'error': str(e)}), 500 