// 深色模式变量
:root.dark-mode {
  // 基础颜色
  --bg-color: #121212;
  --text-color: #e0e0e0;
  --border-color: #333333;
  --card-bg: #1e1e1e;
  --header-bg: rgba(30, 30, 30, 0.95);
  --sidebar-bg: #1e1e1e;
  --hover-bg: #2c2c2c;
  --active-bg: #333333;
  --scrollbar-color: rgba(140, 140, 140, 0.4);
  --shadow-color: rgba(0, 0, 0, 0.5);
  --input-bg: #2c2c2c;
  --input-text: #999999;
  --input-placeholder: #666666;
  --table-header-bg: #252525;
  --table-row-hover: #2a2a2a;
  --table-border: #333333;
  --modal-bg: #1e1e1e;
  --drawer-bg: #1e1e1e;
  --tooltip-bg: #333333;
  --tooltip-text: #e0e0e0;
  --dropdown-bg: #1e1e1e;
  --dropdown-hover: #2c2c2c;
  --select-bg: #1e1e1e;
  --select-text: #e0e0e0;
}

// 深色模式样式
.dark-mode {
  // 全局背景和文本颜色
  background-color: var(--bg-color);
  color: var(--text-color);

  //home背景色
  .content-wrapper {
    background-color: #1e1e1e !important;
  }
  // 简单移除所有按钮的边框
  .ant-btn {
    border: none !important;
  }

  // 全局标签页样式 - 最高优先级
  .ant-tabs-tab {
    color: rgba(255, 255, 255, 0.85) !important;

    &:hover {
      color: #ffffff !important;
    }
  }

  .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #1890ff !important;
  }

  // 全局选择器样式覆盖
  .ant-select-selection,
  .ant-select-selection--single,
  .ant-select-selection__rendered,
  .ant-select-selection-selected-value {
    background-color: var(--input-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
  }

  // 全局下拉菜单样式覆盖
  .ant-dropdown,
  .ant-dropdown-menu,
  .ant-dropdown-menu-item,
  .ant-dropdown-menu-submenu-title {
    background-color: var(--dropdown-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
  }

  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-submenu-title:hover {
    background-color: var(--dropdown-hover) !important;
  }

  // 全局选择器下拉菜单已选中项样式覆盖
  .ant-select-dropdown-menu-item-selected {
    background-color: rgba(24, 144, 255, 0.15) !important;
    color: #1890ff !important;

    &:hover {
      background-color: rgba(24, 144, 255, 0.25) !important;
    }
  }

  // 布局组件
  .layout-dashboard {
    background-color: var(--bg-color);

    // 侧边栏
    .ant-layout-sider.sider-primary {
      background: var(--sidebar-bg) !important;

      .brand-name {
        color: var(--text-color);
      }

      .ant-menu-item {
        .label {
          color: var(--text-color);
        }

        .icon {
          background-color: var(--active-bg);
        }

        svg path {
          fill: var(--text-color);
        }

        &:hover {
          background-color: var(--hover-bg);
        }
      }

      .ant-menu-submenu-title {
        &:hover {
          background-color: var(--hover-bg);
        }
      }

      .brand-divider {
        background: linear-gradient(to right, rgba(255,255,255,0.02), rgba(255,255,255,0.06), rgba(255,255,255,0.02));
      }
    }

    // 头部
    .ant-layout-header {
      background: transparent;

      .ant-breadcrumb {
        .ant-breadcrumb-link a {
          color: rgba(255, 255, 255, 0.65);
        }

        .ant-breadcrumb-item .anticon {
          color: rgba(255, 255, 255, 0.65);
        }

        span:last-child .ant-breadcrumb-link {
          color: var(--text-color);
        }

        // 修复面包屑分隔符（斜线）颜色
        .ant-breadcrumb-separator {
          color: rgba(255, 255, 255, 0.45);
        }
      }

      // 头部导航按钮样式
      .header-nav-buttons {
        // 导航按钮样式
        .header-nav-button {
          color: rgba(255, 255, 255, 0.65);

          // 按钮之间的分隔线
          &:not(:last-child)::after {
            background-color: rgba(255, 255, 255, 0.15);
          }

          &:hover {
            color: #40a9ff;
            background-color: rgba(24, 144, 255, 0.15);
          }

          &.active {
            color: white;
            font-weight: 500;
            /* 移除固定背景色，使用 bg-${sidebarColor} 类 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

            /* 确保透明度在暗黑模式下也能正确应用 */
            &.nav-btn-transparent {
              opacity: 0.8 !important;
            }
          }
        }
      }

      // 头部控制区域
      .header-control {
        // 所有按钮和链接
        .ant-btn-link, .ant-dropdown-link, a {
          color: var(--text-color);
        }

        // 所有SVG图标
        svg {
          path {
            fill: var(--text-color);
          }
        }

        // 语言切换器
        .language-switcher {
          color: var(--text-color);

          &:hover {
            color: #1890ff;
          }
        }

        // 节点选择器
        .node-selector-link {
          color: var(--text-color);

          &:hover {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
          }

          .node-name {
            color: var(--text-color);
          }
        }

        // 节点选择下拉菜单
        .ant-dropdown-trigger + .ant-dropdown {
          .ant-dropdown-menu {
            background-color: var(--dropdown-bg) !important;
            border: 1px solid var(--border-color) !important;

            .ant-dropdown-menu-item {
              color: var(--text-color) !important;

              &:hover {
                background-color: var(--dropdown-hover) !important;
              }
            }
          }
        }
      }

        // 通知按钮
      .notification-trigger svg path {
        fill: var(--text-color);
      }

      // 主题切换按钮
      .theme-toggle-button svg {
        path, circle {
          fill: var(--text-color);
          stroke: var(--text-color);
        }
      }
      }
    }

    // 普通头部
    .ant-layout-header {
      background: rgba(18, 18, 18, 0.95) !important;
    }

    // 固定头部
    &.navbar-fixed {
      .ant-layout>div>.ant-affix .ant-layout-header {
        background: rgba(18, 18, 18, 0.95) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2) !important;
        backdrop-filter: blur(12px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 0 0 15px 15px;
        margin: 0 8px !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

    }

    // 通知弹出框
    .notification-popover {
      .ant-popover-inner-content {
        background-color: var(--dropdown-bg);
        color: var(--text-color);
      }

      .ant-popover-arrow {
        border-color: var(--dropdown-bg);
        background-color: var(--dropdown-bg);
      }

      .notification-container {
        .notification-header {
          background-color: var(--table-header-bg);
          border-color: var(--border-color);
          color: var(--text-color);

          .ant-btn-link {
            color: #1890ff;
          }
        }

        .notification-list {
          .notification-item {
            border-color: var(--border-color);

            .notification-title {
              color: var(--text-color);
            }

            .notification-message {
              color: rgba(255, 255, 255, 0.65);
            }

            .notification-time {
              color: rgba(255, 255, 255, 0.45);
            }
          }

          .empty-notification {
            color: rgba(255, 255, 255, 0.45);
          }
        }
      }
    }

    // 内容区域
    .ant-layout-content {
      background-color: var(--bg-color);

      // 空数据状态
      .ant-empty {
        background-color: var(--card-bg);
        color: var(--text-color);

        .ant-empty-image {
          opacity: 0.5;
        }

        .ant-empty-description {
          color: var(--text-color);
        }
      }
      // 空白区域
      .ant-table-placeholder {
        background-color: var(--card-bg);
        border-color: var(--border-color);

        .ant-empty {
          background-color: transparent;
        }
      }
      // 内容卡片
      .content-card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
      }

      // 通用内容容器
      .content-container,
      .main-content,
      .data-container,
      .empty-container {
        background-color: var(--card-bg);
        border-color: var(--border-color);
      }
    }

    // 卡片
    .ant-card {
      background-color: var(--card-bg) !important;
      border-color: var(--border-color);

      .ant-card-head {
        background-color: var(--table-header-bg) !important;
        border-color: var(--border-color);

        .ant-card-head-title,
        .cli-title {
          color: var(--text-color);
        }
      }

      .ant-card-body {
        background-color: var(--card-bg) !important;
        color: var(--text-color);
      }

      // 嵌套卡片
      .ant-card {
        background-color: var(--card-bg) !important;

        .ant-card-head {
          background-color: var(--table-header-bg) !important;
        }
      }
    }

    // 特定组件卡片样式覆盖
    .task-card,
    .criclebox,
    .process-card,
    .docker-card,
    .kubernetes-card,
    .hardware-card,
    .filesystem-card,
    .port-card,
    .package-card,
    .testcase-card,
    .proxy-card,
    .cli-card,
    .repository-config-card,
    .download-results-card,
    .host-config-card {
      background-color: var(--card-bg) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;

      // 确保卡片内的标签页也能正确显示
      .ant-tabs {
        .ant-tabs-tab {
          color: rgba(255, 255, 255, 0.85) !important;

          &:hover {
            color: #ffffff !important;
          }

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #1890ff !important;
            }
          }
        }
      }

      .ant-card {
        background-color: var(--card-bg) !important;

        .ant-card-head {
          background-color: var(--table-header-bg) !important;
        }
      }

      // 空数据状态
      .ant-empty {
        background-color: var(--card-bg) !important;

        .ant-empty-image {
          opacity: 0.5;
        }

        .ant-empty-description {
          color: var(--text-color) !important;
        }
      }

      // 表格空数据
      .ant-table-placeholder {
        background-color: var(--card-bg) !important;
        border-color: var(--border-color) !important;
      }

      // 代理配置卡片特殊样式
      .proxy-form {
        .ant-input-group {
          .ant-input-group-addon {
            background-color: var(--table-header-bg);
            color: var(--text-color);
            border-color: var(--border-color);
          }
        }

        .proxy-input-group {
          background-color: var(--card-bg);
          border-color: var(--border-color);

          .proxy-input-label {
            color: var(--text-color);
          }
        }
      }

      // 步骤图标和连接线颜色修复
      .steps-flow {
        .ant-steps-item {
          &-process,
          &-finish {
            .ant-steps-item-container {
              .ant-steps-item-content {
                .ant-steps-item-title::after {
                  background-color: #1890ff !important;
                }
              }
            }

            .step-icon {
              color: #1890ff !important;
            }
          }

          &-wait {
            .ant-steps-item-container {
              .ant-steps-item-content {
                .ant-steps-item-title::after {
                  background-color: rgba(255, 255, 255, 0.2) !important;
                }
              }
            }

            .step-icon {
              color: rgba(255, 255, 255, 0.45) !important;
            }
          }

          &:last-child {
            .step-icon {
              color: rgba(255, 255, 255, 0.45) !important;
            }

            &.ant-steps-item-process,
            &.ant-steps-item-finish {
              .step-icon {
                color: #1890ff !important;
              }
            }
          }
        }
      }
    }

    // 全局分页控件样式
    .ant-pagination {
      .ant-pagination-item {
        background-color: var(--card-bg);
        border-color: var(--border-color);

        a {
          color: var(--text-color);
        }

        &-active {
          border-color: #1890ff;

          a {
            color: #1890ff;
          }
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next {
        .ant-pagination-item-link {
          background-color: var(--card-bg);
          border-color: var(--border-color);
          color: var(--text-color);
        }
      }

      .ant-pagination-options {
        .ant-select-selector {
          background-color: var(--card-bg);
          border-color: var(--border-color);
          color: var(--text-color);
        }
      }

      .ant-pagination-total-text {
        color: var(--text-color);
      }

      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        .ant-pagination-item-container {
          .ant-pagination-item-link-icon {
            color: #1890ff;
          }
          .ant-pagination-item-ellipsis {
            color: var(--text-color);
          }
        }
      }

      // 分页跳转输入框样式
      .ant-pagination-options-quick-jumper {
        color: var(--text-color);

        input {
          background-color: var(--input-bg);
          border-color: var(--border-color);
          color: var(--text-color);

          &::placeholder {
            color: var(--input-placeholder);
          }

          &:hover, &:focus {
            border-color: #1890ff;
          }
        }
      }
    }

    // 标签页样式
    .ant-tabs {
      color: var(--text-color);

      .ant-tabs-tab {
        color: rgba(255, 255, 255, 0.85) !important; // 提高未选中标签的亮度

        &:hover {
          color: #ffffff !important; // 悬停时使用纯白色
        }

        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #1890ff !important;
          }
        }
      }

      .ant-tabs-ink-bar {
        background-color: #1890ff !important;
      }
    }

    // ===== 新表格系统暗黑模式适配 =====

    // 配置类表格暗黑模式
    .config-table {
      .ant-table {
        border-color: var(--border-color) !important;
        background-color: var(--card-bg);

        .ant-table-thead > tr > th {
          background-color: var(--table-header-bg);
          border-color: var(--border-color);
          color: var(--text-color);
        }

        .ant-table-tbody > tr {
          &:hover > td {
            background-color: var(--table-row-hover);
          }

          > td {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-color);
          }

          // 表格行选择状态 - 修复全选时的白色背景
          &.ant-table-row-selected > td {
            background-color: rgba(24, 144, 255, 0.1) !important;
            color: var(--text-color) !important;
          }
        }

        // 表格内分页
        .ant-pagination {
          .ant-pagination-item {
            background-color: var(--card-bg);
            border-color: var(--border-color);

            a {
              color: var(--text-color);
            }

            &-active {
              border-color: #1890ff;
              a {
                color: #1890ff;
              }
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            .ant-pagination-item-link {
              background-color: var(--card-bg);
              border-color: var(--border-color);
              color: var(--text-color);
            }
          }

          .ant-pagination-options {
            .ant-select-selector {
              background-color: var(--card-bg);
              border-color: var(--border-color);
              color: var(--text-color);
            }
          }

          .ant-pagination-total-text {
            color: var(--text-color);
          }
        }
      }

      // 配置表格中的输入框
      .ant-input {
        background-color: var(--input-bg);
        border-color: var(--border-color);
        color: var(--input-text);

        &:focus, &:hover {
          border-color: #40a9ff;
        }
      }
    }

    // 结果类表格暗黑模式
    .result-table {
      .ant-table {
        border-color: var(--border-color) !important;
        background-color: var(--card-bg);

        .ant-table-thead > tr > th {
          background-color: var(--table-header-bg);
          border-color: var(--border-color);
          color: var(--text-color);
        }

        .ant-table-tbody > tr {
          &:hover > td {
            background-color: var(--table-row-hover);
          }

          > td {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-color);
          }

          // 表格行选择状态 - 修复全选时的白色背景
          &.ant-table-row-selected > td {
            background-color: rgba(24, 144, 255, 0.1) !important;
            color: var(--text-color) !important;
          }

          // 表格行选择悬停状态
          //&.ant-table-row-selected:hover > td {
          //  background-color: rgba(24, 144, 255, 0.15) !important;
          //  color: var(--text-color) !important;
          //}
        }

        // 空状态
        .ant-empty .ant-empty-description {
          color: var(--text-color);
        }
      }
    }

    // 统计卡片暗黑模式
    .stats-summary {
      background-color: var(--table-header-bg);
      border-color: var(--border-color);

      .ant-statistic .ant-statistic-title {
        color: var(--text-color);
      }
    }

    // 全局统计组件标题颜色修复（用于非表格系统中的统计组件）
    .ant-statistic:not(.stats-summary .ant-statistic) {
      .ant-statistic-title {
        color: var(--text-color) !important;
      }
    }



    // 输入框
    .ant-input {
      background-color: var(--input-bg);
      color: var(--input-text);
      border-color: var(--border-color);

      &::placeholder {
        color: var(--input-placeholder);
      }

      &:hover, &:focus {
        border-color: #1890ff;
      }
    }

    // 输入框组
    .ant-input-group {
      .ant-input {
        background-color: var(--input-bg);
        color: var(--input-text);
        border-color: var(--border-color);
      }

      .ant-input-group-addon {
        background-color: var(--table-header-bg);
        color: var(--text-color);
        border-color: var(--border-color);
      }
    }

    // 输入框前缀/后缀
    .ant-input-affix-wrapper {
      background-color: var(--input-bg);
      border-color: var(--border-color);

      .ant-input {
        background-color: transparent;
        color: var(--input-text);
      }

      .ant-input-prefix,
      .ant-input-suffix {
        color: rgba(255, 255, 255, 0.45);
      }

      &:hover, &:focus, &-focused {
        border-color: #1890ff;
      }
    }

    // 表单
    .ant-form {
      .ant-form-item-label > label {
        color: var(--text-color);
      }

      .ant-form-item-explain,
      .ant-form-item-extra {
        color: rgba(255, 255, 255, 0.45);
      }

      .ant-form-item-has-error {
        .ant-input,
        .ant-input-affix-wrapper,
        .ant-input:hover,
        .ant-input-affix-wrapper:hover {
          background-color: var(--input-bg);
          border-color: #ff4d4f;
        }

        .ant-form-item-explain,
        .ant-form-item-split {
          color: #ff4d4f;
        }
      }
    }

    // 搜索表单
    .search-form {
      background-color: var(--card-bg) !important;
      border: 1px solid var(--border-color);
    }

    // 按钮
    .ant-btn {
      &:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-link):not([class*="bg-"]) {
        background-color: var(--hover-bg);
        border-color: var(--border-color);
        color: #1890ff;

        &:hover {
          border-color: #1890ff;
          color: #40a9ff;
        }
      }

      // 确保带有bg-*类的按钮在暗黑模式下保持正确的颜色
      &[class*="bg-"] {
        color: white !important;

        &:hover {
          opacity: 0.9;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
      }
    }

    // 表格操作按钮
    .table-actions {
      .ant-btn {
        color: var(--text-color);

        &:hover {
          color: #1890ff;
        }
      }

      .delete-btn {
        &:hover {
          color: #ff4d4f;
        }
      }
    }

    // 修复操作列单元格背景色
    .project-table .ant-table-tbody > tr > td {
      background-color: var(--card-bg) !important;
    }

    // 图标
    .anticon {
      color: var(--text-color);
    }

    // 卡片标题中的SVG图标
    .package-card {
      .card-header-wrapper {
        .header-wrapper {
          .logo-wrapper {
            .icon {
              svg {
                path {
                  fill: currentColor;
                }
              }
            }
          }
        }
      }
    }

    // 上传组件
    .ant-upload {
      &.ant-upload-select {
        background-color: var(--card-bg);
        border-color: var(--border-color);

        .ant-upload {
          color: var(--text-color);
        }

        &:hover {
          border-color: #1890ff;
        }
      }

      &.ant-upload-drag {
        background-color: var(--card-bg);
        border-color: var(--border-color);

        .ant-upload-drag-container {
          p.ant-upload-text,
          p.ant-upload-hint {
            color: var(--text-color);
          }
        }

        &:hover {
          border-color: #1890ff;
        }
      }
    }

    // 复选框
    .ant-checkbox-wrapper {
      color: var(--text-color);

      .ant-checkbox {
        .ant-checkbox-inner {
          background-color: var(--input-bg);
          border-color: var(--border-color);
        }

        &.ant-checkbox-checked {
          .ant-checkbox-inner {
            background-color: #1890ff;
            border-color: #1890ff;
          }
        }
      }
    }

    // 单选框
    .ant-radio-wrapper {
      color: var(--text-color);

      .ant-radio {
        .ant-radio-inner {
          background-color: var(--input-bg);
          border-color: var(--border-color);
        }

        &.ant-radio-checked {
          .ant-radio-inner {
            border-color: #1890ff;

            &:after {
              background-color: #1890ff;
            }
          }
        }
      }
    }

    // 开关
    .ant-switch {
      background-color: rgba(255, 255, 255, 0.2);

      &.ant-switch-checked {
        background-color: #1890ff;
      }
    }

    // 下拉菜单
    .ant-dropdown-menu {
      background-color: var(--dropdown-bg);
      border: 1px solid var(--border-color);

      .ant-dropdown-menu-item {
        color: var(--text-color);

        &:hover {
          background-color: var(--dropdown-hover);
        }
      }
    }

    // 选择器
    .ant-select {
      .ant-select-selector,
      .ant-select-selection,
      .ant-select-selection--single {
        background-color: var(--input-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color);
      }

      .ant-select-arrow,
      .ant-select-arrow-icon {
        color: var(--text-color);
      }

      &.ant-select-focused {
        .ant-select-selector,
        .ant-select-selection,
        .ant-select-selection--single {
          border-color: #1890ff !important;
        }
      }

      // 旧版选择器样式（Ant Design Vue 1.x）
      .ant-select-selection__rendered {
        color: var(--text-color);
        background-color: var(--input-bg);
      }
    }

    // 选择器下拉菜单
    .ant-select-dropdown {
      background-color: var(--dropdown-bg);
      border: 1px solid var(--border-color);

      .ant-select-item,
      .ant-select-dropdown-menu-item {
        color: var(--text-color);

        &-option-active,
        &:hover,
        &-active {
          background-color: var(--dropdown-hover);
        }
      }
    }

    // 节点选择弹窗
    .node-select-dropdown,
    .ant-dropdown-menu-node-select,
    .node-dropdown {
      background-color: var(--dropdown-bg) !important;
      border-color: var(--border-color) !important;

      .ant-dropdown-menu,
      .ant-dropdown-menu-item,
      .ant-dropdown-menu-submenu-title {
        background-color: var(--dropdown-bg) !important;
        color: var(--text-color) !important;
      }

      .ant-dropdown-menu-item:hover,
      .ant-dropdown-menu-submenu-title:hover {
        background-color: var(--dropdown-hover) !important;
      }

      .node-item,
      .node-name,
      .node-ip {
        color: var(--text-color) !important;
      }
    }

    // 语言下拉菜单
    .active-language {
      color: #1890ff !important;
    }

    // 抽屉
    .ant-drawer {
      .ant-drawer-content {
        background-color: var(--drawer-bg);

        .ant-drawer-header {
          background-color: var(--drawer-bg);
          border-color: var(--border-color);

          .ant-drawer-title {
            color: var(--text-color);
          }
        }

        .ant-drawer-body {
          background-color: var(--drawer-bg);
          color: var(--text-color);
        }
      }
    }

    // 设置抽屉 - 使用更高特异性选择器覆盖默认样式
    .settings-drawer {
      .ant-drawer-body .drawer-content {
        // 确保所有h6标题都有正确的颜色
        h6 {
          color: var(--text-color) !important;
        }

        // 特别针对第一个标题"Configurator"
        > h6:first-child {
          color: var(--text-color) !important;
        }

        // 确保所有标题都有正确的颜色，无论嵌套层级
        .sidebar-color h6,
        .sidenav-type h6,
        .navbar-fixed h6 {
          color: var(--text-color) !important;
        }

        hr {
          border-color: var(--border-color);
        }

        p {
          color: rgba(255, 255, 255, 0.65);
        }

        // 侧边栏颜色选择器
        .sidebar-color {
          h6 {
            color: var(--text-color);
          }

          .ant-radio-group {
            .ant-radio-button-wrapper {
              // 确保未选中的颜色按钮在暗黑模式下有边框
              border: 2px solid var(--border-color);

              // 为每个颜色按钮添加特定样式
              &.bg-primary {
                background-color: #1890ff !important;
                border-color: #1890ff !important;
              }

              &.bg-purple {
                background-color: #722ed1 !important;
                border-color: #722ed1 !important;
              }

              &.bg-green {
                background-color: #52c41a !important;
                border-color: #52c41a !important;
              }

              &.bg-gray {
                background-color: #595959 !important;
                border-color: #595959 !important;
              }

              &.ant-radio-button-wrapper-checked {
                border: 2px solid #ffffff !important;
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
              }
            }
          }
        }

        // 侧边栏类型选择器
        .sidenav-type {
          h6 {
            color: var(--text-color);
          }
        }

        // 固定导航栏选项
        .navbar-fixed {
          h6 {
            color: var(--text-color);
          }
        }

        .ant-radio-button-wrapper {
          background-color: var(--card-bg);
          color: var(--text-color);
          border-color: var(--border-color);

          &:not(:first-child)::before {
            background-color: var(--border-color);
          }

          &.ant-radio-button-wrapper-checked {
            color: #fff;
            border-color: #1890ff;
          }
        }

        .btn-close {
          color: var(--text-color) !important;

          svg {
            stroke: var(--text-color) !important;

            path {
              stroke: var(--text-color) !important;
              fill: none !important;
            }

            g {
              stroke: var(--text-color) !important;
            }
          }
        }

        // 确保关闭按钮在任何位置都能正确显示
        .ant-drawer-body .btn-close {
          color: var(--text-color) !important;

          svg, svg path, svg g {
            stroke: var(--text-color) !important;
          }
        }
      }
    }

    // 模态框
    .ant-modal {
      .ant-modal-content {
        background-color: var(--modal-bg);

        .ant-modal-header {
          background-color: var(--modal-bg);
          border-color: var(--border-color);

          .ant-modal-title {
            color: var(--text-color);
          }
        }

        .ant-modal-body {
          background-color: var(--modal-bg);
          color: var(--text-color);

          // 修复模态框中的分割线颜色
          .env-item, .mount-item, .info-item {
            border-bottom-color: var(--border-color);
          }

          // 修复标签和强调文本颜色
          .mount-tag {
            background-color: var(--input-bg);
            color: var(--text-color);
          }

          .info-item strong {
            color: #aaa;
          }
        }

        .ant-modal-footer {
          background-color: var(--modal-bg);
          border-color: var(--border-color);
        }
      }
    }

    // 结果页面
    .ant-result {
      background-color: var(--card-bg);
      color: var(--text-color);

      .ant-result-title,
      .ant-result-subtitle {
        color: var(--text-color);
      }

      .ant-result-icon {
        .anticon {
          color: var(--text-color);
        }
      }

      &.ant-result-info,
      &.ant-result-success,
      &.ant-result-warning,
      &.ant-result-error {
        background-color: var(--card-bg);
      }
    }

    // IP选择框
    .ip-select-modal {
      .ant-modal-content {
        background-color: var(--modal-bg);

        .ant-modal-header {
          background-color: var(--modal-bg);
          border-color: var(--border-color);

          .ant-modal-title {
            color: var(--text-color);
          }
        }

        .ant-modal-body {
          background-color: var(--modal-bg);
          color: var(--text-color);

          .ip-list {
            background-color: var(--card-bg);
            border-color: var(--border-color);

            .ip-item {
              border-bottom-color: var(--border-color);
              color: var(--text-color);

              &:hover {
                background-color: var(--hover-bg);
              }

              &.selected {
                background-color: rgba(24, 144, 255, 0.1);
              }
            }
          }
        }

        .ant-modal-footer {
          background-color: var(--modal-bg);
          border-color: var(--border-color);
        }
      }
    }

    // 确保所有选择器相关元素都应用深色模式
    .ant-select-selection-selected-value,
    .ant-select-selection__rendered,
    .ant-select-selection__placeholder {
      color: var(--text-color) !important;
    }

    // 检测IP结果列表
    .detected-ip-list {
      background-color: var(--card-bg);
      border-color: var(--border-color);

      .ip-item {
        color: var(--text-color);
        border-bottom-color: var(--border-color);

        &:hover {
          background-color: var(--hover-bg);
        }

        .ip-address {
          color: var(--text-color);
        }

        .ip-status {
          &.reachable {
            color: #52c41a;
          }

          &.unreachable {
            color: #ff4d4f;
          }
        }
      }
    }

    // 滚动条
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--bg-color);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--scrollbar-color);
      border-radius: 4px;
    }

    // ProcessInfo组件的暗黑模式样式
    .process-card {
      // 最后查看的行样式
      .last-viewed-row td {
        background-color: #2a2a2a !important;
        border-top: 1px solid #1890ff !important;
        border-bottom: 1px solid #1890ff !important;
      }

      // 为第一个单元格添加左侧边框
      .last-viewed-row td:first-child {
        border-left: 3px solid #1890ff !important;
      }

      // 为最后一个单元格添加右侧边框
      .last-viewed-row td:last-child {
        border-right: 3px solid #1890ff !important;
      }

      // AI分析模态框样式
      .process-info {
        background-color: var(--card-bg) !important;
      }

      // Markdown内容样式
      .markdown-content {
        code {
          background-color: rgba(255, 255, 255, 0.1);
        }

        pre {
          background-color: #2a2a2a;
        }
      }
    }

    // ProcessDetail组件的暗黑模式样式
    .process-details {
      background-color: var(--card-bg) !important;
      border-color: var(--border-color) !important;
      color: var(--text-color) !important;

      .process-field {
        .field-label {
          color: #ff7875 !important; // 使用更亮的粉色，以便在暗色背景下更加可见
        }

        .field-value {
          color: var(--text-color) !important;
        }
      }
    }

    // ProcessDetail组件中的其他元素
    .font-semibold {
      background-color: var(--card-bg) !important;
      color: var(--text-color) !important;
      border-color: var(--border-color) !important;
    }

    // JSON详情模态框标题样式
    .detail-modal .ant-modal-confirm-title {
      color: var(--text-color) !important;
    }

    // 树形选择器节点文字颜色
    .ant-select-tree-node-content-wrapper span {
      color: var(--text-color) !important;
    }

    // 命令行界面顶部的"n hosts selected"选择项标签
    .cli-card .card-header-wrapper .ant-select-selection__choice {
      background-color: var(--card-bg) !important;
      color: var(--text-color) !important;
      border-color: var(--border-color) !important;
    }

    // TestCaseInfo组件的模态框样式
    .ant-modal-content {
      background-color: var(--card-bg) !important;

      .ant-modal-header {
        background-color: var(--card-bg) !important;
        border-color: var(--border-color) !important;

        .ant-modal-title {
          color: var(--text-color) !important;
        }
      }

      .ant-modal-close {
        color: var(--text-color) !important;

        &:hover {
          color: var(--primary-color) !important;
        }
      }

      .ant-modal-body {
        background-color: var(--card-bg) !important;
        color: var(--text-color) !important;

        .ant-descriptions {
          background-color: var(--card-bg) !important;
          color: var(--text-color) !important;

          .ant-descriptions-item-label {
            background-color: var(--table-header-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--border-color) !important;
          }

          .ant-descriptions-item-content {
            background-color: var(--card-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--border-color) !important;

            .testcase-content {
              color: var(--text-color) !important;
              background-color: var(--input-bg) !important;
              border-left-color: #1890ff !important;
            }
          }
        }

        // 修复描述列表的表格边框颜色
        .ant-descriptions-bordered {
          .ant-descriptions-view {
            border: 1px solid var(--border-color) !important;
          }

          .ant-descriptions-item-label,
          .ant-descriptions-item-content {
            border-right: 1px solid var(--border-color) !important;
            border-bottom: 1px solid var(--border-color) !important;
          }
        }
      }
    }

    // TaskPanel组件的进度条样式
    .ant-progress-line {
      .ant-progress-outer {
        .ant-progress-inner {
          background-color: rgba(50, 50, 50, 0.8) !important; // 深灰色背景
        }
      }
      .ant-progress-bg {
        background-color: rgba(24, 144, 255, 0.7) !important; // 半透明蓝色进度条
      }
    }

    // 表格边框和分页样式
    .ant-table {
      background-color: var(--card-bg) !important;
      color: var(--text-color) !important;

      .ant-table-thead > tr > th {
        background-color: var(--table-header-bg) !important;
        color: var(--text-color) !important;
        border-color: var(--border-color) !important;
      }

      .ant-table-tbody > tr > td {
        border-color: var(--border-color) !important;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: var(--hover-bg) !important;
      }

      // 表格底部分页区域
      .ant-table-footer {
        background-color: var(--card-bg) !important;
        border-color: var(--border-color) !important;
      }

      // 表格分页器
      .ant-pagination {
        background-color: var(--card-bg) !important;
        border-top: 1px solid var(--border-color) !important;

        .ant-pagination-item {
          background-color: var(--card-bg) !important;
          border-color: var(--border-color) !important;

          a {
            color: var(--text-color) !important;
          }

          &.ant-pagination-item-active {
            border-color: var(--primary-color) !important;

            a {
              color: var(--primary-color) !important;
            }
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next,
        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
          color: var(--text-color) !important;

          .ant-pagination-item-link {
            background-color: var(--card-bg) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
          }
        }

        // 分页跳转输入框样式
        .ant-pagination-options-quick-jumper {
          color: var(--text-color) !important;

          input {
            background-color: var(--input-bg) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;

            &:hover, &:focus {
              border-color: #1890ff !important;
            }
          }
        }
      }
    }

    // 特别针对TaskPanel中的表格
    .task-card {
      .ant-table-wrapper {
        border-color: var(--border-color) !important;

        .ant-table {
          border-color: var(--border-color) !important;

          .ant-table-content {
            border-color: var(--border-color) !important;
          }

          .ant-table-body {
            border-color: var(--border-color) !important;
          }

          .ant-table-placeholder {
            background-color: var(--card-bg) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
          }
        }
      }
    }

    // ProjectManager.vue 表格样式已被通用样式覆盖，保留按钮样式
    .ant-card {
      .ant-table {
        // 修复操作列的按钮样式
        .ant-space {
          .ant-btn-danger {
            background-color: #2a2a2a !important; // 深色背景
            border-color: #ff4d4f !important; // 红色边框
            color: #ff4d4f !important; // 红色文字

            &:hover, &:focus {
              background-color: #3a3a3a !important; // 悬停时稍亮的背景
              border-color: #ff7875 !important; // 悬停时稍亮的边框
              color: #ff7875 !important; // 悬停时稍亮的文字
            }
          }
        }
      }
    }

    // 修改删除选中节点按钮的样式
    .ant-btn-danger {
      background-color: #2a2a2a !important; // 深色背景
      border-color: #ff4d4f !important; // 红色边框
      color: #ff4d4f !important; // 红色文字

      &:hover, &:focus {
        background-color: #3a3a3a !important; // 悬停时稍亮的背景
        border-color: #ff7875 !important; // 悬停时稍亮的边框
        color: #ff7875 !important; // 悬停时稍亮的文字
      }

      &:active {
        background-color: #303030 !important; // 点击时的背景
        border-color: #ff7875 !important;
        color: #ff7875 !important;
      }

      &[disabled], &[disabled]:hover {
        background-color: #2a2a2a !important;
        border-color: #5f5f5f !important;
        color: #5f5f5f !important;
      }
    }

    // 修复所有页面中的虚线分割线颜色
    .info-item {
      border-bottom-color: var(--border-color) !important;

      strong {
        color: #aaa !important;
      }
    }

    // 修复标签页下面的线是白色的问题 - 更精确的选择器
    .ant-tabs {
      .ant-tabs-bar {
        border-bottom-color: var(--border-color) !important;
      }

      .ant-tabs-nav {
        border-bottom-color: var(--border-color) !important;

        &::before {
          border-bottom-color: var(--border-color) !important;
        }

        .ant-tabs-tab {
          color: var(--text-color) !important;
          border-color: var(--border-color) !important;

          &.ant-tabs-tab-active {
            color: var(--primary-color) !important;
          }
        }

        .ant-tabs-ink-bar {
          background-color: #1890ff !important; /* 使用明亮的蓝色，确保在暗黑模式下清晰可见 */
          height: 3px !important; /* 增加高度，使下划线更加明显 */
        }
      }

      .ant-tabs-content {
        border-color: var(--border-color) !important;
      }
    }
  }

// 确认框样式 - 修复暗黑模式下文字看不清的问题
.dark-mode {
  // 确认框样式
  .ant-modal-confirm {
    .ant-modal-content {
      background-color: var(--modal-bg) !important;

      .ant-modal-body {
        background-color: var(--modal-bg) !important;

        .ant-modal-confirm-body-wrapper {
          .ant-modal-confirm-body {
            .ant-modal-confirm-title {
              color: var(--text-color) !important;
            }

            .ant-modal-confirm-content {
              color: var(--text-color) !important;

              // 提示文字
              div {
                color: var(--text-color) !important;
              }

              // 项目创建提示文字
              .project-hint-text {
                color: var(--text-color) !important;
              }
            }
          }

          .ant-modal-confirm-btns {
            .ant-btn {
              border: 1px solid var(--border-color) !important;
            }
          }
        }
      }
    }
  }
}