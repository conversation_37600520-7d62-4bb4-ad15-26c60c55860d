from sqlalchemy import Column, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class HardwareSnapshot(Base):
    __tablename__ = 'hardware_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'), nullable=False)
    device_info = Column(String, nullable=False)
    device_type = Column(String, nullable=False)

    host = relationship("HostConfig", back_populates="hardware_snapshot")

    def to_dict(self):
        return {
            'node_id': self.node_id,
            'device_info': self.device_info,
            'device_type': self.device_type,
        }
