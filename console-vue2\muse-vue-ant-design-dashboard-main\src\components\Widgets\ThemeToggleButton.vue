<template>
  <a-tooltip :title="isDarkMode ? $t('common.lightMode') : $t('common.darkMode')">
    <a class="theme-toggle-button" @click="toggleTheme">
      <svg v-if="isDarkMode" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- 太阳图标 -->
        <path d="M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z" fill="#111827"/>
        <path d="M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22" stroke="#111827" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- 月亮图标 -->
        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" fill="#111827" stroke="#111827" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </a>
  </a-tooltip>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  name: 'ThemeToggleButton',
  computed: {
    ...mapState(['darkMode']),
    isDarkMode() {
      return this.darkMode;
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode']),
    toggleTheme() {
      this.toggleDarkMode();
    }
  }
}
</script>

<style scoped>
.theme-toggle-button {
  display: inline-block;
  cursor: pointer;
  padding: 4px;
  transition: transform 0.3s ease;
}

.theme-toggle-button:hover {
  transform: rotate(30deg);
}
</style>
