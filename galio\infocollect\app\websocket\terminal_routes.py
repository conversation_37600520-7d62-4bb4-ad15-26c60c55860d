import json
from log.logger import log_debug, log_info, log_warning, log_error
from flask import request
from app.websocket.terminal_handler import TerminalConnection
from app.websocket.socketio_instance import socketio


terminals = {}


def register_terminal_handlers():
    @socketio.on('connect')
    def handle_connect(auth):
        try:
            sid = request.sid
            terminal = TerminalConnection(sid)
            terminals[sid] = terminal
            log_info(f"Terminal connection established - Session ID: {sid}")

            # socketio.start_background_task(terminal.handle_connection)
            # terminal._emit_message('system', f'Terminal connection established {terminal.available_nodes}')
        except Exception as e:
            log_error(f"Connection error: {str(e)}")
            socketio.emit('message', {
                'type': 'error',
                'message': f'Connection error: {str(e)}'
            }, room=sid)

    @socketio.on('message')
    def handle_message(message):
        try:
            sid = request.sid
            terminal = terminals.get(sid)
            if not terminal:
                log_warning(f"Terminal session not found - Session ID: {sid}")
                socketio.emit('message', {
                    'type': 'error',
                    'message': 'Terminal session not found'
                }, room=sid)
                return

            log_debug(f"Received message from {sid}: {message}")
            data = json.loads(message)
            ip = data.get('data', {}).get('ip')
            aibash = data.get('data', {}).get('aibash', True)
            command = data.get('data', {}).get('command')
            project = data.get('data', {}).get('project')
            
            terminal.project_db = project
            
            if terminal.available_nodes is None:
                terminal.init_nodes()

            # 更新选中的IP列表
            if ip and ip != '*':
                terminal.pending_ips = [ip.strip() for ip in ip.split(',')]
            else:
                terminal.pending_ips = []

            if data.get('event') == 'input':
                if aibash:
                    if terminal.pending_status:
                        terminal.pending_status = False
                        if command.lower() == "y":
                            terminal.batch_exec_command()
                        else:
                            terminal._emit_message('output', f"取消执行\n")
                    else:
                        success, generate_command = terminal.generate_aibash_cmd(command)
                        if not success:
                            return
                        terminal.pending_status = True
                        terminal._emit_message('output',
                                               f"是否执行: {generate_command}\n 确认请输入y，放弃输入n =>")
                # elif ip == '*':
                #     terminal.send_command(command)
                else:
                    if terminal.check_ip_in_database(ip):
                        terminal.send_command_by_ip(ip, command)
                    else:
                        socketio.emit('message', {
                            'type': 'error',
                            'message': f'ip {ip} session not found'
                        }, room=sid)
        except Exception as e:
            socketio.emit('message', {
                'type': 'error',
                'message': str(e)
            }, room=sid)

    @socketio.on('disconnect')
    def handle_disconnect():
        sid = request.sid
        if sid in terminals:
            terminals[sid].destory()
            del terminals[sid]
            log_info(f"Terminal disconnected - Session ID: {sid}")
