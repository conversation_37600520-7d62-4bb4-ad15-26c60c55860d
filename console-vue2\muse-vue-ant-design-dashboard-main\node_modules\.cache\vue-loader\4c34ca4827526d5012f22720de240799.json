{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue", "mtime": 1751522841423}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["JsonDetailModal.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JsonDetailModal.vue", "sourceRoot": "src/components/Widgets", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 组件不直接渲染模态框，而是提供方法 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport VueJsonPretty from 'vue-json-pretty';\r\n\r\nexport default {\r\n  name: 'JsonDetailModal',\r\n  methods: {\r\n    /**\r\n     * 显示JSON详情模态框\r\n     * @param {string} title 模态框标题\r\n     * @param {object|string} data 要显示的数据\r\n     * @param {object} options 配置选项\r\n     */\r\n    showDetailModal(title, data, options = {}) {\r\n      // 计算响应式尺寸\r\n      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);\r\n      const contentHeight = Math.min(700, window.innerHeight * 0.6);\r\n\r\n      // 生成唯一ID\r\n      const ids = {\r\n        search: `search-${Date.now()}`,\r\n        counter: `counter-${Date.now()}`,\r\n        theme: `theme-${Date.now()}`\r\n      };\r\n\r\n      // 默认使用暗色主题\r\n      let isDarkTheme = true;\r\n\r\n      // 创建标题、搜索框和复制按钮\r\n      const header = (\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <a-icon type=\"code\" style=\"margin-right: 8px; font-size: 16px;\" />\r\n            <span style=\"font-weight: 500;\">{title}</span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <div id={ids.counter} style=\"margin-right: 10px; min-width: 60px; text-align: right; color: #666;\"></div>\r\n            <a-input\r\n              id={ids.search}\r\n              placeholder=\"搜索 (Enter: ↓  Shift+Enter: ↑)\"\r\n              allowClear\r\n              prefix={<a-icon type=\"search\" style=\"color: rgba(0,0,0,.25)\" />}\r\n              style=\"width: 250px;\"\r\n            />\r\n            <a-button\r\n              id={ids.theme}\r\n              type=\"link\"\r\n              icon=\"bg-colors\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"切换主题\"\r\n            />\r\n            <a-button\r\n              id=\"copy-btn\"\r\n              type=\"link\"\r\n              icon=\"copy\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"复制内容\"\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n      // 准备内容元素\r\n      const contentElement = typeof data === 'object' ? (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class=\"json-container theme-dark\" id=\"json-container\">\r\n        </div>\r\n      ) : (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>\r\n          {String(data)}\r\n        </div>\r\n      );\r\n\r\n      // 创建模态框\r\n      this.$root.$confirm({\r\n        title: header,\r\n        content: contentElement,\r\n        width: modalWidth,\r\n        okText: options.okText || '关闭',\r\n        icon: null,\r\n        cancelButtonProps: { style: { display: 'none' } },\r\n        class: 'detail-modal',\r\n        maskClosable: false, // 防止点击外部关闭\r\n        getContainer: () => document.body.appendChild(document.createElement('div'))\r\n      });\r\n\r\n      // 在模态框内容区域渲染VueJsonPretty组件\r\n      setTimeout(() => {\r\n        if (typeof data === 'object') {\r\n          // 查找JSON容器\r\n          const container = document.getElementById('json-container');\r\n          if (container) {\r\n            // 创建VueJsonPretty组件实例\r\n            const JsonViewer = new Vue({\r\n              render: h => h(VueJsonPretty, {\r\n                props: {\r\n                  data: data,\r\n                  deep: Infinity, // 设置为Infinity以默认展开所有节点\r\n                  showDoubleQuotes: true,\r\n                  showLength: true,\r\n                  showLineNumbers: true,  // 添加行号显示\r\n                },\r\n                style: {\r\n                  height: '100%',\r\n                  overflow: 'auto'\r\n                }\r\n              })\r\n            });\r\n\r\n            // 挂载组件\r\n            JsonViewer.$mount();\r\n            container.appendChild(JsonViewer.$el);\r\n          }\r\n        }\r\n\r\n        // 获取搜索相关元素\r\n        const searchInput = document.getElementById(ids.search);\r\n        const counterElement = document.getElementById(ids.counter);\r\n\r\n        // 搜索功能变量\r\n        let matches = [];\r\n        let currentMatchIndex = -1;\r\n\r\n        // 如果有搜索框，添加搜索功能\r\n        if (searchInput && counterElement) {\r\n          // 高亮匹配项函数\r\n          const highlightMatches = (searchTerm) => {\r\n            // 重置匹配\r\n            matches = [];\r\n            currentMatchIndex = -1;\r\n\r\n            // 清除计数器\r\n            counterElement.textContent = '';\r\n\r\n            if (!searchTerm) return;\r\n\r\n            try {\r\n              // 查找所有键和值节点\r\n              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');\r\n\r\n              // 创建正则表达式\r\n              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\r\n\r\n              // 移除之前的高亮\r\n              document.querySelectorAll('.vjs-search-match').forEach(el => {\r\n                el.classList.remove('vjs-search-match');\r\n              });\r\n\r\n              document.querySelectorAll('.vjs-search-current').forEach(el => {\r\n                el.classList.remove('vjs-search-current');\r\n              });\r\n\r\n              // 查找匹配项\r\n              jsonNodes.forEach(node => {\r\n                const text = node.textContent;\r\n                let match;\r\n                regex.lastIndex = 0;\r\n\r\n                while ((match = regex.exec(text)) !== null) {\r\n                  matches.push({\r\n                    node: node,\r\n                    text: match[0]\r\n                  });\r\n\r\n                  // 防止无限循环\r\n                  if (match.index === regex.lastIndex) {\r\n                    regex.lastIndex++;\r\n                  }\r\n                }\r\n              });\r\n\r\n              // 更新计数器\r\n              if (matches.length === 0) {\r\n                counterElement.textContent = '无匹配项';\r\n                return;\r\n              }\r\n\r\n              counterElement.textContent = `0/${matches.length}`;\r\n\r\n              // 为所有匹配项添加高亮类\r\n              matches.forEach(match => {\r\n                match.node.classList.add('vjs-search-match');\r\n              });\r\n            } catch (error) {\r\n              console.error('搜索错误:', error);\r\n            }\r\n          };\r\n\r\n          // 导航到特定匹配项\r\n          const navigateToMatch = (index) => {\r\n            if (matches.length === 0) return;\r\n\r\n            // 确保索引在有效范围内\r\n            index = Math.max(0, Math.min(matches.length - 1, index));\r\n\r\n            // 移除之前匹配项的当前高亮\r\n            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {\r\n              matches[currentMatchIndex].node.classList.remove('vjs-search-current');\r\n            }\r\n\r\n            // 更新当前索引和计数器\r\n            currentMatchIndex = index;\r\n            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;\r\n\r\n            // 高亮当前匹配项\r\n            const currentMatch = matches[currentMatchIndex];\r\n            if (currentMatch) {\r\n              currentMatch.node.classList.add('vjs-search-current');\r\n\r\n              // 确保父节点是展开的\r\n              let parent = currentMatch.node.parentElement;\r\n              while (parent) {\r\n                if (parent.classList && parent.classList.contains('vjs-tree-node')) {\r\n                  // 如果节点是折叠的，点击展开按钮\r\n                  if (!parent.classList.contains('is-expanded')) {\r\n                    const expandBtn = parent.querySelector('.vjs-tree-brackets');\r\n                    if (expandBtn) expandBtn.click();\r\n                  }\r\n                }\r\n                parent = parent.parentElement;\r\n              }\r\n\r\n              // 滚动到匹配项\r\n              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          };\r\n\r\n          // 添加搜索输入事件\r\n          let searchTimeout;\r\n          searchInput.addEventListener('input', (e) => {\r\n            // 使用防抖优化性能\r\n            if (searchTimeout) clearTimeout(searchTimeout);\r\n            searchTimeout = setTimeout(() => {\r\n              highlightMatches(e.target.value.trim());\r\n            }, 300);\r\n          });\r\n\r\n          // 添加键盘导航事件\r\n          searchInput.addEventListener('keydown', (e) => {\r\n            if (e.key === 'Enter') {\r\n              e.preventDefault();\r\n              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加主题切换功能\r\n        const themeButton = document.getElementById(ids.theme);\r\n        if (themeButton) {\r\n          themeButton.addEventListener('click', () => {\r\n            const container = document.querySelector('.json-container');\r\n            if (container) {\r\n              // 切换主题类\r\n              if (container.classList.contains('theme-light')) {\r\n                container.classList.remove('theme-light');\r\n                container.classList.add('theme-dark');\r\n                container.style.backgroundColor = '#1e1e1e';\r\n                isDarkTheme = true;\r\n              } else {\r\n                container.classList.remove('theme-dark');\r\n                container.classList.add('theme-light');\r\n                container.style.backgroundColor = '#fff';\r\n                isDarkTheme = false;\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加复制功能\r\n        const copyButton = document.getElementById('copy-btn');\r\n        if (copyButton) {\r\n          copyButton.addEventListener('click', () => {\r\n            try {\r\n              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);\r\n\r\n              // 使用现代Clipboard API\r\n              if (navigator.clipboard && window.isSecureContext) {\r\n                navigator.clipboard.writeText(textToCopy)\r\n                  .then(() => {\r\n                    this.$message.success(this.$t('common.copiedToClipboard'));\r\n                  })\r\n                  .catch(err => {\r\n                    console.error('复制失败:', err);\r\n                    this.$message.error('复制失败');\r\n                  });\r\n              } else {\r\n                // 备用方法\r\n                const textArea = document.createElement('textarea');\r\n                textArea.value = textToCopy;\r\n                document.body.appendChild(textArea);\r\n                textArea.select();\r\n                const successful = document.execCommand('copy');\r\n                document.body.removeChild(textArea);\r\n\r\n                if (successful) {\r\n                  this.$message.success(this.$t('common.copiedToClipboard'));\r\n                } else {\r\n                  this.$message.error(this.$t('common.copyFailed'));\r\n                }\r\n              }\r\n            } catch (err) {\r\n              this.$message.error(this.$t('common.copyFailed'));\r\n            }\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 覆盖详情弹窗的样式 */\r\n.detail-modal {\r\n  .ant-modal-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-content {\r\n    margin-top: 12px !important;\r\n    margin-bottom: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-btns {\r\n    margin-top: 16px !important;\r\n    margin-bottom: 8px !important;\r\n  }\r\n\r\n  /* 复制按钮样式 */\r\n  #copy-btn {\r\n    transition: all 0.3s;\r\n\r\n    &:hover {\r\n      color: #40a9ff !important;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    &:active {\r\n      color: #096dd9 !important;\r\n    }\r\n  }\r\n\r\n  /* 搜索匹配项样式 */\r\n  .vjs-search-match {\r\n    background-color: rgba(255, 255, 0, 0.3);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .vjs-search-current {\r\n    background-color: rgba(255, 165, 0, 0.6);\r\n    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);\r\n  }\r\n\r\n  /* 主题样式 */\r\n  .json-container.theme-dark {\r\n    .vjs-tree {\r\n      background-color: #1e1e1e !important;\r\n      color: #d4d4d4 !important;\r\n\r\n      .vjs-key {\r\n        color: #9cdcfe !important; /* 浅蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-string {\r\n        color: #ce9178 !important; /* 橙红色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-number {\r\n        color: #b5cea8 !important; /* 浅绿色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-boolean {\r\n        color: #569cd6 !important; /* 蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-null {\r\n        color: #c586c0 !important; /* 紫色 */\r\n      }\r\n\r\n      .vjs-tree-brackets {\r\n        color: #d4d4d4 !important; /* 浅灰色 */\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 调整内容区域的左侧边距 */\r\n.ant-modal-confirm-body {\r\n  padding-left: 12px !important;\r\n}\r\n\r\n/* 调整内容区域的图标和文本间距 */\r\n.ant-modal-confirm-body > .anticon {\r\n  display: none !important;\r\n}\r\n\r\n/* 调整标题和内容的左侧边距 */\r\n.ant-modal-confirm-body > .ant-modal-confirm-title,\r\n.ant-modal-confirm-body > .ant-modal-confirm-content {\r\n  margin-left: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n/* 调整内容区域的下边距 */\r\n.detail-modal .ant-modal-confirm-content {\r\n  margin-bottom: 8px !important; /* 适当的下边距 */\r\n}\r\n\r\n/* 调整底部按钮区域的上下边距 */\r\n.detail-modal .ant-modal-confirm-btns {\r\n  margin-top: 8px !important; /* 减小上边距 */\r\n  margin-bottom: 4px !important;\r\n  padding-top: 0 !important; /* 移除上内边距 */\r\n  padding-bottom: 0 !important;\r\n  border-top: none !important; /* 移除分隔线 */\r\n}\r\n\r\n/* 调整底部按钮本身的样式 */\r\n.detail-modal .ant-modal-confirm-btns button {\r\n  margin-top: 0 !important;\r\n  margin-bottom: 0 !important;\r\n  padding: 6px 16px !important;\r\n  height: auto !important;\r\n}\r\n</style>\r\n"]}]}