from flask import Blueprint, jsonify
from log.logger import log_error
from services.crictl_service import CrictlService
from routes.database_wraps import with_database_session

bp = Blueprint('crictl', __name__)

resource_mapping = {
    "crictl_host_config": "get_crictl_host_config",
    "crictl_container": "get_crictl_containers",
    "crictl_pod": "get_crictl_pods",
}


@bp.route('/<resource_type>/<string:node_ip>', methods=['GET'])
@with_database_session
def get_crictl_resource(resource_type, node_ip, db):
    crictl_service = CrictlService(db)
    node = crictl_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    method_name = resource_mapping.get(resource_type)
    if not method_name:
        return jsonify({"error": "Resource type not supported"}), 400

    method = getattr(crictl_service, method_name, None)
    if not method:
        return jsonify({"error": "Service method not found"}), 500

    try:
        result = method(node.id)
        return jsonify(result if result is not None else []), 200
    except Exception as e:
        log_error(f"Error processing request: {e}")
        return jsonify({"error": f"Internal server error: {e}"}), 500
