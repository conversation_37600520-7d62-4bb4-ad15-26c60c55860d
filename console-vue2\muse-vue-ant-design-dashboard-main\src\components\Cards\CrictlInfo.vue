<template>
  <a-card
    :bordered="false"
    class="header-solid h-full crictl-card"
    :bodyStyle="{ padding: 0 }"
  >
    <!-- 引用JsonDetailModal组件 -->
    <JsonDetailModal ref="jsonDetailModal" />
    <div class="crictl-info-container">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <!-- Host Config Tab -->
        <a-tab-pane key="crictl_host_config" tab="Host Config">
          <div v-if="activeTab === 'crictl_host_config'" class="host-config-container">
            <a-row :gutter="[16, 16]">
              <a-col :span="8" v-for="card in hostConfigCards" :key="card.title">
                <a-card :title="card.title" :bordered="false" class="host-config-card">
                  <p v-for="(item, index) in card.items" :key="index" class="info-item">
                    <strong>{{ item.label }}:</strong>
                    {{ item.getValue(hostConfig) }}
                  </p>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- Pods Tab -->
        <a-tab-pane key="crictl_pod" tab="Pods">
          <a-table
            :columns="podColumns"
            :data-source="podData"
            :rowKey="record => record.pod_id"
            :loading="loadingPods"
            :pagination="{ pageSize: 30 }"
            :scroll="{ x: 1500 }"
          >
          </a-table>
        </a-tab-pane>

        <!-- Containers Tab -->
        <a-tab-pane key="crictl_container" tab="Containers">
          <a-table
            :columns="containerColumns"
            :data-source="containerData"
            :rowKey="record => record.container_id"
            :loading="loadingContainers"
            :pagination="{ pageSize: 20 }"
            :scroll="{ x: 1800 }"
          >
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>

    <network-listening-modal
      :visible="networkDetailsVisible"
      :network-data="selectedNetwork"
      @update:visible="networkDetailsVisible = $event"
      @close="handleNetworkDetailsClose"
    />

    <a-modal
      v-model:visible="environmentDetailsVisible"
      title="Environment Variables"
      width="800px"
      @cancel="handleEnvironmentDetailsClose"
    >
      <template v-slot:footer>
        <a-button @click="handleEnvironmentDetailsClose">Close</a-button>
      </template>
      <template v-if="selectedEnvironment">
        <div class="env-container">
          <div v-for="(env, index) in getAllEnvironmentVars()" :key="index" class="env-item">
            {{ env }}
          </div>
        </div>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="mountDetailsVisible"
      title="Mount Details"
      width="800px"
      @cancel="handleMountDetailsClose"
    >
      <template v-slot:footer>
        <a-button @click="handleMountDetailsClose">Cancel</a-button>
      </template>
      <template v-if="selectedMountContainer">
        <div class="mounts-container">
          <div v-for="(mount, index) in getAllMounts()" :key="index" class="mount-item">
            <div class="mount-path">
              <span class="mount-source">{{ mount.host_path }}</span>
              <span class="mount-arrow">→</span>
              <span class="mount-dest">{{ mount.container_path }}</span>
            </div>
            <div class="mount-details">
              <span class="mount-tag">{{ mount.readonly ? 'RO' : 'RW' }}</span>
              <span v-if="mount.propagation" class="mount-tag">
                {{ mount.propagation.replace('PROPAGATION_', '') }}
              </span>
              <span v-if="mount.selinux_relabel" class="mount-tag">SELinux Relabel</span>
            </div>
          </div>
        </div>
      </template>
    </a-modal>

    <process-table
      :visible="processDetailsVisible"
      :process-container="selectedProcessContainer"
      user-field="user"
      :include-tty="false"
      @update:visible="processDetailsVisible = $event"
      @close="handleProcessDetailsClose"
    />

    <process-detail-modal
      :visible="processDetailInfoVisible"
      :process-info="selectedProcessInfo"
      user-field="user"
      @update:visible="processDetailInfoVisible = $event"
      @close="handleProcessDetailInfoClose"
    />
  </a-card>
</template>

<script>
import { mapState } from 'vuex'
import axios from '@/api/axiosInstance'
import JsonDetailModal from '../Widgets/JsonDetailModal.vue'
import { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index'
import { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index'
import { CriMountCell } from '../Widgets/Mount/mount_index'
// 不再使用单独的环境变量组件
// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index'

export default {
  components: {
    JsonDetailModal,
    ProcessDetailModal,
    ProcessTable,
    NetworkListeningModal,
    NetworkListeningCell,
    CriMountCell,
    // EnvironmentCell,
    // EnvironmentModal
  },
  name: 'CrictlInfo',
  props: {
    nodeIp: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // Host Config related data
      hostConfigCards: [
        {
          title: 'Version Information',
          items: [
            {
              label: 'Version',
              getValue: (config) => {
                if (!config.version_info) return 'N/A'
                const match = config.version_info.match(/Version:\s*(.+)/)
                return match ? match[1].trim() : 'N/A'
              }
            },
            {
              label: 'Runtime Name',
              getValue: (config) => {
                if (!config.version_info) return 'N/A'
                const match = config.version_info.match(/RuntimeName:\s*(.+)/)
                return match ? match[1].trim() : 'N/A'
              }
            },
            {
              label: 'Runtime Version',
              getValue: (config) => {
                if (!config.version_info) return 'N/A'
                const match = config.version_info.match(/RuntimeVersion:\s*(.+)/)
                return match ? match[1].trim() : 'N/A'
              }
            },
            {
              label: 'Runtime API Version',
              getValue: (config) => {
                if (!config.version_info) return 'N/A'
                const match = config.version_info.match(/RuntimeApiVersion:\s*(.+)/)
                return match ? match[1].trim() : 'N/A'
              }
            }
          ]
        },
        {
          title: 'Runtime Status',
          items: [
            {
              label: 'Runtime Ready',
              getValue: (config) => {
                const condition = config.runtime_info?.status?.conditions?.find(c => c.type === 'RuntimeReady')
                return condition ? `${condition.status ? 'Ready' : 'Not Ready'}` : 'N/A'
              }
            },
            {
              label: 'Network Ready',
              getValue: (config) => {
                const condition = config.runtime_info?.status?.conditions?.find(c => c.type === 'NetworkReady')
                return condition ? `${condition.status ? 'Ready' : 'Not Ready'}` : 'N/A'
              }
            },
            {
              label: 'Sandbox Image',
              getValue: (config) => config.runtime_info?.config?.sandboxImage || 'N/A'
            }
          ]
        },
        {
          title: 'Security',
          items: [
            {
              label: 'Root User',
              getValue: (config) => config.is_root_user ? 'Yes' : 'No'
            }
          ]
        }
      ],
      hostConfig: {},
      podData: [],
      containerData: [],
      loadingHostConfig: false,
      loadingPods: false,
      loadingContainers: false,
      activeTab: 'crictl_host_config',

      // pods
      podColumns: [
        { title: 'Pod ID', dataIndex: 'pod_id', key: 'pod_id', width: '120px', ellipsis: true },
        {
          title: 'Name',
          dataIndex: ['metadata', 'name'],
          key: 'name',
          width: '160px',
          ellipsis: true
        },
        {
          title: 'Namespace',
          dataIndex: ['metadata', 'namespace'],
          key: 'namespace',
          width: '80px'
        },
        {
          title: 'State',
          dataIndex: 'state',
          key: 'state',
          width: '120px'
        },
        {
          title: 'Network',
          dataIndex: 'network',
          key: 'network',
          width: '180px',
          customRender: (_, record) => {
            const networkData = record.network;
            if (!networkData || Object.keys(networkData).length === 0) {
              return 'N/A';
            }
            const ips = [];
            if (networkData.ip) {
              ips.push(networkData.ip);
            }
            if (networkData.additionalIps && networkData.additionalIps.length > 0) {
              ips.push(...networkData.additionalIps);
            }
            return ips.length > 0 ? ips.join(',  ') : 'N/A';
          }
        },
      ],

      // Containers
      containerColumns: [
        {
          title: 'Container ID',
          dataIndex: 'container_id',
          key: 'container_id',
          width: '140px',
          ellipsis: false,
        },
        {
          title: 'Name',
          dataIndex: 'inspect_data',
          key: 'name',
          width: '15%',
          ellipsis: false,
          customRender: (_, record) => {
            try {
              const inspectData = typeof record.inspect_data === 'string'
                ? JSON.parse(record.inspect_data)
                : record.inspect_data;
              return {
                children: inspectData?.status?.metadata?.name || 'N/A',
                props: {
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'break-word'
                  }
                }
              };
            } catch (e) {
              console.warn('Failed to render container name:', e);
              return 'N/A';
            }
          }
        },

        {
          title: 'Environment',
          dataIndex: 'env',
          key: 'env',
          width: '200px',
          customRender: (_, record) => {
            try {
              const envVars = typeof record.env === 'string' ? JSON.parse(record.env) : record.env || [];
              if (!envVars.length) return 'N/A';

              return (
                <div style="font-size: 12px;">
                  {envVars.slice(0, 1).map((env, index) => (
                    <div key={index} style="padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {env.length > 200 ? env.substring(0, 200) + '...' : env}
                    </div>
                  ))}
                  {envVars.length > 1 && (
                    <a-button
                      type="link"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        this.showEnvironmentDetails(record);
                      }}
                    >
                      +{envVars.length - 1} more...
                    </a-button>
                  )}
                </div>
              );
            } catch (e) {
              console.warn('Failed to render env vars:', e);
              return 'None';
            }
          }
        },
        {
          title: 'Mounts',
          dataIndex: 'mounts',
          key: 'mounts',
          width: '25%',
          ellipsis: false,
          customRender: (_, record) => {
            return (
              <cri-mount-cell
                record={record}
                onShow-details={this.showMountDetails}
              />
            );
          }
        },
        {
          title: 'Network Listening',
          dataIndex: 'exposures',
          key: 'exposures',
          width: '25%',
          ellipsis: false,
          customRender: (_, record) => {
            return (
              <network-listening-cell
                record={record}
                onShow-details={this.showNetworkDetails}
              />
            );
          }
        },
        {
          title: 'Processes',
          dataIndex: 'processes',
          key: 'processes',
          width: '120px',
          align: 'center',
          customRender: (_, record) => {
            if (!record || !record.processes) return 'N/A';
            const processes = typeof record.processes === 'string' ? JSON.parse(record.processes) : record.processes;

            const processCount = processes.pid ? processes.pid.length : 0;

            return (
              <a-button
                type="link"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  this.showProcessDetails(record);
                }}
              >
                {processCount} {processCount === 1 ? 'process' : 'processes'}
              </a-button>
            );
          }
        },
        {
          title: 'Inspect Data',
          dataIndex: 'inspect_data',
          key: 'inspect_data',
          width: '150px',
          align: 'center',
          customRender: (_, record) => (
            <span style="display: inline-block; line-height: 22px;">
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.showInspectDetails(record);
                }}
                style="color: #1890ff; cursor: pointer;"
              >
                View Inspect
              </a>
            </span>
          )
        }
      ],

      // 添加新的数据
      networkDetailsVisible: false,
      selectedNetwork: null,

      // 添加新的数据
      mountDetailsVisible: false,
      selectedMountContainer: null,

      // 添加环境变量相关数据
      environmentDetailsVisible: false,
      selectedEnvironment: null,

      // 添加新的数据
      processDetailsVisible: false,
      selectedProcessContainer: null,
      processDetailInfoVisible: false,
      selectedProcessInfo: null,


    }
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject'])
  },
  watch: {
    selectedNodeIp(newVal) {
      if (newVal) {
        this.fetchActiveTabData()
      }
    }
  },
  created() {
    if (this.selectedNodeIp) {
      this.fetchActiveTabData()
    }
  },
  methods: {
    async fetchActiveTabData() {
      if (!this.selectedNodeIp || !this.currentProject) return

      const loadingKey = `loading${this.activeTab.replace('crictl_', '').charAt(0).toUpperCase() + this.activeTab.slice(7)}`
      this[loadingKey] = true

      try {
        const response = await axios.get(`/api/crictl/${this.activeTab}/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject
          }
        })

        const dataKey = `${this.activeTab.replace('crictl_', '')}Data`
        if (this.activeTab === 'crictl_host_config') {
          this.hostConfig = response.data
        } else {
          this[dataKey] = response.data
        }
      } catch (error) {
        console.error(`Error fetching ${this.activeTab}:`, error)
      } finally {
        this[loadingKey] = false
      }
    },
    handleTabChange(key) {
      this.activeTab = key
      this.fetchActiveTabData()
    },
    showNetworkDetails(container) {
      this.selectedNetwork = {
        ...container,
        exposures: typeof container.exposures === 'string'
          ? JSON.parse(container.exposures)
          : container.exposures
      };
      this.networkDetailsVisible = true;
    },
    handleNetworkDetailsClose() {
      this.networkDetailsVisible = false;
      this.selectedNetwork = null;
    },

    showMountDetails(container) {
      this.selectedMountContainer = {
        ...container,
        mounts: typeof container.mounts === 'string'
          ? JSON.parse(container.mounts)
          : container.mounts
      };
      this.mountDetailsVisible = true;
    },
    handleMountDetailsClose() {
      this.mountDetailsVisible = false;
      this.selectedMountContainer = null;
    },
    getAllMounts() {
      if (!this.selectedMountContainer?.mounts) return [];
      return this.selectedMountContainer.mounts;
    },
    showEnvironmentDetails(container) {
      this.selectedEnvironment = {
        ...container,
        env: typeof container.env === 'string'
          ? JSON.parse(container.env)
          : container.env || []
      };
      this.environmentDetailsVisible = true;
    },
    handleEnvironmentDetailsClose() {
      this.environmentDetailsVisible = false;
      this.selectedEnvironment = null;
    },
    getAllEnvironmentVars() {
      if (!this.selectedEnvironment?.env) return [];
      return this.selectedEnvironment.env;
    },
    showProcessDetails(container) {
      this.selectedProcessContainer = container;
      this.processDetailsVisible = true;
    },
    handleProcessDetailsClose() {
      this.processDetailsVisible = false;
      this.selectedProcessContainer = null;
    },
    getProcessList() {
      if (!this.selectedProcessContainer?.processes) return [];

      // 检查是否有新格式的进程列表
      const processes = this.selectedProcessContainer.processes;
      if (processes.process_list && processes.process_list.length > 0) {
        return processes.process_list;
      }

      // 兼容旧格式
      return processes.pid.map((_, index) => ({
        pid: processes.pid[index],
        ppid: processes.ppid[index],
        user: processes.user[index],
        cmd: processes.cmd[index],
        time: processes.time[index],
        stime: processes.stime[index]
      }));
    },
    showProcessDetailInfo(process) {
      this.selectedProcessInfo = process;
      this.processDetailInfoVisible = true;
    },
    handleProcessDetailInfoClose() {
      this.processDetailInfoVisible = false;
      this.selectedProcessInfo = null;
    },
    showInspectDetails(container) {
      try {
        const inspectData = typeof container.inspect_data === 'string'
          ? JSON.parse(container.inspect_data)
          : container.inspect_data || {};
        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);
      } catch (e) {
        console.error('Failed to parse inspect data:', e);
        this.$message.error('Failed to parse inspect data');
      }
    },
  }
}
</script>

<style scoped lang="scss">
.crictl-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

/* 信息项样式 */
.info-item {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #f0f0f0;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  strong {
    margin-right: 8px;
    color: #555;
  }
}

.card-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-wrapper {
  display: flex;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

/* 容器详情弹窗样式 */
.container-header {
  margin-bottom: 12px;
}

.container-id {
  display: flex;
  gap: 8px;
}

.container-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-top: 8px;
}

/* 详情卡片样式 */
.detail-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
}

/* 详情项样式 */
.detail-item {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: #fcfcfc;
  border-radius: 4px;
  margin-bottom: 4px;

  &:last-child {
    border-bottom: none;
  }
}

/* 标签和值样式 */
.detail-label-badge {
  display: inline-block;
  background-color: #f5f7fa;
  color: #1890ff;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.detail-value {
  padding: 4px 0 4px 10px;
  color: #333;
  word-break: break-word;
  line-height: 1.5;
}

.mounts-container {
  max-height: 300px;
  overflow-y: auto;
}

.mount-item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.mount-path {
  margin-bottom: 4px;
}

.mount-source {
  color: #1890ff;
}

.mount-arrow {
  margin: 0 8px;
  color: #999;
}

.mount-dest {
  color: #52c41a;
}

.mount-details {
  display: flex;
  gap: 8px;
}

.mount-tag {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.inspect-data-pre, .detail-pre {
  max-height: 600px;
  overflow-y: auto;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.detail-pre {
  max-height: 300px;
  margin: 0;
}
</style>