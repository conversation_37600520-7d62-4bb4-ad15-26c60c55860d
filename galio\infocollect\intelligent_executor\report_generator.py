"""
报告生成器 - 生成安全测试报告
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from jinja2 import Template
import os

from infocollect.log.logger import log_info, log_error, log_debug


class ReportGenerator:
    """安全测试报告生成器"""
    
    def __init__(self):
        pass

    def generate_security_test_report(self, 
                                    test_session_data: Dict[str, Any],
                                    format_type: str = 'html') -> Dict[str, Any]:
        """生成安全测试报告"""
        try:
            log_info(f"Generating security test report in {format_type} format")
            
            # 处理测试会话数据
            processed_data = self._process_test_session_data(test_session_data)
            
            # 生成报告内容
            if format_type.lower() == 'html':
                content = self._generate_html_report(processed_data)
            elif format_type.lower() == 'json':
                content = self._generate_json_report(processed_data)
            elif format_type.lower() == 'markdown':
                content = self._generate_markdown_report(processed_data)
            else:
                content = self._generate_json_report(processed_data)
            
            # 生成报告摘要
            summary = self._generate_report_summary(processed_data)
            
            return {
                "success": True,
                "report_content": content,
                "report_summary": summary,
                "format": format_type,
                "generated_at": datetime.now().isoformat(),
                "metadata": self._generate_report_metadata(processed_data)
            }
            
        except Exception as e:
            log_error(f"Error generating security test report: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _process_test_session_data(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理测试会话数据"""
        processed = {
            "session_info": {
                "session_id": session_data.get('session_id', 'unknown'),
                "start_time": session_data.get('start_time', datetime.now().isoformat()),
                "end_time": session_data.get('end_time', datetime.now().isoformat()),
                "duration": session_data.get('duration', 0),
                "node_ip": session_data.get('node_ip', 'local')
            },
            "security_info": session_data.get('security_info', {}),
            "matched_testcases": session_data.get('matched_testcases', []),
            "analysis_results": session_data.get('analysis_results', []),
            "execution_results": session_data.get('execution_results', []),
            "statistics": self._calculate_statistics(session_data)
        }
        
        return processed

    def _calculate_statistics(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算统计信息"""
        analysis_results = session_data.get('analysis_results', [])
        execution_results = session_data.get('execution_results', [])
        
        stats = {
            "total_testcases": len(session_data.get('matched_testcases', [])),
            "analyzed_testcases": len(analysis_results),
            "total_commands": 0,
            "successful_commands": 0,
            "failed_commands": 0,
            "security_issues_found": 0,
            "high_risk_issues": 0,
            "medium_risk_issues": 0,
            "low_risk_issues": 0
        }
        
        # 统计执行结果
        for exec_result in execution_results:
            if isinstance(exec_result, dict) and 'results' in exec_result:
                commands = exec_result['results']
                stats["total_commands"] += len(commands)
                stats["successful_commands"] += sum(1 for cmd in commands if cmd.get('success', False))
                stats["failed_commands"] += sum(1 for cmd in commands if not cmd.get('success', False))
        
        # 分析安全问题
        for analysis in analysis_results:
            if isinstance(analysis, dict) and 'analysis' in analysis:
                analysis_data = analysis['analysis']
                risk_assessment = analysis_data.get('risk_assessment', '')
                
                if isinstance(risk_assessment, str):
                    risk_lower = risk_assessment.lower()
                    if '高风险' in risk_lower or 'high risk' in risk_lower:
                        stats["high_risk_issues"] += 1
                    elif '中风险' in risk_lower or 'medium risk' in risk_lower:
                        stats["medium_risk_issues"] += 1
                    elif '低风险' in risk_lower or 'low risk' in risk_lower:
                        stats["low_risk_issues"] += 1
        
        stats["security_issues_found"] = (stats["high_risk_issues"] + 
                                        stats["medium_risk_issues"] + 
                                        stats["low_risk_issues"])
        
        return stats

    def _generate_html_report(self, data: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        stats = data.get('statistics', {})
        session_info = data.get('session_info', {})
        testcases = data.get('matched_testcases', [])
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>安全测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-item {{ background: #e8f4fd; padding: 15px; border-radius: 5px; flex: 1; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 安全测试报告</h1>
        <p>生成时间: {session_info.get('end_time', 'N/A')}</p>
        <p>测试节点: {session_info.get('node_ip', 'N/A')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <h3>测试用例</h3>
            <p>{stats.get('total_testcases', 0)} 个</p>
        </div>
        <div class="stat-item">
            <h3>安全问题</h3>
            <p>{stats.get('security_issues_found', 0)} 个</p>
        </div>
        <div class="stat-item">
            <h3>执行成功率</h3>
            <p>{stats.get('successful_commands', 0) / max(stats.get('total_commands', 1), 1) * 100:.1f}%</p>
        </div>
    </div>
    
    <h2>风险分布</h2>
    <table>
        <tr><th>风险等级</th><th>数量</th></tr>
        <tr><td>高风险</td><td>{stats.get('high_risk_issues', 0)}</td></tr>
        <tr><td>中风险</td><td>{stats.get('medium_risk_issues', 0)}</td></tr>
        <tr><td>低风险</td><td>{stats.get('low_risk_issues', 0)}</td></tr>
    </table>
    
    <h2>测试用例详情</h2>
"""
        
        for i, testcase in enumerate(testcases[:5]):
            html += f"""
    <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px;">
        <h3>{testcase.get('Testcase_Name', f'测试用例 {i+1}')}</h3>
        <p><strong>编号:</strong> {testcase.get('Testcase_Number', 'N/A')}</p>
        <p><strong>等级:</strong> {testcase.get('Testcase_Level', 'N/A')}</p>
        <p><strong>测试步骤:</strong> {str(testcase.get('Testcase_TestSteps', ''))[:200]}...</p>
    </div>
"""
        
        html += """
</body>
</html>
"""
        return html

    def _generate_json_report(self, data: Dict[str, Any]) -> str:
        """生成JSON格式报告"""
        return json.dumps(data, ensure_ascii=False, indent=2)

    def _generate_markdown_report(self, data: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        stats = data.get('statistics', {})
        session_info = data.get('session_info', {})
        testcases = data.get('matched_testcases', [])
        
        md = f"""# 安全测试报告

## 测试概览
- **生成时间**: {session_info.get('end_time', 'N/A')}
- **测试节点**: {session_info.get('node_ip', 'N/A')}
- **测试用例总数**: {stats.get('total_testcases', 0)}
- **发现安全问题**: {stats.get('security_issues_found', 0)}
- **执行成功率**: {stats.get('successful_commands', 0) / max(stats.get('total_commands', 1), 1) * 100:.1f}%

## 风险分布
| 风险等级 | 数量 |
|---------|------|
| 高风险 | {stats.get('high_risk_issues', 0)} |
| 中风险 | {stats.get('medium_risk_issues', 0)} |
| 低风险 | {stats.get('low_risk_issues', 0)} |

## 测试用例详情
"""
        
        for i, testcase in enumerate(testcases[:5]):
            md += f"""
### {testcase.get('Testcase_Name', f'测试用例 {i+1}')}
- **编号**: {testcase.get('Testcase_Number', 'N/A')}
- **等级**: {testcase.get('Testcase_Level', 'N/A')}
- **测试步骤**: {str(testcase.get('Testcase_TestSteps', ''))[:200]}...

"""
        
        return md

    def _generate_report_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成报告摘要"""
        stats = data.get('statistics', {})
        
        summary = {
            "total_testcases": stats.get('total_testcases', 0),
            "execution_success_rate": 0,
            "security_issues_found": stats.get('security_issues_found', 0),
            "risk_distribution": {
                "high": stats.get('high_risk_issues', 0),
                "medium": stats.get('medium_risk_issues', 0),
                "low": stats.get('low_risk_issues', 0)
            },
            "recommendations": self._generate_recommendations(data)
        }
        
        # 计算执行成功率
        total_commands = stats.get('total_commands', 0)
        successful_commands = stats.get('successful_commands', 0)
        if total_commands > 0:
            summary["execution_success_rate"] = round((successful_commands / total_commands) * 100, 2)
        
        return summary

    def _generate_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        stats = data.get('statistics', {})
        
        # 基于统计信息生成建议
        if stats.get('high_risk_issues', 0) > 0:
            recommendations.append("发现高风险安全问题，建议立即修复")
        
        if stats.get('failed_commands', 0) > stats.get('successful_commands', 0):
            recommendations.append("大量命令执行失败，建议检查测试环境配置")
        
        if stats.get('total_testcases', 0) < 5:
            recommendations.append("匹配的测试用例较少，建议扩展测试用例库")
        
        if not recommendations:
            recommendations.append("总体安全状况良好，建议定期进行安全测试")
        
        return recommendations

    def _generate_report_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成报告元数据"""
        return {
            "report_version": "1.0",
            "generator": "SecurityTestAgent",
            "total_pages": 1,
            "data_sources": list(data.get('security_info', {}).keys()),
            "test_types": ["security", "compliance", "vulnerability"],
            "report_language": "zh-CN"
        } 