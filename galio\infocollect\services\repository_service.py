import os
import subprocess
import re
import shutil
import stat
import uuid
import time
import threading
from urllib.parse import urlparse
from typing import Dict, List, Tuple
from sqlalchemy.orm import Session
from datamodel.repository_datamodel import CodeRepository
from sqlalchemy.orm.exc import NoResultFound
from log.logger import log_error, log_debug

# 全局任务状态存储
active_download_tasks = {}
task_lock = threading.Lock()


class CodeRepositoryService:
    def __init__(self, db: Session):
        self.db = db

    def get_all_repositories(self, detail: bool = True) -> List[Dict]:
        """获取所有代码仓配置"""
        repositories = self.db.query(CodeRepository).order_by(CodeRepository.id.asc()).all()

        if detail:
            return [
                {
                    "id": repo.id,
                    "microservice_name": repo.microservice_name,
                    "repository_url": repo.repository_url,
                    "branch_name": repo.branch_name,
                }
                for repo in repositories
            ]
        else:
            return [
                {
                    "id": repo.id,
                    "microservice_name": repo.microservice_name,
                    "repository_url": repo.repository_url,
                }
                for repo in repositories
            ]

    def _is_valid_git_url_format(self, repository_url: str) -> bool:
        """检查 Git URL 格式是否有效"""
        repository_url = repository_url.strip()

        if not repository_url:
            return False

        # SSH 格式：ssh://开头，.git结尾
        if repository_url.startswith('ssh://') and repository_url.endswith('.git'):
            return True

        # HTTPS 格式：https://开头，.git结尾
        if repository_url.startswith('https://') and repository_url.endswith('.git'):
            return True

        return False

    def validate_repository_url(self, repository_url: str, branch_name: str = None) -> tuple:
        """
        验证代码仓地址格式
        返回 (is_valid, error_message)

        支持两种格式：
        1. SSH: ssh://开头，.git结尾
        2. HTTPS: https://开头，.git结尾
        """
        try:
            repository_url = repository_url.strip()

            if not repository_url:
                return False, "Repository URL cannot be empty"

            if not self._is_valid_git_url_format(repository_url):
                return False, ("Unsupported repository URL format. Supported formats:\n"
                             "1. SSH: ssh://user@host:port/path.git\n"
                             "2. HTTPS clone: https://host/path.git")

            # 检查分支名
            if not branch_name or not branch_name.strip():
                return False, "Branch name is required"

            return True, ""

        except Exception as e:
            return False, f"URL validation error: {str(e)}"

    def save_repositories(self, repositories_data: List[Dict]) -> Dict:
        successful = []
        failed = []

        try:
            for repo in repositories_data:
                microservice_name = repo.get('microservice_name', '').strip()
                repository_url = repo.get('repository_url', '').strip()
                branch_name = repo.get('branch_name', '').strip()

                if not microservice_name:
                    failed.append({
                        **repo,
                        'error': 'Missing microservice name'
                    })
                    continue

                if not repository_url:
                    failed.append({
                        **repo,
                        'error': 'Missing repository URL'
                    })
                    continue

                is_valid, error_msg = self.validate_repository_url(repository_url, branch_name)
                if not is_valid:
                    failed.append({
                        **repo,
                        'error': error_msg
                    })
                    continue

                filtered_repo = {
                    'microservice_name': microservice_name,
                    'repository_url': repository_url,
                    'branch_name': branch_name
                }

                # Check if repository URL already exists
                existing = self.db.query(CodeRepository)\
                    .filter(CodeRepository.repository_url == filtered_repo['repository_url'])\
                    .with_for_update()\
                    .first()

                if existing:
                    # Update existing repository with the same URL
                    for key, value in filtered_repo.items():
                        setattr(existing, key, value)
                    repo_record = existing
                else:
                    # Add as new repository
                    repo_record = CodeRepository(**filtered_repo)
                    self.db.add(repo_record)

                self.db.flush()

                successful.append({
                    "id": repo_record.id,
                    "microservice_name": repo_record.microservice_name,
                    "repository_url": repo_record.repository_url,
                    "branch_name": repo_record.branch_name,
                })

            # 如果有成功的操作，提交事务
            if successful:
                self.db.commit()

            return {
                "successful": successful,
                "failed": failed
            }
        except Exception as e:
            self.db.rollback()
            raise type(e)(f"Error saving repositories: {str(e)}") from e

    def delete_repository(self, repo_id: int) -> None:
        try:
            repo = self.db.query(CodeRepository).get(repo_id)
            if not repo:
                raise NoResultFound(f"Repository with id {repo_id} not found")

            self.db.delete(repo)
            self.db.commit()
        except NoResultFound as e:
            self.db.rollback()
            raise e
        except Exception as e:
            self.db.rollback()
            raise e

    def batch_delete_repositories(self, repo_ids: List[int]) -> None:
        """批量删除代码仓配置"""
        try:
            self.db.query(CodeRepository).filter(CodeRepository.id.in_(repo_ids)).delete(synchronize_session=False)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise e

    def parse_repository_url(self, repository_url: str, branch_name: str = None) -> Tuple[str, str, bool]:
        """
        解析代码仓地址，返回 (git_url, branch, is_valid)
        使用统一的验证逻辑
        """
        try:
            repository_url = repository_url.strip()
            branch_name = branch_name.strip() if branch_name else ""

            # 使用统一的验证方法
            is_valid, _ = self.validate_repository_url(repository_url, branch_name)

            return repository_url, branch_name, is_valid

        except Exception:
            return repository_url, branch_name, False

    def validate_repositories_for_download(self, repositories: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        valid_repos = []
        invalid_repos = []

        for repo in repositories:
            microservice_name = repo.get('microservice_name', '')
            repository_url = repo.get('repository_url', '')
            branch_name = repo.get('branch_name', '')

            if not microservice_name or not repository_url:
                invalid_repos.append({
                    **repo,
                    'error': 'Missing microservice name or repository URL'
                })
                continue

            git_url, branch, is_valid = self.parse_repository_url(repository_url, branch_name)

            if not is_valid:
                invalid_repos.append({
                    **repo,
                    'error': 'Unsupported repository format or missing branch name'
                })
                continue

            if not branch:
                invalid_repos.append({
                    **repo,
                    'error': 'Missing branch name'
                })
                continue

            valid_repos.append({
                **repo,
                'git_url': git_url,
                'branch': branch
            })

        return valid_repos, invalid_repos

    def convert_https_to_ssh(self, url: str) -> str:
        """
        将 HTTPS 格式的 Git 仓库地址转换为 SSH 格式
        参考华为 CodeHub 的转换逻辑

        例如：
        https://codehub-dg-g.huawei.com/user/repo.git -> ssh://***************************:2222/user/repo.git
        https://github.com/user/repo.git -> **************:user/repo.git
        """
        try:
            git_url = None
            url_pattern = r"^(http://|https://).*(?=\.git$)"
            ret = re.findall(url_pattern, url)
            parse_result = urlparse(url)

            if ret:
                if "codehub-dg-g.huawei.com" in parse_result.netloc:
                    git_url = "ssh://***************************:2222" + parse_result.path
                else:
                    host = parse_result.netloc
                    path = parse_result.path
                    if path.startswith('/'):
                        path = path[1:]  # 移除开头的 /
                    git_url = f"git@{host}:{path}"
            else:
                # 不是 HTTP/HTTPS 格式或不以 .git 结尾，直接返回原 URL
                git_url = url

            log_debug(f"URL conversion: {url} -> {git_url}")
            return git_url

        except Exception as e:
            log_error(f"URL conversion failed: {url}, error: {str(e)}")
            return url  # 转换失败，返回原URL

    def clone_repository(self, git_url: str, branch: str, target_path: str) -> Tuple[bool, str]:
        """
        克隆单个代码仓
        返回 (success, error_message)
        """
        try:
            # 如果是 HTTPS 格式，转换为 SSH 格式
            actual_git_url = git_url
            if git_url.startswith('https://') and git_url.endswith('.git'):
                actual_git_url = self.convert_https_to_ssh(git_url)

            cmd = ['git', 'clone', '-b', branch, actual_git_url, target_path]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode == 0:
                return True, ""
            else:
                error_msg = result.stderr.strip() if result.stderr else result.stdout.strip()
                return False, f"git clone -b {branch} {actual_git_url}: {error_msg}"

        except subprocess.TimeoutExpired:
            return False, f"git clone -b {branch} {actual_git_url}: Timeout after 5 minutes"
        except Exception as e:
            return False, f"git clone -b {branch} {actual_git_url}: {str(e)}"

    def _force_remove_tree(self, path: str) -> None:
        """强制删除目录树，处理权限问题"""
        for root, dirs, files in os.walk(path, topdown=False):
            for name in files:
                file_path = os.path.join(root, name)
                try:
                    os.chmod(file_path, stat.S_IWRITE)
                    os.remove(file_path)
                except OSError:
                    pass
            for name in dirs:
                dir_path = os.path.join(root, name)
                try:
                    os.chmod(dir_path, stat.S_IWRITE)
                    os.rmdir(dir_path)
                except OSError:
                    pass
        try:
            os.chmod(path, stat.S_IWRITE)
            os.rmdir(path)
        except OSError:
            pass

    def _remove_existing_directory(self, target_path: str) -> bool:
        try:
            shutil.rmtree(target_path)
            return True
        except OSError:
            try:
                self._force_remove_tree(target_path)
                return True
            except Exception:
                return False

    def start_download_task(self, repositories: List[Dict], download_path: str) -> str:
        """启动异步下载任务"""
        task_id = str(uuid.uuid4())
        
        # 初始化任务状态
        with task_lock:
            active_download_tasks[task_id] = {
                "task_id": task_id,
                "status": "running",
                "repositories": {},
                "metadata": {
                    "start_time": time.time(),
                    "total_repos": len(repositories),
                    "completed_repos": 0,
                    "download_path": download_path
                }
            }
            
            # 初始化每个代码仓的状态
            for repo in repositories:
                repo_key = f"{repo.get('microservice_name', 'unknown')}_{repo.get('repository_url', 'unknown')}"
                active_download_tasks[task_id]["repositories"][repo_key] = {
                    "microservice_name": repo.get('microservice_name', ''),
                    "repository_url": repo.get('repository_url', ''),
                    "branch_name": repo.get('branch_name', ''),
                    "status": "pending",
                    "progress": 0,
                    "error_detail": None,
                    "download_path": None
                }

        # 启动后台下载线程
        download_thread = threading.Thread(
            target=self._execute_download_task,
            args=(task_id, repositories, download_path)
        )
        download_thread.daemon = True
        download_thread.start()

        return task_id

    def _execute_download_task(self, task_id: str, repositories: List[Dict], download_path: str):
        """执行下载任务"""
        try:
            os.makedirs(download_path, exist_ok=True)
        except Exception as e:
            with task_lock:
                if task_id in active_download_tasks:
                    active_download_tasks[task_id]["status"] = "failed"
                    active_download_tasks[task_id]["error"] = f"Cannot create download path: {str(e)}"
            return

        processed_paths = set()

        for repo in repositories:
            repo_key = f"{repo.get('microservice_name', 'unknown')}_{repo.get('repository_url', 'unknown')}"
            
            try:
                # 更新状态为正在处理
                with task_lock:
                    if task_id in active_download_tasks:
                        active_download_tasks[task_id]["repositories"][repo_key]["status"] = "downloading"
                        active_download_tasks[task_id]["repositories"][repo_key]["progress"] = 10

                git_url = repo.get('repository_url')
                branch = repo.get('branch_name')
                
                if not git_url:
                    raise Exception("Repository URL is required")

                repo_name = git_url.split("/")[-1].split(".")[0]
                dir_name = f"{repo_name}-{branch}"
                target_path = os.path.join(download_path, dir_name)

                if target_path in processed_paths:
                    raise Exception(f"Duplicate repository: {target_path}")
                
                processed_paths.add(target_path)

                # 更新进度
                with task_lock:
                    if task_id in active_download_tasks:
                        active_download_tasks[task_id]["repositories"][repo_key]["progress"] = 30

                if os.path.exists(target_path):
                    if not self._remove_existing_directory(target_path):
                        raise Exception(f"Cannot remove existing directory: {target_path}")

                try:
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                except Exception as e:
                    raise Exception(f"Cannot create directory: {str(e)}")

                # 更新进度
                with task_lock:
                    if task_id in active_download_tasks:
                        active_download_tasks[task_id]["repositories"][repo_key]["progress"] = 50

                success, error_msg = self.clone_repository(git_url, branch, target_path)
                
                with task_lock:
                    if task_id in active_download_tasks:
                        task = active_download_tasks[task_id]
                        if success:
                            task["repositories"][repo_key]["status"] = "success"
                            task["repositories"][repo_key]["progress"] = 100
                            task["repositories"][repo_key]["download_path"] = target_path
                        else:
                            task["repositories"][repo_key]["status"] = "failed"
                            task["repositories"][repo_key]["error_detail"] = error_msg
                        
                        task["metadata"]["completed_repos"] += 1
                        
                        # 检查是否所有代码仓都完成
                        if task["metadata"]["completed_repos"] == task["metadata"]["total_repos"]:
                            all_success = all(
                                repo_status["status"] == "success"
                                for repo_status in task["repositories"].values()
                            )
                            task["status"] = "success" if all_success else "partial_success"
                            task["metadata"]["end_time"] = time.time()
                            task["metadata"]["duration"] = task["metadata"]["end_time"] - task["metadata"]["start_time"]
                    
            except Exception as e:
                with task_lock:
                    if task_id in active_download_tasks:
                        task = active_download_tasks[task_id]
                        task["repositories"][repo_key]["status"] = "failed"
                        task["repositories"][repo_key]["error_detail"] = str(e)
                        task["metadata"]["completed_repos"] += 1
                        
                        # 检查是否所有代码仓都完成
                        if task["metadata"]["completed_repos"] == task["metadata"]["total_repos"]:
                            task["status"] = "failed"
                            task["metadata"]["end_time"] = time.time()
                            task["metadata"]["duration"] = task["metadata"]["end_time"] - task["metadata"]["start_time"]

    def get_download_task_status(self, task_id: str) -> Dict:
        """获取下载任务状态"""
        with task_lock:
            task = active_download_tasks.get(task_id)
            if not task:
                return None

            # 转换格式以匹配前端期望的结构
            successful = []
            failed = []

            for repo_key, repo_status in task["repositories"].items():
                repo_data = {
                    "microservice_name": repo_status["microservice_name"],
                    "repository_url": repo_status["repository_url"],
                    "branch_name": repo_status["branch_name"]
                }

                if repo_status["status"] == "success":
                    successful.append({
                        **repo_data,
                        "download_path": repo_status["download_path"]
                    })
                elif repo_status["status"] == "failed":
                    failed.append({
                        **repo_data,
                        "error": repo_status["error_detail"]
                    })

            return {
                "task_id": task_id,
                "status": task["status"],
                "metadata": task["metadata"],
                "repositories": task["repositories"],
                "successful": successful,
                "failed": failed
            }
