(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b84be87"],{2310:function(e,t,a){},a049:function(e,t,a){"use strict";a("2310")},e300:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("SmartOrchestrationInfo")],1)],1)],1)},l=[],n=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full",attrs:{bordered:!1},scopedSlots:e._u([{key:"title",fn:function(){return[t("h6",{staticClass:"font-semibold m-0"},[e._v("智能测试用例分析")])]},proxy:!0},{key:"extra",fn:function(){return[t("a-button",{attrs:{type:"primary",loading:e.analyzing,disabled:!e.hasNodeData},on:{click:e.showAnalysisModal},scopedSlots:e._u([{key:"icon",fn:function(){return[t("BranchesOutlined")]},proxy:!0}])},[e._v(" 开始智能分析 ")])]},proxy:!0}])},[t("a-row",{staticClass:"mb-16",attrs:{gutter:16}},[t("a-col",{attrs:{span:24}},[e.hasNodeData?t("a-alert",{staticClass:"mb-16",attrs:{message:"节点数据已就绪",description:`已检测到 ${e.availableDataTypes.length} 种类型的数据：${e.availableDataTypes.join("、")}`,type:"success","show-icon":""}}):t("a-alert",{staticClass:"mb-16",attrs:{message:"未检测到节点数据",description:"请先在其他功能页面收集节点信息（进程、硬件、端口等）后再进行智能分析",type:"info","show-icon":""}})],1)],1),e.analysisResults.length>0?t("div",[t("a-divider",{attrs:{orientation:"left"}},[e._v("分析结果")]),t("a-collapse",{staticClass:"mb-16",model:{value:e.activeKeys,callback:function(t){e.activeKeys=t},expression:"activeKeys"}},e._l(e.analysisResults,(function(a,s){return t("a-collapse-panel",{key:s,attrs:{header:`${a.info_type.toUpperCase()} 信息分析 - ${"success"===a.status?"成功":"warning"===a.status?"警告":"失败"}`},scopedSlots:e._u([{key:"extra",fn:function(){return[t("a-tag",{attrs:{color:e.getStatusColor(a.status)}},[e._v(" "+e._s(e.getStatusText(a.status))+" ")])]},proxy:!0}],null,!0)},[t("a-descriptions",{staticClass:"mb-16",attrs:{title:"查询信息",column:1,size:"small"}},[t("a-descriptions-item",{attrs:{label:"信息类型"}},[e._v(e._s(a.info_type))]),t("a-descriptions-item",{attrs:{label:"查询文本"}},[e._v(e._s(a.query_text))]),t("a-descriptions-item",{attrs:{label:"匹配用例数"}},[e._v(e._s(a.matched_testcases.length))])],1),t("a-divider",{attrs:{orientation:"left","orientation-margin":"0"}},[e._v("匹配的测试用例")]),t("a-table",{staticClass:"mb-16",attrs:{dataSource:a.matched_testcases,columns:e.testcaseColumns,pagination:!1,size:"small"},scopedSlots:e._u([{key:"bodyCell",fn:function({column:a,record:s}){return["Testcase_Name"===a.key?[t("a-tooltip",{attrs:{title:s.Testcase_Name}},[t("span",[e._v(e._s(e.truncateText(s.Testcase_Name,30)))])])]:e._e(),"Testcase_TestSteps"===a.key?[t("a-tooltip",{attrs:{title:s.Testcase_TestSteps}},[t("span",[e._v(e._s(e.truncateText(s.Testcase_TestSteps,50)))])])]:e._e()]}}],null,!0)}),t("a-divider",{attrs:{orientation:"left","orientation-margin":"0"}},[e._v("执行结果")]),t("a-table",{attrs:{dataSource:a.execution_results,columns:e.executionColumns,pagination:!1,size:"small",expandable:{expandedRowRender:e.expandedRowRender}},scopedSlots:e._u([{key:"bodyCell",fn:function({column:a,record:s}){return["status"===a.key?[t("a-tag",{attrs:{color:e.getStatusColor(s.status)}},[e._v(" "+e._s(e.getStatusText(s.status))+" ")])]:e._e(),"testcase_name"===a.key?[t("a-tooltip",{attrs:{title:s.testcase_name}},[t("span",[e._v(e._s(e.truncateText(s.testcase_name,30)))])])]:e._e()]}}],null,!0)})],1)})),1)],1):e._e(),t("a-modal",{attrs:{title:"智能测试用例分析配置",width:800,confirmLoading:e.analyzing},on:{ok:e.startAnalysis},model:{value:e.analysisModalVisible,callback:function(t){e.analysisModalVisible=t},expression:"analysisModalVisible"}},[t("a-form",{attrs:{layout:"vertical"}},[t("a-form-item",{attrs:{label:"选择节点",required:""}},[t("a-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要分析的节点"},model:{value:e.selectedNodeId,callback:function(t){e.selectedNodeId=t},expression:"selectedNodeId"}},e._l(e.availableNodes,(function(a){return t("a-select-option",{key:a.id,attrs:{value:a.id}},[e._v(" "+e._s(a.name)+" ("+e._s(a.ip)+") ")])})),1)],1),t("a-form-item",{attrs:{label:"选择分析类型",required:""}},[t("a-checkbox-group",{model:{value:e.selectedAnalysisTypes,callback:function(t){e.selectedAnalysisTypes=t},expression:"selectedAnalysisTypes"}},[t("a-row",e._l(e.availableDataTypes,(function(a){return t("a-col",{key:a,attrs:{span:8}},[t("a-checkbox",{attrs:{value:a}},[e._v(e._s(e.getTypeName(a)))])],1)})),1)],1)],1)],1)],1)],1)},i=[],o=(a("0643"),a("a573"),a("8520")),r=a("f64c"),c={name:"IntelligentTestCaseInfo",components:{BranchesOutlined:o["BranchesOutlined"],CheckCircleOutlined:o["CheckCircleOutlined"],ExclamationCircleOutlined:o["ExclamationCircleOutlined"],CloseCircleOutlined:o["CloseCircleOutlined"]},data(){return{analyzing:!1,analysisModalVisible:!1,selectedNodeId:null,selectedAnalysisTypes:[],analysisResults:[],activeKeys:["0"],availableNodes:[],availableDataTypes:[],testcaseColumns:[{title:"用例编号",dataIndex:"Testcase_Number",key:"Testcase_Number",width:120},{title:"用例名称",dataIndex:"Testcase_Name",key:"Testcase_Name",ellipsis:!0},{title:"用例级别",dataIndex:"Testcase_Level",key:"Testcase_Level",width:80},{title:"测试步骤",dataIndex:"Testcase_TestSteps",key:"Testcase_TestSteps",ellipsis:!0}],executionColumns:[{title:"用例编号",dataIndex:"testcase_number",key:"testcase_number",width:120},{title:"用例名称",dataIndex:"testcase_name",key:"testcase_name",ellipsis:!0},{title:"执行状态",dataIndex:"status",key:"status",width:100},{title:"执行消息",dataIndex:"message",key:"message",ellipsis:!0}]}},computed:{hasNodeData(){return this.availableNodes.length>0&&this.availableDataTypes.length>0}},mounted(){this.loadAvailableNodes(),this.detectAvailableDataTypes()},methods:{async loadAvailableNodes(){try{const e=await this.$http.get("/api/node/nodes");e.data&&e.data.length>0&&(this.availableNodes=e.data)}catch(e){console.error("加载节点列表失败:",e)}},detectAvailableDataTypes(){const e=["process","package","hardware","filesystem","port","docker","kubernetes"],t=[];e.forEach(e=>{const a=e+"_data";localStorage.getItem(a)&&t.push(e)}),this.availableDataTypes=t},showAnalysisModal(){this.hasNodeData?(this.selectedAnalysisTypes=[...this.availableDataTypes],this.analysisModalVisible=!0,1===this.availableNodes.length&&(this.selectedNodeId=this.availableNodes[0].id)):r["a"].warning("请先收集节点数据")},async startAnalysis(){if(this.selectedNodeId)if(0!==this.selectedAnalysisTypes.length){this.analyzing=!0,this.analysisResults=[];try{for(const e of this.selectedAnalysisTypes){const t=this.getCollectedData(e);if(t){const a=await this.$http.post("/api/intelligent/analyze-and-execute",{node_id:this.selectedNodeId,info_type:e,collected_data:t});this.analysisResults.push(a.data)}}this.analysisModalVisible=!1,this.activeKeys=this.analysisResults.map((e,t)=>t.toString()),r["a"].success(`完成了 ${this.analysisResults.length} 种数据类型的智能分析`)}catch(e){console.error("分析失败:",e),r["a"].error("分析过程中出现错误")}finally{this.analyzing=!1}}else r["a"].error("请选择分析类型");else r["a"].error("请选择节点")},getCollectedData(e){const t=e+"_data",a=localStorage.getItem(t);return a?JSON.parse(a):null},getTypeName(e){const t={process:"进程信息",package:"软件包信息",hardware:"硬件信息",filesystem:"文件系统信息",port:"端口信息",docker:"Docker信息",kubernetes:"Kubernetes信息"};return t[e]||e},getStatusColor(e){const t={success:"green",partial:"orange",warning:"orange",failed:"red",error:"red",info:"blue"};return t[e]||"default"},getStatusText(e){const t={success:"成功",partial:"部分成功",warning:"警告",failed:"失败",error:"错误",info:"信息"};return t[e]||e},truncateText(e,t){return e?e.length>t?e.substring(0,t)+"...":e:""},expandedRowRender(e){return e.outputs&&0!==e.outputs.length?`\n        <div style="margin: 16px 0;">\n          <h4>命令执行详情:</h4>\n          ${e.outputs.map((e,t)=>`\n            <div style="margin-bottom: 12px; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px;">\n              <p><strong>命令 ${t+1}:</strong> <code>${e.command}</code></p>\n              <p><strong>退出码:</strong> <span style="color: ${0===e.exit_code?"green":"red"}">${e.exit_code}</span></p>\n              ${e.output?`<p><strong>输出:</strong><br><pre style="background: #f5f5f5; padding: 8px; border-radius: 4px; white-space: pre-wrap;">${e.output}</pre></p>`:""}\n              ${e.error?`<p><strong>错误:</strong><br><pre style="background: #fff2f0; padding: 8px; border-radius: 4px; color: red; white-space: pre-wrap;">${e.error}</pre></p>`:""}\n            </div>\n          `).join("")}\n        </div>\n      `:"无执行详情"}}},d=c,u=(a("a049"),a("2877")),p=Object(u["a"])(d,n,i,!1,null,"430709ea",null),y=p.exports,h={components:{SmartOrchestrationInfo:y}},m=h,_=Object(u["a"])(m,s,l,!1,null,null,null);t["default"]=_.exports}}]);
//# sourceMappingURL=chunk-4b84be87.fc218baf.js.map