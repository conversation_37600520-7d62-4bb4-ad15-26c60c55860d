import paramiko
import time
from app.obsclient.logger import log_error, log_info, log_debug


class CbhClient:
    def __init__(self, remote_host, username, port, private_key_path, password):
        self.remote_host = remote_host
        self.username = username
        self.port = port
        self.pkey = private_key_path
        self.passwd = password

    def ssh_connect(self, host_ip, command, cbh_switch_account):
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        try:
            private_key = paramiko.RSAKey.from_private_key_file(self.pkey, self.passwd)
            client.connect(self.remote_host, self.port, self.username, pkey=private_key)

            shell = client.invoke_shell()

            shell.send('=' + host_ip + '\r')
            time.sleep(0.5)
            self.receive_output(shell)

            shell.send('\r')
            time.sleep(1)

            output = self.receive_output(shell)
            account_id = self.check_user_exists(output, cbh_switch_account)
            if not account_id:
                return

            shell.send(f'{account_id}\r')
            time.sleep(1)

            shell.send('su')
            time.sleep(0.5)
            shell.send('\r')
            self.receive_output(shell)
            time.sleep(0.5)

            # check cmd is list or str
            # self.execute_command(shell, cmd)
            # time.sleep(1)
            for cmd in command:
                shell.send(cmd)
                time.sleep(0.5)
                shell.send('\r')
                time.sleep(3)

                if "nohup" in cmd:
                    # timeout > 900 ,return
                    output = self.receive_output(shell, 9000)

            log_info("start the next step")
        except Exception as e:
            log_error(f"Connection failed: {e}")
        finally:
            client.close()

    @staticmethod
    def check_user_exists(main, user):
        lines = main.strip().split('\n')
        for line in lines:
            if user in line:
                id_index = line.split()[0].strip('[]')
                return id_index

        log_error(f"Switch account: '{user}' not exists！")
        return False

    @staticmethod
    def receive_output(shell_instance, timeout=2):
        output_data = ''
        start_time = time.time()
        # counter to track the occurrences of exec_success
        exec_success_count = 0
        while True:
            if shell_instance.recv_ready():
                resp = shell_instance.recv(9999).decode('utf-8')
                output_data += resp

                exec_success_count += resp.count("exec_success")
                if exec_success_count >= 2:
                    log_debug("execute generated_script.sh success.")
                    break

            if time.time() - start_time > timeout:
                log_debug("...Timeout waiting for output...")
                break
            time.sleep(0.1)

        return output_data

    @staticmethod
    def execute_command(shell, cmd):
        if isinstance(cmd, list):
            for command in cmd:
                shell.send(command)
                time.sleep(0.5)
                shell.send('\r')
                time.sleep(3)
                # output = self.receive_output(shell)
                # print("output:", output)
        else:
            shell.send(cmd)
            time.sleep(0.5)
            shell.send('\r')
            time.sleep(3)
