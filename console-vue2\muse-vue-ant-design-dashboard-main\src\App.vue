<!--
	This is the main page of the application, the layout component is used here,
	and the router-view is passed to it.
	Layout component is dynamically declared based on the layout for each route,
	specified in routes list router/index.js .
 -->

<template>
	<div id="app">
		<component :is="layout" v-if="isReady">
			<router-view />
		</component>
		<div v-else>Loading...</div>
	</div>
</template>

<script>

	export default {
		name: 'App',
		data() {
			return {
				isReady: false
			}
		},
		computed: {
			// Sets components name based on current route's specified layout, defaults to
			// <layout-default></layout-default> component.
			layout() {
				return "layout-" + (this.$route.meta.layout || "simple");
			},
			isDarkMode() {
				return this.$store.state.darkMode;
			}
		},
		created() {
			this.isReady = true;
			// 初始化深色模式
			if (this.$store.state.darkMode) {
				document.documentElement.classList.add('dark-mode');
			} else {
				document.documentElement.classList.remove('dark-mode');
			}
		},
		watch: {
			// 监听深色模式变化
			isDarkMode(newValue) {
				if (newValue) {
					document.documentElement.classList.add('dark-mode');
				} else {
					document.documentElement.classList.remove('dark-mode');
				}
			}
		}
	}

</script>

<style lang="scss">
</style>