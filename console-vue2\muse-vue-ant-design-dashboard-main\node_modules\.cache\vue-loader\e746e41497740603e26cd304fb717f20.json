{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue?vue&type=style&index=0&id=d5e55958&lang=scss", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\JsonDetailModal.vue", "mtime": 1751522841423}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751014595046}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751014596662}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751014595604}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["JsonDetailModal.vue"], "names": [], "mappings": ";AA6TA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JsonDetailModal.vue", "sourceRoot": "src/components/Widgets", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 组件不直接渲染模态框，而是提供方法 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport VueJsonPretty from 'vue-json-pretty';\r\n\r\nexport default {\r\n  name: 'JsonDetailModal',\r\n  methods: {\r\n    /**\r\n     * 显示JSON详情模态框\r\n     * @param {string} title 模态框标题\r\n     * @param {object|string} data 要显示的数据\r\n     * @param {object} options 配置选项\r\n     */\r\n    showDetailModal(title, data, options = {}) {\r\n      // 计算响应式尺寸\r\n      const modalWidth = Math.min(options.width || 1200, window.innerWidth * 0.9);\r\n      const contentHeight = Math.min(700, window.innerHeight * 0.6);\r\n\r\n      // 生成唯一ID\r\n      const ids = {\r\n        search: `search-${Date.now()}`,\r\n        counter: `counter-${Date.now()}`,\r\n        theme: `theme-${Date.now()}`\r\n      };\r\n\r\n      // 默认使用暗色主题\r\n      let isDarkTheme = true;\r\n\r\n      // 创建标题、搜索框和复制按钮\r\n      const header = (\r\n        <div style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <a-icon type=\"code\" style=\"margin-right: 8px; font-size: 16px;\" />\r\n            <span style=\"font-weight: 500;\">{title}</span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center;\">\r\n            <div id={ids.counter} style=\"margin-right: 10px; min-width: 60px; text-align: right; color: #666;\"></div>\r\n            <a-input\r\n              id={ids.search}\r\n              placeholder=\"搜索 (Enter: ↓  Shift+Enter: ↑)\"\r\n              allowClear\r\n              prefix={<a-icon type=\"search\" style=\"color: rgba(0,0,0,.25)\" />}\r\n              style=\"width: 250px;\"\r\n            />\r\n            <a-button\r\n              id={ids.theme}\r\n              type=\"link\"\r\n              icon=\"bg-colors\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"切换主题\"\r\n            />\r\n            <a-button\r\n              id=\"copy-btn\"\r\n              type=\"link\"\r\n              icon=\"copy\"\r\n              style=\"margin-left: 8px; color: #1890ff; font-size: 16px;\"\r\n              title=\"复制内容\"\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n      // 准备内容元素\r\n      const contentElement = typeof data === 'object' ? (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; margin: 0; padding: 12px; background-color: #1e1e1e; border-radius: 4px;`} class=\"json-container theme-dark\" id=\"json-container\">\r\n        </div>\r\n      ) : (\r\n        <div style={`height: ${contentHeight}px; overflow: auto; white-space: pre-wrap; padding: 12px; background-color: #1e1e1e; border-radius: 4px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; line-height: 1.5; color: #d4d4d4;`}>\r\n          {String(data)}\r\n        </div>\r\n      );\r\n\r\n      // 创建模态框\r\n      this.$root.$confirm({\r\n        title: header,\r\n        content: contentElement,\r\n        width: modalWidth,\r\n        okText: options.okText || '关闭',\r\n        icon: null,\r\n        cancelButtonProps: { style: { display: 'none' } },\r\n        class: 'detail-modal',\r\n        maskClosable: false, // 防止点击外部关闭\r\n        getContainer: () => document.body.appendChild(document.createElement('div'))\r\n      });\r\n\r\n      // 在模态框内容区域渲染VueJsonPretty组件\r\n      setTimeout(() => {\r\n        if (typeof data === 'object') {\r\n          // 查找JSON容器\r\n          const container = document.getElementById('json-container');\r\n          if (container) {\r\n            // 创建VueJsonPretty组件实例\r\n            const JsonViewer = new Vue({\r\n              render: h => h(VueJsonPretty, {\r\n                props: {\r\n                  data: data,\r\n                  deep: Infinity, // 设置为Infinity以默认展开所有节点\r\n                  showDoubleQuotes: true,\r\n                  showLength: true,\r\n                  showLineNumbers: true,  // 添加行号显示\r\n                },\r\n                style: {\r\n                  height: '100%',\r\n                  overflow: 'auto'\r\n                }\r\n              })\r\n            });\r\n\r\n            // 挂载组件\r\n            JsonViewer.$mount();\r\n            container.appendChild(JsonViewer.$el);\r\n          }\r\n        }\r\n\r\n        // 获取搜索相关元素\r\n        const searchInput = document.getElementById(ids.search);\r\n        const counterElement = document.getElementById(ids.counter);\r\n\r\n        // 搜索功能变量\r\n        let matches = [];\r\n        let currentMatchIndex = -1;\r\n\r\n        // 如果有搜索框，添加搜索功能\r\n        if (searchInput && counterElement) {\r\n          // 高亮匹配项函数\r\n          const highlightMatches = (searchTerm) => {\r\n            // 重置匹配\r\n            matches = [];\r\n            currentMatchIndex = -1;\r\n\r\n            // 清除计数器\r\n            counterElement.textContent = '';\r\n\r\n            if (!searchTerm) return;\r\n\r\n            try {\r\n              // 查找所有键和值节点\r\n              const jsonNodes = document.querySelectorAll('.vjs-key, .vjs-value');\r\n\r\n              // 创建正则表达式\r\n              const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'gi');\r\n\r\n              // 移除之前的高亮\r\n              document.querySelectorAll('.vjs-search-match').forEach(el => {\r\n                el.classList.remove('vjs-search-match');\r\n              });\r\n\r\n              document.querySelectorAll('.vjs-search-current').forEach(el => {\r\n                el.classList.remove('vjs-search-current');\r\n              });\r\n\r\n              // 查找匹配项\r\n              jsonNodes.forEach(node => {\r\n                const text = node.textContent;\r\n                let match;\r\n                regex.lastIndex = 0;\r\n\r\n                while ((match = regex.exec(text)) !== null) {\r\n                  matches.push({\r\n                    node: node,\r\n                    text: match[0]\r\n                  });\r\n\r\n                  // 防止无限循环\r\n                  if (match.index === regex.lastIndex) {\r\n                    regex.lastIndex++;\r\n                  }\r\n                }\r\n              });\r\n\r\n              // 更新计数器\r\n              if (matches.length === 0) {\r\n                counterElement.textContent = '无匹配项';\r\n                return;\r\n              }\r\n\r\n              counterElement.textContent = `0/${matches.length}`;\r\n\r\n              // 为所有匹配项添加高亮类\r\n              matches.forEach(match => {\r\n                match.node.classList.add('vjs-search-match');\r\n              });\r\n            } catch (error) {\r\n              console.error('搜索错误:', error);\r\n            }\r\n          };\r\n\r\n          // 导航到特定匹配项\r\n          const navigateToMatch = (index) => {\r\n            if (matches.length === 0) return;\r\n\r\n            // 确保索引在有效范围内\r\n            index = Math.max(0, Math.min(matches.length - 1, index));\r\n\r\n            // 移除之前匹配项的当前高亮\r\n            if (currentMatchIndex >= 0 && currentMatchIndex < matches.length) {\r\n              matches[currentMatchIndex].node.classList.remove('vjs-search-current');\r\n            }\r\n\r\n            // 更新当前索引和计数器\r\n            currentMatchIndex = index;\r\n            counterElement.textContent = `${currentMatchIndex + 1}/${matches.length}`;\r\n\r\n            // 高亮当前匹配项\r\n            const currentMatch = matches[currentMatchIndex];\r\n            if (currentMatch) {\r\n              currentMatch.node.classList.add('vjs-search-current');\r\n\r\n              // 确保父节点是展开的\r\n              let parent = currentMatch.node.parentElement;\r\n              while (parent) {\r\n                if (parent.classList && parent.classList.contains('vjs-tree-node')) {\r\n                  // 如果节点是折叠的，点击展开按钮\r\n                  if (!parent.classList.contains('is-expanded')) {\r\n                    const expandBtn = parent.querySelector('.vjs-tree-brackets');\r\n                    if (expandBtn) expandBtn.click();\r\n                  }\r\n                }\r\n                parent = parent.parentElement;\r\n              }\r\n\r\n              // 滚动到匹配项\r\n              currentMatch.node.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          };\r\n\r\n          // 添加搜索输入事件\r\n          let searchTimeout;\r\n          searchInput.addEventListener('input', (e) => {\r\n            // 使用防抖优化性能\r\n            if (searchTimeout) clearTimeout(searchTimeout);\r\n            searchTimeout = setTimeout(() => {\r\n              highlightMatches(e.target.value.trim());\r\n            }, 300);\r\n          });\r\n\r\n          // 添加键盘导航事件\r\n          searchInput.addEventListener('keydown', (e) => {\r\n            if (e.key === 'Enter') {\r\n              e.preventDefault();\r\n              navigateToMatch(e.shiftKey ? currentMatchIndex - 1 : currentMatchIndex + 1);\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加主题切换功能\r\n        const themeButton = document.getElementById(ids.theme);\r\n        if (themeButton) {\r\n          themeButton.addEventListener('click', () => {\r\n            const container = document.querySelector('.json-container');\r\n            if (container) {\r\n              // 切换主题类\r\n              if (container.classList.contains('theme-light')) {\r\n                container.classList.remove('theme-light');\r\n                container.classList.add('theme-dark');\r\n                container.style.backgroundColor = '#1e1e1e';\r\n                isDarkTheme = true;\r\n              } else {\r\n                container.classList.remove('theme-dark');\r\n                container.classList.add('theme-light');\r\n                container.style.backgroundColor = '#fff';\r\n                isDarkTheme = false;\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        // 添加复制功能\r\n        const copyButton = document.getElementById('copy-btn');\r\n        if (copyButton) {\r\n          copyButton.addEventListener('click', () => {\r\n            try {\r\n              const textToCopy = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);\r\n\r\n              // 使用现代Clipboard API\r\n              if (navigator.clipboard && window.isSecureContext) {\r\n                navigator.clipboard.writeText(textToCopy)\r\n                  .then(() => {\r\n                    this.$message.success(this.$t('common.copiedToClipboard'));\r\n                  })\r\n                  .catch(err => {\r\n                    console.error('复制失败:', err);\r\n                    this.$message.error('复制失败');\r\n                  });\r\n              } else {\r\n                // 备用方法\r\n                const textArea = document.createElement('textarea');\r\n                textArea.value = textToCopy;\r\n                document.body.appendChild(textArea);\r\n                textArea.select();\r\n                const successful = document.execCommand('copy');\r\n                document.body.removeChild(textArea);\r\n\r\n                if (successful) {\r\n                  this.$message.success(this.$t('common.copiedToClipboard'));\r\n                } else {\r\n                  this.$message.error(this.$t('common.copyFailed'));\r\n                }\r\n              }\r\n            } catch (err) {\r\n              this.$message.error(this.$t('common.copyFailed'));\r\n            }\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* 覆盖详情弹窗的样式 */\r\n.detail-modal {\r\n  .ant-modal-body {\r\n    padding: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-content {\r\n    margin-top: 12px !important;\r\n    margin-bottom: 12px !important;\r\n  }\r\n\r\n  .ant-modal-confirm-btns {\r\n    margin-top: 16px !important;\r\n    margin-bottom: 8px !important;\r\n  }\r\n\r\n  /* 复制按钮样式 */\r\n  #copy-btn {\r\n    transition: all 0.3s;\r\n\r\n    &:hover {\r\n      color: #40a9ff !important;\r\n      transform: scale(1.1);\r\n    }\r\n\r\n    &:active {\r\n      color: #096dd9 !important;\r\n    }\r\n  }\r\n\r\n  /* 搜索匹配项样式 */\r\n  .vjs-search-match {\r\n    background-color: rgba(255, 255, 0, 0.3);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .vjs-search-current {\r\n    background-color: rgba(255, 165, 0, 0.6);\r\n    box-shadow: 0 0 3px 1px rgba(255, 165, 0, 0.3);\r\n  }\r\n\r\n  /* 主题样式 */\r\n  .json-container.theme-dark {\r\n    .vjs-tree {\r\n      background-color: #1e1e1e !important;\r\n      color: #d4d4d4 !important;\r\n\r\n      .vjs-key {\r\n        color: #9cdcfe !important; /* 浅蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-string {\r\n        color: #ce9178 !important; /* 橙红色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-number {\r\n        color: #b5cea8 !important; /* 浅绿色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-boolean {\r\n        color: #569cd6 !important; /* 蓝色 */\r\n      }\r\n\r\n      .vjs-value.vjs-value-null {\r\n        color: #c586c0 !important; /* 紫色 */\r\n      }\r\n\r\n      .vjs-tree-brackets {\r\n        color: #d4d4d4 !important; /* 浅灰色 */\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 调整内容区域的左侧边距 */\r\n.ant-modal-confirm-body {\r\n  padding-left: 12px !important;\r\n}\r\n\r\n/* 调整内容区域的图标和文本间距 */\r\n.ant-modal-confirm-body > .anticon {\r\n  display: none !important;\r\n}\r\n\r\n/* 调整标题和内容的左侧边距 */\r\n.ant-modal-confirm-body > .ant-modal-confirm-title,\r\n.ant-modal-confirm-body > .ant-modal-confirm-content {\r\n  margin-left: 0 !important;\r\n  padding-left: 0 !important;\r\n}\r\n\r\n/* 调整内容区域的下边距 */\r\n.detail-modal .ant-modal-confirm-content {\r\n  margin-bottom: 8px !important; /* 适当的下边距 */\r\n}\r\n\r\n/* 调整底部按钮区域的上下边距 */\r\n.detail-modal .ant-modal-confirm-btns {\r\n  margin-top: 8px !important; /* 减小上边距 */\r\n  margin-bottom: 4px !important;\r\n  padding-top: 0 !important; /* 移除上内边距 */\r\n  padding-bottom: 0 !important;\r\n  border-top: none !important; /* 移除分隔线 */\r\n}\r\n\r\n/* 调整底部按钮本身的样式 */\r\n.detail-modal .ant-modal-confirm-btns button {\r\n  margin-top: 0 !important;\r\n  margin-bottom: 0 !important;\r\n  padding: 6px 16px !important;\r\n  height: auto !important;\r\n}\r\n</style>\r\n"]}]}