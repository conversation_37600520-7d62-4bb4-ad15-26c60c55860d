import os
from log.logger import log_error, log_info, log_debug
from app.ssh.manager import SSHConnectionManager
from app.core.command_builder import build_remote_commands
from app.core.node_management import NodeManagement
from app.models.node import Node


class NodeService:
    @staticmethod
    def remote_snap(node: Node, proxy_ip: str, db_file: str) -> (bool, str):
        commands = build_remote_commands(node, proxy_ip)
        try:
            with SSHConnectionManager(node).connection() as ssh_client:
                exit_code, output, error = ssh_client.execute_command(commands)
                if exit_code != 0:
                    err_msg = f"Failed to execute the command: {error}"
                    log_error(err_msg)
                    return False, err_msg

                exit_code, output, error = ssh_client.execute_command(
                    "cd /root/.test && nohup sh generated_script.sh > output.txt 2>&1 &&"
                    " cd /root && rm -rf .test && echo exec_success && wait"
                )
                if exit_code != 0:
                    err_msg = f"Failed to execute the script: {error}"
                    log_error(err_msg)
                    return False, err_msg

                if "exec_success" in output:
                    success = NodeService.process_data_files(node, db_file)
                    if success:
                        return True, ""
                    else:
                        err_msg = "Data processing failed"
                        log_error(err_msg)
                        return False, err_msg
                else:
                    err_msg = "Script did not return success flag"
                    log_error(err_msg)
                    return False, err_msg

        except Exception as e:
            err_msg = f"Exception during remote_snap: {str(e)}"
            log_error(err_msg)
            return False, err_msg

    @staticmethod
    def process_data_files(node: Node, db_file: str) -> bool:
        file_paths = NodeService.generate_file_paths(node)
        processed_file_paths = []
        
        for path in file_paths:
            if os.path.exists(path):
                processed_file_paths.append(path)
                log_debug(f"Preparing to Insert Files: {path}")
            else:
                file_type = 'Docker' if 'docker' in path \
                    else 'CRI' if 'crictl' in path \
                    else 'K8s' if 'k8s' in path \
                    else '其他'
                log_debug(f"{file_type} {path} not exits，check whether the pkl file exists.")

        if processed_file_paths:
            result = NodeManagement.handle_sql_insertion(
                processed_file_paths, 
                node.host_name, 
                node.ip,
                db_file
            )
            return all(status == 'success' for status in result.values())
        return True

    @staticmethod
    def generate_file_paths(node: Node):
        return (
            f"cache/uploads/{node.host_name}_{node.ip}_os_package_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_process_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_hardware_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_filesystem_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_port_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_k8s_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_docker_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_crictl_info.pkl",
            f"cache/uploads/{node.host_name}_{node.ip}_agent.log"
        )
