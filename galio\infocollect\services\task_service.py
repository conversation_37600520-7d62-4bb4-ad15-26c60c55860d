import uuid
import time
import threading
import queue
import os

from app.services.node_service import NodeService
from app.models.node import Node
from sqlalchemy.sql.expression import or_

active_tasks = {}


class TaskService:
    def __init__(self, db):
        self.db = db
        self.task_queue = queue.Queue()

    def get_all_nodes(self):
        from datamodel.config_datamodel import HostConfig
        nodes = self.db.query(HostConfig).all()
        result = []
        for node in nodes:
            result.append({
                "host_name": node.host_name,
                "ip": node.ip,
                "ssh_port": node.ssh_port,
                "login_user": node.login_user,
                "login_pwd": node.login_pwd,
                "switch_root_cmd": node.switch_root_cmd,
                "switch_root_pwd": node.switch_root_pwd
            })
        return result

    def start_collect_task(self, targets, proxy_ip):
        """
        Given a list of node IPs (selected by the user) and a proxy IP,
        verify they exist in the db and then start a background collection task.
        """
        from datamodel.config_datamodel import HostConfig
        selected_nodes = self.db.query(HostConfig).filter(or_(*[HostConfig.ip == ip for ip in targets])).all()
        if not selected_nodes:
            raise ValueError("No valid nodes found for the provided targets.")

        task_id = str(uuid.uuid4())
        task = {
            "task_id": task_id,
            "nodes": {},
            "metadata": {
                "overall_status": "running",
                "start_time": time.time(),
                "duration_sec": 0,
                "total_nodes": len(selected_nodes),
                "completed_nodes": 0
            }
        }
        
        # Initialize each node status
        for node in selected_nodes:
            task["nodes"][node.ip] = {
                "status": "pending",
                "host_name": node.host_name,
                "progress": 0,
                "error_detail": None
            }

        active_tasks[task_id] = task

        # 创建后台线程来处理任务
        def process_tasks():
            # thread_count = min(max(len(selected_nodes) // 10, 5), 20)
            thread_count = min(max(len(selected_nodes) // 5, 10), 40)
            threads = []
            
            for _ in range(thread_count):
                thread = threading.Thread(target=self.worker, args=(task_id, proxy_ip))
                thread.daemon = True
                thread.start()
                threads.append(thread)

            # 添加任务到队列
            for db_node in selected_nodes:
                node = Node(
                    host_name=db_node.host_name,
                    ip=db_node.ip,
                    ssh_port=db_node.ssh_port,
                    login_user=db_node.login_user,
                    login_pwd=db_node.login_pwd,
                    switch_root_cmd=db_node.switch_root_cmd,
                    switch_root_pwd=db_node.switch_root_pwd
                )
                self.task_queue.put((task_id, node))

            # 等待所有线程完成
            for thread in threads:
                thread.join()

        # 启动后台处理线程
        background_thread = threading.Thread(target=process_tasks)
        background_thread.daemon = True
        background_thread.start()

        return task_id

    def worker(self, task_id, proxy_ip):
        while True:
            try:
                task_q_item = self.task_queue.get(timeout=60)
                sub_task_id, node = task_q_item
                if sub_task_id != task_id:
                    self.task_queue.put(task_q_item)
                    continue
                
                task = active_tasks[task_id]
                task["nodes"][node.ip]["status"] = "processing"
                task["nodes"][node.ip]["progress"] = 30
                
                try:
                    agent_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                              'application_snapshot_agent.zip')
                    if not os.path.exists(agent_path):
                        raise Exception("application_snapshot_agent.zip not found on server")
                    
                    # 从数据库会话对象中获取数据库文件路径
                    db_file = self.db.bind.engine.url.database
                    res, error_msg = NodeService.remote_snap(node, proxy_ip, db_file)
                    task = active_tasks[task_id]
                    if res:
                        task["nodes"][node.ip]["status"] = "success"
                        task["nodes"][node.ip]["progress"] = 100
                    else:
                        task["nodes"][node.ip]["status"] = "failed"
                        task["nodes"][node.ip]["error_detail"] = error_msg
                    
                    # 更新完成节点数量
                    task["metadata"]["completed_nodes"] += 1
                    
                    # 检查是否所有节点都完成
                    if task["metadata"]["completed_nodes"] == task["metadata"]["total_nodes"]:
                        task["metadata"]["duration_sec"] = time.time() - task["metadata"]["start_time"]
                        task["metadata"]["overall_status"] = "success" if all(
                            info["status"] == "success" for info in task["nodes"].values()
                        ) else "failed"
                
                except Exception as e:
                    task = active_tasks[task_id]
                    task["nodes"][node.ip]["status"] = "failed"
                    # 提供更详细的错误信息
                    error_detail = str(e)                  
                    task["nodes"][node.ip]["error_detail"] = error_detail
                    task["metadata"]["completed_nodes"] += 1
                    
                    # 如果是最后一个节点，更新整体状态
                    if task["metadata"]["completed_nodes"] == task["metadata"]["total_nodes"]:
                        task["metadata"]["duration_sec"] = time.time() - task["metadata"]["start_time"]
                        task["metadata"]["overall_status"] = "failed"
                
                finally:
                    self.task_queue.task_done()
                    
            except queue.Empty:
                # 队列超时，检查是否还有未完成的任务
                task = active_tasks[task_id]
                if task["metadata"]["completed_nodes"] == task["metadata"]["total_nodes"]:
                    break
                continue
