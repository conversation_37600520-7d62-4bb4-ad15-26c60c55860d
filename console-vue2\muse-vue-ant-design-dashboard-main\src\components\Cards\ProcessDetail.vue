<template>
    <div>
        <template v-if="currentProject">
            <div style="margin-bottom: 16px;">
                <a-button
                    :class="`bg-${sidebarColor}`"
                    icon="arrow-left"
                    @click="goToProcessList"
                    style="margin-right: 16px; color: white"
                >
                    Back to Process
                </a-button>
                <a-button
                    :class="`bg-${sidebarColor}`"
                    icon="arrow-left"
                    @click="goBack"
                    style="margin-right: 16px; color: white"
                >
                    Back to Port
                </a-button>
            </div>
            <a-card :bordered="false" class="header-solid h-full" :bodyStyle="{paddingTop: '16px', paddingBottom: '16px'}">
                <template #title>
                    <div class="card-header-wrapper">
                        <div class="header-wrapper">
                            <div class="logo-wrapper">
                                <a-icon
                                    type="profile"
                                    style="font-size: 18px"
                                    :class="`text-${sidebarColor}`"
                                />
                            </div>
                            <h5 class="card-title">Process details for pid {{ pid }}</h5>
                        </div>
                    </div>
                </template>
                <a-row :gutter="[24, 24]">
                    <a-col :span="24">
                        <div class="process-details">
                            <div v-for="field in orderedFields" :key="field" class="process-field">
                                <a-tooltip placement="right" :title="fieldDescriptions[field + ':']">
                                    <span class="field-label">{{field}}:</span>
                                    <span class="field-value">{{processDetails?.[field] || 'N/A'}}</span>
                                </a-tooltip>
                            </div>
                        </div>
                    </a-col>
                </a-row>
            </a-card>
        </template>
        <template v-else>
            <a-alert
                message="No Project Selected"
                description="Please select a project first to view process details."
                type="warning"
                show-icon
            >
                <template #description>
                    Please select a project first to view process details.
                    <a-button type="link" @click="goToProjects">Go to Projects</a-button>
                </template>
            </a-alert>
        </template>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';

export default {
    data() {
        return {
            pid: this.$route.params.pid,
            processDetails: null,
            fromPage: this.$route.query.page || 1,
            orderedFields: [
                'pid', 'uid', 'gid', 'cmdline', 'state',
                'exe', 'cwd', 'capability', 'environ', 'memory_maps'
            ],
            fieldDescriptions: {
                'pid:': '进程唯一标识符 - 每个进程的唯一标识号',
                'uid:': '用户标识符 - 启动该进程的用户的唯一标识符',
                'gid:': '组标识符 - 启动该进程的用户所属组的唯一标识符',
                'cmdline:': '命令行 - 启动进程时使用的完整命令行参数',
                'state:': '进程状态 - 进程当前的运行状态',
                'exe:': '可执行文件 - 获取指定进程的可执行文件路径',
                'cwd:': '工作目录 - 进程当前工作的目录',
                'capability:': '进程能力 - 与进程相关的能力信息，指示该进程具有哪些特权',
                'environ:': '环境变量 - 与该进程相关联的环境变量列表',
                'memory_maps:': '内存映射 - 该进程使用的内存区域的信息'
            }
        };
    },
    computed: {
        ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
    },
    watch: {
        currentProject: {
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    this.fetchProcessDetails();
                }
            }
        },
        selectedNodeIp: {
            immediate: true,
            handler(newVal) {
                if (newVal && this.currentProject) {
                    this.fetchProcessDetails();
                }
            }
        }
    },
    methods: {
        async fetchProcessDetails() {
            if (!this.selectedNodeIp || !this.currentProject) {
                console.error('Node IP or project is not defined');
                return;
            }
            try {
                const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {
                    params: {
                        pid: this.pid,
                        fields: this.orderedFields.join(','),
                        dbFile: this.currentProject
                    }
                });
                this.processDetails = response.data;
            } catch (error) {
                console.error('Error fetching process details:', error);
                this.$message.error('Failed to fetch process details');
            }
        },
        goBack() {
            this.$router.push({
                name: 'Port',
                query: { page: this.fromPage }
            });
        },
        goToProcessList() {
            // 确保在返回前已经设置了lastViewedPid
            const currentPid = this.$route.params.pid;

            // 使用字符串类型的PID，确保类型匹配
            this.$store.dispatch('processList/updateLastViewedPid', currentPid.toString());

            // 返回进程列表页面
            this.$router.push({ name: 'Process' });
        },
        goToProjects() {
            this.$router.push('/projects');
        }
    },
};
</script>


<style scoped lang="scss">
.process-details {
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    word-break: break-all;
    background-color: #ffffff;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
}

.process-field {
    margin: 8px 0;
    line-height: 1.5;

    .field-label {
        color: #d10d7d;
        font-size: 0.95em;
        min-width: 80px;
        display: inline-block;
        margin-right: 8px;
    }

    .field-value {
        word-break: break-all;
    }
}

.font-semibold {
    display: block;
    width: 100%;
    padding: 10px 15px;
    background: #fff;
    color: #333;
    font-size: 16px;
    font-family: 'Arial', sans-serif;
    border-radius: 6px;
    margin-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: none;
}

.field-color {
    color: red !important;
}

.card-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-wrapper {
    display: flex;
    align-items: center;
}

.logo-wrapper {
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}
</style>
