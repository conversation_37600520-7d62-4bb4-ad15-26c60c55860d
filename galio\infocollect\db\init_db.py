import os
import datetime
import sqlite3
import threading
import gc

from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from log.logger import log_debug, log_error, log_info, log_warning

Base = declarative_base()

DB_DIR = os.path.dirname(os.path.abspath(__file__))

_db_connections = {}
_engine_cache = {}
_connection_lock = threading.Lock()


def force_close_db_connections(db_path):
    """强制关闭数据库连接"""
    try:
        temp_conn = sqlite3.connect(db_path)
        temp_conn.close()

        if db_path in _db_connections:
            try:
                _db_connections[db_path].remove()
            except:
                pass
            try:
                _db_connections[db_path].close()
            except:
                pass
            del _db_connections[db_path]

        if db_path in _engine_cache:
            try:
                _engine_cache[db_path].dispose()
            except:
                pass
            del _engine_cache[db_path]

        gc.collect()
    except Exception as e:
        log_error(f"Error in force_close_db_connections: {e}")


def close_all_connections(db_file: str = None):
    with _connection_lock:
        if db_file:
            db_path = os.path.join('db', db_file)
            force_close_db_connections(db_path)
        else:
            for db_file in list(_db_connections.keys()):
                db_path = os.path.join('db', db_file)
                force_close_db_connections(db_path)


@contextmanager
def get_db(db_file: str = 'testcase_corpus.db'):
    """获取数据库连接，使用上下文管理器模式

    Args:
        db_file: 数据库文件名，默认为测试用例数据库

    Returns:
        SQLAlchemy会话对象
    """
    if not os.path.isabs(db_file):
        projects_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'db')
        db_file = os.path.join(projects_dir, db_file)

    with _connection_lock:
        if db_file not in _engine_cache:
            _engine_cache[db_file] = create_engine(
                f'sqlite:///{db_file}',
                connect_args={"check_same_thread": False},
                pool_size=10,
                max_overflow=20
            )

        session_local = sessionmaker(autocommit=False, autoflush=False, bind=_engine_cache[db_file])
        db = session_local()

    try:
        yield db
    finally:
        db.close()


def get_database_url(db_file):
    return f'sqlite:///{db_file}'


def generate_new_db_file():
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    db_file = os.path.join(DB_DIR, f'node_monitor_{timestamp}.db')
    return db_file


def initialize_database(db_file=None):
    try:
        log_info(f"===========Initializing database at path: {db_file}===========")
        if db_file is None:
            db_file = generate_new_db_file()

        log_info(f"Initializing database at path: {db_file}")

        os.makedirs(os.path.dirname(db_file), exist_ok=True)
        database_url = get_database_url(db_file)
        engine = create_engine(database_url, connect_args={"check_same_thread": False})

        Base.metadata.create_all(bind=engine)

        base_dir = os.path.dirname(os.path.abspath(__file__))
        schemas = [
            os.path.join(base_dir, 'schema', 'host_config_schema.sql'),
            os.path.join(base_dir, 'schema', 'process_schema.sql'),
            os.path.join(base_dir, 'schema', 'os_package_schema.sql'),
            os.path.join(base_dir, 'schema', 'hardware_schema.sql'),
            os.path.join(base_dir, 'schema', 'filesystem_schema.sql'),
            os.path.join(base_dir, 'schema', 'kubernetes_schema.sql'),
            os.path.join(base_dir, 'schema', 'docker_schema.sql'),
            os.path.join(base_dir, 'schema', 'crictl_schema.sql'),
            os.path.join(base_dir, 'schema', 'port_schema.sql'),
            os.path.join(base_dir, 'schema', 'repository_schema.sql'),
            os.path.join(base_dir, 'schema', 'agent_log_schema.sql'),
        ]

        session_maker = sessionmaker(bind=engine)
        db = session_maker()
        try:
            for schema in schemas:
                if os.path.exists(schema):
                    sql_script = load_sql_script(schema)
                    statements = sql_script.split(';')

                    for statement in statements:
                        statement = statement.strip()
                        if statement:
                            try:
                                db.execute(text(statement))
                            except Exception as e:
                                log_error(f"Error executing SQL: {str(e)}")
                                raise

                    db.commit()
        finally:
            db.close()

        log_info(f"===========Initializing database finished: {db_file}===========")
        return db_file

    except Exception as e:
        log_error(f"Database initialization failed: {str(e)}")
        raise


def load_sql_script(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            return content
    except Exception as e:
        log_error(f"Error loading SQL script {filename}: {str(e)}")
        raise


def init_project_database():
    try:
        db_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'db')
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
        main_db_path = os.path.join(db_dir, 'project.db')

        db_engine = create_engine(f'sqlite:///{main_db_path}', connect_args={"check_same_thread": False})
        Base.metadata.create_all(db_engine)

        # 加载主数据库的SQL定义文件
        base_dir = os.path.dirname(os.path.abspath(__file__))
        main_schema = os.path.join(base_dir, 'schema', 'main_db_schema.sql')

        if os.path.exists(main_schema):
            session_maker = sessionmaker(bind=db_engine)
            db = session_maker()
            try:
                sql_script = load_sql_script(main_schema)
                statements = sql_script.split(';')

                for statement in statements:
                    statement = statement.strip()
                    if statement:
                        try:
                            db.execute(text(statement))
                        except Exception as e:
                            log_error(f"Error executing SQL: {str(e)}")
                            raise

                db.commit()
            finally:
                db.close()
        else:
            log_error(f"Main database schema file not found: {main_schema}")
            raise FileNotFoundError(f"Main database schema file not found: {main_schema}")

        log_info('Successfully initialized the project database.')
        return db_engine
    except Exception as e:
        log_error(f'Failed to initialize project database: {str(e)}')
        raise


def init_testcase_database():
    """初始化测试用例数据库并导入数据"""
    try:
        db_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'db')
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
        testcase_db_path = os.path.join(db_dir, 'testcase_corpus.db')

        db_engine = create_engine(f'sqlite:///{testcase_db_path}', connect_args={"check_same_thread": False})

        base_dir = os.path.dirname(os.path.abspath(__file__))
        testcase_schema = os.path.join(base_dir, 'schema', 'testcase_schema.sql')

        if os.path.exists(testcase_schema):
            session_maker = sessionmaker(bind=db_engine)
            db = session_maker()
            try:
                sql_script = load_sql_script(testcase_schema)
                statements = sql_script.split(';')

                for statement in statements:
                    statement = statement.strip()
                    if statement:
                        try:
                            db.execute(text(statement))
                        except Exception as e:
                            log_error(f"Error executing SQL: {str(e)}")

                db.commit()

                # 导入测试用例数据
                try:
                    from infocollect.services.testcase_service import TestcaseService

                    excel_path = os.path.join(
                        os.path.dirname(os.path.dirname(__file__)),
                        'cache',
                        'testcase',
                        'SecurityLAB_Test_Pretest.xls'
                    )

                    if os.path.exists(excel_path):
                        testcase_service = TestcaseService()
                        testcase_service.import_testcases_from_excel(excel_path)
                    else:
                        log_warning(f"Testcase Excel file not found: {excel_path}")
                except Exception as e:
                    log_error(f"Error importing testcases: {str(e)}")
            finally:
                db.close()
        else:
            log_error(f"Testcase database schema file not found: {testcase_schema}")

        log_info('Successfully initialized the testcase database.')
        return db_engine
    except Exception as e:
        log_error(f'Failed to initialize testcase database: {str(e)}')
        raise
