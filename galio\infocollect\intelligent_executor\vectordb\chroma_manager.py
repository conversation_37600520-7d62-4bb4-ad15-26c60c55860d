"""
ChromaDB向量数据库管理器
提供测试用例的向量化存储和搜索功能
"""

import os
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class ChromaVectorManager:
    """ChromaDB向量数据库管理器"""
    
    def __init__(self, db_path: str = None, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化ChromaDB管理器
        
        Args:
            db_path: 数据库存储路径
            model_name: 嵌入模型名称
        """
        # 设置数据库路径
        if db_path is None:
            current_dir = Path(__file__).parent.parent.parent
            db_path = current_dir / "cache" / "vectordb" / "chroma"
        
        self.db_path = Path(db_path)
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 初始化嵌入模型
        self.model_name = model_name
        self.embedding_model = None
        self._init_embedding_model()
        
        # 集合名称
        self.collection_name = "testcase_collection"
        self.collection = None
        
        logger.info(f"ChromaVectorManager initialized with db_path: {self.db_path}")
    
    def _init_embedding_model(self):
        """初始化嵌入模型"""
        try:
            # 尝试从本地加载模型
            local_model_path = Path(__file__).parent.parent.parent / "cache" / "models" / self.model_name
            if local_model_path.exists():
                self.embedding_model = SentenceTransformer(str(local_model_path))
                logger.info(f"Loaded local embedding model from: {local_model_path}")
            else:
                # 从HuggingFace下载模型
                self.embedding_model = SentenceTransformer(self.model_name)
                logger.info(f"Downloaded embedding model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise
    
    def get_or_create_collection(self) -> chromadb.Collection:
        """获取或创建集合"""
        try:
            # 尝试获取现有集合
            self.collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Retrieved existing collection: {self.collection_name}")
        except Exception:
            # 创建新集合
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "测试用例向量集合"}
            )
            logger.info(f"Created new collection: {self.collection_name}")
        
        return self.collection
    
    def create_testcase_text(self, testcase: Dict[str, Any]) -> str:
        """
        将测试用例字段组合成文本用于向量化
        
        Args:
            testcase: 测试用例数据
            
        Returns:
            组合后的文本
        """
        parts = []
        
        # 提取关键字段
        name = testcase.get("Testcase_Name", "")
        level = testcase.get("Testcase_Level", "")
        prepare_condition = testcase.get("Testcase_PrepareCondition", "")
        test_steps = testcase.get("Testcase_TestSteps", "")
        expected_result = testcase.get("Testcase_ExpectedResult", "")
        
        # 组合文本
        if name:
            parts.append(f"名称: {name}")
        if level:
            parts.append(f"级别: {level}")
        if prepare_condition:
            parts.append(f"准备条件: {prepare_condition}")
        if test_steps:
            parts.append(f"测试步骤: {test_steps}")
        if expected_result:
            parts.append(f"预期结果: {expected_result}")
        
        return " | ".join(parts)
    
    def insert_testcases(self, testcases: List[Dict[str, Any]], clear_before_insert: bool = False) -> Dict[str, Any]:
        """
        插入测试用例到向量数据库
        
        Args:
            testcases: 测试用例列表
            clear_before_insert: 是否在插入前清空集合
            
        Returns:
            插入结果
        """
        try:
            collection = self.get_or_create_collection()
            
            # 清空集合（如果需要）
            if clear_before_insert:
                self.client.delete_collection(self.collection_name)
                collection = self.get_or_create_collection()
                logger.info("Cleared collection before inserting new data")
            
            # 准备数据
            documents = []
            metadatas = []
            ids = []
            
            for testcase in testcases:
                testcase_id = testcase.get("Testcase_Number") or testcase.get("id")
                if not testcase_id:
                    continue
                
                # 创建文档文本
                document_text = self.create_testcase_text(testcase)
                
                # 准备元数据
                metadata = {
                    "Testcase_Number": testcase.get("Testcase_Number", ""),
                    "Testcase_Name": testcase.get("Testcase_Name", ""),
                    "Testcase_Level": testcase.get("Testcase_Level", ""),
                    "Testcase_PrepareCondition": testcase.get("Testcase_PrepareCondition", ""),
                    "Testcase_TestSteps": testcase.get("Testcase_TestSteps", ""),
                    "Testcase_ExpectedResult": testcase.get("Testcase_ExpectedResult", "")
                }
                
                documents.append(document_text)
                metadatas.append(metadata)
                ids.append(str(testcase_id))
            
            if not documents:
                return {"success": False, "error": "No valid testcase data found"}
            
            # 批量插入
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Successfully inserted {len(documents)} testcases")
            
            return {
                "success": True,
                "inserted_count": len(documents),
                "collection_name": self.collection_name
            }
            
        except Exception as e:
            logger.error(f"Failed to insert testcases: {e}")
            return {"success": False, "error": str(e)}
    
    def search_testcases(self, query: str, top_k: int = 10, score_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        搜索相似的测试用例
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            score_threshold: 相似度阈值
            
        Returns:
            搜索结果列表
        """
        try:
            collection = self.get_or_create_collection()
            
            # 执行搜索
            results = collection.query(
                query_texts=[query],
                n_results=top_k
            )
            
            # 处理结果
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # 计算相似度分数 (ChromaDB返回的是距离，需要转换为相似度)
                    similarity = 1 - distance
                    
                    # 应用阈值过滤
                    if similarity >= score_threshold:
                        result = {
                            "Testcase_Number": metadata.get("Testcase_Number", ""),
                            "Testcase_Name": metadata.get("Testcase_Name", ""),
                            "Testcase_Level": metadata.get("Testcase_Level", ""),
                            "Testcase_PrepareCondition": metadata.get("Testcase_PrepareCondition", ""),
                            "Testcase_TestSteps": metadata.get("Testcase_TestSteps", ""),
                            "Testcase_ExpectedResult": metadata.get("Testcase_ExpectedResult", ""),
                            "similarity": similarity,
                            "document": doc
                        }
                        search_results.append(result)
            
            logger.info(f"Search completed: {len(search_results)} results for query '{query}'")
            return search_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            collection = self.get_or_create_collection()
            count = collection.count()
            
            return {
                "collection_name": self.collection_name,
                "total_documents": count,
                "status": "healthy"
            }
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {
                "collection_name": self.collection_name,
                "total_documents": 0,
                "status": "error",
                "error": str(e)
            }
    
    def clear_collection(self) -> bool:
        """清空集合"""
        try:
            self.client.delete_collection(self.collection_name)
            logger.info(f"Collection {self.collection_name} cleared")
            return True
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            return False
