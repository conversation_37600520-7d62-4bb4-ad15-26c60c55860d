from flask import Flask, request, jsonify
from pymilvus import MilvusClient
import os

app = Flask(__name__)

# 不在模块级别初始化客户端，而是在需要时创建
def get_milvus_client():
    """获取 Milvus 客户端实例"""
    try:
        # 使用新版本pymilvus的正确URI格式连接Milvus Lite
        return MilvusClient(uri="./milvus_demo.db")
    except Exception as e:
        print(f"Failed to connect to Milvus: {e}")
        raise

@app.route('/insert', methods=['POST'])
def insert_vectors():
    client = None
    try:
        # 在函数内部创建客户端
        client = get_milvus_client()
        
        data = request.json
        vectors = data.get("vectors")
        ids = data.get("ids")

        if not vectors or not ids:
            return jsonify({"status": "error", "message": "Missing vectors or ids"}), 400

        if len(vectors) != len(ids):
            return jsonify({"status": "error", "message": "Number of vectors and ids must match"}), 400

        collection_name = "example_collection"
        dim = len(vectors[0])

        # 检查集合是否存在，如果不存在则创建
        if not client.has_collection(collection_name):
            # 使用简化的集合创建方法
            client.create_collection(
                collection_name=collection_name,
                dimension=dim,
                metric_type="L2",
                auto_id=False  # 使用用户提供的ID
            )
            print(f"Created collection '{collection_name}' with dimension {dim}")

        # 修正数据格式：每个实体作为一个字典
        entities = []
        for i in range(len(ids)):
            entities.append({
                "id": ids[i],
                "vector": vectors[i]
            })
        
        # 插入数据
        result = client.insert(collection_name, entities)
        print(f"Inserted {len(ids)} entities into collection '{collection_name}'")

        return jsonify({
            "status": "success", 
            "inserted_count": len(ids),
            "collection_name": collection_name
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        # 确保客户端被正确关闭
        if client:
            try:
                client.close()
            except:
                pass

@app.route('/search', methods=['POST'])
def search_vectors():
    client = None
    try:
        client = get_milvus_client()
        
        data = request.json
        query_vector = data.get("query_vector")
        top_k = data.get("top_k", 5)
        
        collection_name = "example_collection"
        
        if not client.has_collection(collection_name):
            return jsonify({"status": "error", "message": f"Collection '{collection_name}' does not exist"}), 404
        
        # 执行向量搜索
        search_results = client.search(
            collection_name=collection_name,
            data=[query_vector],
            limit=top_k,
            output_fields=["id"]
        )
        
        results = []
        for hits in search_results:
            for hit in hits:
                results.append({
                    "id": hit["id"],
                    "distance": hit["distance"]
                })
        
        return jsonify({
            "status": "success",
            "results": results
        })
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass

@app.route('/status', methods=['GET'])
def get_status():
    client = None
    try:
        client = get_milvus_client()
        collection_name = "example_collection"
        
        if client.has_collection(collection_name):
            # 获取集合统计信息
            stats = client.get_collection_stats(collection_name)
            return jsonify({
                "status": "success",
                "collection_exists": True,
                "collection_name": collection_name,
                "stats": stats
            })
        else:
            return jsonify({
                "status": "success",
                "collection_exists": False,
                "collection_name": collection_name
            })
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if client:
            try:
                client.close()
            except:
                pass

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "service": "Milvus Lite Service"})


if __name__ == '__main__':
    print("Starting Milvus Lite Service...")
    print("Available endpoints:")
    print("  POST /insert - Insert vectors")
    print("  POST /search - Search similar vectors")
    print("  GET /status - Get collection status")
    print("  GET /health - Health check")
    
    # 重要：禁用 Flask 的自动重载功能来避免数据库锁冲突
    app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)