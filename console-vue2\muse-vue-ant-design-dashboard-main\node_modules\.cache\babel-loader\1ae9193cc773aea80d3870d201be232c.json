{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue", "mtime": 1751874855810}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "name", "props", "visible", "type", "Boolean", "default", "testcase", "Object", "fetchDetails", "data", "loading", "detailedTestcase", "watch", "newVal", "fetchTestcaseDetails", "computed", "currentTestcase", "methods", "Testcase_Number", "response", "get", "similarity", "error", "console", "$message", "handleClose", "$emit", "getLevelColor", "level", "colors", "getSimilarityColor"], "sources": ["src/components/Widgets/TestCaseDetailModal.vue"], "sourcesContent": ["<template>\n  <a-modal\n    :visible=\"visible\"\n    title=\"测试用例详情\"\n    width=\"800px\"\n    :footer=\"null\"\n    @cancel=\"handleClose\"\n  >\n    <a-descriptions v-if=\"testcase\" bordered>\n      <a-descriptions-item label=\"用例编号\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_Number }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"用例名称\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_Name }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"用例级别\" :span=\"3\">\n        <a-tag :color=\"getLevelColor(testcase.Testcase_Level)\">\n          {{ testcase.Testcase_Level }}\n        </a-tag>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"相似度\" :span=\"3\" v-if=\"testcase.similarity !== undefined\">\n        <a-progress\n          :percent=\"Math.round(testcase.similarity * 100)\"\n          size=\"small\"\n          :stroke-color=\"getSimilarityColor(testcase.similarity)\"\n        />\n        <span style=\"margin-left: 8px;\">{{ (testcase.similarity * 100).toFixed(1) }}%</span>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"准备条件\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_PrepareCondition }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"测试步骤\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_TestSteps }}\n        </div>\n      </a-descriptions-item>\n      <a-descriptions-item label=\"预期结果\" :span=\"3\">\n        <div class=\"testcase-content\">\n          {{ testcase.Testcase_ExpectedResult }}\n        </div>\n      </a-descriptions-item>\n    </a-descriptions>\n  </a-modal>\n</template>\n\n<script>\nimport axios from '@/api/axiosInstance';\n\nexport default {\n  name: 'TestCaseDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    testcase: {\n      type: Object,\n      default: null\n    },\n    // 是否需要通过API获取详细信息（当传入的testcase只有基本信息时）\n    fetchDetails: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      detailedTestcase: null\n    };\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal && this.fetchDetails && this.testcase && !this.detailedTestcase) {\n        this.fetchTestcaseDetails();\n      }\n    },\n    testcase() {\n      this.detailedTestcase = null;\n    }\n  },\n  computed: {\n    currentTestcase() {\n      return this.detailedTestcase || this.testcase;\n    }\n  },\n  methods: {\n    async fetchTestcaseDetails() {\n      if (!this.testcase || !this.testcase.Testcase_Number) return;\n      \n      this.loading = true;\n      try {\n        const response = await axios.get(`/api/testcase/${this.testcase.Testcase_Number}`);\n        this.detailedTestcase = {\n          ...response.data,\n          // 保留原有的相似度信息（如果有的话）\n          similarity: this.testcase.similarity\n        };\n      } catch (error) {\n        console.error('获取测试用例详情失败:', error);\n        this.$message.error('获取测试用例详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    handleClose() {\n      this.$emit('close');\n      this.detailedTestcase = null;\n    },\n    \n    getLevelColor(level) {\n      const colors = {\n        'level 0': 'red',\n        'level 1': 'orange', \n        'level 2': 'green',\n        'level 3': 'blue',\n        'level 4': 'purple',\n        'P0': 'red',\n        'P1': 'orange',\n        'P2': 'blue', \n        'P3': 'green',\n        'P4': 'gray'\n      };\n      return colors[level] || 'default';\n    },\n    \n    getSimilarityColor(similarity) {\n      if (similarity >= 0.8) return '#52c41a'; // 绿色\n      if (similarity >= 0.6) return '#faad14'; // 橙色\n      return '#f5222d'; // 红色\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.testcase-content {\n  white-space: pre-wrap;\n  word-break: break-word;\n  max-height: 300px;\n  overflow-y: auto;\n  padding: 12px;\n  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.75);\n  background-color: #f9f9f9;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n}\n</style>\n"], "mappings": "AAoDA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,YAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAI,KAAA;IACA;MACAC,OAAA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACAV,QAAAW,MAAA;MACA,IAAAA,MAAA,SAAAL,YAAA,SAAAF,QAAA,UAAAK,gBAAA;QACA,KAAAG,oBAAA;MACA;IACA;IACAR,SAAA;MACA,KAAAK,gBAAA;IACA;EACA;EACAI,QAAA;IACAC,gBAAA;MACA,YAAAL,gBAAA,SAAAL,QAAA;IACA;EACA;EACAW,OAAA;IACA,MAAAH,qBAAA;MACA,UAAAR,QAAA,UAAAA,QAAA,CAAAY,eAAA;MAEA,KAAAR,OAAA;MACA;QACA,MAAAS,QAAA,SAAApB,KAAA,CAAAqB,GAAA,uBAAAd,QAAA,CAAAY,eAAA;QACA,KAAAP,gBAAA;UACA,GAAAQ,QAAA,CAAAV,IAAA;UACA;UACAY,UAAA,OAAAf,QAAA,CAAAe;QACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAZ,OAAA;MACA;IACA;IAEAe,YAAA;MACA,KAAAC,KAAA;MACA,KAAAf,gBAAA;IACA;IAEAgB,cAAAC,KAAA;MACA,MAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,MAAA,CAAAD,KAAA;IACA;IAEAE,mBAAAT,UAAA;MACA,IAAAA,UAAA;MACA,IAAAA,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}