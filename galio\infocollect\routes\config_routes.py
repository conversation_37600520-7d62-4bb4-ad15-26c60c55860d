from flask import Blueprint, request, jsonify, send_file
from services.config_service import HostConfigService
from routes.database_wraps import with_database_session
import os
import csv
from werkzeug.utils import secure_filename
from pathlib import Path
import pandas as pd

bp = Blueprint('config', __name__)


@bp.route('/', methods=['GET'])
@with_database_session
def get_hosts(db):
    detail = request.args.get('detail', 'false').lower() == 'true'
    try:
        host_service = HostConfigService(db)
        hosts = host_service.get_all_hosts(detail=detail)
        return jsonify(hosts)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/', methods=['POST'])
@with_database_session
def save_host_config(db):
    try:
        data = request.get_json()
        hosts_data = data.get('hosts', [])
        
        if not isinstance(hosts_data, list):
            return jsonify({"error": "Invalid data format"}), 400

        host_service = HostConfigService(db)
        host_service.save_hosts(hosts_data)
        return jsonify({"message": "Operation completed"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/<int:host_id>', methods=['DELETE'])
@with_database_session
def delete_host(host_id, db):
    try:
        host_service = HostConfigService(db)
        host_service.delete_host(host_id)
        return jsonify({"message": "删除成功"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/batch-delete', methods=['POST'])
@with_database_session
def batch_delete_hosts(db):
    try:
        data = request.get_json()
        host_ids = data.get('ids', [])
        
        host_service = HostConfigService(db)
        host_service.batch_delete_hosts(host_ids)
        return jsonify({"message": "Hosts deleted successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/template', methods=['GET'])
def download_template():
    # 获取项目根目录
    root_dir = Path(__file__).parent.parent
    template_path = os.path.join(root_dir, 'config', 'hosts.csv')
    
    if not os.path.exists(template_path):
        return jsonify({"error": "Template file not found"}), 404
        
    return send_file(template_path, as_attachment=True, download_name='hosts_template.csv')


@bp.route('/upload', methods=['POST'])
@with_database_session
def upload_hosts(db):
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        if not file.filename.endswith(('.csv', '.xlsx')):
            return jsonify({"error": "Invalid file format. Only CSV and XLSX files are supported"}), 400

        required_fields = ['host_name', 'ip', 'ssh_port', 'login_user', 'login_pwd']
        hosts_data = []

        host_service = HostConfigService(db)
        
        try:
            if file.filename.endswith('.csv'):
                df = pd.read_csv(file)
            else:
                df = pd.read_excel(file)

            # 验证必要字段
            if not all(field in df.columns for field in required_fields):
                return jsonify({
                    "error": f"File must contain these fields: {', '.join(required_fields)}"
                }), 400

            df = df.fillna('')
            hosts_data = df.to_dict('records')

            # 验证必要字段的值
            for row in hosts_data:
                if not all(row.get(field) for field in required_fields):
                    return jsonify({
                        "error": f"These fields cannot be empty: {', '.join(required_fields)}"
                    }), 400

                if 'ssh_port' in row:
                    row['ssh_port'] = str(row['ssh_port'])

            saved_hosts = host_service.save_hosts(hosts_data)
            return jsonify({
                "message": f"Successfully processed {len(saved_hosts)} hosts. "
                          f"Existing IPs will be updated, new IPs will be added.",
                "data": saved_hosts
            }), 200

        except pd.errors.EmptyDataError:
            return jsonify({"error": "The file is empty"}), 400
        except Exception as e:
            return jsonify({"error": f"Error processing file: {str(e)}"}), 400

    except Exception as e:
        return jsonify({"error": str(e)}), 500
