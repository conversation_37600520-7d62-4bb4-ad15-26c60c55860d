from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class ProcessSnapshot(Base):
    __tablename__ = 'process_snapshot'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'))
    pid = Column(Integer)
    uid = Column(String)
    gid = Column(String)
    cmdline = Column(String)
    state = Column(String)
    exe = Column(String)
    cwd = Column(String)
    capability = Column(String)
    environ = Column(String)
    memory_maps = Column(String)

    host = relationship("HostConfig", back_populates="process_snapshot")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'pid': self.pid,
            'uid': self.uid,
            'gid': self.gid,
            'cmdline': self.cmdline,
            'state': self.state,
            'exe': self.exe,
            'cwd': self.cwd,
            'capability': self.capability,
            'environ': self.environ,
            'memory_maps': self.memory_maps
        }
