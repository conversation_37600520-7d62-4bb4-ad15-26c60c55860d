import paramiko
from log.logger import log_error, log_warning, log_info
import time
from app.ssh.client import SSHClient


class SFTPClient:
    def __init__(self, node):
        self.node = node
        self.sftp_client = None
        self.transport = None
        self.ssh_client = SSHClient(node)
        self.original_config = None
        self.config_backup_path = None

    def connect(self):
        """建立SFTP连接"""
        try:
            # 确保SSHClient已连接
            if not self.ssh_client.is_connected():
                self.ssh_client.connect()

                # 配置允许root SFTP访问
            self._setup_root_sftp_access()

            # 使用root建立SFTP连接
            self.transport = paramiko.Transport((self.node.ip, self.node.ssh_port))
            root_password = self.node.switch_root_pwd if self.node.login_user != 'root' else self.node.login_pwd
            self.transport.connect(username='root', password=root_password)

            self.sftp_client = paramiko.SFTPClient.from_transport(self.transport)

        except Exception as e:
            log_error(f"SFTP connection failed for {self.node.host_name}:  {str(e)}")
            self.cleanup()
            raise

    def _setup_root_sftp_access(self):
        """配置允许root SFTP访问"""
        try:
            # 确保SSHClient已连接
            if not self.ssh_client.is_connected():
                self.ssh_client.connect()

                # 备份配置
            exit_code, output, error = self.ssh_client.execute_command(
                'cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak'
            )
            if exit_code != 0:
                raise Exception(f"Failed to backup sshd_config: {error}")

            # 修改配置
            commands = [
                'sed -i "s/^#*PermitRootLogin.*/PermitRootLogin yes/" /etc/ssh/sshd_config',
                'sed -i "s/^#*Subsystem.*sftp.*/Subsystem sftp internal-sftp/" /etc/ssh/sshd_config',
                'systemctl restart sshd',
                'sleep 6'
            ]

            for cmd in commands:
                exit_code, output, error = self.ssh_client.execute_command(cmd)
                if exit_code != 0:
                    raise Exception(f"Failed to execute command {cmd}: {error}")

            # 等待SSHD服务启动
            # max_retries = 5
            # retry_delay = 5
            # for attempt in range(max_retries):
            #     exit_code, output, error = self.ssh_client.execute_command('systemctl is-active sshd')
            #     print("==========exit_code=============", exit_code)
            #     print("==========output=============", output)
            #     print("==========error=============", error)
            #     if exit_code == 0:
            #         output_lines = output.strip().split('\n')
            #         result = output_lines[-1].strip() if output_lines else ''
            #         print("!!!!!!!!result!!!", result)
            #         import re
            #         if re.search(r'active.*active', output, re.IGNORECASE):
            #             log_info("SSHD service is active. Proceeding...")
            #             break
            #     if attempt < max_retries - 1:
            #         time.sleep(retry_delay)
            #         print(f"Waiting for SSHD to start... Attempt {attempt + 1} of {max_retries}")
            #     else:
            #         raise Exception("sshd did not become active after restart")

        except Exception as e:
            log_error(f"Failed to setup root SFTP access: {str(e)}")
            raise

    def cleanup(self):
        """清理连接和配置"""
        try:
            # 其他清理逻辑...
            pass
        finally:
            # 恢复原始sshd配置
            if not self.ssh_client.is_connected():
                self.ssh_client.connect()

                # 检查备份文件是否存在
            exit_code, output, error = self.ssh_client.execute_command(
                'ls /etc/ssh/sshd_config.bak'
            )
            if exit_code == 0:
                log_info("Backup configuration file exists. Proceeding with restore.")
                # 执行恢复命令
                exit_code, output, error = self.ssh_client.execute_command(
                    'mv -f /etc/ssh/sshd_config.bak  /etc/ssh/sshd_config && systemctl restart sshd'
                )
                if exit_code == 0:
                    log_info("Successfully recover original sshd configuration.")
                else:
                    log_warning(f"Failed to restore sshd config: {error}")
            else:
                log_warning("Backup configuration file does not exist. Skipping restore.")

            self.ssh_client.disconnect()

            if self.sftp_client:
                self.sftp_client.close()
            if self.transport:
                self.transport.close()

    def upload_file(self, local_path, remote_path, callback=None):
        """上传文件，支持进度回调"""
        try:
            self.sftp_client.put(local_path, remote_path, callback=callback)
        except Exception as e:
            log_error(f"Failed to upload file {local_path}: {str(e)}")
            raise

    def ensure_remote_dir(self, remote_path):
        """确保远程目录存在"""
        try:
            self.sftp_client.stat(remote_path)
        except IOError:
            self.sftp_client.mkdir(remote_path)
