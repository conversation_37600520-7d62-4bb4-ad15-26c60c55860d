<template>
  <div class="config-table">
    <!-- 选中节点数量显示 -->
    <div v-if="selectedRowKeys.length > 0" class="selected-count">
      {{ $t('common.selectedNodes', { count: selectedRowKeys.length }) }}
    </div>

    <a-table
      :dataSource="nodes"
      :columns="columns"
      rowKey="ip"
      size="middle"
      :pagination="{
        pageSize: 10,
        total: nodes.length,
        showSizeChanger: false
      }"
      :rowSelection="rowSelection"
      @rowClick="handleRowClick"
    />
  </div>
</template>

<script>
import axios from '@/api/axiosInstance';

export default {
  name: 'NodeSelector',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    projectFile: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      nodes: []
    };
  },
  computed: {
    selectedRowKeys: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      }
    },
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys) => {
          this.selectedRowKeys = selectedRowKeys;
        },
        getCheckboxProps: () => ({
          disabled: this.disabled
        })
      };
    },
    columns() {
      return [
        {
          title: this.$t('tool.columns.hostName'),
          dataIndex: 'host_name',
          key: 'host_name'
        },
        {
          title: this.$t('tool.columns.ip'),
          dataIndex: 'ip',
          key: 'ip'
        }
      ];
    }
  },
  watch: {
    projectFile: {
      handler(newVal) {
        if (newVal) {
          this.fetchNodes();
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchNodes() {
      try {
        const response = await axios.get(`/api/node/nodes?dbFile=${encodeURIComponent(this.projectFile)}`);
        this.nodes = response.data.items;
      } catch (error) {
        console.error('Error fetching nodes:', error);
        this.$notify.error({
          title: 'Error',
          message: 'Failed to fetch nodes'
        });
      }
    },
    handleRowClick() {
      // 可根据需要添加单击行的逻辑
    }
  }
};
</script>

<style scoped>
.selected-count {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: rgba(82, 196, 26, 0.1) !important;
  border-color: rgba(82, 196, 26, 0.3) !important;
  border-radius: 4px;
  color: #52c41a !important;
  font-size: 14px;
  font-weight: 500;
}
</style>
