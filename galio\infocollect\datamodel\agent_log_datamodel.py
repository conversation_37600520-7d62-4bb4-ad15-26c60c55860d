from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from .config_datamodel import Base


class AgentLog(Base):
    __tablename__ = 'agent_log'

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, ForeignKey('host_config.id'), nullable=False)
    log_level = Column(String(10), nullable=False)
    timestamp = Column(DateTime, nullable=False)
    module = Column(String(100))
    log_content = Column(Text, nullable=False)

    host = relationship("HostConfig", back_populates="agent_logs")

    def to_dict(self):
        return {
            'id': self.id,
            'node_id': self.node_id,
            'log_level': self.log_level,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'module': self.module,
            'log_content': self.log_content
        } 