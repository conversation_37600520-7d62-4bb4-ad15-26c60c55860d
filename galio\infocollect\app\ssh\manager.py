from log.logger import log_error
from contextlib import contextmanager
from app.models.node import Node
from app.ssh.client import SSHClient


class SSHConnectionManager:
    def __init__(self, node: Node):
        self.node = node

    @contextmanager
    def connection(self):
        """获取SSH连接的上下文管理器"""
        client = None
        try:
            client = SSHClient(self.node)
            client.connect()
            yield client
        except Exception as e:
            log_error(f"SSH connection failed for {self.node.host_name}: {str(e)}")
            raise
        finally:
            if client and client.is_connected():
                client.disconnect()
