import time
import paramiko
from log.logger import log_error


class SSHClient:
    def __init__(self, node):
        self.node = node
        self.ssh_client = None
        self.__transport = None
        self.sftp_client = None

    def connect(self):
        self.ssh_client = paramiko.SSHClient()
        self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.__transport = paramiko.Transport((self.node.ip, self.node.ssh_port))
        self.__transport.connect(username=self.node.login_user, password=self.node.login_pwd)
        self.ssh_client._transport = self.__transport

    def disconnect(self):
        if self.__transport:
            self.__transport.close()

    def is_connected(self):
        return self.__transport and self.__transport.is_active()

    def execute_command(self, cmd):

        try:
            if self.node.login_user != 'root':
                client = self.ssh_client.invoke_shell()
                buff = ''

                # Switch to root user
                exit_code, buff = self._switch_to_root(client)
                if exit_code != 0:
                    return exit_code, '', buff

                # Execute command
                client.send(cmd + '\n')
                buff = ''
                while not buff.endswith('# '):
                    resp = client.recv(9999)
                    buff += resp.decode('utf-8')
                return 0, buff, ''

            else:
                stdin, stdout, stderr = self.ssh_client.exec_command(cmd)
                exit_code = stdout.channel.recv_exit_status()
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                return exit_code, output, error

        except Exception as e:
            return 1, '', str(e)

    def _switch_to_root(self, client):
        """Helper method to switch to root user

        Returns:
            tuple: (exit_code, message)
        """
        try:
            buff = ''
            resp = client.recv(9999)
            buff += resp.decode('utf-8')

            client.send(self.node.switch_root_cmd.strip() + '\n')

            while not buff.endswith(": "):
                resp = client.recv(9999)
                buff += resp.decode('utf-8')

            client.send(self.node.switch_root_pwd.strip() + '\n')

            for retry in range(3):
                resp = client.recv(9999)
                buff += resp.decode('utf-8')
                if buff.endswith('# '):
                    return 0, buff
                time.sleep(1)

            error_msg = "switch root failed, please check root password or cmd"
            log_error(buff)
            log_error(error_msg)
            return 1, error_msg

        except Exception as e:
            return 1, str(e)
