<template>
  <a-card
    :bordered="false"
    class="header-solid h-full task-card"
    :bodyStyle="{ padding: '8px 16px' }"
    :headStyle="{ borderBottom: '1px solid #e8e8e8' }"
  >

    <!-- 流程图 -->
    <div class="steps-container">
      <a-steps :current="currentStepComputed" class="steps-flow" size="small">
        <a-step>
          <template #icon>
            <a-icon type="apartment" class="step-icon" />
          </template>
        </a-step>

        <a-step>
          <template #icon>
            <a-icon type="global" class="step-icon" />
          </template>
        </a-step>

        <a-step>
          <template #icon>
            <a-tooltip :title="getPlayIconTooltip">
              <a-icon
                type="play-circle"
                class="step-icon"
                :class="{
                  'clickable': selectedIp && selectedRowKeys.length > 0 && !isProcessing,
                  'ready-to-start': selectedIp && selectedRowKeys.length > 0 && !isProcessing
                }"
                @click="selectedIp && selectedRowKeys.length > 0 && !isProcessing && handleStart()"
                :style="{
                  color: (selectedIp && selectedRowKeys.length > 0 && !isProcessing)
                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色
                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色
                }"
              />
            </a-tooltip>
          </template>
        </a-step>
      </a-steps>
    </div>

    <!-- 节点选择区域 -->
    <a-card style="margin: 0 0 16px;" size="small" :title="$t('common.configureNodes')">
      <node-selector
        v-model="selectedRowKeys"
        :project-file="currentProject"
        :disabled="isProcessing"
        @input="onNodesSelected"
      />
    </a-card>

    <!-- 代理配置区域 -->
    <a-card style="margin-bottom: 16px;" size="small" :title="$t('common.configureProxy')">
      <proxy-selector
        v-model="selectedIp"
        :disabled="isProcessing"
        @change="handleProxyChange"
      />
    </a-card>

    <!-- 任务状态 -->
    <task-progress-card :task-type="'task'" :is-processing="isProcessing" />
  </a-card>
</template>

<script>
import axios from '@/api/axiosInstance';
import { mapState, mapActions } from 'vuex';
import NotificationMixin from '@/mixins/NotificationMixin';
import TaskPollingMixin from '@/mixins/TaskPollingMixin';
import ProxySelector from '@/components/common/ProxySelector.vue';
import TaskProgressCard from '@/components/common/TaskProgressCard.vue';
import NodeSelector from '@/components/common/NodeSelector.vue';

export default {
  mixins: [NotificationMixin, TaskPollingMixin],
  components: {
    ProxySelector,
    TaskProgressCard,
    NodeSelector
  },
  data() {
    return {
      selectedRowKeys: [],
      selectedIp: null,
      currentStep: 0,
    };
  },
  computed: {
    ...mapState(['activeTask', 'currentProject', 'sidebarColor']),
    // 任务进度相关计算属性已移至 TaskProgressCard 组件

    taskId: {
      get() {
        return this.activeTask?.task_id;
      },
      set(value) {
        this.$store.dispatch('updateTask', value ? { task_id: value } : null);
      }
    },
    // 任务进度相关计算属性已移至 TaskProgressCard 组件
    currentStepComputed() {
      if (this.isProcessing) {
        return 1;  // 运行中时，只点亮前两个图标
      }
      if (this.selectedRowKeys.length === 0) {
        return -1;  // 没有选择任何节点，所有图标不点亮
      }
      if (this.selectedRowKeys.length > 0 && !this.selectedIp) {
        return 0;   // 选择了节点但未选择IP，点亮第一步图标和连接线
      }
      return 2;     // 选择了节点和IP，且未在运行时，点亮所有三个图标和连接线
    },
    getPlayIconTooltip() {
      if (this.isProcessing) {
        return 'Task is in progress...';
      }
      if (!this.selectedRowKeys.length) {
        return 'Please select nodes first';
      }
      if (!this.selectedIp) {
        return 'Please select a proxy IP';
      }
      return 'Click to start collection!'; // 当都选择完成时显示这个提示
    }
  },
  created() {
    if (!this.checkDatabaseStatus()) {
      return;
    }
    // 只检查当前项目的活动任务
    const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);
    if (taskInfo) {
      const { projectFile } = JSON.parse(taskInfo);
      if (projectFile === this.currentProject) {
        this.checkActiveTask();
      } else {
        // 清除任务信息如果属于不同项目
        localStorage.removeItem(`taskInfo_${this.currentProject}`);
        localStorage.removeItem(`taskCompleted_${this.currentProject}`);
        this.$store.dispatch('updateTask', null);
      }
    }
  },
  methods: {
    ...mapActions(['addNotification']),
    checkDatabaseStatus() {
      if (!this.currentProject) {
        this.$notify.error({
          title: 'Database Error',
          message: 'No project database selected. Please select a project first.'
        });
        this.$router.push('/projects');
        return false;
      }
      return true;
    },

    // 处理节点选择变化
    onNodesSelected(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
      if (this.selectedRowKeys.length) {
        this.currentStep = 1;
      } else {
        this.currentStep = 0;
      }
    },

    // 处理代理IP变化
    handleProxyChange(ip) {
      this.selectedIp = ip;
    },

    async handleStart() {
      if (!this.checkDatabaseStatus()) return;
      if (!this.selectedRowKeys.length || !this.selectedIp) {
        this.$notify.warning({
          title: 'No Nodes or Proxy Selected',
          message: 'Please select one or more nodes and a reachable IP to collect the data.'
        });
        return;
      }

      this.isProcessing = true;
      this.notificationSent = false; // 重置通知标志位

      // 清除之前的任务通知记录
      const previousTaskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);
      if (previousTaskInfo) {
        try {
          const { taskId } = JSON.parse(previousTaskInfo);
          if (taskId) {
            this.clearTaskNotificationMark(taskId, 'task', this.currentProject);
          }
        } catch (e) {
          console.error('Error clearing previous task notification:', e);
        }
      }

      try {
        const { data } = await axios.post('/api/task/collect', {
          targets: this.selectedRowKeys,
          proxy_ip: this.selectedIp,
          dbFile: this.currentProject
        });

        if (data && data.task_id) {
          localStorage.setItem(`taskInfo_${this.currentProject}`, JSON.stringify({
            taskId: data.task_id,
            projectFile: this.currentProject
          }));
          localStorage.removeItem(`taskCompleted_${this.currentProject}`);

          this.taskId = data.task_id;
          this.startPolling(data.task_id, 'task', 'task');
        }
      } catch (error) {
        console.error('Error starting task:', error);
        this.$notify.error({
          title: 'Task Start Failed',
          message: error.message || 'Server connection error.',
        });
        this.isProcessing = false;
      }
    },

    // 重写 checkActiveTask 方法，调用混入中的方法
    async checkActiveTask() {
      try {
        const taskInfo = localStorage.getItem(`taskInfo_${this.currentProject}`);
        const taskCompleted = localStorage.getItem(`taskCompleted_${this.currentProject}`);

        if (taskInfo) {
          const { taskId, projectFile } = JSON.parse(taskInfo);

          if (projectFile !== this.currentProject) {
            throw new Error('Task belongs to different project');
          }

          const response = await axios.get(`/api/task/${taskId}`);

          if (response.data) {
            this.$store.dispatch('updateTask', response.data);

            if (response.data.nodes) {
              const nodes = Object.values(response.data.nodes);
              const allCompleted = nodes.every(node =>
                ['success', 'failed'].includes(node.status)
              );

              if (!allCompleted && !taskCompleted) {
                this.isProcessing = true;
                this.startPolling(taskId, 'task', 'task');
              } else if (allCompleted) {
                this.isProcessing = false;
                localStorage.setItem(`taskCompleted_${this.currentProject}`, 'true');
              }
            }
          }
        }
      } catch (error) {
        console.error('Error checking active task:', error);
        localStorage.removeItem(`taskInfo_${this.currentProject}`);
        localStorage.removeItem(`taskCompleted_${this.currentProject}`);
      }
    },



    activated() {
      // 当组件被激活时（从缓存中恢复）立即检查任务状态
      this.checkActiveTask();
    },
  },
  watch: {
    // 监听 currentProject 变化
    currentProject: {
      handler(newProject, oldProject) {
        if (newProject !== oldProject) {
          // 清除之前项目的任务状态
          this.$store.dispatch('updateTask', null);
          this.stopPolling();
          // 检查新项目的活动任务
          this.checkActiveTask();
        }
      },
      immediate: true
    }
  }
};
</script>

<style scoped lang="scss">
// 基础卡片样式
.task-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 20px;
}

// 步骤容器
.steps-container {
  width: 50%;
  margin: 0 auto 24px;
  padding: 12px 0;
}

// 深度选择器样式集中管理
::v-deep {
  .ant-card {
    border-radius: 8px;
    overflow: hidden;
    .ant-card-head {
      background: #f0f2f5;
    }
  }

  .ant-progress {
    border-radius: 3px;
  }
  .ant-tooltip-inner {
    max-width: 500px;
    white-space: pre-wrap;
  }
  .ant-table-tbody > tr:last-child > td {
    border-bottom: 1px solid #f0f0f0;
  }
  .steps-flow {
    .ant-steps-item {
      &-process,
      &-finish {
        .ant-steps-item-container {
          .ant-steps-item-content {
            .ant-steps-item-title::after {
              background-color: #3b4149 !important;
              height: 2px !important;
              top: 25px !important;
            }
          }
        }
      }

      &-wait {
        .ant-steps-item-container {
          .ant-steps-item-content {
            .ant-steps-item-title::after {
              background-color: #d9d9d9 !important;
              height: 2px !important;
              top: 25px !important;
            }
          }
        }

        .step-icon {
          color: #d9d9d9 !important;
        }
      }

      &-icon {
        width: 88px;
        height: 88px;
        line-height: 80px;
        padding: 4px;
        font-size: 40px;
        border-width: 2px;
        margin-top: -20px;
        color: #3b4149;

        .step-icon {
          font-size: 40px;
          color: #3b4149;
        }
      }

      &-tail::after {
        height: 2px;
      }

      &:last-child {
        .step-icon {
          color: #d9d9d9 !important;
        }

        &.ant-steps-item-process,
        &.ant-steps-item-finish {
          .step-icon {
            color: #3b4149 !important;
          }
        }
      }
    }
  }
  .ready-to-start {
    animation: pulse 1.2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .clickable {
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
