{"version": 3, "sources": ["webpack:///./src/components/Cards/ProcessInfo.vue?9f39", "webpack:///./src/views/Process.vue", "webpack:///./src/components/Cards/ProcessInfo.vue", "webpack:///src/components/Cards/ProcessInfo.vue", "webpack:///./src/components/Cards/ProcessInfo.vue?cc9c", "webpack:///./src/components/Cards/ProcessInfo.vue?0e79", "webpack:///src/views/Process.vue", "webpack:///./src/views/Process.vue?f87b", "webpack:///./src/views/Process.vue?f3c9", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "fetchProcesses", "proxy", "columns", "processes", "record", "pid", "rowClick", "pagination", "handleAIAnalysisClose", "model", "value", "aiAnalysisVisible", "callback", "$$v", "expression", "selectedProcess", "_e", "aiAnalysisLoading", "aiAnalysisResult", "domProps", "formatMarkdown", "aiAnalysisError", "components", "RefreshButton", "data", "h", "$createElement", "title", "dataIndex", "width", "ellipsis", "align", "customRender", "_", "e", "stopPropagation", "showAIAnalysis", "pageSize", "current", "onChange", "page", "$store", "dispatch", "computed", "mapState", "watch", "selectedNodeIp", "handler", "immediate", "currentPage", "newPage", "mounted", "$nextTick", "scrollPosition", "setTimeout", "window", "scrollTo", "top", "behavior", "lastViewedPid", "applyHighlight", "updated", "length", "beforeRouteLeave", "to", "_from", "next", "name", "methods", "response", "axios", "get", "params", "fields", "dbFile", "currentProject", "error", "console", "_index", "isLastViewed", "click", "viewProcessDetails", "style", "cursor", "process", "pageYOffset", "document", "documentElement", "scrollTop", "body", "$router", "push", "requestAIAnalysis", "rows", "querySelectorAll", "i", "row", "cells", "textContent", "includes", "classList", "add", "j", "cell", "backgroundColor", "borderTop", "borderBottom", "borderLeft", "borderRight", "processData", "aiResponse", "post", "process_data", "process_list", "timeout", "success", "analysis", "_error$response", "message", "markdown", "formatted", "replace", "match", "trim", "component", "ProcessInfo", "$event", "$emit", "text", "props", "type", "String", "default"], "mappings": "kHAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,gBAAgB,IAAI,IAAI,IAExMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,mCAAmCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,IAAKC,YAAYR,EAAIS,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACT,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACU,MAAM,QAAQZ,EAAIa,aAAeT,MAAM,CAAC,MAAQ,KAAK,OAAS,KAAK,MAAQ,6BAA6B,QAAU,gBAAgB,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,EAAI,8ZAA8ZF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIc,GAAGd,EAAIe,GAAGf,EAAIgB,GAAG,2BAA2Bd,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACe,GAAG,CAAC,QAAUjB,EAAIkB,mBAAmB,OAAOC,OAAM,MAAS,CAACjB,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIoB,QAAQ,cAAcpB,EAAIqB,UAAU,OAASC,GAAUA,EAAOC,IAAI,UAAYvB,EAAIwB,SAAS,WAAaxB,EAAIyB,YAAYjB,YAAYR,EAAIS,GAAG,CAAC,CAACC,IAAI,YAAYC,GAAG,WAAW,MAAO,CAACT,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,0BAA0Be,OAAM,OAAUjB,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,uBAAuB,MAAQ,SAASa,GAAG,CAAC,OAASjB,EAAI0B,uBAAuBlB,YAAYR,EAAIS,GAAG,CAAC,CAACC,IAAI,SAASC,GAAG,WAAW,MAAO,CAACT,EAAG,WAAW,CAACe,GAAG,CAAC,MAAQjB,EAAI0B,wBAAwB,CAAC1B,EAAIc,GAAG,aAAaK,OAAM,KAAQQ,MAAM,CAACC,MAAO5B,EAAI6B,kBAAmBC,SAAS,SAAUC,GAAM/B,EAAI6B,kBAAkBE,GAAKC,WAAW,sBAAsB,CAAC9B,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAAEL,EAAIiC,gBAAiB/B,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,IAAI,CAACA,EAAG,SAAS,CAACF,EAAIc,GAAG,UAAUd,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAIiC,gBAAgBV,UAAUvB,EAAIkC,KAAMlC,EAAImC,kBAAmBjC,EAAG,aAAa,CAACE,MAAM,CAAC,QAAUJ,EAAImC,kBAAkB,OAAS,MAAMnC,EAAIkC,MAAOlC,EAAImC,mBAAqBnC,EAAIoC,iBAAkBlC,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,mBAAmBgC,SAAS,CAAC,UAAYrC,EAAIe,GAAGf,EAAIsC,eAAetC,EAAIoC,wBAAwBpC,EAAIkC,KAAOlC,EAAImC,mBAAsBnC,EAAIoC,kBAAqBpC,EAAIuC,gBAAwHvC,EAAIkC,KAA3GhC,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,2BAA2B,IAAcJ,EAAImC,mBAAqBnC,EAAIuC,gBAAiBrC,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,UAAU,CAACE,MAAM,CAAC,QAAU,iBAAiB,YAAcJ,EAAIuC,gBAAgB,KAAO,QAAQ,YAAY,OAAO,GAAGvC,EAAIkC,MAAM,MAAM,IAErrF5B,EAAkB,G,oCC2EP,GACfkC,WAAA,CACAC,sBAEAC,OAAA,MAAAC,EAAA,KAAAC,eACA,OACAvB,UAAA,GACAD,QAAA,CACA,CACAyB,MAAA,MACAC,UAAA,MACApC,IAAA,MACAqC,MAAA,KAEA,CACAF,MAAA,eACAC,UAAA,UACApC,IAAA,UACAqC,MAAA,MACAC,UAAA,GAEA,CACAH,MAAA,cACAnC,IAAA,cACAqC,MAAA,MACAE,MAAA,SACAC,cAAAC,EAAA7B,IAAAqB,EAAA,kBAEA,WAAA9B,aAAA,MACA,2BACA,mBACAuC,IACAA,EAAAC,kBACA,KAAAC,eAAAhC,MACA,eAOAG,WAAA,CACA8B,SAAA,IAEAC,QAAA,EAEAC,SAAAC,IAEA,KAAAC,OAAAC,SAAA,gCAAAF,KAIA7B,mBAAA,EACAM,mBAAA,EACAC,iBAAA,KACAG,gBAAA,KACAN,gBAAA,OAGA4B,SAAA,IACAC,eAAA,uDACAA,eAAA,iEAEAC,MAAA,CACAC,eAAA,CACAC,UACA,KAAA/C,kBAEAgD,WAAA,GAGAC,YAAA,CACAF,QAAAG,GACAA,GAAA,KAAA3C,WAAA+B,UAAAY,IACA,KAAA3C,WAAA+B,QAAAY,IAGAF,WAAA,IAGAG,UACA,KAAAnD,iBAGA,KAAAoD,UAAA,KAEA,KAAAC,eAAA,GACAC,WAAA,KACAC,OAAAC,SAAA,CACAC,IAAA,KAAAJ,eACAK,SAAA,UAEA,KAIA,KAAAC,eACAL,WAAA,KAAAM,eAAA,QAKAC,UAEA,KAAAF,eAAA,KAAAxD,UAAA2D,OAAA,GACA,KAAAF,kBAIAG,iBAAAC,EAAAC,EAAAC,GAEA,kBAAAF,EAAAG,OACA,KAAA1B,OAAAC,SAAA,0BAEA,KAAAD,OAAAC,SAAA,mCAEAwB,KAEAE,QAAA,CACA,uBACA,QAAAtB,eAIA,IACA,MAAAuB,QAAAC,OAAAC,IAAA,uBAAAzB,eAAA,CACA0B,OAAA,CACAC,OAAA,cACAC,OAAA,KAAAC,kBAGA,KAAAxE,UAAAkE,EAAA7C,KACA,MAAAoD,GACAC,QAAAD,MAAA,4BAAAA,QAZAC,QAAAD,MAAA,2BAeAtE,SAAAF,EAAA0E,GAEA,MAAAC,EAAA,KAAApB,eAAAvD,EAAAC,MAAA,KAAAsD,cAEA,OACA5D,GAAA,CACAiF,WACA,KAAAC,mBAAA7E,KAGAV,MAAA,CAEA,kBAAAqF,GAEAG,MAAA,CACAC,OAAA,aAIAF,mBAAAG,GAEA,MAAA/B,EAAAE,OAAA8B,aAAAC,SAAAC,gBAAAC,WAAAF,SAAAG,KAAAD,WAAA,EACA,KAAA/C,OAAAC,SAAA,mCAAAW,GAGA,KAAAZ,OAAAC,SAAA,kCAAA0C,EAAA/E,KAGA,KAAAqF,QAAAC,KAAA,CAAAxB,KAAA,gBAAAK,OAAA,CAAAnE,IAAA+E,EAAA/E,QAIA+B,eAAAgD,GACA,KAAArE,gBAAAqE,EACA,KAAAzE,mBAAA,EACA,KAAAO,iBAAA,KACA,KAAAG,gBAAA,KAGA,KAAAuE,qBAIAhC,iBACA,SAAAD,cAAA,OAGA,MAAAkC,EAAAP,SAAAQ,iBAAA,kBACA,QAAAC,EAAA,EAAAA,EAAAF,EAAA/B,OAAAiC,IAAA,CACA,MAAAC,EAAAH,EAAAE,GACAE,EAAAD,EAAAF,iBAAA,MAGA,GAAAG,EAAAnC,OAAA,GAAAmC,EAAA,GAAAC,YAAAC,SAAA,KAAAxC,eAAA,CAEAqC,EAAAI,UAAAC,IAAA,mBAGA,QAAAC,EAAA,EAAAA,EAAAL,EAAAnC,OAAAwC,IAAA,CACA,MAAAC,EAAAN,EAAAK,GACAC,EAAArB,MAAAsB,gBAAA,UACAD,EAAArB,MAAAuB,UAAA,oBACAF,EAAArB,MAAAwB,aAAA,oBAGA,IAAAJ,IACAC,EAAArB,MAAAyB,WAAA,qBAIAL,IAAAL,EAAAnC,OAAA,IACAyC,EAAArB,MAAA0B,YAAA,qBAKA,SAKApG,wBACA,KAAAG,mBAAA,EACA,KAAAO,iBAAA,KACA,KAAAG,gBAAA,MAGA,0BACA,QAAAN,gBAKA,QAAA+B,eAAA,CAKA,KAAA7B,mBAAA,EACA,KAAAI,gBAAA,KAEA,IAEA,MAAAgD,QAAAC,OAAAC,IAAA,uBAAAzB,eAAA,CACA0B,OAAA,CACAnE,IAAA,KAAAU,gBAAAV,IACAoE,OAAA,mEACAC,OAAA,KAAAC,gBAAA,MAIAkC,EAAAxC,EAAA7C,KAGAsF,QAAAxC,OAAAyC,KAAA,2BACAC,aAAA,CACAC,aAAA,CAAAJ,KAEA,CACAK,QAAA,MAGAJ,EAAAtF,KAAA2F,QACA,KAAAjG,iBAAA4F,EAAAtF,KAAA4F,SAEA,KAAA/F,gBAAAyF,EAAAtF,KAAAoD,OAAA,iCAEA,MAAAA,GAAA,IAAAyC,EACAxC,QAAAD,MAAA,qBAAAA,GACA,KAAAvD,iBAAA,QAAAgG,EAAAzC,EAAAP,gBAAA,IAAAgD,GAAA,QAAAA,IAAA7F,YAAA,IAAA6F,OAAA,EAAAA,EAAAzC,UAAA0C,SAAA,oCACA,QACA,KAAArG,mBAAA,QArCA,KAAAI,gBAAA,wBALA,KAAAA,gBAAA,oCA8CAD,eAAAmG,GAEA,IAAAA,EAAA,SAGA,IAAAC,EAAAD,EACAE,QAAA,4BACAA,QAAA,6BACAA,QAAA,8BACAA,QAAA,+BACAA,QAAA,gCACAA,QAAA,iCA4BA,OAzBAD,IACAC,QAAA,wCACAA,QAAA,4BACAA,QAAA,oCACAA,QAAA,0BAGAD,IAAAC,QAAA,kDAGAD,IAAAC,QAAA,gCAGAD,IACAC,QAAA,qCACAA,QAAA,oCAGAD,IAAAC,QAAA,+BAAAC,GACA,OAAAA,EAAAC,OAAA,MAAAD,EAAA,aAIAF,IAAAC,QAAA,6DAEAD,KCnYmW,I,wBCQ/VI,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCDA,GACftG,WAAA,CACAuG,gBCpBgV,ICO5U,EAAY,eACd,EACAhJ,EACAO,GACA,EACA,KACA,KACA,MAIa,e,kEClBf,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACU,MAAM,CAAC,iBAAkB,QAAQZ,EAAIa,cAAgBT,MAAM,CAAC,KAAO,UAAUa,GAAG,CAAC,MAAQ,SAAS+H,GAAQ,OAAOhJ,EAAIiJ,MAAM,cAAc,CAACjJ,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAIkJ,MAAQlJ,EAAIgB,GAAG,mBAAmB,QAEhRV,EAAkB,G,YCWP,GACfuD,SAAA,IACAC,eAAA,mBAEAuB,KAAA,gBACA8D,MAAA,CACAD,KAAA,CACAE,KAAAC,OACAC,QAAA,MCrBqW,I,YCOjWR,EAAY,eACd,EACA/I,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAAwI,E", "file": "static/js/chunk-364a09a0.ff89fb38.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessInfo.vue?vue&type=style&index=0&id=16962dbd&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('ProcessInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full process-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"width\":\"20\",\"height\":\"20\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 512 512\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"d\":\"M<PERSON> 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.process')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchProcesses}})],1)])]},proxy:true}])},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.processes,\"rowKey\":record => record.pid,\"customRow\":_vm.rowClick,\"pagination\":_vm.pagination},scopedSlots:_vm._u([{key:\"emptyText\",fn:function(){return [_c('a-empty',{attrs:{\"description\":\"No processes found\"}})]},proxy:true}])}),_c('a-modal',{attrs:{\"title\":\"AI Security Analysis\",\"width\":\"800px\"},on:{\"cancel\":_vm.handleAIAnalysisClose},scopedSlots:_vm._u([{key:\"footer\",fn:function(){return [_c('a-button',{on:{\"click\":_vm.handleAIAnalysisClose}},[_vm._v(\"Close\")])]},proxy:true}]),model:{value:(_vm.aiAnalysisVisible),callback:function ($$v) {_vm.aiAnalysisVisible=$$v},expression:\"aiAnalysisVisible\"}},[_c('div',{staticClass:\"ai-analysis-container\"},[(_vm.selectedProcess)?_c('div',{staticClass:\"process-info\"},[_c('p',[_c('strong',[_vm._v(\"PID:\")]),_vm._v(\" \"+_vm._s(_vm.selectedProcess.pid))])]):_vm._e(),(_vm.aiAnalysisLoading)?_c('a-skeleton',{attrs:{\"loading\":_vm.aiAnalysisLoading,\"active\":\"\"}}):_vm._e(),(!_vm.aiAnalysisLoading && _vm.aiAnalysisResult)?_c('div',{staticClass:\"analysis-results\"},[_c('div',{staticClass:\"markdown-content\",domProps:{\"innerHTML\":_vm._s(_vm.formatMarkdown(_vm.aiAnalysisResult))}})]):_vm._e(),(!_vm.aiAnalysisLoading && !_vm.aiAnalysisResult && !_vm.aiAnalysisError)?_c('div',{staticClass:\"no-analysis\"},[_c('a-empty',{attrs:{\"description\":\"Analyzing process...\"}})],1):_vm._e(),(!_vm.aiAnalysisLoading && _vm.aiAnalysisError)?_c('div',{staticClass:\"analysis-error\"},[_c('a-alert',{attrs:{\"message\":\"Analysis Error\",\"description\":_vm.aiAnalysisError,\"type\":\"error\",\"show-icon\":\"\"}})],1):_vm._e()],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full process-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg width=\"20\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" :class=\"`text-${sidebarColor}`\">\r\n              <path fill=\"currentColor\" d=\"M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.process') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchProcesses\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"processes\"\r\n      :rowKey=\"record => record.pid\"\r\n      :customRow=\"rowClick\"\r\n      :pagination=\"pagination\"\r\n    >\r\n      <template #emptyText>\r\n        <a-empty description=\"No processes found\" />\r\n      </template>\r\n    </a-table>\r\n\r\n    <!-- AI分析模态框 -->\r\n    <a-modal\r\n      v-model:visible=\"aiAnalysisVisible\"\r\n      title=\"AI Security Analysis\"\r\n      width=\"800px\"\r\n      @cancel=\"handleAIAnalysisClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleAIAnalysisClose\">Close</a-button>\r\n      </template>\r\n\r\n      <div class=\"ai-analysis-container\">\r\n        <div v-if=\"selectedProcess\" class=\"process-info\">\r\n          <p><strong>PID:</strong> {{ selectedProcess.pid }}</p>\r\n        </div>\r\n\r\n        <a-skeleton :loading=\"aiAnalysisLoading\" active v-if=\"aiAnalysisLoading\" />\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisResult\" class=\"analysis-results\">\r\n          <div v-html=\"formatMarkdown(aiAnalysisResult)\" class=\"markdown-content\"></div>\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && !aiAnalysisResult && !aiAnalysisError\" class=\"no-analysis\">\r\n          <a-empty description=\"Analyzing process...\" />\r\n        </div>\r\n\r\n        <div v-if=\"!aiAnalysisLoading && aiAnalysisError\" class=\"analysis-error\">\r\n          <a-alert\r\n            message=\"Analysis Error\"\r\n            :description=\"aiAnalysisError\"\r\n            type=\"error\"\r\n            show-icon\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      processes: [],\r\n      columns: [\r\n        {\r\n          title: 'PID',\r\n          dataIndex: 'pid',\r\n          key: 'pid',\r\n          width: 120,\r\n        },\r\n        {\r\n          title: 'Process Name',\r\n          dataIndex: 'cmdline',\r\n          key: 'cmdline',\r\n          width: '95%',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'AI Analysis',\r\n          key: 'ai_analysis',\r\n          width: '15%',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <a-button\r\n              class={`bg-${this.sidebarColor}`}\r\n              style=\"color: white\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                this.showAIAnalysis(record);\r\n              }}\r\n            >\r\n              Analyze\r\n            </a-button>\r\n          ),\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n        // 使用计算属性来绑定当前页码\r\n        current: 1,\r\n        // 添加分页变化事件处理\r\n        onChange: (page) => {\r\n          // 当页码变化时，更新Vuex中的页码状态\r\n          this.$store.dispatch('processList/updateCurrentPage', page);\r\n        }\r\n      },\r\n      // AI分析相关\r\n      aiAnalysisVisible: false,\r\n      aiAnalysisLoading: false,\r\n      aiAnalysisResult: null,\r\n      aiAnalysisError: null,\r\n      selectedProcess: null,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    ...mapState('processList', ['currentPage', 'scrollPosition', 'lastViewedPid']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp: {\r\n      handler() {\r\n        this.fetchProcesses();\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听Vuex中的currentPage变化，同步到分页组件\r\n    currentPage: {\r\n      handler(newPage) {\r\n        if (newPage && this.pagination.current !== newPage) {\r\n          this.pagination.current = newPage;\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchProcesses();\r\n\r\n    // 恢复之前保存的滚动位置和应用高亮效果\r\n    this.$nextTick(() => {\r\n      // 恢复滚动位置\r\n      if (this.scrollPosition > 0) {\r\n        setTimeout(() => {\r\n          window.scrollTo({\r\n            top: this.scrollPosition,\r\n            behavior: 'auto'\r\n          });\r\n        }, 100);\r\n      }\r\n\r\n      // 如果有lastViewedPid，尝试应用高亮效果\r\n      if (this.lastViewedPid) {\r\n        setTimeout(this.applyHighlight, 500); // 延迟确保表格已渲染\r\n      }\r\n    });\r\n  },\r\n\r\n  updated() {\r\n    // 只有当processes数组有内容且有lastViewedPid时才应用高亮\r\n    if (this.lastViewedPid && this.processes.length > 0) {\r\n      this.applyHighlight();\r\n    }\r\n  },\r\n  // 当用户离开进程列表页面但不是通过点击进程详情时，清除保存的滚动位置和分页信息\r\n  beforeRouteLeave(to, _from, next) {\r\n    // 如果不是导航到进程详情页面，则清除滚动位置和分页信息\r\n    if (to.name !== 'ProcessDetail') {\r\n      this.$store.dispatch('processList/resetState');\r\n      // 清除最后查看的进程ID\r\n      this.$store.dispatch('processList/clearLastViewedPid');\r\n    }\r\n    next();\r\n  },\r\n  methods: {\r\n    async fetchProcesses() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            fields: 'pid,cmdline',\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        this.processes = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching processes:', error);\r\n      }\r\n    },\r\n    rowClick(record, _index) {\r\n      // 检查是否是最后查看的进程\r\n      const isLastViewed = this.lastViewedPid && record.pid === this.lastViewedPid;\r\n\r\n      return {\r\n        on: {\r\n          click: () => {\r\n            this.viewProcessDetails(record);\r\n          },\r\n        },\r\n        class: {\r\n          // 如果是最后查看的进程，添加高亮类\r\n          'last-viewed-row': isLastViewed\r\n        },\r\n        style: {\r\n          cursor: 'pointer'\r\n        }\r\n      };\r\n    },\r\n    viewProcessDetails(process) {\r\n      // 保存当前滚动位置到Vuex store\r\n      const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n      this.$store.dispatch('processList/updateScrollPosition', scrollPosition);\r\n\r\n      // 保存当前查看的进程ID\r\n      this.$store.dispatch('processList/updateLastViewedPid', process.pid);\r\n\r\n      // 导航到进程详情页面\r\n      this.$router.push({ name: 'ProcessDetail', params: { pid: process.pid } });\r\n    },\r\n\r\n    // AI分析相关方法\r\n    showAIAnalysis(process) {\r\n      this.selectedProcess = process;\r\n      this.aiAnalysisVisible = true;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n\r\n      // 自动开始分析\r\n      this.requestAIAnalysis();\r\n    },\r\n\r\n    // 应用高亮效果到最后查看的进程行\r\n    applyHighlight() {\r\n      if (!this.lastViewedPid) return;\r\n\r\n      // 尝试查找包含lastViewedPid的行\r\n      const rows = document.querySelectorAll('.ant-table-row');\r\n      for (let i = 0; i < rows.length; i++) {\r\n        const row = rows[i];\r\n        const cells = row.querySelectorAll('td');\r\n\r\n        // 检查第一个单元格（PID列）是否包含lastViewedPid\r\n        if (cells.length > 0 && cells[0].textContent.includes(this.lastViewedPid)) {\r\n          // 添加高亮类\r\n          row.classList.add('last-viewed-row');\r\n\r\n          // 直接设置样式以确保高亮效果生效\r\n          for (let j = 0; j < cells.length; j++) {\r\n            const cell = cells[j];\r\n            cell.style.backgroundColor = '#f5f5f5';\r\n            cell.style.borderTop = '1px solid #1890ff';\r\n            cell.style.borderBottom = '1px solid #1890ff';\r\n\r\n            // 为第一个单元格添加左侧边框\r\n            if (j === 0) {\r\n              cell.style.borderLeft = '3px solid #1890ff';\r\n            }\r\n\r\n            // 为最后一个单元格添加右侧边框\r\n            if (j === cells.length - 1) {\r\n              cell.style.borderRight = '3px solid #1890ff';\r\n            }\r\n          }\r\n\r\n          // 找到后退出循环\r\n          break;\r\n        }\r\n      }\r\n    },\r\n\r\n    handleAIAnalysisClose() {\r\n      this.aiAnalysisVisible = false;\r\n      this.aiAnalysisResult = null;\r\n      this.aiAnalysisError = null;\r\n    },\r\n\r\n    async requestAIAnalysis() {\r\n      if (!this.selectedProcess) {\r\n        this.aiAnalysisError = \"No process selected for analysis\";\r\n        return;\r\n      }\r\n\r\n      if (!this.selectedNodeIp) {\r\n        this.aiAnalysisError = \"No node selected\";\r\n        return;\r\n      }\r\n\r\n      this.aiAnalysisLoading = true;\r\n      this.aiAnalysisError = null;\r\n\r\n      try {\r\n        // 获取完整的进程信息\r\n        const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n          params: {\r\n            pid: this.selectedProcess.pid,\r\n            fields: 'pid,uid,gid,cmdline,state,exe,cwd,capability,environ,memory_maps',\r\n            dbFile: this.currentProject || ''\r\n          }\r\n        });\r\n\r\n        const processData = response.data;\r\n\r\n        // 调用AI分析API，设置更长的超时时间\r\n        const aiResponse = await axios.post('/api/ai/analyze/process', {\r\n          process_data: {\r\n            process_list: [processData]\r\n          }\r\n        }, {\r\n          timeout: 600000 // 10分钟超时\r\n        });\r\n\r\n        if (aiResponse.data.success) {\r\n          this.aiAnalysisResult = aiResponse.data.analysis;\r\n        } else {\r\n          this.aiAnalysisError = aiResponse.data.error || \"Failed to analyze process data\";\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI analysis error:\", error);\r\n        this.aiAnalysisError = error.response?.data?.error || error.message || \"An error occurred during analysis\";\r\n      } finally {\r\n        this.aiAnalysisLoading = false;\r\n      }\r\n    },\r\n\r\n    formatMarkdown(markdown) {\r\n      // 简单的Markdown格式化，可以使用更复杂的库如marked.js\r\n      if (!markdown) return '';\r\n\r\n      // 替换标题\r\n      let formatted = markdown\r\n        .replace(/^# (.*$)/gm, '<h1>$1</h1>')\r\n        .replace(/^## (.*$)/gm, '<h2>$1</h2>')\r\n        .replace(/^### (.*$)/gm, '<h3>$1</h3>')\r\n        .replace(/^#### (.*$)/gm, '<h4>$1</h4>')\r\n        .replace(/^##### (.*$)/gm, '<h5>$1</h5>')\r\n        .replace(/^###### (.*$)/gm, '<h6>$1</h6>');\r\n\r\n      // 替换粗体和斜体\r\n      formatted = formatted\r\n        .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n        .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\r\n        .replace(/__(.*?)__/g, '<strong>$1</strong>')\r\n        .replace(/_(.*?)_/g, '<em>$1</em>');\r\n\r\n      // 替换代码块\r\n      formatted = formatted.replace(/```([\\s\\S]*?)```/g, '<pre><code>$1</code></pre>');\r\n\r\n      // 替换行内代码\r\n      formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');\r\n\r\n      // 替换列表\r\n      formatted = formatted\r\n        .replace(/^\\s*\\d+\\.\\s+(.*$)/gm, '<li>$1</li>')\r\n        .replace(/^\\s*[-*]\\s+(.*$)/gm, '<li>$1</li>');\r\n\r\n      // 替换段落\r\n      formatted = formatted.replace(/^(?!<[a-z])(.*$)/gm, function(match) {\r\n        return match.trim() ? '<p>' + match + '</p>' : '';\r\n      });\r\n\r\n      // 替换链接\r\n      formatted = formatted.replace(/\\[(.*?)\\]\\((.*?)\\)/g, '<a href=\"$2\" target=\"_blank\">$1</a>');\r\n\r\n      return formatted;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n.process-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.card-title {\r\n  color: #333;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-bottom: 0;\r\n}\r\n\r\n\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px; /* 匹配卡片圆角 */\r\n}\r\n\r\n/* 表头样式 */\r\n.ant-table-thead > tr > th {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #f0f0f0; /* 浅色分割线 */\r\n  color: #666;\r\n}\r\n\r\n/* 悬停效果 */\r\n.ant-table-tbody > tr:hover > td {\r\n  background-color: #fafafa !important;\r\n}\r\n\r\n/* 处理长文本 */\r\n.ant-table-cell {\r\n  white-space: normal; /* 允许文本换行 */\r\n  word-break: break-word; /* 在单词内换行 */\r\n  overflow-wrap: break-word; /* 确保长单词也能换行 */\r\n}\r\n\r\n/* 确保 action 列始终显示 */\r\n.ant-table-fixed-right {\r\n  z-index: 2;\r\n  background-color: #fff;\r\n}\r\n\r\n/* AI分析相关样式 */\r\n.ai-analysis-container {\r\n  padding: 16px;\r\n}\r\n\r\n.process-info {\r\n  margin-bottom: 10px;\r\n  padding: 8px 16px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #1890ff;\r\n\r\n  p {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.analysis-results {\r\n  margin-top: 10px;\r\n}\r\n\r\n.markdown-content {\r\n  line-height: 1.6;\r\n}\r\n\r\n.markdown-content h1,\r\n.markdown-content h2,\r\n.markdown-content h3,\r\n.markdown-content h4,\r\n.markdown-content h5,\r\n.markdown-content h6 {\r\n  margin-top: 24px;\r\n  margin-bottom: 16px;\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n}\r\n\r\n.markdown-content h1 {\r\n  font-size: 2em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h2 {\r\n  font-size: 1.5em;\r\n  border-bottom: 1px solid #eaecef;\r\n  padding-bottom: 0.3em;\r\n}\r\n\r\n.markdown-content h3 {\r\n  font-size: 1.25em;\r\n}\r\n\r\n.markdown-content h4 {\r\n  font-size: 1em;\r\n}\r\n\r\n.markdown-content p {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content code {\r\n  padding: 0.2em 0.4em;\r\n  margin: 0;\r\n  font-size: 85%;\r\n  background-color: rgba(27, 31, 35, 0.05);\r\n  border-radius: 3px;\r\n  font-family: \"SFMono-Regular\", Consolas, \"Liberation Mono\", Menlo, monospace;\r\n}\r\n\r\n.markdown-content pre {\r\n  padding: 16px;\r\n  overflow: auto;\r\n  font-size: 85%;\r\n  line-height: 1.45;\r\n  background-color: #f6f8fa;\r\n  border-radius: 3px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.markdown-content pre code {\r\n  padding: 0;\r\n  margin: 0;\r\n  background-color: transparent;\r\n  border: 0;\r\n  word-break: normal;\r\n  white-space: pre;\r\n}\r\n\r\n.markdown-content li {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n\r\n\r\n.no-analysis {\r\n  margin-top: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.analysis-error {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 最后查看的行样式 - 浅灰色背景和轮廓线 */\r\n.last-viewed-row td {\r\n  background-color: #f5f5f5 !important;\r\n  position: relative;\r\n  border-top: 1px solid #1890ff !important;\r\n  border-bottom: 1px solid #1890ff !important;\r\n}\r\n\r\n/* 为第一个单元格添加左侧边框 */\r\n.last-viewed-row td:first-child {\r\n  border-left: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 为最后一个单元格添加右侧边框 */\r\n.last-viewed-row td:last-child {\r\n  border-right: 3px solid #1890ff !important;\r\n}\r\n\r\n/* 悬停时加强边框效果 */\r\n.last-viewed-row:hover td {\r\n  border-top: 2px solid #1890ff !important;\r\n  border-bottom: 2px solid #1890ff !important;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProcessInfo.vue?vue&type=template&id=16962dbd&scoped=true\"\nimport script from \"./ProcessInfo.vue?vue&type=script&lang=js\"\nexport * from \"./ProcessInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./ProcessInfo.vue?vue&type=style&index=0&id=16962dbd&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"16962dbd\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<!-- Process Attributes Column -->\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<!-- Process Attributes Card -->\r\n\t\t\t\t<ProcessInfo></ProcessInfo>\r\n\t\t\t\t<!-- Process Attributes Card -->\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport ProcessInfo from \"@/components/Cards/ProcessInfo\";\r\n\r\nexport default {\r\n    components: {\r\n        ProcessInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Process.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Process.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Process.vue?vue&type=template&id=1f3c6fab\"\nimport script from \"./Process.vue?vue&type=script&lang=js\"\nexport * from \"./Process.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}