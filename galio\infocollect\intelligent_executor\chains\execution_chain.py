"""
执行链 - 用于处理命令执行的LangChain链
"""

from typing import Dict, List, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI

from infocollect.log.logger import log_info, log_error, log_debug


class ExecutionChain:
    """
    执行链
    
    主要功能:
    1. 命令生成
    2. 执行计划制定
    3. 结果验证
    4. 错误处理
    """
    
    def __init__(self, llm: Optional[ChatOpenAI] = None):
        """
        初始化执行链
        
        Args:
            llm: LangChain LLM实例
        """
        self.llm = llm or ChatOpenAI(temperature=0.1)
        self.json_parser = JsonOutputParser()
        
        # 初始化提示词模板
        self._init_prompts()
        
        log_info("ExecutionChain initialized")

    def _init_prompts(self):
        """初始化提示词模板"""
        
        # 命令生成提示词
        self.command_generation_prompt = PromptTemplate(
            input_variables=["analysis_result"],
            template="""
基于以下分析结果，生成具体的执行命令序列：

分析结果:
{analysis_result}

请按照以下JSON格式返回命令序列:
{{
    "commands": [
        {{
            "step_number": 1,
            "description": "步骤描述",
            "command": "具体命令",
            "type": "shell",
            "timeout": 30,
            "expected_result": "预期结果",
            "validation": {{
                "type": "output",
                "pattern": "验证模式"
            }}
        }}
    ],
    "execution_plan": {{
        "total_steps": 3,
        "estimated_time": "预估总时间",
        "dependencies": ["依赖项1", "依赖项2"],
        "risk_level": "风险等级"
    }}
}}

请确保命令安全可执行，并包含适当的验证机制。
            """
        )

    async def generate_execution_plan(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成执行计划
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            执行计划
        """
        try:
            log_debug("Generating execution plan")
            
            # 构建命令生成链
            command_chain = self.command_generation_prompt | self.llm | self.json_parser
            
            # 生成执行计划
            execution_plan = await command_chain.ainvoke({
                "analysis_result": str(analysis_result)
            })
            
            log_info("Execution plan generated successfully")
            return execution_plan
            
        except Exception as e:
            log_error(f"Error generating execution plan: {e}")
            return {
                "commands": [],
                "execution_plan": {
                    "total_steps": 0,
                    "estimated_time": "unknown",
                    "dependencies": [],
                    "risk_level": "high"
                },
                "error": str(e)
            } 