"""
智能执行器核心类
基于Lang<PERSON>hain实现的智能代理系统
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI

from ..agents.case_analyzer.analyzer import CaseAnalyzer
from ..agents.execution_engine.engine import ExecutionEngine
from ..agents.report_generator.generator import ReportGenerator
# from ..vectordb.manager import VectorDBManager
from ..utils.session_manager import SessionManager
from ..chains.analysis_chain import AnalysisChain
from ..chains.execution_chain import ExecutionChain

from infocollect.log.logger import log_info, log_error, log_debug


@dataclass
class ExecutionSession:
    """执行会话数据类"""
    session_id: str
    start_time: str
    case_info: Dict[str, Any] = field(default_factory=dict)
    matched_cases: List[Dict[str, Any]] = field(default_factory=list)
    analysis_results: List[Dict[str, Any]] = field(default_factory=list)
    execution_results: List[Dict[str, Any]] = field(default_factory=list)
    report_data: Optional[Dict[str, Any]] = None
    end_time: Optional[str] = None
    duration: float = 0.0
    status: str = "running"


class IntelligentExecutor:
    """
    智能执行器 - 基于LangChain的智能代理系统
    
    主要功能:
    1. 用例智能匹配和分析
    2. 自动化执行引擎
    3. 智能报告生成
    4. 向量数据库支持
    """
    
    def __init__(self, 
                 openai_api_key: Optional[str] = None,
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0.1):
        """
        初始化智能执行器
        
        Args:
            openai_api_key: OpenAI API密钥
            model_name: 使用的模型名称
            temperature: 模型温度参数
        """
        # 初始化LLM
        self.llm = ChatOpenAI(
            api_key=openai_api_key,
            model=model_name,
            temperature=temperature
        )
        
        # 初始化各个组件
        self.case_analyzer = CaseAnalyzer(llm=self.llm)
        self.execution_engine = ExecutionEngine(llm=self.llm)
        self.report_generator = ReportGenerator(llm=self.llm)
        self.vectordb_manager = VectorDBManager()
        self.session_manager = SessionManager()
        
        # 初始化LangChain链
        self.analysis_chain = AnalysisChain(llm=self.llm)
        self.execution_chain = ExecutionChain(llm=self.llm)
        
        # 创建工具列表
        self.tools = self._create_tools()
        
        # 创建ReAct Agent
        self.agent = self._create_react_agent()
        
        log_info("IntelligentExecutor initialized successfully")

    def _create_tools(self) -> List[Tool]:
        """创建Agent可用的工具"""
        return [
            Tool(
                name="analyze_case",
                description="分析用例信息并生成执行步骤",
                func=self._tool_analyze_case
            ),
            Tool(
                name="search_similar_cases",
                description="在向量数据库中搜索相似用例",
                func=self._tool_search_similar_cases
            ),
            Tool(
                name="execute_commands",
                description="执行生成的命令序列",
                func=self._tool_execute_commands
            ),
            Tool(
                name="generate_report",
                description="生成执行报告",
                func=self._tool_generate_report
            )
        ]

    def _create_react_agent(self) -> AgentExecutor:
        """创建ReAct Agent"""
        prompt = PromptTemplate.from_template("""
你是一个智能执行器代理，负责分析用例信息、执行相关操作并生成报告。

你有以下工具可用:
{tools}

使用以下格式:
Question: 需要处理的问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是[{tool_names}]中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个思考/行动/观察可以重复N次)
Thought: 我现在知道最终答案了
Final Answer: 对原始问题的最终答案

Question: {input}
{agent_scratchpad}
        """)
        
        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True)

    async def execute_intelligent_analysis(self, 
                                         case_info: Dict[str, Any],
                                         max_similar_cases: int = 5,
                                         auto_execute: bool = False) -> Dict[str, Any]:
        """
        执行智能分析流程
        
        Args:
            case_info: 用例信息
            max_similar_cases: 最大相似用例数量
            auto_execute: 是否自动执行
            
        Returns:
            执行结果字典
        """
        # 创建执行会话
        session = ExecutionSession(
            session_id=str(uuid.uuid4()),
            start_time=datetime.now().isoformat(),
            case_info=case_info
        )
        
        try:
            log_info(f"Starting intelligent analysis session: {session.session_id}")
            
            # 使用Agent执行分析
            query = f"分析以下用例信息并生成执行方案: {case_info}"
            result = await self.agent.ainvoke({"input": query})
            
            # 更新会话状态
            session.end_time = datetime.now().isoformat()
            session.status = "completed"
            
            # 保存会话
            self.session_manager.save_session(session)
            
            return {
                "success": True,
                "session_id": session.session_id,
                "result": result,
                "session_data": self._serialize_session(session)
            }
            
        except Exception as e:
            log_error(f"Error in intelligent analysis: {e}")
            session.status = "failed"
            session.end_time = datetime.now().isoformat()
            
            return {
                "success": False,
                "session_id": session.session_id,
                "error": str(e),
                "session_data": self._serialize_session(session)
            }

    async def _tool_analyze_case(self, case_info_str: str) -> str:
        """工具：分析用例"""
        try:
            case_info = eval(case_info_str)  # 简单解析，实际应用中需要更安全的方法
            result = await self.case_analyzer.analyze_case(case_info)
            return f"用例分析完成: {result}"
        except Exception as e:
            return f"用例分析失败: {str(e)}"

    async def _tool_search_similar_cases(self, query: str) -> str:
        """工具：搜索相似用例"""
        try:
            results = await self.vectordb_manager.search_similar_cases(query)
            return f"找到 {len(results)} 个相似用例: {results}"
        except Exception as e:
            return f"搜索相似用例失败: {str(e)}"

    async def _tool_execute_commands(self, commands_str: str) -> str:
        """工具：执行命令"""
        try:
            commands = eval(commands_str)  # 简单解析，实际应用中需要更安全的方法
            result = await self.execution_engine.execute_commands(commands)
            return f"命令执行完成: {result}"
        except Exception as e:
            return f"命令执行失败: {str(e)}"

    async def _tool_generate_report(self, data_str: str) -> str:
        """工具：生成报告"""
        try:
            data = eval(data_str)  # 简单解析，实际应用中需要更安全的方法
            result = await self.report_generator.generate_report(data)
            return f"报告生成完成: {result}"
        except Exception as e:
            return f"报告生成失败: {str(e)}"

    def _serialize_session(self, session: ExecutionSession) -> Dict[str, Any]:
        """序列化会话数据"""
        return {
            "session_id": session.session_id,
            "start_time": session.start_time,
            "end_time": session.end_time,
            "duration": session.duration,
            "status": session.status,
            "case_info": session.case_info,
            "matched_cases_count": len(session.matched_cases),
            "analysis_results_count": len(session.analysis_results),
            "execution_results_count": len(session.execution_results)
        }

    def get_session_history(self) -> List[Dict[str, Any]]:
        """获取会话历史"""
        return self.session_manager.get_all_sessions()

    def get_session_by_id(self, session_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取会话"""
        return self.session_manager.get_session(session_id) 