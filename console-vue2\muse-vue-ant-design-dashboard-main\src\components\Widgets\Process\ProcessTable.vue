<template>
  <div>
    <a-modal
      :visible="localVisible"
      title="Process Details"
      width="1500px"
      @cancel="handleClose"
      class="process-table-modal"
    >
      <template v-slot:footer>
        <a-button @click="handleClose">Close</a-button>
      </template>
      <template v-if="processContainer">
        <a-table
          :columns="processColumns"
          :dataSource="getProcessList()"
          :pagination="{ pageSize: 10 }"
          size="middle"
        >
        </a-table>
      </template>
    </a-modal>

    <process-detail-modal
      :visible="processDetailInfoVisible"
      :process-info="selectedProcessInfo"
      :user-field="userField"
      @update:visible="processDetailInfoVisible = $event"
      @close="handleProcessDetailInfoClose"
    />
  </div>
</template>

<script>
import ProcessDetailModal from './ProcessDetailModal.vue';

export default {
  name: 'ProcessTable',
  components: {
    ProcessDetailModal
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    processContainer: {
      type: Object,
      default: null
    },
    // 允许自定义用户字段名称（uid或user）
    userField: {
      type: String,
      default: 'user'
    },
    // 是否包含TTY列
    includeTty: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localVisible: this.visible,
      processDetailInfoVisible: false,
      selectedProcessInfo: null
    };
  },
  watch: {
    visible(newValue) {
      this.localVisible = newValue;
    }
  },
  computed: {
    processColumns() {
      const columns = [
        { title: 'PID', dataIndex: 'pid', key: 'pid', width: '60px' },
        { title: 'PPID', dataIndex: 'ppid', key: 'ppid', width: '60px' },
        {
          title: 'UID',
          dataIndex: this.userField,
          key: this.userField,
          width: '80px'
        },
        { title: 'GID', dataIndex: 'gid', key: 'gid', width: '60px' },
        { title: 'State', dataIndex: 'state', key: 'state', width: '80px' },
        { title: 'Start Time', dataIndex: 'stime', key: 'stime', width: '100px' }
      ];

      if (this.includeTty) {
        columns.push({ title: 'TTY', dataIndex: 'tty', key: 'tty', width: '60px' });
      }

      columns.push(
        { title: 'Time', dataIndex: 'time', key: 'time', width: '60px' },
        {
          title: 'Command',
          dataIndex: 'cmd',
          key: 'cmd',
          width: '500px',
          ellipsis: false,
          customRender: (text) => {
            return {
              children: text,
              props: {
                style: {
                  whiteSpace: 'normal',
                  wordBreak: 'break-word'
                }
              }
            };
          }
        },
        {
          title: 'Details',
          key: 'details',
          width: '100px',
          align: 'center',
          customRender: (_, record) => {
            const hasDetails = record.exe || record.cwd || record.capability || record.environ || record.memory_maps;
            return hasDetails ? (
              <a-button
                type="link"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  this.showProcessDetailInfo(record);
                }}
              >
                View Details
              </a-button>
            ) : 'N/A';
          }
        }
      );

      return columns;
    }
  },
  methods: {
    handleClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    getProcessList() {
      if (!this.processContainer?.processes) return [];

      // 检查是否有新格式的进程列表
      const processes = this.processContainer.processes;

      // Docker格式
      if (processes.process_list && processes.process_list.length > 0) {
        return processes.process_list;
      }

      // Crictl格式
      if (processes.pid && Array.isArray(processes.pid)) {
        return processes.pid.map((_, index) => ({
          pid: processes.pid[index],
          ppid: processes.ppid[index],
          [this.userField]: processes[this.userField][index],
          cmd: processes.cmd[index],
          time: processes.time[index],
          stime: processes.stime[index]
        }));
      }

      return [];
    },
    showProcessDetailInfo(process) {
      this.selectedProcessInfo = process;
      this.processDetailInfoVisible = true;
    },
    handleProcessDetailInfoClose() {
      this.processDetailInfoVisible = false;
      this.selectedProcessInfo = null;
    }
  }
}
</script>

<style scoped lang="scss">
/* 进程表格样式 */
.ant-table-cell {
  white-space: pre-line !important;
  vertical-align: top;
  padding: 8px;
}
</style>
