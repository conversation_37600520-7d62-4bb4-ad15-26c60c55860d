{"name": "muse-vue-ant-design-dashboard", "version": "1.0.0", "description": "A user-friendly, open source and beautiful dashboard based on Ant Design Vue.", "main": "src/main.js", "homepage": "https://demos.creative-tim.com/muse-ant-design-dashboard/", "repository": "https://github.com/creativetimofficial/muse-ant-design-dashboard", "license": "MIT", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "cross-env CI=false cross-env PUBLIC_URL=/ vue-cli-service build", "dev": "npm run serve"}, "dependencies": {"ant-design-vue": "1.7.8", "axios": "^1.7.9", "chart.js": "3.4.1", "core-js": "3.15.2", "muse-vue-ant-design-dashboard": "file:", "sass": "^1.83.0", "socket.io-client": "^4.8.1", "vue": "^2.6.14", "vue-github-buttons": "^3.1.0", "vue-i18n": "^8.28.2", "vue-json-pretty": "^1.6.7", "vue-router": "3.5.2", "vuex": "^3.6.2", "xterm": "^4.19.0", "xterm-addon-fit": "^0.5.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.13", "@vue/cli-plugin-router": "4.5.13", "@vue/cli-service": "4.5.13", "cross-env": "^7.0.3", "sass-loader": "^10", "vue-template-compiler": "2.6.14"}}