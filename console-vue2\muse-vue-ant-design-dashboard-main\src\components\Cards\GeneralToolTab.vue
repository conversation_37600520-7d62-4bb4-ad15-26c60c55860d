<template>
  <a-form :form="form" layout="vertical">
    <!-- 工具配置 - 上传包和编辑脚本在一行 -->
    <a-form-item :label="$t('tool.uploadToolPackage')">
      <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
        <!-- 工具包上传 -->
        <div style="flex: 1; min-width: 200px;">
          <a-upload
            name="script"
            :before-upload="beforeUpload"
            :show-upload-list="true"
            @change="handleUploadChange"
            :disabled="isProcessing"
            :file-list="fileList"
            accept=".zip,application/zip,application/x-zip-compressed"
          >
            <a-button
              :disabled="isProcessing"
              class="nav-style-button"
            >
              <a-icon type="upload" /> {{ $t('tool.selectToolPackage') }}
            </a-button>
          </a-upload>
          <span v-if="fileName" style="margin-left: 10px; color: #666; font-size: 12px;">
            {{ fileName }}
          </span>
        </div>
        
        <!-- 脚本编辑 -->
        <div style="flex: 1; min-width: 200px;">
          <script-editor
            v-model="scriptContent"
            :script-tabs="scriptTabs"
            :default-tab="activeScriptTab"
            :disabled="isProcessing"
            :filename="'script.sh'"
            :success-message="'Script saved successfully'"
            @script-saved="onScriptSaved"
          />
        </div>
      </div>
    </a-form-item>

    <!-- 本地保存路径 -->
    <a-form-item :label="$t('tool.localSaveDirectory')">
      <a-input
        v-model="localSavePath"
        placeholder="e.g., results/tool_results"
        :disabled="isProcessing"
      />
    </a-form-item>
  </a-form>
</template>

<script>
import axios from '@/api/axiosInstance';
import { mapState } from 'vuex';
import { getGeneralScriptNames, getScriptContent } from '@/assets/scripts';
import ScriptEditor from '@/components/common/ScriptEditor.vue';

export default {
  name: 'GeneralToolTab',
  components: {
    ScriptEditor
  },
  props: {
    isProcessing: {
      type: Boolean,
      default: false
    },
    currentProject: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      uploadUrl: '/api/script/upload',
      toolPath: '',
      localSavePath: 'eg : D:\\_Projects_python\\SEC-SPIDER\\release\\ssp\\sSpider\\upload\\task',
      fileList: [],
      fileName: '',
      scriptContent: '',
      scriptTabs: [],
      activeScriptTab: 'default_command',
      scriptPath: ''
    };
  },
  computed: {
    ...mapState(['sidebarColor']),
    toolConfigComplete() {
      return this.toolPath && this.scriptContent && this.localSavePath;
    }
  },
  created() {
    // 初始化通用脚本选项卡
    this.scriptTabs = getGeneralScriptNames();
    // 加载默认通用脚本内容
    this.loadScriptContent(this.activeScriptTab);
  },
  methods: {
    loadScriptContent(tabKey) {
      this.activeScriptTab = tabKey;
      this.scriptContent = getScriptContent(tabKey);
    },
    onScriptSaved(path) {
      this.scriptPath = path;
      this.$emit('script-saved', path);
    },
    beforeUpload(file) {
      const isZip = file.type === 'application/zip' ||
                    file.type === 'application/x-zip-compressed' ||
                    file.name.endsWith('.zip');
      if (!isZip) {
        this.$message.error('You can only upload ZIP files!');
        return false;
      }

      this.file = file;
      this.fileName = file.name; // 设置文件名

      // 手动上传文件
      this.uploadFile(file);

      return false;  // 阻止自动上传
    },
    async uploadFile(file) {
      // 显示上传中的消息
      const uploadingMessage = this.$message.loading('Uploading...', 0);

      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post(this.uploadUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        // 关闭上传中消息
        uploadingMessage();

        if (response.data && response.data.path) {
          this.toolPath = response.data.path;
          this.$emit('tool-path-changed', response.data.path);
          this.$message.success(`${file.name} uploaded successfully`);

          // 更新fileList以触发handleUploadChange
          const fileInfo = {
            uid: file.uid,
            name: file.name,
            status: 'done',
            response: response.data
          };
          this.fileList = [fileInfo];

          // 手动触发计算属性更新
          this.$forceUpdate();
        } else {
          this.$message.error(`${file.name} upload response missing path field`);
        }
      } catch (error) {
        // 确保在错误情况下也关闭上传消息
        uploadingMessage();

        console.error('Upload error:', error);
        this.$message.error(`${file.name} upload failed: ${error.response?.data?.error || error.message}`);
      }
    },
    handleUploadChange(info) {
      this.fileList = [...info.fileList];

      // 限制只显示最后一个上传的文件
      this.fileList = this.fileList.slice(-1);

      const status = info.file.status;
      if (status === 'done') {
        if (info.file.response && info.file.response.path) {
          this.toolPath = info.file.response.path;
          this.$emit('tool-path-changed', info.file.response.path);
          this.fileName = info.file.name; // 确保这里也设置文件名
          this.$message.success(`${info.file.name} uploaded successfully`);

          // 手动触发计算属性更新
          this.$forceUpdate();
        } else {
          this.$message.error(`${info.file.name} upload response missing path field`);
        }
      } else if (status === 'error') {
        this.$message.error(`${info.file.name} upload failed.`);
      }
    },
    getToolData() {
      return {
        toolPath: this.toolPath,
        scriptContent: this.scriptContent,
        scriptPath: this.scriptPath,
        localSavePath: this.localSavePath
      };
    }
  }
};
</script>
