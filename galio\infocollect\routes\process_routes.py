from flask import Blueprint, jsonify, request
from services.process_service import ProcessService
from routes.database_wraps import with_database_session

bp = Blueprint('process', __name__)


@bp.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_processes_or_detail(node_ip, db):
    pid = request.args.get('pid')
    fields = request.args.get('fields', default='pid,cmdline', type=str).split(',')
    
    process_service = ProcessService(db)
    try:
        process_service.validate_fields(fields)
    except ValueError as e:
        return jsonify({"error": str(e)}), 400
    
    node = process_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    if pid:
        result = process_service.get_process(node.id, int(pid), fields)
        if not result:
            return jsonify({"error": "Process not found"}), 404
        return jsonify(result), 200
    
    result = process_service.get_processes(node.id, fields)
    if not result:
        return jsonify([]), 200
    
    return jsonify(result), 200
