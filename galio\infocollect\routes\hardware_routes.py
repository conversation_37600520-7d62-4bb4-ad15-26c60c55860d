from flask import Blueprint, jsonify
from services.hardware_service import HardwareService
from routes.database_wraps import with_database_session

bp = Blueprint('hardware', __name__)


@bp.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_hardware(node_ip, db):
    hardware_service = HardwareService(db)
    node = hardware_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404

    result = hardware_service.get_hardware(node.id)
    if not result:
        return jsonify([]), 200

    return jsonify(result), 200