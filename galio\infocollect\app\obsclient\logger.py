import datetime


class LogColors:
    DEBUG = "\033[0;37m"
    INFO = "\033[0;32m"
    WARNING = "\033[93m"
    ERROR = "\033[91m"
    RESET = "\033[0m"


def current_time():
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def log_debug(message):
    print(f"{LogColors.DEBUG}[DEBUG] {current_time()} {message} {LogColors.RESET}")


def log_info(message):
    print(f"{LogColors.INFO}[INFO] {current_time()}{LogColors.RESET} {message}")


def log_warning(message):
    print(f"{LogColors.WARNING}[WARNING] {current_time()}{LogColors.RESET} {message}")


def log_error(message):
    print(f"{LogColors.ERROR}[ERROR] {current_time()}{LogColors.RESET} {message}")
