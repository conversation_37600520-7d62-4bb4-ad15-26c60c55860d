<template>
  <a-card
    :bordered="false"
    class="header-solid h-full filesystem-card"
    :bodyStyle="{ padding: 0 }"
  >

    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16" :class="`text-${sidebarColor}`">
              <path :fill="'currentColor'" fill-rule="evenodd" d="m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z" clip-rule="evenodd"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.mount') }}</h6>
        </div>
        <div>
          <RefreshButton @refresh="fetchFilesystem" />
        </div>
      </div>
    </template>

    <a-table
      :columns="columns"
      :dataSource="filesystemItems"
      :rowKey="(record) => record.key || record.device"
      :pagination="pagination"
      :loading="loading"
    >
    </a-table>
  </a-card>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';
import RefreshButton from '../Widgets/RefreshButton.vue';

export default {
  components: {
    RefreshButton
  },
  name: 'FilesystemInfo',
  data() {
    return {
      filesystemItems: [],
      loading: false,
      columns: [
        {
          title: 'Device',
          dataIndex: 'device',
          key: 'device',
          width: '20%',
          ellipsis: true,
        },
        {
          title: 'Mount Point',
          dataIndex: 'mount_point',
          key: 'mount_point',
          width: '25%',
          ellipsis: true,
        },
        {
          title: 'File System Type',
          dataIndex: 'fs_type',
          key: 'fs_type',
          width: '15%',
          ellipsis: true,
        },
        {
          title: 'Mount Options',
          dataIndex: 'mount_options',
          key: 'mount_options',
          width: '40%',
          ellipsis: true,
        },
      ],
      pagination: {
        pageSize: 100
      },
    };
  },
  computed: {
    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),
  },
  watch: {
    selectedNodeIp(newIp) {
      this.fetchFilesystem();
    },
  },
  mounted() {
    this.fetchFilesystem();
  },
  methods: {
    async fetchFilesystem() {
      if (!this.selectedNodeIp) {
        console.error('Node IP is not defined');
        this.filesystemItems = [];
        return;
      }
      try {
        // 显示加载状态
        this.loading = true;
        const response = await axios.get(`/api/filesystem/${this.selectedNodeIp}`, {
          params: {
            dbFile: this.currentProject
          }
        });

        // 处理数据，确保每条记录有唯一的key
        this.filesystemItems = response.data.map((item, index) => ({
          ...item,
          // 使用组合键作为唯一标识，防止device重复导致的渲染问题
          key: `${item.device}_${item.mount_point}_${index}`
        }));
      } catch (error) {
        console.error('Error fetching filesystem:', error);
        this.filesystemItems = [];
      } finally {
        // 无论成功失败，都关闭加载状态
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.filesystem-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  position: relative; /* 确保卡片有相对定位，防止内部元素溢出 */
  z-index: 1; /* 设置合适的z-index，防止与其他元素冲突 */
}

.ant-table {
  border-radius: 0 0 8px 8px; /* 匹配卡片圆角 */
}

/* 表头样式 */
.ant-table-thead > tr > th {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0; /* 浅色分割线 */
  color: #666;
}

/* 悬停效果 */
.ant-table-tbody > tr:hover > td {
  background-color: #fafafa !important;
}

/* 优化表格滚动行为 */
::v-deep .ant-table-body {
  overflow-x: auto !important;
}

/* 确保表格内容不会溢出 */
::v-deep .ant-table-row td {
  word-break: break-word;
  white-space: normal;
}

/* 优化表格在移动设备上的显示 */
@media (max-width: 768px) {
  ::v-deep .ant-pagination {
    font-size: 12px;
  }

  ::v-deep .ant-pagination-options {
    margin-left: 8px;
  }
}
</style>
