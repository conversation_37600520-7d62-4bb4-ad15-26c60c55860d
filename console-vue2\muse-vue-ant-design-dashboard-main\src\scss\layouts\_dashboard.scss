//
// Template's dashboard layout styles.
//

// 定义CSS变量
:root {
	--primary-color-rgb: 30, 144, 255;
}

// Dashboard Layout
.layout-dashboard {
	background: transparent;
	text-align: left;
	position: relative;
	overflow-x: hidden;

	@media( min-width: $lg ){
		overflow: auto;
	}

	// Primary Sidebar
	.ant-layout-sider.sider-primary {
		width: 250px;
		position: fixed;
		left: 0;
		z-index: 99;
		height: 100vh;
		overflow: auto;
		margin: 0;
		padding: 33px 20px;
		background: #ffffff !important;

		// 整体侧边栏容器
		.sidebar-container {
			display: flex;
			flex-direction: column;
			height: 100%;
			overflow: hidden;
		}

		// 品牌区域样式
		.brand-section {
			padding: 16px 0 8px;
			flex-shrink: 0;
		}

		.brand-header {
			display: flex;
			align-items: center;
			padding: 0 16px;
			margin-bottom: 8px;
		}

		.brand-divider {
			height: 1px;
			background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.06), rgba(0,0,0,0.02));
			margin: 0 8px;
			box-shadow: 0 1px 2px rgba(0,0,0,0.03);
		}

		.logo-container {
			width: 38px;
			height: 38px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 10px;
			position: relative;
			flex-shrink: 0;
		}

		.ios-logo {
			width: 100%;
			height: 100%;
			filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
			transition: transform 0.3s ease;

			&:hover {
				transform: scale(1.1);
			}
		}

		.brand-text-container {
			display: flex;
			flex-direction: column;
			justify-content: center;
		}
		// 品牌logo字体
		.brand-name {
			font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
			font-weight: 600;
			font-size: 14px;
			color: #333;
			letter-spacing: -0.3px;
			line-height: 1.2;
		}

		// 导航区域样式
		.navigation-section {
			flex: 1;
			overflow-y: auto;
			overflow-x: hidden; // 防止水平滚动条出现
			padding-top: 8px;
		}

		// 菜单样式增强
		.sidebar-menu {
			position: relative;
			z-index: 1;
			overflow-x: hidden; // 防止水平滚动条出现
		}

		// 底部区域样式
		.footer-section {
			flex-shrink: 0;
			height: 60px;
			position: relative;
		}

		// 侧边栏底部动画组件样式
		.sidebar-footer {
			width: 100%;
			height: 100%;
			z-index: 2;
		}

		@media( min-width: $lg ){
			margin: 10px 0 0 10px;
			padding: 13px 10px;
			height: calc(100vh - 10px);
			background: transparent !important;
		}

		&.ant-layout-sider-dark {
			background-color: #001529 !important;
			box-shadow: $shadow-1;
		}

		&.ant-layout-sider-zero-width {
			overflow-x: hidden;
			width: 210px;
			margin: 0;
			padding: 33px 0px;

			@media( min-width: $lg ){
				padding: 13px 0;
				margin: 20px 0 0 0;
			}
		}

		.ant-layout-sider-children {
			width: 210px;
			@media( min-width: $lg ){
				width: auto;
			}
		}

		.brand {
			font-weight: $fw-semibold;
			padding: $submenu-links-p;
			margin-top: -15px;
			margin-left: 15px;

			span {
				vertical-align: middle;
			}
			img {
				height: 40px;
				margin-right: 10px;
			}
		}
		&.ant-layout-sider-dark .brand span {
			color: $color-gray-1;
		}
		hr {
			margin: 18px 0;
			border: none;
			height: 1px;
			background-color: $color-gray-2;
		}

		&.ant-layout-sider-white {
			background-color: $color-gray-1 !important;
			box-shadow: $shadow-1;
			margin: 0;
			padding-top: 33px;
			border-radius: 0px;

			@media( min-width: $lg ){
				margin: 20px 0 64px 20px;
				padding-top: 13px;
				border-radius: 8px;
			}
		}
		.ant-menu-inline {
			border: none;
			.ant-menu-item,
			.ant-menu-submenu {
				margin: 0;
				overflow: visible;
				&::after {
					display: none;
				}
			}
			.ant-menu-submenu-title {
				overflow: visible;
				transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
				position: relative;
				transform: translateY(0);
				box-shadow: 0 0 0 rgba(0, 0, 0, 0);

				// 子菜单标题悬停效果 - 只有背景色变化
				&:hover {
					background-color: rgba(245, 247, 250, 0.7);
				}

				// 增强标题样式
				.enhanced-title {
					transition: color 0.2s ease;

					&:hover {
						color: rgba(var(--primary-color-rgb), 0.9);
					}
				}
			}
		}

		// 单独为菜单项添加移动效果
		.ant-menu-item {
			transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
			transform: translateX(0);

			&:hover {
				transform: translateX(8px);
			}
		}

		// 子菜单项的移动效果
		.ant-menu-sub .ant-menu-item {
			transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
			transform: translateX(0);

			&:hover {
				transform: translateX(8px);
			}
		}

		// 保持子菜单标题（分组）不移动
		.ant-menu-submenu-title {
			transition: background-color 0.3s ease !important;
			transform: none !important;
		}

		// 确保分组展开时不影响其他样式
		.ant-menu-submenu {
			&:hover {
				.ant-menu-submenu-title {
					background-color: rgba(0, 0, 0, 0.02);
				}
			}
		}

		a {
			padding: $submenu-links-p;
			color: $color-gray-12;
			border-radius: 8px;
			transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
		}

		.ant-menu-item,
		.ant-menu-submenu {
			padding: 0 !important;
			height: auto;
			line-height: normal;
			transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
			position: relative;
			transform: translateY(0);
			box-shadow: 0 0 0 rgba(0, 0, 0, 0);

			// 移除鼠标悬停时的背景色变化
			&:hover {
				// 不添加背景色
			}

			&.ant-menu-item:active,
			&.ant-menu-submenu-title:active,
			&.ant-menu-item-selected,
			.ant-menu-submenu:active,
			.ant-menu-submenu-selected {
				background-color: transparent;
			}

			a {
				padding: $submenu-links-p;
				color: $color-gray-12;
				border-radius: 8px;
				transition: all 0.3s ease;
			}

			.icon {
				display: inline-flex;
				width: 32px;
				height: 32px;
				background-color: $color-gray-1;
				box-shadow: $shadow-2;
				border-radius: 6px;
				justify-content: center;
				align-items: center;
				margin-right: 11px;
				vertical-align: middle;
				transition: background-color 0.2s ease;
			}

			svg path {
				fill: $color-gray-6;
				transition: fill 0.2s ease;
			}
			// 左侧导航栏字体样式
			.label {
				vertical-align: middle;
				font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
				font-weight: 500;
				font-size: 14px;
				line-height: 1.4;
				letter-spacing: 0.01em;
				color: rgba(0, 0, 0, 0.65);
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
			}

			// 移除冗余的悬停效果，已在a:hover中处理
			&.ant-menu-item-selected .router-link-active,
			.router-link-active {
				background-color: transparent; // 移除白色背景
				box-shadow: none;
				.icon {
					background-color: $color-primary;
				}
				svg path {
					fill: $color-gray-1;
				}
				.label {
					font-weight: $fw-semibold;
					color: $color-primary; // 使文字颜色变为主题色
				}
			}
			// 单独处理a标签的悬停，确保只有当前项的图标亮起
			> a:hover {
				color: $color-gray-12;
				.icon {
					transition: background-color 0.2s ease;
				}

				svg path {
					transition: fill 0.2s ease;
				}
			}
			&.menu-item-header {
				padding: 10px 16px !important;
				color: $color-gray-7;
				font-weight: $fw-bold;
				font-size: 13px;
				text-transform: uppercase;
			}
		}

		&.ant-layout-sider-white {
			.ant-menu-item {
				.icon {
					background-color: $color-gray-2;
				}
				&.ant-menu-item-selected .router-link-active,
				.router-link-active {
					background-color: transparent;
					box-shadow: none;
					.icon {
						background-color: $color-primary;
					}
					svg path {
						fill: $color-gray-1;
					}
					.label {
						color: $color-primary; // 使文字颜色变为主题色
						font-weight: $fw-semibold;
					}
				}
			}
		}

		&.ant-layout-sider-dark {
			margin: 0;
			padding-top: 33px;
			border-radius: 0px;

			@media( min-width: $lg ){
				margin: 20px 0 0 20px;
				padding-top: 13px;
				border-radius: 8px;
			}
			.ant-menu-item {
				a {
					padding: 10px 10px;
					border-radius: 6px;
					margin: 7px 0;
				}
				.icon {
					background-color: transparent;
					height: auto
				}
				.label {
					color: rgba(255, 255, 255, 0.85);
					text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
				}
				&.ant-menu-item-selected .router-link-active,
				.router-link-active {
					background-color: transparent; // 移除蓝色背景，保持黑色背景
					box-shadow: none;
					.icon {
						background-color: transparent;
						box-shadow: none;
					}
					svg path {
						fill: $color-gray-1;
					}
					.label {
						color: $color-primary; // 使文字颜色变为主题色
						font-weight: $fw-semibold;
					}
				}
				&.menu-item-header {
					color: $color-gray-1;
					opacity: .7;
				}
			}
		}

		$colors: (
			"primary": $color-primary,
			"purple": $color-purple,
			"green": $color-green,
			"gray": $color-gray,
		) ;
		@each $name, $color in $colors {
			// 为每个主题颜色设置对应的悬停效果
			&.ant-layout-sider-#{$name} {
				// 菜单项悬停效果 - 只改变图标背景色
				.ant-menu-item > a:hover {
					.icon {
						background-color: rgba($color, 0.15);
					}

					svg path {
						fill: rgba($color, 0.8);
					}
				}

				// 子菜单项悬停效果 - 只改变图标背景色
				.ant-menu-submenu .ant-menu-item a:hover {
					.icon {
						background-color: rgba($color, 0.15);
					}

					svg path {
						fill: rgba($color, 0.8);
					}
				}

				// 菜单组标题样式 - 默认灰色，悬停时变为主题色
				.enhanced-title {
					color: #818080 !important; // 默认颜色为灰色，使用!important确保优先级

					&:hover {
						color: rgba($color, 0.8) !important; // 悬停时变为主题色，使用!important确保优先级
					}

					&:hover::before {
						background-color: rgba($color, 0.6);
					}
				}

				// 活动状态
				.ant-menu-item {
					&.ant-menu-item-selected .router-link-active,
					.router-link-active {
						.icon {
							background-color: $color;
						}

						// 确保活动状态下图标仍然可见
						svg path {
							fill: #ffffff;
						}
					}
				}
			}
		}

		.aside-footer {
			display: none;
			padding-top: 50px;
			padding-bottom: 33px;

			@media( min-width: $lg ){
				padding-bottom: 30px;
			}
		}
		.footer-box {
			background-color: $color-gray-1;
			color: $color-gray-12;
			padding: 16px;
			border-radius: 8px;
			box-shadow: $shadow-1;
			margin-bottom: 8px;

			.icon {
				display: inline-flex;
				width: 32px;
				height: 32px;
				box-shadow: $shadow-2;
				border-radius: 6px;
				justify-content: center;
				align-items: center;
				margin-bottom: 15px;
				background-color: $color-primary;
			}
			svg path {
				fill: $color-gray-1;
			}
			h6 {
				font-weight: $fw-semibold;
				font-size: 16px;
				color: $color-gray-12;
				margin-bottom: 0;
			}
			p {
				color: $color-gray-7;
				font-weight: $fw-semibold;
			}
			button {
				margin: 0;
			}
		}

		&.ant-layout-sider-dark .footer-box,
		&.ant-layout-sider-white .footer-box {
			background-color: $color-gray-2;
			box-shadow: none;
		}
		&.ant-layout-sider-primary .footer-box {
			background-color: $color-primary;
			color: $color-gray-1;
			box-shadow: none;
			.icon {
				background-color: $color-gray-1;
			}
			svg path {
				fill: $color-primary;
			}
			h6 {
				color: $color-gray-1;
			}
			p {
				color: $color-gray-3;
			}
			button,
			a {
				background-color: $color-gray-1;
				color: $color-gray-12;
			}
		}
		@each $name, $color in $colors {
			&.ant-layout-sider-#{$name} .footer-box {
				background-color: $color;
				color: $color-gray-1;
				box-shadow: none;
				.icon {
					background-color: $color-gray-1;
				}
				svg path {
					fill: $color;
				}
				h6 {
					color: $color-gray-1;
				}
				p {
					color: $color-gray-3;
				}
				button,
				a {
					background-color: $color-gray-1;
					border-color: $color-gray-4;
					color: $color-gray-12;
				}
			}
		}
	}
	&.has-sidebar .ant-layout-sider.sider-primary .aside-footer {
		display: block;
	}
	.sidebar-overlay {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: $color-gray-12;
		opacity: .5;
		z-index: 9;

		@media( min-width: $lg ){
			background-color: transparent;
			opacity: 0;
			pointer-events: none;
		}
	}
	// / Primary Sidebar

	// Content/Main Layout
	.ant-layout {
		background: transparent;
		position: relative;
		min-height: 100vh;
		width: 100%;
		flex-shrink: 0;

		@media( min-width: $lg ){
			width: auto;
			flex-shrink: 1;
			margin-left: 220px;
		}
	}
	.ant-menu {
		background: transparent;
	}
	.ant-layout-content {
		padding: 0px;
		margin: 0px 20px 0px;
	}
	// / Content/Main Layout

	// Note: Header styles have been moved to _header.scss for better organization

	// Page Footer
	.ant-layout-footer {
		background: transparent;
		margin: 0px 20px 20px 20px;
		padding: 0px;

		@media( min-width: $md ){
			margin: 0px 0px 20px 20px;
		}
		.ant-menu-horizontal {
			border: none;
			line-height: 1.5;
			margin-top: 20px;
			text-align: center;

			@media( min-width: $md ){
				margin-top: 0;
				text-align: right;
			}
			> .ant-menu-item,
			> .ant-menu-item:hover,
			> .ant-menu-item-active,
			> .ant-menu-item-selected,
			> .ant-menu-submenu,
			> .ant-menu-submenu:hover,
			> .ant-menu-submenu-active,
			> .ant-menu-submenu-selected {
				color: $color-gray-7;
				font-weight: $fw-semibold;
				border: none;
			}
			> .ant-menu-item:hover,
			> .ant-menu-item-active,
			> .ant-menu-item-selected,
			> .ant-menu-submenu:hover,
			> .ant-menu-submenu-active,
			> .ant-menu-submenu-selected {
				color: $color-gray-10;
			}

			.ant-menu-submenu-title {
				font-size: 16px;
				font-weight: bold;
			}
		}
		.copyright {
			margin: 0;
			font-weight: $fw-semibold;
			color: $color-gray-7;
			text-align: center;

			@media( min-width: $md ){
				text-align: left;
			}

			svg {
				width: 16px;
				height: 16px;
				vertical-align: -2px;
				path {
					fill: $color-danger;
				}
			}
			a {
				font-weight: $fw-bold;
				color: $color-gray-12;
			}
		}
	}
	// / Page Footer
	}
