<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="139">
            <item index="0" class="java.lang.String" itemvalue="trove_classifiers" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="3" class="java.lang.String" itemvalue="cffi" />
            <item index="4" class="java.lang.String" itemvalue="dl" />
            <item index="5" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="6" class="java.lang.String" itemvalue="invoke" />
            <item index="7" class="java.lang.String" itemvalue="jnius" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="pyasn1" />
            <item index="10" class="java.lang.String" itemvalue="wmi" />
            <item index="11" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="12" class="java.lang.String" itemvalue="docutils" />
            <item index="13" class="java.lang.String" itemvalue="gevent" />
            <item index="14" class="java.lang.String" itemvalue="truststore" />
            <item index="15" class="java.lang.String" itemvalue="attr" />
            <item index="16" class="java.lang.String" itemvalue="Cython" />
            <item index="17" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="18" class="java.lang.String" itemvalue="blinker" />
            <item index="19" class="java.lang.String" itemvalue="pywin32" />
            <item index="20" class="java.lang.String" itemvalue="xmlrpclib" />
            <item index="21" class="java.lang.String" itemvalue="Mailman" />
            <item index="22" class="java.lang.String" itemvalue="tornado" />
            <item index="23" class="java.lang.String" itemvalue="railroad" />
            <item index="24" class="java.lang.String" itemvalue="asgiref" />
            <item index="25" class="java.lang.String" itemvalue="ConfigParser" />
            <item index="26" class="java.lang.String" itemvalue="ipython" />
            <item index="27" class="java.lang.String" itemvalue="HTMLParser" />
            <item index="28" class="java.lang.String" itemvalue="urllib3_secure_extra" />
            <item index="29" class="java.lang.String" itemvalue="concurrencytest" />
            <item index="30" class="java.lang.String" itemvalue="sspi" />
            <item index="31" class="java.lang.String" itemvalue="lockfile" />
            <item index="32" class="java.lang.String" itemvalue="watchdog" />
            <item index="33" class="java.lang.String" itemvalue="usercustomize" />
            <item index="34" class="java.lang.String" itemvalue="Sphinx" />
            <item index="35" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="36" class="java.lang.String" itemvalue="keyring" />
            <item index="37" class="java.lang.String" itemvalue="gssapi" />
            <item index="38" class="java.lang.String" itemvalue="Pillow" />
            <item index="39" class="java.lang.String" itemvalue="werkzeug" />
            <item index="40" class="java.lang.String" itemvalue="wheel" />
            <item index="41" class="java.lang.String" itemvalue="PyYAML" />
            <item index="42" class="java.lang.String" itemvalue="cryptography" />
            <item index="43" class="java.lang.String" itemvalue="setuptools" />
            <item index="44" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="45" class="java.lang.String" itemvalue="pycparser" />
            <item index="46" class="java.lang.String" itemvalue="click" />
            <item index="47" class="java.lang.String" itemvalue="jinja2" />
            <item index="48" class="java.lang.String" itemvalue="zipp" />
            <item index="49" class="java.lang.String" itemvalue="flask" />
            <item index="50" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="51" class="java.lang.String" itemvalue="paramiko" />
            <item index="52" class="java.lang.String" itemvalue="pip" />
            <item index="53" class="java.lang.String" itemvalue="psutil" />
            <item index="54" class="java.lang.String" itemvalue="bcrypt" />
            <item index="55" class="java.lang.String" itemvalue="tqdm" />
            <item index="56" class="java.lang.String" itemvalue="apscheduler" />
            <item index="57" class="java.lang.String" itemvalue="concurrent-log-handler" />
            <item index="58" class="java.lang.String" itemvalue="requests" />
            <item index="59" class="java.lang.String" itemvalue="esdk-obs-python" />
            <item index="60" class="java.lang.String" itemvalue="pycocotools" />
            <item index="61" class="java.lang.String" itemvalue="setuptools-pipfile" />
            <item index="62" class="java.lang.String" itemvalue="setuptools-scm" />
            <item index="63" class="java.lang.String" itemvalue="wushan-security" />
            <item index="64" class="java.lang.String" itemvalue="schedule" />
            <item index="65" class="java.lang.String" itemvalue="ccms" />
            <item index="66" class="java.lang.String" itemvalue="urllib3" />
            <item index="67" class="java.lang.String" itemvalue="jsonschema" />
            <item index="68" class="java.lang.String" itemvalue="aiohttp" />
            <item index="69" class="java.lang.String" itemvalue="websockets" />
            <item index="70" class="java.lang.String" itemvalue="octopus-simulation-interface" />
            <item index="71" class="java.lang.String" itemvalue="urwid-mitmproxy" />
            <item index="72" class="java.lang.String" itemvalue="trio-websocket" />
            <item index="73" class="java.lang.String" itemvalue="Brotli" />
            <item index="74" class="java.lang.String" itemvalue="mitmproxy" />
            <item index="75" class="java.lang.String" itemvalue="h11" />
            <item index="76" class="java.lang.String" itemvalue="Levenshtein" />
            <item index="77" class="java.lang.String" itemvalue="mitmproxy-windows" />
            <item index="78" class="java.lang.String" itemvalue="Jinja2" />
            <item index="79" class="java.lang.String" itemvalue="sniffio" />
            <item index="80" class="java.lang.String" itemvalue="websocket-client" />
            <item index="81" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="82" class="java.lang.String" itemvalue="trio" />
            <item index="83" class="java.lang.String" itemvalue="pylsqpack" />
            <item index="84" class="java.lang.String" itemvalue="selenium" />
            <item index="85" class="java.lang.String" itemvalue="certifi" />
            <item index="86" class="java.lang.String" itemvalue="soupsieve" />
            <item index="87" class="java.lang.String" itemvalue="pyparsing" />
            <item index="88" class="java.lang.String" itemvalue="outcome" />
            <item index="89" class="java.lang.String" itemvalue="Flask" />
            <item index="90" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="91" class="java.lang.String" itemvalue="kaitaistruct" />
            <item index="92" class="java.lang.String" itemvalue="service-identity" />
            <item index="93" class="java.lang.String" itemvalue="pyasn1_modules" />
            <item index="94" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="95" class="java.lang.String" itemvalue="mitmproxy_rs" />
            <item index="96" class="java.lang.String" itemvalue="rapidfuzz" />
            <item index="97" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="98" class="java.lang.String" itemvalue="zope.interface" />
            <item index="99" class="java.lang.String" itemvalue="zstandard" />
            <item index="100" class="java.lang.String" itemvalue="wsproto" />
            <item index="101" class="java.lang.String" itemvalue="attrs" />
            <item index="102" class="java.lang.String" itemvalue="colorama" />
            <item index="103" class="java.lang.String" itemvalue="requests-raw" />
            <item index="104" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="105" class="java.lang.String" itemvalue="aioquic" />
            <item index="106" class="java.lang.String" itemvalue="idna" />
            <item index="107" class="java.lang.String" itemvalue="referencing" />
            <item index="108" class="java.lang.String" itemvalue="greenlet" />
            <item index="109" class="java.lang.String" itemvalue="prance" />
            <item index="110" class="java.lang.String" itemvalue="pydivert" />
            <item index="111" class="java.lang.String" itemvalue="h2" />
            <item index="112" class="java.lang.String" itemvalue="ldap3" />
            <item index="113" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="114" class="java.lang.String" itemvalue="swagger-parser" />
            <item index="115" class="java.lang.String" itemvalue="rpds-py" />
            <item index="116" class="java.lang.String" itemvalue="exrex" />
            <item index="117" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="118" class="java.lang.String" itemvalue="zope.event" />
            <item index="119" class="java.lang.String" itemvalue="passlib" />
            <item index="120" class="java.lang.String" itemvalue="hyperframe" />
            <item index="121" class="java.lang.String" itemvalue="pyperclip" />
            <item index="122" class="java.lang.String" itemvalue="six" />
            <item index="123" class="java.lang.String" itemvalue="importlib_resources" />
            <item index="124" class="java.lang.String" itemvalue="packaging" />
            <item index="125" class="java.lang.String" itemvalue="publicsuffix2" />
            <item index="126" class="java.lang.String" itemvalue="swagger-spec-validator" />
            <item index="127" class="java.lang.String" itemvalue="hpack" />
            <item index="128" class="java.lang.String" itemvalue="sortedcontainers" />
            <item index="129" class="java.lang.String" itemvalue="chardet" />
            <item index="130" class="java.lang.String" itemvalue="PySocks" />
            <item index="131" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="132" class="java.lang.String" itemvalue="msgpack" />
            <item index="133" class="java.lang.String" itemvalue="pandas" />
            <item index="134" class="java.lang.String" itemvalue="PyNaCl" />
            <item index="135" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="136" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="137" class="java.lang.String" itemvalue="tzdata" />
            <item index="138" class="java.lang.String" itemvalue="pytz" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="TsLint" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>