{"version": 3, "sources": ["webpack:///./src/components/Cards/ToolsPanel.vue?92a1", "webpack:///./src/views/GenerateScript.vue", "webpack:///./src/components/Cards/ToolsPanel.vue", "webpack:///./src/components/Cards/GeneralToolTab.vue", "webpack:///./src/assets/scripts/default_command.js", "webpack:///./src/assets/scripts/spider_command.js", "webpack:///./src/assets/scripts/index.js", "webpack:///./src/components/common/ScriptEditor.vue", "webpack:///src/components/common/ScriptEditor.vue", "webpack:///./src/components/common/ScriptEditor.vue?89fd", "webpack:///./src/components/common/ScriptEditor.vue?7f63", "webpack:///src/components/Cards/GeneralToolTab.vue", "webpack:///./src/components/Cards/GeneralToolTab.vue?da47", "webpack:///./src/components/Cards/GeneralToolTab.vue?2746", "webpack:///./src/components/Cards/SpiderToolTab.vue", "webpack:///src/components/Cards/SpiderToolTab.vue", "webpack:///./src/components/Cards/SpiderToolTab.vue?c64b", "webpack:///./src/components/Cards/SpiderToolTab.vue?3f38", "webpack:///src/components/Cards/ToolsPanel.vue", "webpack:///./src/components/Cards/ToolsPanel.vue?ac87", "webpack:///./src/components/Cards/ToolsPanel.vue?f58e", "webpack:///src/views/GenerateScript.vue", "webpack:///./src/views/GenerateScript.vue?1b1f", "webpack:///./src/views/GenerateScript.vue?4be5"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "borderBottom", "currentStepComputed", "scopedSlots", "_u", "key", "fn", "proxy", "getPlayIconTooltip", "class", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "selectedIp", "isProcessing", "toolConfigComplete", "style", "color", "on", "$event", "handleStart", "staticStyle", "$t", "handleToolTabChange", "ref", "currentProject", "onToolPathChanged", "onGeneralScriptSaved", "onSpiderScriptSaved", "on<PERSON>un<PERSON><PERSON><PERSON>", "onNodesSelected", "model", "value", "callback", "$$v", "expression", "handleProxyChange", "form", "beforeUpload", "fileList", "handleUploadChange", "_v", "_s", "fileName", "_e", "scriptTabs", "activeScriptTab", "onScriptSaved", "scriptContent", "localSavePath", "defaultScriptContent", "scriptMap", "name", "content", "spiderScriptContent", "getGeneralScriptNames", "getSpiderScriptNames", "getScriptContent", "_scriptMap$key", "disabled", "showScriptModal", "confirmScript", "scriptModalVisible", "maxHeight", "overflow", "handleModalVisibleChange", "handleScriptOk", "handleScriptCancel", "handleTabChange", "_l", "tab", "minRows", "maxRows", "scriptContentInternal", "props", "type", "String", "default", "scriptType", "Array", "defaultTab", "Boolean", "filename", "successMessage", "data", "script<PERSON>ath", "computed", "get", "set", "$emit", "watch", "newVal", "methods", "loadScriptContent", "tabKey", "trim", "saveScriptToFile", "$message", "warning", "path", "success", "blob", "Blob", "file", "File", "formData", "FormData", "append", "response", "axios", "post", "headers", "error", "_error$response", "console", "message", "component", "components", "ScriptEditor", "required", "$form", "createForm", "uploadUrl", "toolPath", "mapState", "created", "isZip", "endsWith", "uploadFile", "uploadingMessage", "loading", "fileInfo", "uid", "status", "$forceUpdate", "info", "slice", "getToolData", "spiderScriptTabs", "activeSpiderScriptTab", "run<PERSON><PERSON><PERSON>", "spiderToolPath", "spiderSavePath", "spiderScriptPath", "loadSpiderScriptContent", "$notify", "title", "requestData", "targets", "proxy_ip", "script_content", "dbFile", "mixins", "NotificationMixin", "TaskPollingMixin", "ProxySelector", "TaskProgressCard", "NodeSelector", "GeneralToolTab", "SpiderToolTab", "currentStep", "activeToolTab", "taskId", "_this$activeToolTask", "activeToolTask", "task_id", "$store", "dispatch", "taskInfo", "localStorage", "getItem", "projectFile", "JSON", "parse", "checkActiveTask", "removeItem", "mapActions", "ip", "$refs", "generalToolTab", "toolData", "spiderToolTab", "startTaskGeneric", "script_path", "result_path", "local_save_path", "endpoint", "errorTitle", "previousTaskInfo", "clearTaskNotificationMark", "e", "responseData", "setItem", "stringify", "initialTaskState", "nodes", "for<PERSON>ach", "host_name", "progress", "$nextTick", "progressElement", "document", "querySelector", "scrollIntoView", "behavior", "block", "pollError", "startPolling", "errorMessage", "taskCompleted", "Error", "Object", "values", "allCompleted", "every", "node", "includes", "startPollingWrapper", "activated", "handler", "newProject", "oldProject", "stopPolling", "immediate", "ToolsPanel"], "mappings": "yIAAA,W,yCCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,eAAe,IAAI,IAAI,IAEvMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,gCAAgCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,YAAa,UAAY,CAAEC,aAAc,uBAAwB,CAACN,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,UAAU,CAACG,YAAY,aAAaD,MAAM,CAAC,QAAUJ,EAAIS,oBAAoB,KAAO,UAAU,CAACP,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,cAAcU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,iBAAiBU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,SAAS,CAACG,YAAY,YAAYD,MAAM,CAAC,KAAO,cAAcU,OAAM,OAAUZ,EAAG,SAAS,CAACQ,YAAYV,EAAIW,GAAG,CAAC,CAACC,IAAI,OAAOC,GAAG,WAAW,MAAO,CAACX,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQJ,EAAIe,qBAAqB,CAACb,EAAG,SAAS,CAACG,YAAY,YAAYW,MAAM,CAC/8B,UAAahB,EAAIiB,gBAAgBC,OAAS,GAAKlB,EAAImB,aAAenB,EAAIoB,cAAgBpB,EAAIqB,mBAC1F,iBAAkBrB,EAAIiB,gBAAgBC,OAAS,GAAKlB,EAAImB,aAAenB,EAAIoB,cAAgBpB,EAAIqB,oBAC/FC,MAAO,CACPC,MAAQvB,EAAIiB,gBAAgBC,OAAS,GAAKlB,EAAImB,aAAenB,EAAIoB,cAAgBpB,EAAIqB,mBACjF,UACA,WACHjB,MAAM,CAAC,KAAO,eAAeoB,GAAG,CAAC,MAAQ,SAASC,GAAQzB,EAAIiB,gBAAgBC,OAAS,GAAKlB,EAAImB,aAAenB,EAAIoB,cAAgBpB,EAAIqB,oBAAsBrB,EAAI0B,mBAAmB,KAAKZ,OAAM,QAAW,IAAI,GAAGZ,EAAG,SAAS,CAACyB,YAAY,CAAC,OAAS,YAAYvB,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAI4B,GAAG,wBAAwB,CAAC1B,EAAG,SAAS,CAACE,MAAM,CAAC,qBAAqB,WAAWoB,GAAG,CAAC,OAASxB,EAAI6B,sBAAsB,CAAC3B,EAAG,aAAa,CAACU,IAAI,UAAUR,MAAM,CAAC,IAAMJ,EAAI4B,GAAG,qBAAuB,SAAS,CAAC1B,EAAG,mBAAmB,CAAC4B,IAAI,iBAAiB1B,MAAM,CAAC,gBAAgBJ,EAAIoB,aAAa,kBAAkBpB,EAAI+B,gBAAgBP,GAAG,CAAC,oBAAoBxB,EAAIgC,kBAAkB,eAAehC,EAAIiC,yBAAyB,GAAG/B,EAAG,aAAa,CAACU,IAAI,SAASR,MAAM,CAAC,IAAMJ,EAAI4B,GAAG,oBAAsB,aAAa,CAAC1B,EAAG,kBAAkB,CAAC4B,IAAI,gBAAgB1B,MAAM,CAAC,gBAAgBJ,EAAIoB,aAAa,oBAAoBpB,EAAIiB,gBAAgB,cAAcjB,EAAImB,WAAW,kBAAkBnB,EAAI+B,gBAAgBP,GAAG,CAAC,eAAexB,EAAIkC,oBAAoB,aAAalC,EAAImC,gBAAgB,IAAI,IAAI,GAAGjC,EAAG,SAAS,CAACyB,YAAY,CAAC,OAAS,YAAYvB,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAI4B,GAAG,2BAA2B,CAAC1B,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeJ,EAAI+B,eAAe,SAAW/B,EAAIoB,cAAcI,GAAG,CAAC,MAAQxB,EAAIoC,iBAAiBC,MAAM,CAACC,MAAOtC,EAAIiB,gBAAiBsB,SAAS,SAAUC,GAAMxC,EAAIiB,gBAAgBuB,GAAKC,WAAW,sBAAsB,GAAGvC,EAAG,SAAS,CAACyB,YAAY,CAAC,gBAAgB,QAAQvB,MAAM,CAAC,KAAO,QAAQ,MAAQJ,EAAI4B,GAAG,2BAA2B,CAAC1B,EAAG,iBAAiB,CAACE,MAAM,CAAC,SAAWJ,EAAIoB,cAAcI,GAAG,CAAC,OAASxB,EAAI0C,mBAAmBL,MAAM,CAACC,MAAOtC,EAAImB,WAAYoB,SAAS,SAAUC,GAAMxC,EAAImB,WAAWqB,GAAKC,WAAW,iBAAiB,GAAGvC,EAAG,qBAAqB,CAACE,MAAM,CAAC,YAAY,OAAO,gBAAgBJ,EAAIoB,iBAAiB,IAElzDd,EAAkB,G,oHCTlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAI2C,KAAK,OAAS,aAAa,CAACzC,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAI4B,GAAG,4BAA4B,CAAC1B,EAAG,MAAM,CAACyB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAO,YAAY,SAAS,CAACzB,EAAG,MAAM,CAACyB,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACzB,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,SAAS,gBAAgBJ,EAAI4C,aAAa,oBAAmB,EAAK,SAAW5C,EAAIoB,aAAa,YAAYpB,EAAI6C,SAAS,OAAS,qDAAqDrB,GAAG,CAAC,OAASxB,EAAI8C,qBAAqB,CAAC5C,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAIoB,eAAe,CAAClB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,YAAYJ,EAAI+C,GAAG,IAAI/C,EAAIgD,GAAGhD,EAAI4B,GAAG,2BAA2B,MAAM,IAAI,GAAI5B,EAAIiD,SAAU/C,EAAG,OAAO,CAACyB,YAAY,CAAC,cAAc,OAAO,MAAQ,OAAO,YAAY,SAAS,CAAC3B,EAAI+C,GAAG,IAAI/C,EAAIgD,GAAGhD,EAAIiD,UAAU,OAAOjD,EAAIkD,MAAM,GAAGhD,EAAG,MAAM,CAACyB,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACzB,EAAG,gBAAgB,CAACE,MAAM,CAAC,cAAcJ,EAAImD,WAAW,cAAcnD,EAAIoD,gBAAgB,SAAWpD,EAAIoB,aAAa,SAAW,YAAY,kBAAkB,6BAA6BI,GAAG,CAAC,eAAexB,EAAIqD,eAAehB,MAAM,CAACC,MAAOtC,EAAIsD,cAAef,SAAS,SAAUC,GAAMxC,EAAIsD,cAAcd,GAAKC,WAAW,oBAAoB,OAAOvC,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAI4B,GAAG,6BAA6B,CAAC1B,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,6BAA6B,SAAWJ,EAAIoB,cAAciB,MAAM,CAACC,MAAOtC,EAAIuD,cAAehB,SAAS,SAAUC,GAAMxC,EAAIuD,cAAcf,GAAKC,WAAW,oBAAoB,IAAI,IAE7kDnC,EAAkB,G,UCDf,MAAMkD,EAAuB,kvCCAvBA,EAAuB,oxBCIvBC,EAAY,CACvB,gBAAmB,CACjBC,KAAM,kBACNC,QAASH,GAEX,eAAkB,CAChBE,KAAM,iBACNC,QAASC,IAaAC,EAAwBA,IAC5B,CAAC,CACNjD,IAAK,kBACL8C,KAAMD,EAAU,mBAAmBC,OAK1BI,EAAuBA,IAC3B,CAAC,CACNlD,IAAK,iBACL8C,KAAMD,EAAU,kBAAkBC,OAKzBK,EAAoBnD,IAAQ,IAAAoD,EACvC,OAAqB,QAAdA,EAAAP,EAAU7C,UAAI,IAAAoD,OAAA,EAAdA,EAAgBL,UAAWH,GC1CpC,IAAIzD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,MAAM,CAACyB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,SAAS,CAACzB,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAIiE,UAAUzC,GAAG,CAAC,MAAQxB,EAAIkE,kBAAkB,CAAChE,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,UAAUJ,EAAI+C,GAAG,IAAI/C,EAAIgD,GAAGhD,EAAI4B,GAAG,yBAAyB,MAAM,GAAI5B,EAAIsD,cAAepD,EAAG,WAAW,CAACG,YAAY,mBAAmBD,MAAM,CAAC,SAAWJ,EAAIiE,UAAUzC,GAAG,CAAC,MAAQxB,EAAImE,gBAAgB,CAACjE,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,kBAAkBJ,EAAI+C,GAAG,IAAI/C,EAAIgD,GAAGhD,EAAI4B,GAAG,uBAAuB,MAAM,GAAG5B,EAAIkD,MAAM,GAAGhD,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQJ,EAAI4B,GAAG,wBAAwB,QAAU5B,EAAIoE,mBAAmB,UAAW,EAAM,cAAe,EAAM,UAAW,EAAM,MAAQ,SAAS,UAAY,CAAEC,UAAW,OAAQC,SAAU,OAAQ/D,QAAS,QAAS,mBAAqBP,EAAIuE,0BAA0B/C,GAAG,CAAC,GAAKxB,EAAIwE,eAAe,OAASxE,EAAIyE,qBAAqB,CAACvE,EAAG,SAAS,CAACyB,YAAY,CAAC,gBAAgB,QAAQH,GAAG,CAAC,OAASxB,EAAI0E,iBAAiBrC,MAAM,CAACC,MAAOtC,EAAIoD,gBAAiBb,SAAS,SAAUC,GAAMxC,EAAIoD,gBAAgBZ,GAAKC,WAAW,oBAAoBzC,EAAI2E,GAAI3E,EAAImD,YAAY,SAASyB,GAAK,OAAO1E,EAAG,aAAa,CAACU,IAAIgE,EAAIhE,IAAIR,MAAM,CAAC,IAAMwE,EAAIlB,WAAU,GAAGxD,EAAG,MAAM,CAACyB,YAAY,CAAC,WAAa,UAAU,QAAU,OAAO,gBAAgB,MAAM,aAAa,UAAU,CAACzB,EAAG,aAAa,CAACyB,YAAY,CAAC,cAAc,2BAA2B,WAAa,UAAU,MAAQ,UAAU,OAAS,iBAAiB,YAAY,OAAO,MAAQ,OAAO,OAAS,QAAQvB,MAAM,CAAC,KAAO,GAAG,YAAc,cAAc,YAAY,CAAEyE,QAAS,GAAIC,QAAS,KAAMzC,MAAM,CAACC,MAAOtC,EAAI+E,sBAAuBxC,SAAS,SAAUC,GAAMxC,EAAI+E,sBAAsBvC,GAAKC,WAAW,4BAA4B,IAAI,IAAI,IAE5vDnC,EAAkB,GCmDP,GACfoD,KAAA,eACAsB,MAAA,CACA1C,MAAA,CACA2C,KAAAC,OACAC,QAAA,IAEAC,WAAA,CACAH,KAAAC,OACAC,QAAA,WAEAhC,WAAA,CACA8B,KAAAI,MACAF,YAAA,IAEAG,WAAA,CACAL,KAAAC,OACAC,QAAA,mBAEAlB,SAAA,CACAgB,KAAAM,QACAJ,SAAA,GAEAK,SAAA,CACAP,KAAAC,OACAC,QAAA,aAEAM,eAAA,CACAR,KAAAC,OACAC,QAAA,8BAGAO,OACA,OACAtB,oBAAA,EACAW,sBAAA,KAAAzC,MACAc,gBAAA,KAAAkC,WACAK,WAAA,KAGAC,SAAA,CACAtC,cAAA,CACAuC,MACA,YAAAd,uBAEAe,IAAAxD,GACA,KAAAyC,sBAAAzC,EACA,KAAAyD,MAAA,QAAAzD,MAIA0D,MAAA,CACA1D,MAAA2D,GACA,KAAAlB,sBAAAkB,IAGAC,QAAA,CACAhC,kBAEA,KAAAa,uBACA,KAAAoB,kBAAA,KAAA/C,iBAEA,KAAAgB,oBAAA,GAGA+B,kBAAAC,GACA,KAAAhD,gBAAAgD,EACA,KAAA9C,cAAAS,EAAAqC,IAGA1B,gBAAA0B,GACA,KAAAD,kBAAAC,IAGA5B,iBACA,KAAAlB,cAAA+C,QAMA,KAAAC,mBAEA,KAAAlC,oBAAA,GAPA,KAAAmC,SAAAC,QAAA,mCAUAjC,6BAIAE,qBAEA,KAAAL,oBAAA,GAIA,sBACA,SAAAd,cAAA+C,OAEA,YADA,KAAAE,SAAAC,QAAA,kCAKA,MAAAC,QAAA,KAAAH,mBAEAG,GACA,KAAAF,SAAAG,QAAA,KAAAjB,iBAIA,yBACA,IAEA,MAAAkB,EAAA,IAAAC,KAAA,MAAAtD,eAAA,CAAA2B,KAAA,eACA4B,EAAA,IAAAC,KAAA,CAAAH,GAAA,KAAAnB,SAAA,CAAAP,KAAA,eAGA8B,EAAA,IAAAC,SACAD,EAAAE,OAAA,iBAAAJ,GAEA,MAAAK,QAAAC,OAAAC,KAAA,0BAAAL,EAAA,CACAM,QAAA,CACA,wCAIA,OAAAH,EAAAxB,MAAAwB,EAAAxB,KAAAe,MACA,KAAAd,WAAAuB,EAAAxB,KAAAe,KACA,KAAAV,MAAA,eAAAmB,EAAAxB,KAAAe,MACAS,EAAAxB,KAAAe,MAEA,KACA,MAAAa,GAAA,IAAAC,EAGA,OAFAC,QAAAF,MAAA,qBAAAA,GACA,KAAAf,SAAAe,MAAA,oCAAAC,EAAAD,EAAAJ,gBAAA,IAAAK,GAAA,QAAAA,IAAA7B,YAAA,IAAA6B,OAAA,EAAAA,EAAAD,UAAAG,UACA,SC5LoW,I,YCOhWC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAAA,E,QC0CA,GACfhE,KAAA,iBACAiE,WAAA,CACAC,gBAEA5C,MAAA,CACA5D,aAAA,CACA6D,KAAAM,QACAJ,SAAA,GAEApD,eAAA,CACAkD,KAAAC,OACA2C,UAAA,IAGAnC,OACA,OACA/C,KAAA,KAAAmF,MAAAC,WAAA,MACAC,UAAA,qBACAC,SAAA,GACA1E,cAAA,6EACAV,SAAA,GACAI,SAAA,GACAK,cAAA,GACAH,WAAA,GACAC,gBAAA,kBACAuC,WAAA,KAGAC,SAAA,IACAsC,eAAA,kBACA7G,qBACA,YAAA4G,UAAA,KAAA3E,eAAA,KAAAC,gBAGA4E,UAEA,KAAAhF,WAAAU,IAEA,KAAAsC,kBAAA,KAAA/C,kBAEA8C,QAAA,CACAC,kBAAAC,GACA,KAAAhD,gBAAAgD,EACA,KAAA9C,cAAAS,EAAAqC,IAEA/C,cAAAoD,GACA,KAAAd,WAAAc,EACA,KAAAV,MAAA,eAAAU,IAEA7D,aAAAiE,GACA,MAAAuB,EAAA,oBAAAvB,EAAA5B,MACA,iCAAA4B,EAAA5B,MACA4B,EAAAnD,KAAA2E,SAAA,QACA,OAAAD,GAKA,KAAAvB,OACA,KAAA5D,SAAA4D,EAAAnD,KAGA,KAAA4E,WAAAzB,IAEA,IAVA,KAAAN,SAAAe,MAAA,mCACA,IAWA,iBAAAT,GAEA,MAAA0B,EAAA,KAAAhC,SAAAiC,QAAA,kBAEA,IACA,MAAAzB,EAAA,IAAAC,SACAD,EAAAE,OAAA,OAAAJ,GAEA,MAAAK,QAAAC,OAAAC,KAAA,KAAAY,UAAAjB,EAAA,CACAM,QAAA,CACA,wCAOA,GAFAkB,IAEArB,EAAAxB,MAAAwB,EAAAxB,KAAAe,KAAA,CACA,KAAAwB,SAAAf,EAAAxB,KAAAe,KACA,KAAAV,MAAA,oBAAAmB,EAAAxB,KAAAe,MACA,KAAAF,SAAAG,QAAAG,EAAAnD,KAAA,0BAGA,MAAA+E,EAAA,CACAC,IAAA7B,EAAA6B,IACAhF,KAAAmD,EAAAnD,KACAiF,OAAA,OACAzB,WAAAxB,MAEA,KAAA7C,SAAA,CAAA4F,GAGA,KAAAG,oBAEA,KAAArC,SAAAe,MAAAT,EAAAnD,KAAA,uCAEA,MAAA4D,GAAA,IAAAC,EAEAgB,IAEAf,QAAAF,MAAA,gBAAAA,GACA,KAAAf,SAAAe,MAAA,GAAAT,EAAAnD,wBAAA,QAAA6D,EAAAD,EAAAJ,gBAAA,IAAAK,GAAA,QAAAA,IAAA7B,YAAA,IAAA6B,OAAA,EAAAA,EAAAD,UAAAG,aAGA3E,mBAAA+F,GACA,KAAAhG,SAAA,IAAAgG,EAAAhG,UAGA,KAAAA,SAAA,KAAAA,SAAAiG,OAAA,GAEA,MAAAH,EAAAE,EAAAhC,KAAA8B,OACA,SAAAA,EACAE,EAAAhC,KAAAK,UAAA2B,EAAAhC,KAAAK,SAAAT,MACA,KAAAwB,SAAAY,EAAAhC,KAAAK,SAAAT,KACA,KAAAV,MAAA,oBAAA8C,EAAAhC,KAAAK,SAAAT,MACA,KAAAxD,SAAA4F,EAAAhC,KAAAnD,KACA,KAAA6C,SAAAG,QAAAmC,EAAAhC,KAAAnD,KAAA,0BAGA,KAAAkF,gBAEA,KAAArC,SAAAe,MAAAuB,EAAAhC,KAAAnD,KAAA,uCAEA,UAAAiF,GACA,KAAApC,SAAAe,MAAAuB,EAAAhC,KAAAnD,KAAA,oBAGAqF,cACA,OACAd,SAAA,KAAAA,SACA3E,cAAA,KAAAA,cACAqC,WAAA,KAAAA,WACApC,cAAA,KAAAA,kBCvMsW,ICOlW,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QClBXxD,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAI2C,KAAK,OAAS,aAAa,CAACzC,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQJ,EAAI4B,GAAG,qBAAqB,CAAC1B,EAAG,MAAM,CAACyB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAO,YAAY,SAAS,CAACzB,EAAG,MAAM,CAACyB,YAAY,CAAC,KAAO,IAAI,YAAY,UAAU,CAACzB,EAAG,gBAAgB,CAACE,MAAM,CAAC,cAAcJ,EAAIgJ,iBAAiB,cAAchJ,EAAIiJ,sBAAsB,SAAWjJ,EAAIoB,aAAa,SAAW,mBAAmB,kBAAkB,oCAAoCI,GAAG,CAAC,eAAexB,EAAIqD,eAAehB,MAAM,CAACC,MAAOtC,EAAI4D,oBAAqBrB,SAAS,SAAUC,GAAMxC,EAAI4D,oBAAoBpB,GAAKC,WAAW,0BAA0B,GAAGvC,EAAG,MAAM,CAACyB,YAAY,CAAC,KAAO,WAAW,YAAY,UAAU,CAACzB,EAAG,WAAW,CAACG,YAAY,mBAAmBsB,YAAY,CAAC,MAAQ,QAAQvB,MAAM,CAAC,QAAUJ,EAAIoB,aAAa,SAAWpB,EAAIoB,eAAiBpB,EAAI4D,qBAAqBpC,GAAG,CAAC,MAAQxB,EAAIkJ,YAAY,CAAChJ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,iBAAiBJ,EAAI+C,GAAG,IAAI/C,EAAIgD,GAAGhD,EAAI4B,GAAG,mBAAqB,YAAY,MAAM,IAAI,QAAQ,IAE/kCtB,EAAkB,GCsCP,GACfoD,KAAA,gBACAiE,WAAA,CACAC,gBAEA5C,MAAA,CACA5D,aAAA,CACA6D,KAAAM,QACAJ,SAAA,GAEAlE,gBAAA,CACAgE,KAAAI,MACAwC,UAAA,GAEA1G,WAAA,CACA8D,KAAAC,OACAC,QAAA,MAEApD,eAAA,CACAkD,KAAAC,OACA2C,UAAA,IAGAnC,OACA,OACA/C,KAAA,KAAAmF,MAAAC,WAAA,MACAnE,oBAAA,GACAoF,iBAAA,GACAC,sBAAA,iBACAE,eAAA,4FACAC,eAAA,6EACAC,iBAAA,mEAGAzD,SAAA,IACAsC,eAAA,mBAEAC,UAEA,KAAAa,iBAAAlF,IAEA,KAAAwF,wBAAA,KAAAL,wBAEA/C,QAAA,CACAoD,wBAAAlD,GACA,KAAA6C,sBAAA7C,EACA,KAAAxC,oBAAAG,EAAAqC,IAEA/C,cAAAoD,GACA,KAAA4C,iBAAA5C,EACA,KAAAV,MAAA,eAAAU,IAEAyC,YACA,SAAAjI,gBAAAC,SAAA,KAAAC,WAKA,YAJA,KAAAoI,QAAA/C,QAAA,CACAgD,MAAA,WACA/B,QAAA,+BAMA,MAAAgC,EAAA,CACAC,QAAA,KAAAzI,gBACA0I,SAAA,KAAAxI,WACAyI,eAAA,KAAAhG,oBACAiG,OAAA,KAAA9H,gBAGA,KAAAgE,MAAA,aAAA0D,IAEAV,cACA,OACAnF,oBAAA,KAAAA,oBACAyF,iBAAA,KAAAA,iBACAF,eAAA,KAAAA,eACAC,eAAA,KAAAA,mBCpHqW,ICOjW,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QCiGA,GACfU,OAAA,CAAAC,OAAAC,QACArC,WAAA,CACAsC,qBACAC,wBACAC,oBACAC,iBACAC,iBAEA3E,OACA,OACAzE,gBAAA,GACAE,WAAA,KACAmJ,YAAA,EACAC,cAAA,UACAtC,SAAA,GACAtC,WAAA,GACArC,cAAA,GACAC,cAAA,GACA8F,iBAAA,GACAzF,oBAAA,KAGAgC,SAAA,IACAsC,eAAA,oDACAsC,OAAA,CACA3E,MAAA,IAAA4E,EACA,eAAAA,EAAA,KAAAC,sBAAA,IAAAD,OAAA,EAAAA,EAAAE,SAEA7E,IAAAxD,GACA,KAAAsI,OAAAC,SAAA,iBAAAvI,EAAA,CAAAqI,QAAArI,GAAA,QAGAjB,qBACA,uBAAAkJ,eACA,KAAAtC,UAAA,KAAA3E,eAAA,KAAAC,eAIA9C,sBACA,YAAAW,aACA,EAEA,KAAA6G,UAAA,iBAAAsC,eAGA,KAAAtC,UAAA,gBAAAsC,eAAA,SAAAtJ,gBAAAC,QAGA,KAAA+G,UAAA,gBAAAsC,gBAAA,KAAAtJ,gBAAAC,OAAA,SAAAC,WACA,EAEA,EALA,GAHA,GAUAJ,qBACA,YAAAK,aACA,yBAEA,iBAAAmJ,eAAA,KAAAtC,SAGA,KAAAhH,gBAAAC,OAGA,KAAAC,WAGA,iCAFA,2BAHA,sBAHA,uCAWAgH,UAEA,MAAA2C,EAAAC,aAAAC,QAAA,qBAAAjJ,gBACA,GAAA+I,EAAA,CACA,kBAAAG,GAAAC,KAAAC,MAAAL,GACAG,IAAA,KAAAlJ,eACA,KAAAqJ,mBAGAL,aAAAM,WAAA,qBAAAtJ,gBACAgJ,aAAAM,WAAA,0BAAAtJ,gBACA,KAAA6I,OAAAC,SAAA,0BAIA3E,QAAA,IACAoF,eAAA,qBAGAzJ,oBAAAjB,GACA,KAAA2J,cAAA3J,GAIA8B,kBAAA6I,GACA,KAAApK,WAAAoK,GAIAnJ,gBAAAnB,GACA,KAAAA,mBAIAe,kBAAAyE,GACA,KAAAwB,SAAAxB,GAIAxE,qBAAAwE,GAGA,GAFA,KAAAd,WAAAc,EAEA,KAAA+E,MAAAC,eAAA,CACA,MAAAC,EAAA,KAAAF,MAAAC,eAAA1C,cACA,KAAAzF,cAAAoI,EAAApI,cACA,KAAAC,cAAAmI,EAAAnI,gBAKArB,oBAAAuE,GAGA,GAFA,KAAA4C,iBAAA5C,EAEA,KAAA+E,MAAAG,cAAA,CACA,MAAAD,EAAA,KAAAF,MAAAG,cAAA5C,cACA,KAAAnF,oBAAA8H,EAAA9H,sBAKA,kBAAA6F,SACA,KAAAmC,iBACAnC,EACA,aACA,cACA,iBAIA,oBACA,SAAApI,mBAKA,YAJA,KAAAkI,QAAA/C,QAAA,CACAgD,MAAA,2BACA/B,QAAA,mDAKA,oBAAA8C,gBAAA,KAAAjH,cAKA,YAJA,KAAAiG,QAAA/C,QAAA,CACAgD,MAAA,YACA/B,QAAA,wCAMA,MAAAgC,EAAA,CACAC,QAAA,KAAAzI,gBACA0I,SAAA,KAAAxI,WACA0K,YAAA,KAAA5D,SACA2B,eAAA,KAAAtG,cACAwI,YAAA,aACAC,gBAAA,KAAAxI,cACAsG,OAAA,KAAA9H,gBAIAyI,QAAA,KAAAoB,iBACAnC,EACA,MACA,YACA,cAIA,GAAAe,EAEA,IACA,MAAAtD,QAAAC,OAAAtB,IAAA,eAAA2E,GACAtD,EAAAxB,OAEA,KAAAkF,OAAAC,SAAA,iBAAA3D,EAAAxB,MAGA,KAAAkD,gBAEA,MAAAtB,GACAE,QAAAF,MAAA,cAAAA,KAaA,uBAAAmC,EAAAuC,EAAAvG,EAAAwG,GACA,SAAAhL,gBAAAC,SAAA,KAAAC,WAKA,OAJA,KAAAoI,QAAA/C,QAAA,CACAgD,MAAA,WACA/B,QAAA,+BAEA,KAGA,KAAArG,cAAA,EAGA,MAAA8K,EAAAnB,aAAAC,QAAA,qBAAAjJ,gBACA,GAAAmK,EACA,IACA,aAAA1B,GAAAU,KAAAC,MAAAe,GACA1B,GACA,KAAA2B,0BAAA3B,EAAA,YAAAzI,gBAEA,MAAAqK,GACA5E,QAAAF,MAAA,6CAAA8E,GAIA,IAGA,MAAA1G,KAAA2G,SAAAlF,OAAAC,KAAA,eAAA4E,EAAAvC,GAEA,GAAA4C,KAAA1B,QAAA,CAEAI,aAAAuB,QAAA,qBAAAvK,eAAAmJ,KAAAqB,UAAA,CACA/B,OAAA6B,EAAA1B,QACAM,YAAA,KAAAlJ,kBAEAgJ,aAAAM,WAAA,0BAAAtJ,gBAGA,KAAAyI,OAAA6B,EAAA1B,QAGA,MAAA6B,EAAA,CACA7B,QAAA0B,EAAA1B,QACA8B,MAAA,IAIA,KAAAxL,gBAAAyL,QAAAnB,IACAiB,EAAAC,MAAAlB,GAAA,CACAA,KACAoB,UAAApB,EACA5C,OAAA,aACAiE,SAAA,KAKA,KAAAhC,OAAAC,SAAA,iBAAA2B,GAGA,KAAAK,UAAA,KAEA,MAAAC,EAAAC,SAAAC,cAAA,iBACAF,GACAA,EAAAG,eAAA,CAAAC,SAAA,SAAAC,MAAA,aAKA,IACA,MAAAjG,QAAAC,OAAAtB,IAAA,eAAAwG,EAAA1B,SAGAzD,EAAAxB,MAEA,KAAAkF,OAAAC,SAAA,iBAAA3D,EAAAxB,MAEA,MAAA0H,GACA5F,QAAAF,MAAA,UAAA8F,GAEA,QAEA,KAAAC,aAAAhB,EAAA1B,QAAA,iBASA,OALA,KAAApE,SAAAG,QAAAjB,GAAA,SAGA,KAAAmD,eAEAyD,EAAA1B,QAEA,YACA,MAAArD,GACAE,QAAAF,MAAA,KAAA0E,SAAA1E,GACA,IAAAgG,EAAA,WAaA,OAXAhG,EAAAG,QACA6F,EAAAhG,EAAAG,QACAH,EAAAJ,UAAAI,EAAAJ,SAAAxB,MAAA4B,EAAAJ,SAAAxB,KAAA4B,QACAgG,EAAAhG,EAAAJ,SAAAxB,KAAA4B,OAGA,KAAAiC,QAAAjC,MAAA,CACAkC,MAAAyC,GAAA,SACAxE,QAAA6F,IAEA,KAAAlM,cAAA,EACA,OAKA,wBACA,IACA,MAAA0J,EAAAC,aAAAC,QAAA,qBAAAjJ,gBACAwL,EAAAxC,aAAAC,QAAA,0BAAAjJ,gBAEA,GAAA+I,EAAA,CACA,aAAAN,EAAA,YAAAS,GAAAC,KAAAC,MAAAL,GAEA,GAAAG,IAAA,KAAAlJ,eACA,UAAAyL,MAAA,qCAGA,MAAAtG,QAAAC,OAAAtB,IAAA,eAAA2E,GAEA,GAAAtD,EAAAxB,OACA,KAAAkF,OAAAC,SAAA,iBAAA3D,EAAAxB,MAEAwB,EAAAxB,KAAA+G,OAAA,CACA,MAAAA,EAAAgB,OAAAC,OAAAxG,EAAAxB,KAAA+G,OACAkB,EAAAlB,EAAAmB,MAAAC,GACA,qBAAAC,SAAAD,EAAAlF,SAGAgF,GAAAJ,EAGAI,IACA,KAAAvM,cAAA,EACA2J,aAAAuB,QAAA,0BAAAvK,eAAA,UAJA,KAAAX,cAAA,EACA,KAAAiM,aAAA7C,EAAA,oBAQA,MAAAlD,GACAE,QAAAF,MAAA,8BAAAA,GACAyD,aAAAM,WAAA,qBAAAtJ,gBACAgJ,aAAAM,WAAA,0BAAAtJ,kBAKAgM,oBAAAvD,GACA,KAAA6C,aAAA7C,EAAA,kBAGAwD,YAEA,KAAA5C,oBAGApF,MAAA,CAEAjE,eAAA,CACAkM,QAAAC,EAAAC,GACAD,IAAAC,IAEA,KAAAvD,OAAAC,SAAA,uBACA,KAAAuD,cAEA,KAAAhD,oBAGAiD,WAAA,KC5ekW,ICQ9V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,QCJA,GACf1G,WAAA,CACA2G,eCjBuV,ICOnV,EAAY,eACd,EACAvO,EACAO,GACA,EACA,KACA,KACA,MAIa,e", "file": "static/js/chunk-99afb646.b2e8f92c.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=style&index=0&id=02b63088&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('ToolsPanel')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full task-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: '8px 16px' },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }}},[_c('div',{staticClass:\"steps-container\"},[_c('a-steps',{staticClass:\"steps-flow\",attrs:{\"current\":_vm.currentStepComputed,\"size\":\"small\"}},[_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"upload\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"apartment\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-icon',{staticClass:\"step-icon\",attrs:{\"type\":\"global\"}})]},proxy:true}])}),_c('a-step',{scopedSlots:_vm._u([{key:\"icon\",fn:function(){return [_c('a-tooltip',{attrs:{\"title\":_vm.getPlayIconTooltip}},[_c('a-icon',{staticClass:\"step-icon\",class:{\n                'clickable': _vm.selectedRowKeys.length > 0 && _vm.selectedIp && !_vm.isProcessing && _vm.toolConfigComplete,\n                'ready-to-start': _vm.selectedRowKeys.length > 0 && _vm.selectedIp && !_vm.isProcessing && _vm.toolConfigComplete\n              },style:({\n                color: (_vm.selectedRowKeys.length > 0 && _vm.selectedIp && !_vm.isProcessing && _vm.toolConfigComplete)\n                  ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\n                  : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\n              }),attrs:{\"type\":\"play-circle\"},on:{\"click\":function($event){_vm.selectedRowKeys.length > 0 && _vm.selectedIp && !_vm.isProcessing && _vm.toolConfigComplete && _vm.handleStart()}}})],1)]},proxy:true}])})],1)],1),_c('a-card',{staticStyle:{\"margin\":\"0 0 16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('tool.configureTool')}},[_c('a-tabs',{attrs:{\"default-active-key\":\"general\"},on:{\"change\":_vm.handleToolTabChange}},[_c('a-tab-pane',{key:\"general\",attrs:{\"tab\":_vm.$t('tool.generalTool') || '通用工具'}},[_c('general-tool-tab',{ref:\"generalToolTab\",attrs:{\"is-processing\":_vm.isProcessing,\"current-project\":_vm.currentProject},on:{\"tool-path-changed\":_vm.onToolPathChanged,\"script-saved\":_vm.onGeneralScriptSaved}})],1),_c('a-tab-pane',{key:\"spider\",attrs:{\"tab\":_vm.$t('tool.spiderTool') || 'Spider工具'}},[_c('spider-tool-tab',{ref:\"spiderToolTab\",attrs:{\"is-processing\":_vm.isProcessing,\"selected-row-keys\":_vm.selectedRowKeys,\"selected-ip\":_vm.selectedIp,\"current-project\":_vm.currentProject},on:{\"script-saved\":_vm.onSpiderScriptSaved,\"run-spider\":_vm.onRunSpider}})],1)],1)],1),_c('a-card',{staticStyle:{\"margin\":\"0 0 16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureNodes')}},[_c('node-selector',{attrs:{\"project-file\":_vm.currentProject,\"disabled\":_vm.isProcessing},on:{\"input\":_vm.onNodesSelected},model:{value:(_vm.selectedRowKeys),callback:function ($$v) {_vm.selectedRowKeys=$$v},expression:\"selectedRowKeys\"}})],1),_c('a-card',{staticStyle:{\"margin-bottom\":\"16px\"},attrs:{\"size\":\"small\",\"title\":_vm.$t('common.configureProxy')}},[_c('proxy-selector',{attrs:{\"disabled\":_vm.isProcessing},on:{\"change\":_vm.handleProxyChange},model:{value:(_vm.selectedIp),callback:function ($$v) {_vm.selectedIp=$$v},expression:\"selectedIp\"}})],1),_c('task-progress-card',{attrs:{\"task-type\":'tool',\"is-processing\":_vm.isProcessing}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.uploadToolPackage')}},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"12px\",\"flex-wrap\":\"wrap\"}},[_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('a-upload',{attrs:{\"name\":\"script\",\"before-upload\":_vm.beforeUpload,\"show-upload-list\":true,\"disabled\":_vm.isProcessing,\"file-list\":_vm.fileList,\"accept\":\".zip,application/zip,application/x-zip-compressed\"},on:{\"change\":_vm.handleUploadChange}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.isProcessing}},[_c('a-icon',{attrs:{\"type\":\"upload\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.selectToolPackage'))+\" \")],1)],1),(_vm.fileName)?_c('span',{staticStyle:{\"margin-left\":\"10px\",\"color\":\"#666\",\"font-size\":\"12px\"}},[_vm._v(\" \"+_vm._s(_vm.fileName)+\" \")]):_vm._e()],1),_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('script-editor',{attrs:{\"script-tabs\":_vm.scriptTabs,\"default-tab\":_vm.activeScriptTab,\"disabled\":_vm.isProcessing,\"filename\":'script.sh',\"success-message\":'Script saved successfully'},on:{\"script-saved\":_vm.onScriptSaved},model:{value:(_vm.scriptContent),callback:function ($$v) {_vm.scriptContent=$$v},expression:\"scriptContent\"}})],1)])]),_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.localSaveDirectory')}},[_c('a-input',{attrs:{\"placeholder\":\"e.g., results/tool_results\",\"disabled\":_vm.isProcessing},model:{value:(_vm.localSavePath),callback:function ($$v) {_vm.localSavePath=$$v},expression:\"localSavePath\"}})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// 默认脚本内容\r\nexport const defaultScriptContent = `#!/bin/bash\r\n\r\n# ----------------------- 第一步：上传并解压工具包 -----------------------\r\n#\r\n# 以下变量会自动替换，无需修改：\r\n# {work_dir}            - 任务工作目录，格式为 \"/root/.test/script_task_{task_id}\"\r\n# {proxy_ip}            - 服务器IP地址\r\n# {server_port}         - 服务器端口\r\n# {download_script_info} - 下载信息（自动编码）\r\n# {output_file_name}    - 上传的工具包名称\r\n#\r\ncd {work_dir}\r\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\r\nunzip -o {output_file_name}\r\n\r\n# ----------------------- 第二步：执行工具命令 --------------------------\r\n#\r\n# 以下为工具执行示例（以s-spider工具为例）\r\n# 请根据实际工具修改此部分命令\r\n#\r\n# 可用变量：无需修改\r\n# {node_ip}   - 目标节点IP\r\n# {node_name} - 目标节点名称\r\n#\r\ncd scan_tools\r\nchmod 777 run.sh\r\ndos2unix run.sh || true\r\nsleep 0.5\r\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\r\nsleep 0.5\r\n\r\n# ----------------------- 第三步：回传结果文件 --------------------------\r\n#\r\n# 重要说明：\r\n# 1. 所有工具运行结果必须打包为 {node_name}_{node_ip}.tar 格式\r\n# 2. {result_file} 变量会被自动替换为正确的结果文件名\r\n# 3. {upload_script_info} 为回传参数（已编码，无需修改）\r\n#\r\n# 如果您的工具生成了其他格式的文件，请使用以下命令将其转换：\r\n# tar -cf {result_file} your_result_files\r\n# 或\r\n# mv your_result_file.ext {result_file}\r\n#\r\ncurl -F \"file=@{result_file}\" \"http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}\"\r\n`;", "// 预置spider\r\nexport const defaultScriptContent = `#!/bin/bash\r\n# This script will be executed on the remote node\r\n\r\n# 以下示例以运行s-spider工具为例：\r\n\r\n# 第一步：上传并解压zip包，\r\ncd {work_dir}\r\ncurl -s http://{proxy_ip}:{server_port}/api/file/download/{download_script_info} -o {output_file_name}\r\nunzip -o scan_tools.zip\r\n\r\n# 第二步： 执行运行命令，按照实际工具执行步骤往下写即可\r\ncd scan_tools\r\nchmod 777 run.sh\r\ndos2unix run.sh || true\r\nsleep 0.5\r\nsh ./run.sh {node_ip} {node_name}_{node_ip} _scanclassic_multithreading_offline_scanmemall\r\nsleep 0.5\r\n\r\n# 第三步： 回传结果文件\r\n# 注意：所有工具运行结果必须打包为{node_name}_{node_ip}.tar格式\r\n# 如果您的工具生成了其他格式的文件，请将其打包为tar格式\r\n# 例如：tar -cf {node_name}_{node_ip}.tar your_result_files\r\n# 或者：mv your_result_file.ext {node_name}_{node_ip}.tar\r\n\r\n# 上传结果文件到服务器\r\ncurl -F \"file=@{result_file}\" \"http://{proxy_ip}:{server_port}/api/file/upload/{upload_script_info}\"`;", "// 导入所有脚本文件\r\nimport { defaultScriptContent } from './default_command';\r\nimport { defaultScriptContent as spiderScriptContent } from './spider_command';\r\n\r\n// 脚本映射表\r\nexport const scriptMap = {\r\n  'default_command': {\r\n    name: 'Default Command',\r\n    content: defaultScriptContent\r\n  },\r\n  'spider_command': {\r\n    name: 'Spider Command',\r\n    content: spiderScriptContent\r\n  }\r\n};\r\n\r\n// 获取所有脚本名称（通用）\r\nexport const getScriptNames = () => {\r\n  return Object.keys(scriptMap).map(key => ({\r\n    key,\r\n    name: scriptMap[key].name\r\n  }));\r\n};\r\n\r\n// 获取通用工具脚本名称\r\nexport const getGeneralScriptNames = () => {\r\n  return [{\r\n    key: 'default_command',\r\n    name: scriptMap['default_command'].name\r\n  }];\r\n};\r\n\r\n// 获取Spider工具脚本名称\r\nexport const getSpiderScriptNames = () => {\r\n  return [{\r\n    key: 'spider_command',\r\n    name: scriptMap['spider_command'].name\r\n  }];\r\n};\r\n\r\n// 根据key获取脚本内容\r\nexport const getScriptContent = (key) => {\r\n  return scriptMap[key]?.content || defaultScriptContent;\r\n};\r\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"10px\"}},[_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.disabled},on:{\"click\":_vm.showScriptModal}},[_c('a-icon',{attrs:{\"type\":\"code\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.editShellScript'))+\" \")],1),(_vm.scriptContent)?_c('a-button',{staticClass:\"nav-style-button\",attrs:{\"disabled\":_vm.disabled},on:{\"click\":_vm.confirmScript}},[_c('a-icon',{attrs:{\"type\":\"check-circle\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.confirmScript'))+\" \")],1):_vm._e()],1),_c('a-modal',{attrs:{\"title\":_vm.$t('tool.editShellScript'),\"visible\":_vm.scriptModalVisible,\"closable\":false,\"maskClosable\":false,\"keyboard\":false,\"width\":\"1200px\",\"bodyStyle\":{ maxHeight: '90vh', overflow: 'auto', padding: '20px' },\"afterVisibleChange\":_vm.handleModalVisibleChange},on:{\"ok\":_vm.handleScriptOk,\"cancel\":_vm.handleScriptCancel}},[_c('a-tabs',{staticStyle:{\"margin-bottom\":\"16px\"},on:{\"change\":_vm.handleTabChange},model:{value:(_vm.activeScriptTab),callback:function ($$v) {_vm.activeScriptTab=$$v},expression:\"activeScriptTab\"}},_vm._l((_vm.scriptTabs),function(tab){return _c('a-tab-pane',{key:tab.key,attrs:{\"tab\":tab.name}})}),1),_c('div',{staticStyle:{\"background\":\"#1e1e1e\",\"padding\":\"16px\",\"border-radius\":\"4px\",\"min-height\":\"700px\"}},[_c('a-textarea',{staticStyle:{\"font-family\":\"'Courier New', monospace\",\"background\":\"#1e1e1e\",\"color\":\"#d4d4d4\",\"border\":\"1px solid #444\",\"font-size\":\"14px\",\"width\":\"100%\",\"resize\":\"none\"},attrs:{\"rows\":30,\"placeholder\":\"#!/bin/bash\",\"auto-size\":{ minRows: 30, maxRows: 40 }},model:{value:(_vm.scriptContentInternal),callback:function ($$v) {_vm.scriptContentInternal=$$v},expression:\"scriptContentInternal\"}})],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <!-- 脚本编辑按钮和确认按钮 -->\r\n    <div style=\"display: flex; align-items: center; gap: 10px;\">\r\n      <a-button @click=\"showScriptModal\" :disabled=\"disabled\" class=\"nav-style-button\">\r\n        <a-icon type=\"code\" /> {{ $t('tool.editShellScript') }}\r\n      </a-button>\r\n      <a-button\r\n        class=\"nav-style-button\"\r\n        v-if=\"scriptContent\"\r\n        @click=\"confirmScript\"\r\n        :disabled=\"disabled\"\r\n      >\r\n        <a-icon type=\"check-circle\" /> {{ $t('tool.confirmScript') }}\r\n      </a-button>\r\n    </div>\r\n\r\n    <!-- 脚本编辑模态框 -->\r\n    <a-modal\r\n      :title=\"$t('tool.editShellScript')\"\r\n      :visible=\"scriptModalVisible\"\r\n      :closable=\"false\"\r\n      :maskClosable=\"false\"\r\n      :keyboard=\"false\"\r\n      @ok=\"handleScriptOk\"\r\n      @cancel=\"handleScriptCancel\"\r\n      width=\"1200px\"\r\n      :bodyStyle=\"{ maxHeight: '90vh', overflow: 'auto', padding: '20px' }\"\r\n      :afterVisibleChange=\"handleModalVisibleChange\"\r\n    >\r\n      <!-- 脚本选项卡 -->\r\n      <a-tabs v-model=\"activeScriptTab\" @change=\"handleTabChange\" style=\"margin-bottom: 16px;\">\r\n        <a-tab-pane v-for=\"tab in scriptTabs\" :key=\"tab.key\" :tab=\"tab.name\" />\r\n      </a-tabs>\r\n\r\n      <div style=\"background: #1e1e1e; padding: 16px; border-radius: 4px; min-height: 700px;\">\r\n        <a-textarea\r\n          v-model=\"scriptContentInternal\"\r\n          :rows=\"30\"\r\n          style=\"font-family: 'Courier New', monospace; background: #1e1e1e; color: #d4d4d4; border: 1px solid #444; font-size: 14px; width: 100%; resize: none;\"\r\n          placeholder=\"#!/bin/bash\"\r\n          :auto-size=\"{ minRows: 30, maxRows: 40 }\"\r\n        />\r\n      </div>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState } from 'vuex';\r\nimport { getScriptContent } from '@/assets/scripts';\r\n\r\nexport default {\r\n  name: 'ScriptEditor',\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    scriptType: {\r\n      type: String,\r\n      default: 'general' // 'general' 或 'spider'\r\n    },\r\n    scriptTabs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    defaultTab: {\r\n      type: String,\r\n      default: 'default_command'\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    filename: {\r\n      type: String,\r\n      default: 'script.sh'\r\n    },\r\n    successMessage: {\r\n      type: String,\r\n      default: 'Script saved successfully'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      scriptModalVisible: false,\r\n      scriptContentInternal: this.value,\r\n      activeScriptTab: this.defaultTab,\r\n      scriptPath: ''\r\n    };\r\n  },\r\n  computed: {\r\n    scriptContent: {\r\n      get() {\r\n        return this.scriptContentInternal;\r\n      },\r\n      set(value) {\r\n        this.scriptContentInternal = value;\r\n        this.$emit('input', value);\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    value(newVal) {\r\n      this.scriptContentInternal = newVal;\r\n    }\r\n  },\r\n  methods: {\r\n    showScriptModal() {\r\n      // 确保脚本内容已初始化\r\n      if (!this.scriptContentInternal) {\r\n        this.loadScriptContent(this.activeScriptTab);\r\n      }\r\n      this.scriptModalVisible = true;\r\n    },\r\n\r\n    loadScriptContent(tabKey) {\r\n      this.activeScriptTab = tabKey;\r\n      this.scriptContent = getScriptContent(tabKey);\r\n    },\r\n\r\n    handleTabChange(tabKey) {\r\n      this.loadScriptContent(tabKey);\r\n    },\r\n\r\n    handleScriptOk() {\r\n      if (!this.scriptContent.trim()) {\r\n        this.$message.warning('Script content cannot be empty');\r\n        return;\r\n      }\r\n\r\n      // 保存脚本内容到文件\r\n      this.saveScriptToFile();\r\n\r\n      this.scriptModalVisible = false;\r\n    },\r\n\r\n    handleModalVisibleChange() {\r\n      // 脚本内容已在data中初始化，不需要额外的处理\r\n    },\r\n\r\n    handleScriptCancel() {\r\n      // 关闭模态框，不保存脚本内容\r\n      this.scriptModalVisible = false;\r\n    },\r\n\r\n    // 直接确认脚本（不打开编辑对话框）\r\n    async confirmScript() {\r\n      if (!this.scriptContent.trim()) {\r\n        this.$message.warning('Script content cannot be empty');\r\n        return;\r\n      }\r\n\r\n      // 保存脚本内容到文件\r\n      const path = await this.saveScriptToFile();\r\n\r\n      if (path) {\r\n        this.$message.success(this.successMessage);\r\n      }\r\n    },\r\n\r\n    async saveScriptToFile() {\r\n      try {\r\n        // 创建一个包含脚本内容的文件对象\r\n        const blob = new Blob([this.scriptContent], { type: 'text/plain' });\r\n        const file = new File([blob], this.filename, { type: 'text/plain' });\r\n\r\n        // 上传脚本文件\r\n        const formData = new FormData();\r\n        formData.append('script_content', file);\r\n\r\n        const response = await axios.post('/api/script/save_script', formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        });\r\n\r\n        if (response.data && response.data.path) {\r\n          this.scriptPath = response.data.path;\r\n          this.$emit('script-saved', response.data.path);\r\n          return response.data.path;\r\n        }\r\n        return null;\r\n      } catch (error) {\r\n        console.error('Script save error:', error);\r\n        this.$message.error(`Failed to save script: ${error.response?.data?.error || error.message}`);\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScriptEditor.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ScriptEditor.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ScriptEditor.vue?vue&type=template&id=2ec0ad04\"\nimport script from \"./ScriptEditor.vue?vue&type=script&lang=js\"\nexport * from \"./ScriptEditor.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <a-form :form=\"form\" layout=\"vertical\">\r\n    <!-- 工具配置 - 上传包和编辑脚本在一行 -->\r\n    <a-form-item :label=\"$t('tool.uploadToolPackage')\">\r\n      <div style=\"display: flex; align-items: center; gap: 12px; flex-wrap: wrap;\">\r\n        <!-- 工具包上传 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <a-upload\r\n            name=\"script\"\r\n            :before-upload=\"beforeUpload\"\r\n            :show-upload-list=\"true\"\r\n            @change=\"handleUploadChange\"\r\n            :disabled=\"isProcessing\"\r\n            :file-list=\"fileList\"\r\n            accept=\".zip,application/zip,application/x-zip-compressed\"\r\n          >\r\n            <a-button\r\n              :disabled=\"isProcessing\"\r\n              class=\"nav-style-button\"\r\n            >\r\n              <a-icon type=\"upload\" /> {{ $t('tool.selectToolPackage') }}\r\n            </a-button>\r\n          </a-upload>\r\n          <span v-if=\"fileName\" style=\"margin-left: 10px; color: #666; font-size: 12px;\">\r\n            {{ fileName }}\r\n          </span>\r\n        </div>\r\n        \r\n        <!-- 脚本编辑 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <script-editor\r\n            v-model=\"scriptContent\"\r\n            :script-tabs=\"scriptTabs\"\r\n            :default-tab=\"activeScriptTab\"\r\n            :disabled=\"isProcessing\"\r\n            :filename=\"'script.sh'\"\r\n            :success-message=\"'Script saved successfully'\"\r\n            @script-saved=\"onScriptSaved\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </a-form-item>\r\n\r\n    <!-- 本地保存路径 -->\r\n    <a-form-item :label=\"$t('tool.localSaveDirectory')\">\r\n      <a-input\r\n        v-model=\"localSavePath\"\r\n        placeholder=\"e.g., results/tool_results\"\r\n        :disabled=\"isProcessing\"\r\n      />\r\n    </a-form-item>\r\n  </a-form>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport { mapState } from 'vuex';\r\nimport { getGeneralScriptNames, getScriptContent } from '@/assets/scripts';\r\nimport ScriptEditor from '@/components/common/ScriptEditor.vue';\r\n\r\nexport default {\r\n  name: 'GeneralToolTab',\r\n  components: {\r\n    ScriptEditor\r\n  },\r\n  props: {\r\n    isProcessing: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    currentProject: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      uploadUrl: '/api/script/upload',\r\n      toolPath: '',\r\n      localSavePath: 'eg : D:\\\\_Projects_python\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\upload\\\\task',\r\n      fileList: [],\r\n      fileName: '',\r\n      scriptContent: '',\r\n      scriptTabs: [],\r\n      activeScriptTab: 'default_command',\r\n      scriptPath: ''\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['sidebarColor']),\r\n    toolConfigComplete() {\r\n      return this.toolPath && this.scriptContent && this.localSavePath;\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化通用脚本选项卡\r\n    this.scriptTabs = getGeneralScriptNames();\r\n    // 加载默认通用脚本内容\r\n    this.loadScriptContent(this.activeScriptTab);\r\n  },\r\n  methods: {\r\n    loadScriptContent(tabKey) {\r\n      this.activeScriptTab = tabKey;\r\n      this.scriptContent = getScriptContent(tabKey);\r\n    },\r\n    onScriptSaved(path) {\r\n      this.scriptPath = path;\r\n      this.$emit('script-saved', path);\r\n    },\r\n    beforeUpload(file) {\r\n      const isZip = file.type === 'application/zip' ||\r\n                    file.type === 'application/x-zip-compressed' ||\r\n                    file.name.endsWith('.zip');\r\n      if (!isZip) {\r\n        this.$message.error('You can only upload ZIP files!');\r\n        return false;\r\n      }\r\n\r\n      this.file = file;\r\n      this.fileName = file.name; // 设置文件名\r\n\r\n      // 手动上传文件\r\n      this.uploadFile(file);\r\n\r\n      return false;  // 阻止自动上传\r\n    },\r\n    async uploadFile(file) {\r\n      // 显示上传中的消息\r\n      const uploadingMessage = this.$message.loading('Uploading...', 0);\r\n\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n\r\n        const response = await axios.post(this.uploadUrl, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        });\r\n\r\n        // 关闭上传中消息\r\n        uploadingMessage();\r\n\r\n        if (response.data && response.data.path) {\r\n          this.toolPath = response.data.path;\r\n          this.$emit('tool-path-changed', response.data.path);\r\n          this.$message.success(`${file.name} uploaded successfully`);\r\n\r\n          // 更新fileList以触发handleUploadChange\r\n          const fileInfo = {\r\n            uid: file.uid,\r\n            name: file.name,\r\n            status: 'done',\r\n            response: response.data\r\n          };\r\n          this.fileList = [fileInfo];\r\n\r\n          // 手动触发计算属性更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          this.$message.error(`${file.name} upload response missing path field`);\r\n        }\r\n      } catch (error) {\r\n        // 确保在错误情况下也关闭上传消息\r\n        uploadingMessage();\r\n\r\n        console.error('Upload error:', error);\r\n        this.$message.error(`${file.name} upload failed: ${error.response?.data?.error || error.message}`);\r\n      }\r\n    },\r\n    handleUploadChange(info) {\r\n      this.fileList = [...info.fileList];\r\n\r\n      // 限制只显示最后一个上传的文件\r\n      this.fileList = this.fileList.slice(-1);\r\n\r\n      const status = info.file.status;\r\n      if (status === 'done') {\r\n        if (info.file.response && info.file.response.path) {\r\n          this.toolPath = info.file.response.path;\r\n          this.$emit('tool-path-changed', info.file.response.path);\r\n          this.fileName = info.file.name; // 确保这里也设置文件名\r\n          this.$message.success(`${info.file.name} uploaded successfully`);\r\n\r\n          // 手动触发计算属性更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          this.$message.error(`${info.file.name} upload response missing path field`);\r\n        }\r\n      } else if (status === 'error') {\r\n        this.$message.error(`${info.file.name} upload failed.`);\r\n      }\r\n    },\r\n    getToolData() {\r\n      return {\r\n        toolPath: this.toolPath,\r\n        scriptContent: this.scriptContent,\r\n        scriptPath: this.scriptPath,\r\n        localSavePath: this.localSavePath\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GeneralToolTab.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GeneralToolTab.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./GeneralToolTab.vue?vue&type=template&id=5e4b3f7f\"\nimport script from \"./GeneralToolTab.vue?vue&type=script&lang=js\"\nexport * from \"./GeneralToolTab.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":_vm.$t('tool.editScript')}},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"12px\",\"flex-wrap\":\"wrap\"}},[_c('div',{staticStyle:{\"flex\":\"1\",\"min-width\":\"200px\"}},[_c('script-editor',{attrs:{\"script-tabs\":_vm.spiderScriptTabs,\"default-tab\":_vm.activeSpiderScriptTab,\"disabled\":_vm.isProcessing,\"filename\":'spider_script.sh',\"success-message\":'Spider script saved successfully'},on:{\"script-saved\":_vm.onScriptSaved},model:{value:(_vm.spiderScriptContent),callback:function ($$v) {_vm.spiderScriptContent=$$v},expression:\"spiderScriptContent\"}})],1),_c('div',{staticStyle:{\"flex\":\"0 0 auto\",\"min-width\":\"100px\"}},[_c('a-button',{staticClass:\"nav-style-button\",staticStyle:{\"width\":\"100%\"},attrs:{\"loading\":_vm.isProcessing,\"disabled\":_vm.isProcessing || !_vm.spiderScriptContent},on:{\"click\":_vm.runSpider}},[_c('a-icon',{attrs:{\"type\":\"play-circle\"}}),_vm._v(\" \"+_vm._s(_vm.$t('tool.runSpider') || '运行Spider')+\" \")],1)],1)])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-form :form=\"form\" layout=\"vertical\">\r\n    <!-- Spider工具配置 - 编辑脚本和运行按钮在一行 -->\r\n    <a-form-item :label=\"$t('tool.editScript')\">\r\n      <div style=\"display: flex; align-items: center; gap: 12px; flex-wrap: wrap;\">\r\n        <!-- 脚本编辑 -->\r\n        <div style=\"flex: 1; min-width: 200px;\">\r\n          <script-editor\r\n            v-model=\"spiderScriptContent\"\r\n            :script-tabs=\"spiderScriptTabs\"\r\n            :default-tab=\"activeSpiderScriptTab\"\r\n            :disabled=\"isProcessing\"\r\n            :filename=\"'spider_script.sh'\"\r\n            :success-message=\"'Spider script saved successfully'\"\r\n            @script-saved=\"onScriptSaved\"\r\n          />\r\n        </div>\r\n        \r\n        <!-- 运行按钮 -->\r\n        <div style=\"flex: 0 0 auto; min-width: 100px;\">  \r\n          <a-button\r\n            class=\"nav-style-button\"\r\n            :loading=\"isProcessing\"\r\n            :disabled=\"isProcessing || !spiderScriptContent\"\r\n            @click=\"runSpider\"\r\n            style=\"width: 100%;\"\r\n          >\r\n            <a-icon type=\"play-circle\" /> {{ $t('tool.runSpider') || '运行Spider' }}\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </a-form-item>\r\n  </a-form>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport { getSpiderScriptNames, getScriptContent } from '@/assets/scripts';\r\nimport ScriptEditor from '@/components/common/ScriptEditor.vue';\r\n\r\nexport default {\r\n  name: 'SpiderToolTab',\r\n  components: {\r\n    ScriptEditor\r\n  },\r\n  props: {\r\n    isProcessing: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    selectedRowKeys: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    selectedIp: {\r\n      type: String,\r\n      default: null\r\n    },\r\n    currentProject: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      spiderScriptContent: '',\r\n      spiderScriptTabs: [],\r\n      activeSpiderScriptTab: 'spider_command',\r\n      spiderToolPath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\static_scan\\\\scan_tools.zip',\r\n      spiderSavePath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\sSpider\\\\upload\\\\task',\r\n      spiderScriptPath: 'infocollect\\\\cache\\\\Tools\\\\SEC-SPIDER\\\\release\\\\ssp\\\\start.bat',\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['sidebarColor'])\r\n  },\r\n  created() {\r\n    // 初始化Spider脚本选项卡\r\n    this.spiderScriptTabs = getSpiderScriptNames();\r\n    // 加载默认Spider脚本内容\r\n    this.loadSpiderScriptContent(this.activeSpiderScriptTab);\r\n  },\r\n  methods: {\r\n    loadSpiderScriptContent(tabKey) {\r\n      this.activeSpiderScriptTab = tabKey;\r\n      this.spiderScriptContent = getScriptContent(tabKey);\r\n    },\r\n    onScriptSaved(path) {\r\n      this.spiderScriptPath = path;\r\n      this.$emit('script-saved', path);\r\n    },\r\n    runSpider() {\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: '未选择节点或代理',\r\n          message: '请选择一个或多个节点和一个可用的代理IP来运行工具。'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 准备请求数据\r\n      const requestData = {\r\n        targets: this.selectedRowKeys,\r\n        proxy_ip: this.selectedIp,\r\n        script_content: this.spiderScriptContent,\r\n        dbFile: this.currentProject\r\n      };\r\n\r\n      this.$emit('run-spider', requestData);\r\n    },\r\n    getToolData() {\r\n      return {\r\n        spiderScriptContent: this.spiderScriptContent,\r\n        spiderScriptPath: this.spiderScriptPath,\r\n        spiderToolPath: this.spiderToolPath,\r\n        spiderSavePath: this.spiderSavePath\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpiderToolTab.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpiderToolTab.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SpiderToolTab.vue?vue&type=template&id=44be2d9f\"\nimport script from \"./SpiderToolTab.vue?vue&type=script&lang=js\"\nexport * from \"./SpiderToolTab.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full task-card\"\r\n    :bodyStyle=\"{ padding: '8px 16px' }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n    <!-- 流程图 -->\r\n    <div class=\"steps-container\">\r\n      <a-steps :current=\"currentStepComputed\" class=\"steps-flow\" size=\"small\">\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"upload\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"apartment\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-icon type=\"global\" class=\"step-icon\" />\r\n          </template>\r\n        </a-step>\r\n\r\n        <a-step>\r\n          <template #icon>\r\n            <a-tooltip :title=\"getPlayIconTooltip\">\r\n              <a-icon\r\n                type=\"play-circle\"\r\n                class=\"step-icon\"\r\n                :class=\"{\r\n                  'clickable': selectedRowKeys.length > 0 && selectedIp && !isProcessing && toolConfigComplete,\r\n                  'ready-to-start': selectedRowKeys.length > 0 && selectedIp && !isProcessing && toolConfigComplete\r\n                }\"\r\n                @click=\"selectedRowKeys.length > 0 && selectedIp && !isProcessing && toolConfigComplete && handleStart()\"\r\n                :style=\"{\r\n                  color: (selectedRowKeys.length > 0 && selectedIp && !isProcessing && toolConfigComplete)\r\n                    ? '#3b4149'  // 当选择完成且未在处理时显示正常颜色\r\n                    : '#d9d9d9'  // 其他情况（包括处理中）显示灰色\r\n                }\"\r\n              />\r\n            </a-tooltip>\r\n          </template>\r\n        </a-step>\r\n      </a-steps>\r\n    </div>\r\n\r\n    <!-- 工具配置区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('tool.configureTool')\">\r\n      <a-tabs default-active-key=\"general\" @change=\"handleToolTabChange\">\r\n        <!-- 通用工具标签页 -->\r\n        <a-tab-pane key=\"general\" :tab=\"$t('tool.generalTool') || '通用工具'\">\r\n          <general-tool-tab\r\n            ref=\"generalToolTab\"\r\n            :is-processing=\"isProcessing\"\r\n            :current-project=\"currentProject\"\r\n            @tool-path-changed=\"onToolPathChanged\"\r\n            @script-saved=\"onGeneralScriptSaved\"\r\n          />\r\n        </a-tab-pane>\r\n\r\n        <!-- Spider工具标签页 -->\r\n        <a-tab-pane key=\"spider\" :tab=\"$t('tool.spiderTool') || 'Spider工具'\">\r\n          <spider-tool-tab\r\n            ref=\"spiderToolTab\"\r\n            :is-processing=\"isProcessing\"\r\n            :selected-row-keys=\"selectedRowKeys\"\r\n            :selected-ip=\"selectedIp\"\r\n            :current-project=\"currentProject\"\r\n            @script-saved=\"onSpiderScriptSaved\"\r\n            @run-spider=\"onRunSpider\"\r\n          />\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </a-card>\r\n\r\n    <!-- 节点选择区域 -->\r\n    <a-card style=\"margin: 0 0 16px;\" size=\"small\" :title=\"$t('common.configureNodes')\">\r\n      <node-selector\r\n        v-model=\"selectedRowKeys\"\r\n        :project-file=\"currentProject\"\r\n        :disabled=\"isProcessing\"\r\n        @input=\"onNodesSelected\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 代理配置区域 -->\r\n    <a-card style=\"margin-bottom: 16px;\" size=\"small\" :title=\"$t('common.configureProxy')\">\r\n      <proxy-selector\r\n        v-model=\"selectedIp\"\r\n        :disabled=\"isProcessing\"\r\n        @change=\"handleProxyChange\"\r\n      />\r\n    </a-card>\r\n\r\n    <!-- 任务状态和日志 - 始终显示 -->\r\n    <task-progress-card :task-type=\"'tool'\" :is-processing=\"isProcessing\" />\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapActions } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport NotificationMixin from '@/mixins/NotificationMixin';\r\nimport TaskPollingMixin from '@/mixins/TaskPollingMixin';\r\nimport ProxySelector from '@/components/common/ProxySelector.vue';\r\nimport TaskProgressCard from '@/components/common/TaskProgressCard.vue';\r\nimport NodeSelector from '@/components/common/NodeSelector.vue';\r\nimport GeneralToolTab from '@/components/Cards/GeneralToolTab.vue';\r\nimport SpiderToolTab from '@/components/Cards/SpiderToolTab.vue';\r\n\r\nexport default {\r\n  mixins: [NotificationMixin, TaskPollingMixin],\r\n  components: {\r\n    ProxySelector,\r\n    TaskProgressCard,\r\n    NodeSelector,\r\n    GeneralToolTab,\r\n    SpiderToolTab\r\n  },\r\n  data() {\r\n    return {\r\n      selectedRowKeys: [],\r\n      selectedIp: null,\r\n      currentStep: 0,\r\n      activeToolTab: 'general',\r\n      toolPath: '',\r\n      scriptPath: '',\r\n      scriptContent: '',\r\n      localSavePath: '',\r\n      spiderScriptPath: '',\r\n      spiderScriptContent: ''\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['activeToolTask', 'currentProject', 'sidebarColor']),\r\n    taskId: {\r\n      get() {\r\n        return this.activeToolTask?.task_id;\r\n      },\r\n      set(value) {\r\n        this.$store.dispatch('updateToolTask', value ? { task_id: value } : null);\r\n      }\r\n    },\r\n    toolConfigComplete() {\r\n      if (this.activeToolTab === 'general') {\r\n        return this.toolPath && this.scriptContent && this.localSavePath;\r\n      }\r\n      return true;\r\n    },\r\n    currentStepComputed() {\r\n      if (this.isProcessing) {\r\n        return 2;  // 运行中时，点亮前三个图标\r\n      }\r\n      if (!this.toolPath && this.activeToolTab === 'general') {\r\n        return -1;  // 没有上传工具包，所有图标不点亮\r\n      }\r\n      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length === 0) {\r\n        return 0;   // 上传了工具包但未选择节点，点亮第一步图标和连接线\r\n      }\r\n      if ((this.toolPath || this.activeToolTab === 'spider') && this.selectedRowKeys.length > 0 && !this.selectedIp) {\r\n        return 1;   // 上传了工具包且选择了节点但未选择IP，点亮前两步图标和连接线\r\n      }\r\n      return 3;     // 全部选择完成且未在运行时，点亮所有图标和连接线\r\n    },\r\n    getPlayIconTooltip() {\r\n      if (this.isProcessing) {\r\n        return 'Task is in progress...';\r\n      }\r\n      if (this.activeToolTab === 'general' && !this.toolPath) {\r\n        return 'Please upload a tool package first';\r\n      }\r\n      if (!this.selectedRowKeys.length) {\r\n        return 'Please select nodes';\r\n      }\r\n      if (!this.selectedIp) {\r\n        return 'Please select a proxy IP';\r\n      }\r\n      return 'Click to start tool execution!'; // 当都选择完成时显示这个提示\r\n    }\r\n  },\r\n  created() {\r\n    // 检查当前项目的活动任务\r\n    const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\r\n    if (taskInfo) {\r\n      const { projectFile } = JSON.parse(taskInfo);\r\n      if (projectFile === this.currentProject) {\r\n        this.checkActiveTask();\r\n      } else {\r\n        // 清除任务信息如果属于不同项目\r\n        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\r\n        this.$store.dispatch('updateToolTask', null);\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions(['addNotification']),\r\n\r\n    // 切换工具标签页\r\n    handleToolTabChange(key) {\r\n      this.activeToolTab = key;\r\n    },\r\n\r\n    // 处理代理IP变化\r\n    handleProxyChange(ip) {\r\n      this.selectedIp = ip;\r\n    },\r\n\r\n    // 处理节点选择变化\r\n    onNodesSelected(selectedRowKeys) {\r\n      this.selectedRowKeys = selectedRowKeys;\r\n    },\r\n\r\n    // 处理工具路径变化\r\n    onToolPathChanged(path) {\r\n      this.toolPath = path;\r\n    },\r\n\r\n    // 处理通用脚本保存\r\n    onGeneralScriptSaved(path) {\r\n      this.scriptPath = path;\r\n      // 从子组件获取最新的脚本内容\r\n      if (this.$refs.generalToolTab) {\r\n        const toolData = this.$refs.generalToolTab.getToolData();\r\n        this.scriptContent = toolData.scriptContent;\r\n        this.localSavePath = toolData.localSavePath;\r\n      }\r\n    },\r\n\r\n    // 处理Spider脚本保存\r\n    onSpiderScriptSaved(path) {\r\n      this.spiderScriptPath = path;\r\n      // 从子组件获取最新的脚本内容\r\n      if (this.$refs.spiderToolTab) {\r\n        const toolData = this.$refs.spiderToolTab.getToolData();\r\n        this.spiderScriptContent = toolData.spiderScriptContent;\r\n      }\r\n    },\r\n\r\n    // 处理Spider运行\r\n    async onRunSpider(requestData) {\r\n      await this.startTaskGeneric(\r\n        requestData,\r\n        'run_spider',\r\n        'Spider任务已启动',\r\n        'Spider任务启动失败'\r\n      );\r\n    },\r\n\r\n    async handleStart() {\r\n      if (!this.toolConfigComplete) {\r\n        this.$notify.warning({\r\n          title: 'Incomplete Configuration',\r\n          message: 'Please complete all tool configuration fields.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      if (this.activeToolTab === 'general' && !this.scriptContent) {\r\n        this.$notify.warning({\r\n          title: 'No Script',\r\n          message: 'Please create a shell script first.'\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 准备请求数据\r\n      const requestData = {\r\n        targets: this.selectedRowKeys,\r\n        proxy_ip: this.selectedIp,\r\n        script_path: this.toolPath,\r\n        script_content: this.scriptContent,\r\n        result_path: 'result.txt', // 默认结果文件路径\r\n        local_save_path: this.localSavePath,\r\n        dbFile: this.currentProject\r\n      };\r\n\r\n      // 保存返回的任务ID\r\n      const taskId = await this.startTaskGeneric(\r\n        requestData,\r\n        'run',\r\n        '通用工具任务已启动',\r\n        '通用工具任务启动失败'\r\n      );\r\n\r\n      // 如果成功获取到任务ID，确保任务状态被更新\r\n      if (taskId) {\r\n        // 立即执行一次轮询，获取初始状态\r\n        try {\r\n          const response = await axios.get(`/api/script/${taskId}`);\r\n          if (response.data) {\r\n            // 确保更新到Vuex\r\n            this.$store.dispatch('updateToolTask', response.data);\r\n\r\n            // 强制更新视图\r\n            this.$forceUpdate();\r\n          }\r\n        } catch (error) {\r\n          console.error('获取初始任务状态失败:', error);\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 通用任务启动方法\r\n     * @param {Object} requestData - 请求数据\r\n     * @param {string} endpoint - API端点\r\n     * @param {string} successMessage - 成功消息\r\n     * @param {string} errorTitle - 错误标题\r\n     * @returns {Promise<string|null>} - 返回任务ID或null\r\n     */\r\n    async startTaskGeneric(requestData, endpoint, successMessage, errorTitle) {\r\n      if (!this.selectedRowKeys.length || !this.selectedIp) {\r\n        this.$notify.warning({\r\n          title: '未选择节点或代理',\r\n          message: '请选择一个或多个节点和一个可用的代理IP来运行工具。'\r\n        });\r\n        return null;\r\n      }\r\n\r\n      this.isProcessing = true;\r\n\r\n      // 清除之前的工具任务通知记录\r\n      const previousTaskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\r\n      if (previousTaskInfo) {\r\n        try {\r\n          const { taskId } = JSON.parse(previousTaskInfo);\r\n          if (taskId) {\r\n            this.clearTaskNotificationMark(taskId, 'tool', this.currentProject);\r\n          }\r\n        } catch (e) {\r\n          console.error('Error clearing previous tool notification:', e);\r\n        }\r\n      }\r\n\r\n      try {\r\n\r\n\r\n        const { data: responseData } = await axios.post(`/api/script/${endpoint}`, requestData);\r\n\r\n        if (responseData && responseData.task_id) {\r\n          // 保存任务信息到本地存储\r\n          localStorage.setItem(`toolTaskInfo_${this.currentProject}`, JSON.stringify({\r\n            taskId: responseData.task_id,\r\n            projectFile: this.currentProject\r\n          }));\r\n          localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\r\n\r\n          // 更新任务ID\r\n          this.taskId = responseData.task_id;\r\n\r\n          // 创建初始任务状态对象并更新到Vuex\r\n          const initialTaskState = {\r\n            task_id: responseData.task_id,\r\n            nodes: {}\r\n          };\r\n\r\n          // 为每个选中的节点创建初始状态\r\n          this.selectedRowKeys.forEach(ip => {\r\n            initialTaskState.nodes[ip] = {\r\n              ip: ip,\r\n              host_name: ip, // 使用IP作为主机名，因为我们不再维护nodes数组\r\n              status: 'processing',\r\n              progress: 0\r\n            };\r\n          });\r\n\r\n          // 立即更新到Vuex，确保UI立即显示进度条\r\n          this.$store.dispatch('updateToolTask', initialTaskState);\r\n\r\n          // 显示任务进度区域\r\n          this.$nextTick(() => {\r\n            // 确保DOM已更新\r\n            const progressElement = document.querySelector('.ant-progress');\r\n            if (progressElement) {\r\n              progressElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n            }\r\n          });\r\n\r\n          // 立即执行一次轮询，获取初始状态\r\n          try {\r\n            const response = await axios.get(`/api/script/${responseData.task_id}`);\r\n\r\n\r\n            if (response.data) {\r\n              // 更新Vuex中的任务状态\r\n              this.$store.dispatch('updateToolTask', response.data);\r\n            }\r\n          } catch (pollError) {\r\n            console.error('初始轮询错误:', pollError);\r\n            // 即使初始轮询失败，也不影响后续的定期轮询\r\n          } finally {\r\n            // 开始定期轮询\r\n            this.startPolling(responseData.task_id, 'tool', 'script');\r\n          }\r\n\r\n          // 显示成功消息\r\n          this.$message.success(successMessage || '任务已启动');\r\n\r\n          // 强制更新视图\r\n          this.$forceUpdate();\r\n\r\n          return responseData.task_id;\r\n        }\r\n        return null;\r\n      } catch (error) {\r\n        console.error(`启动${endpoint}任务出错:`, error);\r\n        let errorMessage = '服务器连接错误。';\r\n\r\n        if (error.message) {\r\n          errorMessage = error.message;\r\n        } else if (error.response && error.response.data && error.response.data.error) {\r\n          errorMessage = error.response.data.error;\r\n        }\r\n\r\n        this.$notify.error({\r\n          title: errorTitle || '任务启动失败',\r\n          message: errorMessage,\r\n        });\r\n        this.isProcessing = false;\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 重写 checkActiveTask 方法，调用混入中的方法\r\n    async checkActiveTask() {\r\n      try {\r\n        const taskInfo = localStorage.getItem(`toolTaskInfo_${this.currentProject}`);\r\n        const taskCompleted = localStorage.getItem(`toolTaskCompleted_${this.currentProject}`);\r\n\r\n        if (taskInfo) {\r\n          const { taskId, projectFile } = JSON.parse(taskInfo);\r\n\r\n          if (projectFile !== this.currentProject) {\r\n            throw new Error('Task belongs to different project');\r\n          }\r\n\r\n          const response = await axios.get(`/api/script/${taskId}`);\r\n\r\n          if (response.data) {\r\n            this.$store.dispatch('updateToolTask', response.data);\r\n\r\n            if (response.data.nodes) {\r\n              const nodes = Object.values(response.data.nodes);\r\n              const allCompleted = nodes.every(node =>\r\n                ['success', 'failed'].includes(node.status)\r\n              );\r\n\r\n              if (!allCompleted && !taskCompleted) {\r\n                this.isProcessing = true;\r\n                this.startPolling(taskId, 'tool', 'script');\r\n              } else if (allCompleted) {\r\n                this.isProcessing = false;\r\n                localStorage.setItem(`toolTaskCompleted_${this.currentProject}`, 'true');\r\n              }\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error checking active task:', error);\r\n        localStorage.removeItem(`toolTaskInfo_${this.currentProject}`);\r\n        localStorage.removeItem(`toolTaskCompleted_${this.currentProject}`);\r\n      }\r\n    },\r\n\r\n    // 使用混入中的 startPolling 方法，但需要提供特定参数\r\n    startPollingWrapper(taskId) {\r\n      this.startPolling(taskId, 'tool', 'script');\r\n    },\r\n\r\n    activated() {\r\n      // 当组件被激活时（从缓存中恢复）立即检查任务状态\r\n      this.checkActiveTask();\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听 currentProject 变化\r\n    currentProject: {\r\n      handler(newProject, oldProject) {\r\n        if (newProject !== oldProject) {\r\n          // 清除之前项目的任务状态\r\n          this.$store.dispatch('updateToolTask', null);\r\n          this.stopPolling();\r\n          // 检查新项目的活动任务\r\n          this.checkActiveTask();\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// 基础卡片样式\r\n.task-card {\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 12px;\r\n  background-color: #fff;\r\n  padding: 20px;\r\n}\r\n\r\n// 步骤容器\r\n.steps-container {\r\n  width: 60%;\r\n  margin: 0 auto 24px;\r\n  padding: 12px 0;\r\n}\r\n\r\n// 深度选择器样式集中管理\r\n::v-deep {\r\n  .ant-card {\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    .ant-card-head {\r\n      background: #f0f2f5;\r\n    }\r\n  }\r\n\r\n  .ant-progress {\r\n    border-radius: 3px;\r\n  }\r\n  .ant-tooltip-inner {\r\n    max-width: 500px;\r\n    white-space: pre-wrap;\r\n  }\r\n  .ant-table-tbody > tr:last-child > td {\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n  .steps-flow {\r\n    .ant-steps-item {\r\n      &-process,\r\n      &-finish {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #3b4149 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      &-wait {\r\n        .ant-steps-item-container {\r\n          .ant-steps-item-content {\r\n            .ant-steps-item-title::after {\r\n              background-color: #d9d9d9 !important;\r\n              height: 2px !important;\r\n              top: 25px !important;\r\n            }\r\n          }\r\n        }\r\n\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n      }\r\n\r\n      &-icon {\r\n        width: 88px;\r\n        height: 88px;\r\n        line-height: 80px;\r\n        padding: 4px;\r\n        font-size: 40px;\r\n        border-width: 2px;\r\n        margin-top: -20px;\r\n        color: #3b4149;\r\n\r\n        .step-icon {\r\n          font-size: 40px;\r\n          color: #3b4149;\r\n        }\r\n      }\r\n\r\n      &-tail::after {\r\n        height: 2px;\r\n      }\r\n\r\n      &:last-child {\r\n        .step-icon {\r\n          color: #d9d9d9 !important;\r\n        }\r\n\r\n        &.ant-steps-item-process,\r\n        &.ant-steps-item-finish {\r\n          .step-icon {\r\n            color: #3b4149 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .ready-to-start {\r\n    animation: pulse 1.2s infinite;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n      opacity: 0.8;\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .clickable {\r\n    cursor: pointer;\r\n    &:hover {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ToolsPanel.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ToolsPanel.vue?vue&type=template&id=02b63088&scoped=true\"\nimport script from \"./ToolsPanel.vue?vue&type=script&lang=js\"\nexport * from \"./ToolsPanel.vue?vue&type=script&lang=js\"\nimport style0 from \"./ToolsPanel.vue?vue&type=style&index=0&id=02b63088&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"02b63088\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<ToolsPanel></ToolsPanel>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport ToolsPanel from \"@/components/Cards/ToolsPanel.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        ToolsPanel,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GenerateScript.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./GenerateScript.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./GenerateScript.vue?vue&type=template&id=1233a260\"\nimport script from \"./GenerateScript.vue?vue&type=script&lang=js\"\nexport * from \"./GenerateScript.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}