# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/

# Database files
*.db
*.sqlite3

# Vector database
vectordb/

# Temporary files
*.tmp
*.temp

# OS
.DS_Store
Thumbs.db

# Models (uncomment if you want to exclude model files from git)
# models/sentence-transformers/
# Note: For distribution, you may want to include models in your package
# and exclude them from git due to size constraints

# Cache
cache/
.cache/

# Environment variables
.env
.env.local 