#!/usr/bin/env python3
# coding: utf-8

from sqlalchemy import <PERSON><PERSON>n, Integer, String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class CodeRepository(Base):
    __tablename__ = 'code_repository'

    id = Column(Integer, primary_key=True)
    microservice_name = Column(String(255), nullable=False)
    repository_url = Column(String(500), nullable=False)
    branch_name = Column(String(255), nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'microservice_name': self.microservice_name,
            'repository_url': self.repository_url,
            'branch_name': self.branch_name
        }
