import os
import configparser
from app.obsclient.logger import log_error


def load_config(file_path):
    config = configparser.ConfigParser(allow_no_value=True, interpolation=None)
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config.read_file(f)
    except FileNotFoundError:
        log_error("Configuration file not found.")
        return None
    except configparser.Error as e:
        log_error(f"Error reading configuration file: {e}")
        return None

    return config


def get_obs_config():
    config_file_path = os.path.join(os.path.dirname(__file__), '../../config/obs_config.ini')
    config = load_config(config_file_path)

    if config is None:
        return None

    ak = config.get('DEFAULT', 'AK', fallback='')
    sk = config.get('DEFAULT', 'SK', fallback='')
    server = config.get('DEFAULT', 'SERVER', fallback='')
    location = server.split('.')[1]

    bucket_name = config.get('DEFAULT', 'BUCKET_NAME', fallback='')
    bucket_store_dir = config.get('DEFAULT', 'BUCKET_STORE_DIR', fallback='')

    upload_file_path = config.get('DEFAULT', 'UPLOAD_FILE_PATH', fallback='')
    upload_object_key = config.get('DEFAULT', 'UPLOAD_OBJECT_KEY', fallback='')

    download_object_key = config.get('DEFAULT', 'DOWNLOAD_OBJECT_KEY', fallback='')
    download_file_path = config.get('DEFAULT', 'DOWNLOAD_FILE_PATH', fallback='')

    cbh_host = config.get('DEFAULT', 'CBH_HOST', fallback='')
    cbh_user_name = config.get('DEFAULT', 'CBH_USER_Name', fallback='')
    cbh_user_port = config.get('DEFAULT', 'CBH_USER_PORT', fallback='')
    cbh_private_key_path = config.get('DEFAULT', 'CBH_PRIVATE_KEY_PATH', fallback='')
    cbh_private_key_passwd = config.get('DEFAULT', 'CBH_PRIVATE_KEY_PASSWD', fallback='', raw=True)
    cbh_switch_account = config.get('DEFAULT', 'CBH_SWITCH_ACCOUNT', fallback='')

    if not ak or not sk or not bucket_name:
        log_error("Access Key ID, Secret Key and Bucket must be provided in the config.")
        return None

    return {
        "ak": ak,
        "sk": sk,
        "server": server,
        "location": location,
        "bucket_name": bucket_name,
        "bucket_store_dir": bucket_store_dir,
        "upload_file_path": upload_file_path,
        "upload_object_key": upload_object_key,
        "download_object_key": download_object_key,
        "download_file_path": download_file_path,
        "cbh_host": cbh_host,
        "cbh_user_name": cbh_user_name,
        "cbh_user_port": cbh_user_port,
        "cbh_private_key_path": cbh_private_key_path,
        "cbh_private_key_passwd": cbh_private_key_passwd,
        "cbh_switch_account": cbh_switch_account,
    }
