import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';
import axios from 'axios';
import SimpleLayout from './layouts/Simple.vue'
import DashboardLayout from './layouts/Dashboard.vue'
import 'xterm/css/xterm.css';
import i18n from './i18n';
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'

// 确保默认使用暗黑模式
if (localStorage.getItem('darkMode') === null) {
  localStorage.setItem('darkMode', 'true');
}

import './scss/app.scss';

Vue.use(Antd);

Vue.config.productionTip = false


Vue.component("layout-simple", SimpleLayout);
Vue.component("layout-dashboard", DashboardLayout);
Vue.component("VueJsonPretty", VueJsonPretty);

Vue.prototype.$axios = axios;

new Vue({
  store,
  router,
  i18n,
  render: h => h(App)
}).$mount('#app')
