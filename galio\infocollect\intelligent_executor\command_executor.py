"""
命令执行器 - 自动执行生成的测试命令和工具
"""

import subprocess
import asyncio
import time
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, TimeoutError
import shlex

from infocollect.log.logger import log_info, log_error, log_debug, log_warning


class CommandExecutor:
    """命令执行器，负责安全地执行测试命令"""
    
    def __init__(self, max_workers: int = 3, default_timeout: int = 300):
        self.max_workers = max_workers
        self.default_timeout = default_timeout
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 定义安全的命令白名单
        self.safe_commands = {
            # Kubernetes命令
            'kubectl': ['get', 'describe', 'logs', 'version', 'cluster-info'],
            # Docker命令
            'docker': ['ps', 'images', 'version', 'info', 'logs', 'inspect'],
            # 网络工具
            'nmap': ['-sT', '-sS', '-sU', '-A', '-O', '-p'],
            'curl': ['-I', '-H', '-X', '--connect-timeout', '--max-time'],
            'wget': ['--spider', '--timeout', '--tries'],
            'ping': ['-c', '-W', '-i'],
            'telnet': [],
            'nc': ['-z', '-v', '-w'],
            # 系统命令
            'ps': ['aux', '-ef', '-eo'],
            'netstat': ['-an', '-tlnp'],
            'ss': ['-tlnp', '-an'],
            'lsof': ['-i', '-p'],
            'systemctl': ['status', 'is-active', 'list-units'],
            'service': ['status'],
            # 安全工具
            'nessus': [],
            'openvas': [],
            'nuclei': ['-t', '-u', '-l'],
            # 信息收集
            'whoami': [],
            'id': [],
            'uname': ['-a'],
            'hostname': [],
            'cat': ['/etc/passwd', '/etc/group', '/proc/version'],
            'ls': ['-la', '-al'],
            'find': ['-name', '-type', '-perm'],
        }
        
        # 危险命令黑名单
        self.dangerous_commands = {
            'rm', 'rmdir', 'dd', 'mkfs', 'fdisk', 'parted',
            'chmod', 'chown', 'chgrp', 'passwd', 'sudo',
            'su', 'mount', 'umount', 'killall', 'pkill',
            'shutdown', 'reboot', 'halt', 'poweroff',
            'iptables', 'ufw', 'firewalld', 'systemctl start',
            'systemctl stop', 'systemctl restart', 'service start',
            'service stop', 'service restart'
        }

    async def execute_command_set(self, command_set: List[Dict[str, Any]], 
                                node_ip: str = None) -> Dict[str, Any]:
        """执行一组命令"""
        try:
            log_info(f"Executing {len(command_set)} commands on node: {node_ip or 'local'}")
            
            results = []
            total_commands = len(command_set)
            successful_commands = 0
            failed_commands = 0
            
            for i, cmd_info in enumerate(command_set):
                log_info(f"Executing command {i+1}/{total_commands}: {cmd_info.get('command', '')}")
                
                result = await self._execute_single_command(cmd_info, node_ip)
                results.append(result)
                
                if result.get('success', False):
                    successful_commands += 1
                else:
                    failed_commands += 1
                
                # 添加延迟避免过于频繁的命令执行
                if i < total_commands - 1:
                    await asyncio.sleep(1)
            
            return {
                "success": True,
                "total_commands": total_commands,
                "successful_commands": successful_commands,
                "failed_commands": failed_commands,
                "results": results,
                "execution_summary": self._generate_execution_summary(results)
            }
            
        except Exception as e:
            log_error(f"Error executing command set: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }

    async def _execute_single_command(self, cmd_info: Dict[str, Any], 
                                    node_ip: str = None) -> Dict[str, Any]:
        """执行单个命令"""
        command = cmd_info.get('command', '')
        description = cmd_info.get('description', '')
        timeout = cmd_info.get('timeout', self.default_timeout)
        
        try:
            # 安全检查
            safety_check = self._check_command_safety(command)
            if not safety_check['safe']:
                return {
                    "success": False,
                    "command": command,
                    "description": description,
                    "error": f"命令安全检查失败: {safety_check['reason']}",
                    "execution_time": 0
                }
            
            # 构建最终执行命令
            final_command = self._build_execution_command(command, node_ip)
            
            start_time = time.time()
            
            # 异步执行命令
            result = await self._run_command_async(final_command, timeout)
            
            execution_time = time.time() - start_time
            
            return {
                "success": result['success'],
                "command": command,
                "final_command": final_command,
                "description": description,
                "stdout": result.get('stdout', ''),
                "stderr": result.get('stderr', ''),
                "return_code": result.get('return_code', -1),
                "execution_time": execution_time,
                "timeout": timeout,
                "node_ip": node_ip
            }
            
        except Exception as e:
            log_error(f"Error executing command '{command}': {e}")
            return {
                "success": False,
                "command": command,
                "description": description,
                "error": str(e),
                "execution_time": 0
            }

    def _check_command_safety(self, command: str) -> Dict[str, Any]:
        """检查命令安全性"""
        try:
            # 基本格式检查
            if not command or not command.strip():
                return {"safe": False, "reason": "空命令"}
            
            # 解析命令
            try:
                parsed = shlex.split(command)
            except ValueError as e:
                return {"safe": False, "reason": f"命令格式错误: {e}"}
            
            if not parsed:
                return {"safe": False, "reason": "无法解析命令"}
            
            base_command = parsed[0]
            
            # 检查危险命令
            for dangerous in self.dangerous_commands:
                if dangerous in command.lower():
                    return {"safe": False, "reason": f"包含危险命令: {dangerous}"}
            
            # 检查是否在白名单中
            if base_command in self.safe_commands:
                return {"safe": True, "reason": "命令在白名单中"}
            
            # 未知命令，需要谨慎处理
            return {"safe": False, "reason": f"未知命令: {base_command}"}
            
        except Exception as e:
            log_error(f"Error checking command safety: {e}")
            return {"safe": False, "reason": f"安全检查失败: {e}"}

    def _build_execution_command(self, command: str, node_ip: str = None) -> str:
        """构建最终执行命令"""
        if node_ip:
            # 如果指定了节点IP，通过SSH执行
            ssh_command = f"ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no {node_ip}"
            final_command = f"{ssh_command} '{command}'"
        else:
            # 本地执行
            final_command = command
        
        return final_command

    async def _run_command_async(self, command: str, timeout: int) -> Dict[str, Any]:
        """异步执行命令"""
        try:
            # 创建子进程
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                limit=1024 * 1024  # 1MB buffer limit
            )
            
            try:
                # 等待进程完成，设置超时
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout
                )
                
                return {
                    "success": process.returncode == 0,
                    "stdout": stdout.decode('utf-8', errors='replace'),
                    "stderr": stderr.decode('utf-8', errors='replace'),
                    "return_code": process.returncode
                }
                
            except asyncio.TimeoutError:
                # 超时，终止进程
                process.terminate()
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                
                return {
                    "success": False,
                    "stdout": "",
                    "stderr": f"命令执行超时 ({timeout}s)",
                    "return_code": -1
                }
                
        except Exception as e:
            log_error(f"Error running command async: {e}")
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "return_code": -1
            }

    def _generate_execution_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成执行摘要"""
        summary = {
            "total_execution_time": sum(r.get('execution_time', 0) for r in results),
            "successful_commands": [r for r in results if r.get('success', False)],
            "failed_commands": [r for r in results if not r.get('success', False)],
            "command_outputs": {},
            "errors": [],
            "warnings": []
        }
        
        # 提取重要输出和错误
        for result in results:
            command = result.get('command', 'unknown')
            
            if result.get('success', False):
                stdout = result.get('stdout', '')
                if stdout and stdout.strip():
                    summary["command_outputs"][command] = stdout[:1000]  # 限制输出长度
            else:
                error_info = {
                    "command": command,
                    "error": result.get('error', result.get('stderr', 'Unknown error'))
                }
                summary["errors"].append(error_info)
        
        return summary

    def validate_execution_results(self, results: List[Dict[str, Any]], 
                                 expected_indicators: List[str] = None) -> Dict[str, Any]:
        """验证执行结果"""
        validation_result = {
            "valid": True,
            "validation_details": [],
            "warnings": [],
            "recommendations": []
        }
        
        for result in results:
            if not result.get('success', False):
                validation_result["valid"] = False
                validation_result["validation_details"].append({
                    "command": result.get('command', ''),
                    "issue": "命令执行失败",
                    "details": result.get('error', result.get('stderr', ''))
                })
            else:
                # 检查输出是否包含预期指标
                if expected_indicators:
                    stdout = result.get('stdout', '').lower()
                    for indicator in expected_indicators:
                        if indicator.lower() not in stdout:
                            validation_result["warnings"].append({
                                "command": result.get('command', ''),
                                "warning": f"输出中未找到预期指标: {indicator}"
                            })
        
        return validation_result

    def cleanup_execution_environment(self) -> Dict[str, Any]:
        """清理执行环境"""
        try:
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            log_info("Command execution environment cleaned up")
            return {"success": True, "message": "Environment cleaned up successfully"}
            
        except Exception as e:
            log_error(f"Error cleaning up execution environment: {e}")
            return {"success": False, "error": str(e)}

    def get_execution_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取执行统计信息"""
        if not results:
            return {"total": 0, "successful": 0, "failed": 0, "success_rate": 0}
        
        total = len(results)
        successful = sum(1 for r in results if r.get('success', False))
        failed = total - successful
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        avg_execution_time = sum(r.get('execution_time', 0) for r in results) / total
        
        return {
            "total": total,
            "successful": successful,
            "failed": failed,
            "success_rate": round(success_rate, 2),
            "average_execution_time": round(avg_execution_time, 2),
            "total_execution_time": sum(r.get('execution_time', 0) for r in results)
        } 