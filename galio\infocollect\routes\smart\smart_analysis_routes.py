# from flask import Blueprint, jsonify, request
# from services.smart.smart_analysis_service import SmartAnalysisService
#
# bp = Blueprint('smart_analysis', __name__)
# smart_analysis_service = SmartAnalysisService()
#
#
# @bp.route('/analyze-and-execute', methods=['POST'])
# def analyze_and_execute_testcases():
#     """
#     分析收集的信息并执行相关测试用例
#
#     POST JSON体:
#     - node_id: 节点ID
#     - info_type: 信息类型 (process, package, hardware, filesystem, port, docker, kubernetes)
#     - collected_data: 收集到的数据
#
#     返回:
#     - 分析和执行结果
#     """
#     try:
#         data = request.get_json()
#         if not data:
#             return jsonify({'error': '请求体必须是JSON格式'}), 400
#
#         node_id = data.get('node_id')
#         info_type = data.get('info_type')
#         collected_data = data.get('collected_data')
#
#         # 验证参数
#         if not node_id:
#             return jsonify({'error': '节点ID不能为空'}), 400
#
#         if not info_type:
#             return jsonify({'error': '信息类型不能为空'}), 400
#
#         if info_type not in ['process', 'package', 'hardware', 'filesystem', 'port', 'docker', 'kubernetes']:
#             return jsonify({'error': '信息类型必须是: process, package, hardware, filesystem, port, docker, kubernetes 之一'}), 400
#
#         if not collected_data:
#             return jsonify({'error': '收集的数据不能为空'}), 400
#
#         # 执行分析和测试用例匹配
#         result = smart_analysis_service.analyze_collected_info_and_execute_testcases(
#             node_id=node_id,
#             info_type=info_type,
#             collected_data=collected_data
#         )
#
#         return jsonify(result), 200
#
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500
#
#
# @bp.route('/search-testcases', methods=['POST'])
# def search_testcases_by_info():
#     """
#     根据收集的信息搜索相关测试用例（不执行）
#
#     POST JSON体:
#     - info_type: 信息类型
#     - collected_data: 收集到的数据
#     - top_k: 返回结果数量，默认为5
#
#     返回:
#     - 匹配的测试用例列表
#     """
#     try:
#         data = request.get_json()
#         if not data:
#             return jsonify({'error': '请求体必须是JSON格式'}), 400
#
#         info_type = data.get('info_type')
#         collected_data = data.get('collected_data')
#         top_k = data.get('top_k', 5)
#
#         # 验证参数
#         if not info_type:
#             return jsonify({'error': '信息类型不能为空'}), 400
#
#         if not collected_data:
#             return jsonify({'error': '收集的数据不能为空'}), 400
#
#         # 生成查询文本
#         query_text = smart_analysis_service._generate_query_from_collected_data(info_type, collected_data)
#
#         if not query_text:
#             return jsonify({
#                 'info_type': info_type,
#                 'query_text': '',
#                 'matched_testcases': [],
#                 'message': '无法生成有效的查询文本'
#             }), 200
#
#         # 搜索相关测试用例
#         matched_testcases = smart_analysis_service._search_relevant_testcases(query_text, info_type)
#
#         return jsonify({
#             'info_type': info_type,
#             'query_text': query_text,
#             'total': len(matched_testcases),
#             'matched_testcases': matched_testcases
#         }), 200
#
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500
#
#
# @bp.route('/execute-testcase', methods=['POST'])
# def execute_single_testcase():
#     """
#     在指定节点上执行单个测试用例
#
#     POST JSON体:
#     - node_id: 节点ID
#     - testcase_number: 测试用例编号
#     - context_data: 上下文数据（可选）
#
#     返回:
#     - 执行结果
#     """
#     try:
#         data = request.get_json()
#         if not data:
#             return jsonify({'error': '请求体必须是JSON格式'}), 400
#
#         node_id = data.get('node_id')
#         testcase_number = data.get('testcase_number')
#         context_data = data.get('context_data', {})
#
#         # 验证参数
#         if not node_id:
#             return jsonify({'error': '节点ID不能为空'}), 400
#
#         if not testcase_number:
#             return jsonify({'error': '测试用例编号不能为空'}), 400
#
#         # 首先搜索测试用例
#         from intelligent_executor.vectordb.manager import VectorDBManager
#         vector_manager = VectorDBManager()
#
#         # 使用测试用例编号搜索
#         testcases = vector_manager.search_testcases(
#             f"测试用例编号 {testcase_number}", search_type="name", top_k=1
#         )
#
#         if not testcases:
#             return jsonify({'error': f'未找到测试用例: {testcase_number}'}), 404
#
#         testcase = testcases[0]
#
#         # 获取节点信息
#         node = smart_analysis_service._get_node_info(node_id)
#         if not node:
#             return jsonify({'error': f'未找到节点: {node_id}'}), 404
#
#         # 执行测试用例
#         execution_results = smart_analysis_service._execute_testcases_on_node(
#             node, [testcase], context_data
#         )
#
#         if execution_results:
#             result = execution_results[0]
#             return jsonify({
#                 'node_id': node_id,
#                 'testcase': testcase,
#                 'execution_result': result
#             }), 200
#         else:
#             return jsonify({'error': '执行测试用例失败'}), 500
#
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500