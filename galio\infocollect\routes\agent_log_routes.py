from flask import Blueprint, jsonify, request
from services.agent_log_service import AgentLogService
from routes.database_wraps import with_database_session

agent_log_routes = Blueprint('agent_log', __name__)


@agent_log_routes.route('/<string:node_ip>', methods=['GET'])
@with_database_session
def get_agent_logs(node_ip, db):
    """获取指定节点的agent日志"""
    limit = request.args.get('limit', default=100, type=int)
    
    agent_log_service = AgentLogService(db)
    
    # 获取节点信息
    node = agent_log_service.get_node(node_ip)
    if not node:
        return jsonify({"error": "Node not found"}), 404
    
    # 获取日志数据
    try:
        logs = agent_log_service.get_agent_logs(node.id, limit)
        return jsonify(logs), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500 