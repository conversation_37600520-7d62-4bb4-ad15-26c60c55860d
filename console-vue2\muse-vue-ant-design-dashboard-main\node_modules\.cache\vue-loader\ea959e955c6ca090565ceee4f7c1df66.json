{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\DockerInfo.vue", "mtime": 1751513794205}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DockerInfo.vue"], "names": [], "mappings": ";AAs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file": "DockerInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full docker-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n    <!-- 引用JsonDetailModal组件 -->\r\n    <JsonDetailModal ref=\"jsonDetailModal\" />\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"25\" height=\"25\"\r\n                viewBox=\"0 0 24 24\"\r\n                :class=\"`text-${sidebarColor}`\"\r\n            >\r\n              <path fill=\"currentColor\" d=\"M13.983 11.078h2.119a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.119a.185.185 0 0 0-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 0 0 .186-.186V3.574a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m0 2.716h2.118a.187.187 0 0 0 .186-.186V6.29a.186.186 0 0 0-.186-.185h-2.118a.185.185 0 0 0-.185.185v1.887c0 .102.082.186.185.186m-2.93 0h2.12a.186.186 0 0 0 .184-.186V6.29a.185.185 0 0 0-.185-.185H8.1a.185.185 0 0 0-.185.185v1.887c0 .102.083.186.185.186m-2.964 0h2.119a.186.186 0 0 0 .185-.186V6.29a.185.185 0 0 0-.185-.185H5.136a.186.186 0 0 0-.186.185v1.887c0 .102.084.186.186.186m5.893 2.715h2.118a.186.186 0 0 0 .186-.185V9.006a.186.186 0 0 0-.186-.186h-2.118a.185.185 0 0 0-.185.185v1.888c0 .102.082.185.185.185m-2.93 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185m-2.964 0h2.119a.185.185 0 0 0 .185-.185V9.006a.185.185 0 0 0-.185-.186h-2.12a.186.186 0 0 0-.185.185v1.888c0 .102.084.185.185.185m-2.92 0h2.12a.185.185 0 0 0 .184-.185V9.006a.185.185 0 0 0-.184-.186h-2.12a.185.185 0 0 0-.184.185v1.888c0 .102.083.185.185.185M23.763 9.89c-.065-.051-.672-.51-1.954-.51-.338.001-.676.03-1.01.087-.248-1.7-1.653-2.53-1.716-2.566l-.344-.199-.226.327c-.284.438-.49.922-.612 1.43-.23.97-.09 1.882.403 2.661-.595.332-1.55.413-1.744.42H.751a.751.751 0 0 0-.75.748 11.376 11.376 0 0 0 .692 4.062c.545 1.428 1.355 2.48 2.41 3.124 1.18.723 3.1 1.137 5.275 1.137.983.003 1.963-.086 2.93-.266a12.248 12.248 0 0 0 3.823-1.389c.98-.567 1.86-1.288 2.61-2.136 1.252-1.418 1.998-2.997 2.553-4.4h.221c1.372 0 2.215-.549 2.68-1.009c.309-.293.55-.65.707-1.046l.098-.288z\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.docker') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchActiveTabData\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <div class=\"container-runtime-tabs\">\r\n      <a-tabs v-model:activeKey=\"activeEngine\">\r\n        <a-tab-pane key=\"docker\" tab=\"Docker\">\r\n          <a-tabs v-model:activeKey=\"activeTab\" @change=\"handleTabChange\">\r\n            <a-tab-pane key=\"docker_host_config\" tab=\"Host Config\">\r\n              <div v-if=\"activeTab === 'docker_host_config'\" class=\"host-config-container\">\r\n                <a-row :gutter=\"[16, 16]\">\r\n                  <!-- Basic Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Basic Information\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Docker Version:</strong> {{ hostConfigData[0]?.docker_version }}</p>\r\n                      <p class=\"info-item\"><strong>Operating System:</strong> {{ getDaemonConfig('OperatingSystem') }}</p>\r\n                      <p class=\"info-item\"><strong>Architecture:</strong> {{ getDaemonConfig('Architecture') }}</p>\r\n                      <p class=\"info-item\"><strong>Kernel Version:</strong> {{ getDaemonConfig('KernelVersion') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Resource Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Resources\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>CPU Cores:</strong> {{ getDaemonConfig('NCPU') }}</p>\r\n                      <p class=\"info-item\"><strong>Total Memory:</strong> {{ formatBytes(getDaemonConfig('MemTotal')) }}</p>\r\n                      <p class=\"info-item\"><strong>Docker Root Dir:</strong> {{ getDaemonConfig('DockerRootDir') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Security Settings -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Security\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>User in Docker Group:</strong> {{ hostConfigData[0]?.user_in_docker_group ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Root User:</strong> {{ hostConfigData[0]?.is_root_user ? 'Yes' : 'No' }}</p>\r\n                      <p class=\"info-item\"><strong>Security Options:</strong> {{ getDaemonConfig('SecurityOptions')?.join(', ') || 'None' }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Container Statistics -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Container Statistics\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Total Containers:</strong> {{ getDaemonConfig('Containers') }}</p>\r\n                      <p class=\"info-item\"><strong>Running:</strong> {{ getDaemonConfig('ContainersRunning') }}</p>\r\n                      <p class=\"info-item\"><strong>Stopped:</strong> {{ getDaemonConfig('ContainersStopped') }}</p>\r\n                      <p class=\"info-item\"><strong>Images:</strong> {{ getDaemonConfig('Images') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Driver Information -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Storage Driver\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Driver:</strong> {{ getDaemonConfig('Driver') }}</p>\r\n                      <p class=\"info-item\"><strong>Logging Driver:</strong> {{ getDaemonConfig('LoggingDriver') }}</p>\r\n                      <p class=\"info-item\"><strong>Cgroup Driver:</strong> {{ getDaemonConfig('CgroupDriver') }}</p>\r\n                    </a-card>\r\n                  </a-col>\r\n\r\n                  <!-- Registry Configuration -->\r\n                  <a-col :span=\"8\">\r\n                    <a-card title=\"Registry Configuration\" :bordered=\"false\">\r\n                      <p class=\"info-item\"><strong>Index Server:</strong> {{ getDaemonConfig('IndexServerAddress') }}</p>\r\n                      <p class=\"info-item\"><strong>Registry Mirrors:</strong></p>\r\n                      <ul>\r\n                        <li v-for=\"mirror in getRegistryMirrors()\" :key=\"mirror\" class=\"info-item\">{{ mirror }}</li>\r\n                      </ul>\r\n                    </a-card>\r\n                  </a-col>\r\n                </a-row>\r\n              </div>\r\n            </a-tab-pane>\r\n            <a-tab-pane key=\"docker_network\" tab=\"Networks\">\r\n              <a-table\r\n                :columns=\"networkColumns\"\r\n                :data-source=\"networkData\"\r\n                :rowKey=\"(record) => record.network_id\"\r\n                v-if=\"activeTab === 'docker_network'\"\r\n                :loading=\"loadingNetworks\"\r\n                :pagination=\"pagination\"\r\n              >\r\n              </a-table>\r\n            </a-tab-pane>\r\n\r\n            <a-tab-pane key=\"docker_container\" tab=\"Containers\">\r\n              <a-table\r\n                :columns=\"containerColumns\"\r\n                :data-source=\"containerData\"\r\n                :scroll=\"{ x: 1500 }\"\r\n                :pagination=\"{ pageSize: 20 }\"\r\n                :row-key=\"record => record.container_id\"\r\n                v-if=\"activeTab === 'docker_container'\"\r\n                :loading=\"loadingContainers\"\r\n              >\r\n                <template #mountsColumn=\"{ record }\">\r\n                  <div v-if=\"record\">\r\n                    <template v-if=\"record.mounts && record.mounts.length\">\r\n                      <div v-for=\"(mount, index) in record.mounts\" :key=\"index\">\r\n                        {{ mount.Source }} → {{ mount.Destination }}\r\n                      </div>\r\n                    </template>\r\n                    <span v-else>N/A</span>\r\n                  </div>\r\n                </template>\r\n              </a-table>\r\n\r\n\r\n\r\n              <network-listening-modal\r\n                :visible=\"networkDetailsVisible\"\r\n                :network-data=\"selectedNetwork\"\r\n                @update:visible=\"networkDetailsVisible = $event\"\r\n                @close=\"handleNetworkDetailsClose\"\r\n              />\r\n            </a-tab-pane>\r\n          </a-tabs>\r\n        </a-tab-pane>\r\n        <a-tab-pane key=\"crictl\" tab=\"CRI\">\r\n          <crictl-info :node-ip=\"selectedNodeIp\" />\r\n        </a-tab-pane>\r\n      </a-tabs>\r\n    </div>\r\n\r\n    <a-modal\r\n      v-model:visible=\"mountDetailsVisible\"\r\n      title=\"Mount Details\"\r\n      width=\"800px\"\r\n      @cancel=\"handleMountDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleMountDetailsClose\">Cancel</a-button>\r\n      </template>\r\n      <template v-if=\"selectedMountContainer\">\r\n        <div class=\"mounts-container\">\r\n          <div v-for=\"(mount, index) in getAllMounts()\" :key=\"index\" class=\"mount-item\">\r\n            <div class=\"mount-path\">\r\n              <span class=\"mount-source\">{{ mount.Source }}</span>\r\n              <span class=\"mount-arrow\">→</span>\r\n              <span class=\"mount-dest\">{{ mount.Destination }}</span>\r\n            </div>\r\n            <div class=\"mount-details\">\r\n              <span v-if=\"mount.Mode\" class=\"mount-tag\">{{ mount.Mode }}</span>\r\n              <span class=\"mount-tag\">{{ mount.RW ? 'RW' : 'RO' }}</span>\r\n              <span v-if=\"mount.Propagation\" class=\"mount-tag\">{{ mount.Propagation }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <a-modal\r\n      v-model:visible=\"environmentDetailsVisible\"\r\n      title=\"Environment Variables\"\r\n      width=\"800px\"\r\n      @cancel=\"handleEnvironmentDetailsClose\"\r\n    >\r\n      <template v-slot:footer>\r\n        <a-button @click=\"handleEnvironmentDetailsClose\">Close</a-button>\r\n      </template>\r\n      <template v-if=\"selectedEnvironment\">\r\n        <div class=\"env-container\">\r\n          <div v-for=\"(env, index) in getAllEnvironmentVars()\" :key=\"index\" class=\"env-item\">\r\n            {{ env }}\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </a-modal>\r\n\r\n    <process-table\r\n      :visible=\"processDetailsVisible\"\r\n      :process-container=\"selectedProcessContainer\"\r\n      user-field=\"uid\"\r\n      :include-tty=\"true\"\r\n      @update:visible=\"processDetailsVisible = $event\"\r\n      @close=\"handleProcessDetailsClose\"\r\n    />\r\n\r\n    <process-detail-modal\r\n      :visible=\"processDetailInfoVisible\"\r\n      :process-info=\"selectedProcessInfo\"\r\n      user-field=\"uid\"\r\n      @update:visible=\"processDetailInfoVisible = $event\"\r\n      @close=\"handleProcessDetailInfoClose\"\r\n    />\r\n\r\n\r\n\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport CrictlInfo from './CrictlInfo.vue';\r\nimport JsonDetailModal from '../Widgets/JsonDetailModal.vue';\r\nimport { ProcessDetailModal, ProcessTable } from '../Widgets/Process/process_index';\r\nimport { NetworkListeningModal, NetworkListeningCell } from '../Widgets/Network/network_index';\r\nimport { MountCell } from '../Widgets/Mount/mount_index';\r\n// 不再使用单独的环境变量组件\r\n// import { EnvironmentCell, EnvironmentModal } from '../Widgets/Environment/environment_index';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    CrictlInfo,\r\n    JsonDetailModal,\r\n    ProcessDetailModal,\r\n    ProcessTable,\r\n    NetworkListeningModal,\r\n    NetworkListeningCell,\r\n    MountCell,\r\n    // EnvironmentCell,\r\n    // EnvironmentModal\r\n  },\r\n  name: 'DockerInfo',\r\n  props: {\r\n    // Using selectedNodeIp from Vuex store instead of prop\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'docker_host_config',\r\n      hostConfigData: [],\r\n      containerData: [],\r\n      networkData: [],\r\n      loadingHostConfig: false,\r\n      loadingContainers: false,\r\n      loadingNetworks: false,\r\n      hostConfigColumns: [\r\n        { title: 'Docker Version', dataIndex: 'docker_version', key: 'docker_version' },\r\n        {\r\n          title: 'Daemon Config',\r\n          dataIndex: 'daemon_config',\r\n          key: 'daemon_config',\r\n        },\r\n        {\r\n          title: 'In Docker Group',\r\n          dataIndex: 'user_in_docker_group',\r\n          key: 'user_in_docker_group',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n        {\r\n          title: 'Is Root',\r\n          dataIndex: 'is_root_user',\r\n          key: 'is_root_user',\r\n          render: (text) => (text ? 'Yes' : 'No'),\r\n        },\r\n      ],\r\n      containerColumns: [\r\n        {\r\n          title: 'Container ID',\r\n          dataIndex: 'container_id',\r\n          key: 'container_id',\r\n          width: '150px',\r\n          ellipsis: false,\r\n        },\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'inspect_data',\r\n          key: 'name',\r\n          width: '15%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              return {\r\n                children: inspectData.Name || 'N/A',\r\n                props: {\r\n                  style: {\r\n                    whiteSpace: 'normal',\r\n                    wordBreak: 'break-word'\r\n                  }\r\n                }\r\n              };\r\n            } catch (e) {\r\n              console.warn('Failed to render container name:', e);\r\n              return 'N/A';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Privileged',\r\n          dataIndex: 'inspect_data',\r\n          key: 'privileged',\r\n          width: '100px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const inspectData = this.parseJsonField(record.inspect_data, {});\r\n              const privileged = inspectData.HostConfig?.Privileged;\r\n              // 将undefined、null或非布尔值视为false\r\n              return privileged === true ? 'Yes' : 'No';\r\n            } catch (e) {\r\n              console.warn('Failed to render privileged status:', e);\r\n              return 'No'; // 出错时默认显示为No\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Environment',\r\n          dataIndex: 'env',\r\n          key: 'env',\r\n          width: '200px',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const envVars = this.parseJsonField(record.env, []);\r\n              if (!envVars.length) return 'No environment variables';\r\n\r\n              return (\r\n                <div style=\"font-size: 12px;\">\r\n                  {envVars.slice(0, 1).map((env, index) => (\r\n                    <div key={index} style=\"padding: 2px 0; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">\r\n                      {env.length > 200 ? env.substring(0, 200) + '...' : env}\r\n                    </div>\r\n                  ))}\r\n                  {envVars.length > 1 && (\r\n                    <a-button\r\n                      type=\"link\"\r\n                      size=\"small\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        this.showEnvironmentDetails(record);\r\n                      }}\r\n                    >\r\n                      +{envVars.length - 1} more...\r\n                    </a-button>\r\n                  )}\r\n                </div>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render env vars:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Mounts',\r\n          dataIndex: 'mounts',\r\n          key: 'mounts',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <mount-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showMountDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Network Listening',\r\n          dataIndex: 'exposures',\r\n          key: 'exposures',\r\n          width: '25%',\r\n          ellipsis: false,\r\n          customRender: (_, record) => {\r\n            return (\r\n              <network-listening-cell\r\n                record={record}\r\n                parseJsonField={this.parseJsonField}\r\n                onShow-details={this.showNetworkDetails}\r\n              />\r\n            );\r\n          }\r\n        },\r\n        {\r\n          title: 'Processes',\r\n          dataIndex: 'processes',\r\n          key: 'processes',\r\n          width: '120px',\r\n          align: 'center',\r\n          customRender: (_, record) => {\r\n            try {\r\n              const processData = this.parseJsonField(record.processes, {});\r\n              if (!processData?.process_list?.length) return 'N/A';\r\n\r\n              return (\r\n                <a onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showProcessDetails(record);\r\n                }}>\r\n                  {processData.process_list.length} processes\r\n                </a>\r\n              );\r\n            } catch (e) {\r\n              console.warn('Failed to render processes:', e);\r\n              return 'None';\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: 'Inspect Data',\r\n          dataIndex: 'inspect_data',\r\n          key: 'inspect_data',\r\n          width: '150px',\r\n          align: 'center',\r\n          customRender: (_, record) => (\r\n            <span style=\"display: inline-block; line-height: 22px;\">\r\n              <a\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  this.showInspectDetails(record);\r\n                }}\r\n                style=\"color: #1890ff; cursor: pointer;\"\r\n              >\r\n                View Inspect\r\n              </a>\r\n            </span>\r\n          )\r\n        }\r\n      ],\r\n      networkColumns: [\r\n        {\r\n          title: 'Name',\r\n          dataIndex: 'name',\r\n          key: 'name',\r\n          width: '120px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Network ID',\r\n          dataIndex: 'network_id',\r\n          key: 'network_id',\r\n          width: '180px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Driver',\r\n          dataIndex: 'driver',\r\n          key: 'driver',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'Scope',\r\n          dataIndex: 'scope',\r\n          key: 'scope',\r\n          width: '100px',\r\n          customRender: (text) => text || 'None'\r\n        },\r\n        {\r\n          title: 'IPv6',\r\n          dataIndex: 'ipv6',\r\n          key: 'ipv6',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Internal',\r\n          dataIndex: 'internal',\r\n          key: 'internal',\r\n          width: '80px',\r\n          customRender: (text) => {\r\n            if (text === undefined || text === null) return 'None';\r\n            return text ? 'Yes' : 'No';\r\n          }\r\n        },\r\n        {\r\n          title: 'Labels',\r\n          dataIndex: 'labels',\r\n          key: 'labels',\r\n          width: '120px',\r\n          customRender: (labels) => {\r\n            if (!labels || Object.keys(labels).length === 0) return 'None';\r\n            return Object.entries(labels).map(([key, value]) => `${key}=${value}`).join(', ');\r\n          }\r\n        },\r\n        {\r\n          title: 'Created',\r\n          dataIndex: 'created_at',\r\n          key: 'created_at',\r\n          width: '160px',\r\n          customRender: (text) => text || 'None'\r\n        }\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n      resourceMapping: {\r\n        'docker_host_config': 'get_docker_host_config',\r\n        'docker_container': 'get_docker_containers',\r\n        'docker_network': 'get_docker_networks',\r\n      },\r\n\r\n\r\n\r\n      networkDetailsVisible: false,\r\n      selectedNetwork: null,\r\n      mountDetailsVisible: false,\r\n      selectedMountContainer: null,\r\n      environmentDetailsVisible: false,\r\n      selectedEnvironment: null,\r\n      processDetailsVisible: false,\r\n      selectedProcessContainer: null,\r\n      processDetailInfoVisible: false,\r\n      selectedProcessInfo: null,\r\n      activeEngine: 'docker'\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.resetData();\r\n      if (newIp) {\r\n        this.fetchActiveTabData();\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    if (this.selectedNodeIp) {\r\n      this.fetchActiveTabData();\r\n    }\r\n  },\r\n  methods: {\r\n    handleTabChange(key) {\r\n      this.activeTab = key;\r\n      this.resetData();\r\n      this.fetchActiveTabData();\r\n    },\r\n    async fetchActiveTabData() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.resetData();\r\n        return;\r\n      }\r\n      const resourceType = this.activeTab;\r\n      const dataName = this.camelCaseToDataName(resourceType);\r\n      const loadingKey = `loading${this.capitalizeFirstLetter(dataName.replace('Data', ''))}`;\r\n\r\n      this[loadingKey] = true;\r\n\r\n      try {\r\n        const response = await axios.get(`/api/docker/${resourceType}/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n        const data = response.data;\r\n        this[dataName] = resourceType === 'docker_host_config' && data ? [data] : data;\r\n      } catch (error) {\r\n        console.error(`Error fetching ${resourceType}:`, error);\r\n        this[dataName] = [];\r\n      } finally {\r\n        this[loadingKey] = false;\r\n      }\r\n    },\r\n    camelCaseToDataName(camelCase) {\r\n      const withoutDocker = camelCase.replace('docker_', '');\r\n      return withoutDocker.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Data';\r\n    },\r\n    capitalizeFirstLetter(string) {\r\n      return string.charAt(0).toUpperCase() + string.slice(1);\r\n    },\r\n    resetData() {\r\n      this.hostConfigData = [];\r\n      this.containerData = [];\r\n      this.networkData = [];\r\n    },\r\n    getDaemonConfig(key) {\r\n      if (!this.hostConfigData[0]?.daemon_config) return null;\r\n      return this.hostConfigData[0].daemon_config[key];\r\n    },\r\n    getRegistryMirrors() {\r\n      const registryConfig = this.getDaemonConfig('RegistryConfig');\r\n      return registryConfig?.Mirrors || [];\r\n    },\r\n    formatBytes(bytes) {\r\n      if (!bytes) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n    },\r\n    parseJsonField(field, defaultValue) {\r\n      try {\r\n        if (Array.isArray(field)) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        if (typeof field === 'object' && field !== null) {\r\n          return this.cleanReactiveObject(field);\r\n        }\r\n        return typeof field === 'string' ? JSON.parse(field) : defaultValue;\r\n      } catch (e) {\r\n        console.warn('Failed to parse JSON field:', e);\r\n        return defaultValue;\r\n      }\r\n    },\r\n\r\n    showInspectDetails(container) {\r\n      try {\r\n        const inspectData = this.parseJsonField(container.inspect_data, {});\r\n        this.$refs.jsonDetailModal.showDetailModal('Container Inspect Data', inspectData);\r\n      } catch (e) {\r\n        console.error('Failed to parse inspect data:', e);\r\n        this.$message.error('Failed to parse inspect data');\r\n      }\r\n    },\r\n    renderPreviewWithMore(items, renderItem, moreText, onClickMore) {\r\n      if (!items || !items.length) return 'None';\r\n\r\n      return (\r\n        <div style=\"font-size: 12px;\">\r\n          {items.slice(0, 2).map((item, index) => renderItem(item, index))}\r\n          {items.length > 2 && (\r\n            <a-button\r\n              type=\"link\"\r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onClickMore();\r\n              }}\r\n            >\r\n              +{items.length - 2} {moreText}\r\n            </a-button>\r\n          )}\r\n        </div>\r\n      );\r\n    },\r\n    cleanReactiveObject(obj) {\r\n      if (!obj) return null;\r\n      if (Array.isArray(obj)) {\r\n        return obj.map(item => this.cleanReactiveObject(item));\r\n      }\r\n      if (typeof obj === 'object') {\r\n        const cleaned = {};\r\n        for (const key in obj) {\r\n          if (!key.startsWith('_') && !key.startsWith('$') && key !== '__ob__') {\r\n            cleaned[key] = this.cleanReactiveObject(obj[key]);\r\n          }\r\n        }\r\n        return cleaned;\r\n      }\r\n      return obj;\r\n    },\r\n    getProcessList() {\r\n      if (!this.selectedProcessContainer?.processes?.process_list) return [];\r\n      return this.selectedProcessContainer.processes.process_list;\r\n    },\r\n    getNetworkListening() {\r\n      if (!this.selectedContainer?.exposures) return [];\r\n      const exposedServices = this.parseJsonField(this.selectedContainer.exposures, {});\r\n      return exposedServices.listening_ports || [];\r\n    },\r\n    showNetworkDetails(container) {\r\n      this.selectedNetwork = {\r\n        ...container,\r\n        exposures: this.parseJsonField(container.exposures, {})\r\n      };\r\n      this.networkDetailsVisible = true;\r\n    },\r\n    handleNetworkDetailsClose() {\r\n      this.networkDetailsVisible = false;\r\n      this.selectedNetwork = null;\r\n    },\r\n\r\n    showMountDetails(container) {\r\n      this.selectedMountContainer = {\r\n        ...container,\r\n        mounts: this.parseJsonField(container.mounts, [])\r\n      };\r\n      this.mountDetailsVisible = true;\r\n    },\r\n    handleMountDetailsClose() {\r\n      this.mountDetailsVisible = false;\r\n      this.selectedMountContainer = null;\r\n    },\r\n    getAllMounts() {\r\n      if (!this.selectedMountContainer?.mounts) return [];\r\n      return this.selectedMountContainer.mounts;\r\n    },\r\n    showEnvironmentDetails(container) {\r\n      this.selectedEnvironment = {\r\n        ...container,\r\n        env: this.parseJsonField(container.env, [])\r\n      };\r\n      this.environmentDetailsVisible = true;\r\n    },\r\n    handleEnvironmentDetailsClose() {\r\n      this.environmentDetailsVisible = false;\r\n      this.selectedEnvironment = null;\r\n    },\r\n    getAllEnvironmentVars() {\r\n      if (!this.selectedEnvironment?.env) return [];\r\n      return this.selectedEnvironment.env;\r\n    },\r\n    showProcessDetails(container) {\r\n      this.selectedProcessContainer = {\r\n        ...container,\r\n        processes: this.parseJsonField(container.processes, {})\r\n      };\r\n      this.processDetailsVisible = true;\r\n    },\r\n    handleProcessDetailsClose() {\r\n      this.processDetailsVisible = false;\r\n      this.selectedProcessContainer = null;\r\n    },\r\n    showProcessDetailInfo(process) {\r\n      this.selectedProcessInfo = process;\r\n      this.processDetailInfoVisible = true;\r\n    },\r\n    handleProcessDetailInfoClose() {\r\n      this.processDetailInfoVisible = false;\r\n      this.selectedProcessInfo = null;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.docker-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.docker-title {\r\n  color: #333;\r\n  font-size: 16px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n// 表格样式会从全局样式继承，不需要在这里硬编码\r\n\r\n.host-config-container {\r\n  padding: 24px;\r\n}\r\n\r\n.ant-card {\r\n  height: 100%;\r\n}\r\n\r\n.ant-card-head {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-tag {\r\n  background: var(--input-bg, #f5f5f5);\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: var(--text-color, #666);\r\n}\r\n\r\n.source-path {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.path-arrow {\r\n  color: var(--disabled-color, #999);\r\n}\r\n\r\n.dest-path {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\n.proto-text {\r\n  color: var(--primary-color, #1890ff);\r\n}\r\n\r\n.program-text {\r\n  color: var(--success-color, #52c41a);\r\n}\r\n\r\nul {\r\n  padding-left: 20px;\r\n  margin: 0;\r\n}\r\n\r\nli {\r\n  word-break: break-all;\r\n}\r\n\r\n.ant-descriptions-bordered .ant-descriptions-item-label {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n.ant-table-cell {\r\n  white-space: pre-line !important;\r\n  vertical-align: top;\r\n  padding: 8px;\r\n}\r\n\r\npre {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  margin: 4px 0;\r\n}\r\n\r\n/* 添加mount项的样式 */\r\n.mount-item {\r\n  padding: 2px 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.mount-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.mounts-container {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.mount-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.mount-path {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mount-source {\r\n  color: #1890ff;\r\n}\r\n\r\n.mount-arrow {\r\n  margin: 0 8px;\r\n  color: #999;\r\n}\r\n\r\n.mount-dest {\r\n  color: #52c41a;\r\n}\r\n\r\n.mount-details {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mount-tag {\r\n  background: #f5f5f5;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.ant-collapse-panel {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.ant-tag {\r\n  margin: 2px;\r\n}\r\n\r\n.ant-collapse-content {\r\n  background: #fafafa;\r\n}\r\n\r\n.env-container {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.env-item {\r\n  padding: 8px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  word-break: break-all;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.ant-descriptions {\r\n  .ant-descriptions-item-label {\r\n    width: 180px;\r\n    background-color: #fafafa;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .ant-descriptions-item-content {\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.details-container {\r\n  width: 100%;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 4px;\r\n}\r\n\r\n.details-row {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.details-header {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.header-item {\r\n  padding: 12px 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.details-content {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n}\r\n\r\n.content-item {\r\n  padding: 12px 16px;\r\n  border-right: 1px solid #f0f0f0;\r\n  text-align: center;\r\n  word-break: break-word;\r\n\r\n  &:last-child {\r\n    border-right: none;\r\n  }\r\n}\r\n\r\n.container-runtime-tabs {\r\n  padding: 24px;\r\n}\r\n\r\n/* 确保标签紧跟在标题下方 */\r\n:deep(.ant-tabs) {\r\n  margin-top: 0;\r\n}\r\n\r\n:deep(.ant-card-head) {\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.card-header-wrapper {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.logo-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.inspect-data-pre, .detail-pre {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n  background-color: transparent !important; /* 移除背景色 */\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.detail-pre {\r\n  max-height: 300px;\r\n  margin: 0;\r\n}\r\n\r\n/* 确保Tab内容区域没有背景色 */\r\n.ant-tabs-tabpane {\r\n  background-color: transparent !important;\r\n}\r\n\r\n\r\n\r\n/* 信息项样式 */\r\n.info-item {\r\n  margin-bottom: 8px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 1px dashed #f0f0f0;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n    padding-bottom: 0;\r\n    border-bottom: none;\r\n  }\r\n\r\n  strong {\r\n    margin-right: 8px;\r\n    color: #555;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}