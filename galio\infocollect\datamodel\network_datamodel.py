# from datamodel.basic_datamodel import *
# from operations.port_matrix import port_matrix
#
# class NetworkCategory(Category):
#     pass
#
#
# class NetworkDataBlock(DataBlock):
#     pass
#
#
# class NetworkDataItem(DataItem):
#     def generate_html(self):
#         # print(self.value)
#         protocol = self.value.split('_')[0].strip()
#         # print(protocol)
#         # listen_net = self.value.split('_')[1].split('#')[0]
#         process = self.value.split('_')[1]
#         if protocol in ["tcp", "tcp6", "udp", "udp6"]:
#             port = process.rsplit(':', 1)[1].strip()
#             # print(port)
#         if protocol in ["tcp", "tcp6", "udp", "udp6"] and port != "":
#             return "协议:{0}    监听端口:{1}   {2}<br />".format(protocol, process, port_matrix.query_port_matrix_by_port(int(port)))
#         else:
#             return "协议:{0}    监听端口:{1}   <br />".format(protocol, process)