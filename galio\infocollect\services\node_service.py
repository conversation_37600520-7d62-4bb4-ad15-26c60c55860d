from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig


class NodeService:
    def __init__(self, db: Session):
        self.db = db

    def add_node(self, host_name: str, ip: str) -> int:
        """添加新节点或返回现有节点ID"""
        existing_node = self.db.query(HostConfig).filter_by(ip=ip).first()
        if existing_node:
            return existing_node.id
        else:
            new_node = HostConfig(hostname=host_name, ip=ip)
            self.db.add(new_node)
            self.db.commit()
            return new_node.id

    def get_all_nodes(self):
        """
        获取所有节点信息
        :return: 节点信息列表
        """
        nodes = self.db.query(HostConfig).all()
        result = []
        for node in nodes:
            result.append({
                "host_name": node.host_name,
                "ip": node.ip,
                "ssh_port": node.ssh_port,
                "login_user": node.login_user,
                "login_pwd": node.login_pwd,
                "switch_root_cmd": node.switch_root_cmd,
                "switch_root_pwd": node.switch_root_pwd
            })
        return result
