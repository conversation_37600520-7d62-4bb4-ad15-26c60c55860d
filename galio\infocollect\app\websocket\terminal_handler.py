import paramiko
from gevent import sleep
from app.models.node import Node
from app.websocket.socketio_instance import socketio
from app.websocket.util import fetch_aibash_cmd, nodes_to_csv_string
from datamodel.config_datamodel import HostConfig
from db.init_db import get_db
from log.logger import log_error, log_warning, log_info


class TerminalConnection:
    def __init__(self, sid):
        """
        初始化终端连接
        :param sid: WebSocket session ID
        """
        self.sid = sid
        self.pending_status = False
        self.pending_cmd = ""
        self.pending_shells = []
        self.shells = {}
        self.shells_output = {}
        self.available_nodes = None
        self.project_db = None
        self.pending_ips = []

    def init_shell(self, ip=None):

        if ip:
            if ip in self.shells:
                return
            node = self.available_nodes.get(ip)
            if node:
                shell = self.start_shell(node)
                if shell:
                    self.shells[ip] = shell
        else:
            for ip, node in self.available_nodes.items():
                if ip in self.shells:
                    continue
                shell = self.start_shell(node)
                if shell:
                    self.shells[ip] = shell

    def destory(self):
        for shell in self.shells.values():
            try:
                shell.close()
            except:
                pass

    def check_ip_in_database(self, ip_string):
        """检查 IP 是否在数据库中"""
        try:
            if not self.project_db:
                raise ValueError("Project database path not set")
            
            if self.available_nodes is None:
                self.init_nodes()
            
            # Split the IP string into individual IPs
            ips = [ip.strip() for ip in ip_string.split(',')]
            with get_db(self.project_db) as db:
                for ip in ips:
                    host_config = db.query(HostConfig).filter_by(ip=ip).first()
                    if not host_config:
                        log_warning(f"IP {ip} not found in database")
                        return False
                    log_info(f"IP {ip} found in database")
            
            return True
        except Exception as e:
            log_error(f"Error checking IP in database: {str(e)}")
            return False

    def get_all_node(self):
        try:
            if not self.project_db:
                raise ValueError("Project database path not set")
            
            nodes = {}
            with get_db(self.project_db) as db:
                host_configs = db.query(HostConfig).all()
                if not host_configs:
                    log_warning("No host configs found in database")
                    return []

                for config in host_configs:
                    try:
                        node = Node(
                            host_name=config.host_name,
                            ip=config.ip,
                            ssh_port=config.ssh_port,
                            login_user=config.login_user,
                            login_pwd=config.login_pwd,
                            switch_root_cmd=config.switch_root_cmd,
                            switch_root_pwd=config.switch_root_pwd
                        )
                        nodes[config.ip] = node
                    except Exception as e:
                        log_error(f"Error creating node from config: {str(e)}")
                        continue

                return nodes
        except Exception as e:
            log_error(f"Error fetching nodes: {str(e)}")
            return []

    def init_nodes(self):
        """初始化可用节点"""
        if not self.project_db:
            raise ValueError("Project database path not set")
        
        self.available_nodes = self.get_all_node()
        if not self.available_nodes:
            self._emit_message('error', 'No available nodes')
            raise ValueError('No available nodes')

    def start_shell(self, node):
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            ssh.connect(
                node.ip,
                port=node.ssh_port,
                username=node.login_user,
                password=node.login_pwd,
                timeout=10
            )

            shell = ssh.invoke_shell()
            shell.set_combine_stderr(True)
            shell.resize_pty(width=80, height=24)

            if node.switch_root_cmd and node.switch_root_pwd:
                shell.send(f"{node.switch_root_cmd}\n")
                sleep(1)
                shell.send(f"{node.switch_root_pwd}\n")
                sleep(1)

            self.shells[node.ip] = shell
            self.shells_output[node.ip] = ""
            shell.send('\n')

            if shell.recv_ready():
                data = shell.recv(4096).decode('utf-8', errors='ignore')
                # if data:
                #     # 确保输出完整性
                #     self._emit_message('output', f"[{ip}] {data}")
                #     sleep(0.1)  # 给输出一个小延迟
            return shell

        except Exception as e:
            self._emit_message('error', f'connect {node.ip} shell fail, {str(e)}')
            return None

    # finally:
    #     for shell in self.shells.values():
    #         try:
    #             shell.close()
    #         except:
    #             pass
    def generate_aibash_cmd(self, command):
        self.pending_shells.clear()
        
        # 只使用选中的节点创建配置
        selected_nodes = {}
        for ip, node in self.available_nodes.items():
            if ip in self.pending_ips:
                selected_nodes[ip] = node
        
        cluster_config = nodes_to_csv_string(selected_nodes)
        result, is_success = fetch_aibash_cmd(command, cluster_config)
        if not is_success:
            self._emit_message('error', f"{result}")
            return False, result

        for ip in self.pending_ips:
            self.pending_shells.append((result, ip))
        return True, result

    def batch_exec_command(self):
        try:
            for command, ip in self.pending_shells:
                try:
                    self.shells_output[ip] = ""

                    # 确保已初始化可用节点
                    if self.available_nodes is None:
                        self.init_nodes()

                    self.init_shell(ip)

                    if ip not in self.shells:
                        self._emit_message('error', f"No active shell connection for {ip}")
                        continue

                    shell = self.shells[ip]
                    if not shell.get_transport() or not shell.get_transport().is_active():
                        self._emit_message('error', f"未发现可用shell for {ip}")
                        continue

                    shell.send(command + '\n')
                    # 等待输出稳定
                    sleep(0.2)

                    # 立即读取可能的回显
                    while shell.recv_ready():
                        data = shell.recv(4096).decode('utf-8', errors='ignore')
                        if data:
                            self.shells_output[ip] = self.shells_output[ip] + data
                        sleep(0.1)

                except Exception as e:
                    self._emit_message('error', '发送命令异常 '+ str(e))

            for ip, shell_output in self.shells_output.items():
                self._emit_message('output',
                                   f"【{ip}】执行结果:\n {shell_output}\n----------------------------------------\n")
            self.shells_output.clear()

        except Exception as e:
            self._emit_message('error', '解析执行结果异常 ' + str(e))

    def send_command(self, command):
        try:
            self.init_shell()
            if not self.shells:
                raise ValueError("No active shell connections")
            
            self.shells_output.clear()
            for ip, shell in self.shells.items():
                if command and shell:
                    if not shell.get_transport() or not shell.get_transport().is_active():
                        continue
                    
                    self.shells_output[ip] = ""
                    shell.send(command + '\n')
                    sleep(0.2)

                    while shell.recv_ready():
                        data = shell.recv(4096).decode('utf-8', errors='ignore')
                        if data:
                            self.shells_output[ip] = self.shells_output[ip] + data
                        sleep(0.1)
                        
            for ip, shell_output in self.shells_output.items():
                self._emit_message('output',
                                   f"【{ip}】执行结果:\n {shell_output}\n----------------------------------------\n")
            self.shells_output.clear()

        except Exception as e:
            self._emit_message('error', str(e))

    def send_command_by_ip(self, ip_string, command):
        try:
            # 确保已初始化可用节点
            if self.available_nodes is None:
                self.init_nodes()
            
            # Split the IP string into individual IPs
            ips = [ip.strip() for ip in ip_string.split(',')]
            
            # Initialize shells for all IPs
            for ip in ips:
                self.init_shell(ip)
            
            # Send command to each IP
            for ip in ips:
                if ip not in self.shells:
                    self._emit_message('error', f"No active shell connection for {ip}")
                    continue

                shell = self.shells[ip]
                if not shell.get_transport() or not shell.get_transport().is_active():
                    self._emit_message('error', f"未发现可用shell for {ip}")
                    continue

                shell.send(command + '\n')
                # 等待输出稳定
                sleep(0.2)

                # 立即读取可能的回显
                while shell.recv_ready():
                    data = shell.recv(4096).decode('utf-8', errors='ignore')
                    if data:
                        self._emit_message('output', f"[{ip}] {data}")
                    sleep(0.1)

        except Exception as e:
            self._emit_message('error', str(e))

    def _emit_message(self, type, data):
        """安全地发送消息到客户端"""
        try:
            socketio.emit('message', {'type': type, 'message': data}, room=self.sid)
        except Exception as e:
            log_error(f"Error emitting message: {str(e)}")
