<template>
  <div class="log-viewer">
    <!-- Log <PERSON><PERSON> -->
    <a-tooltip :title="$t('log.viewLogs')">
      <a class="log-button" @click="showLogModal">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 448 512">
            <path d="M160 80c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 352c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-352zM0 272c0-26.5 21.5-48 48-48l32 0c26.5 0 48 21.5 48 48l0 160c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48L0 272zM368 96l32 0c26.5 0 48 21.5 48 48l0 288c0 26.5-21.5 48-48 48l-32 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48z"/>
        </svg>
      </a>
    </a-tooltip>

    <!-- Log <PERSON> -->
    <a-modal
      :title="$t('log.title')"
      :visible="visible"
      @cancel="handleCancel"
      :width="1200"
      :footer="null"
      :bodyStyle="{ padding: '0' }"
    >
      <div class="log-modal-content">
        <!-- Header Controls -->
        <div class="log-controls">
          <div class="log-controls-left">
            <span class="current-node-info">
              {{ $t('log.currentNode') }}: {{ currentNodeInfo }}
            </span>
            <a-select 
              v-model="selectedLogLevel" 
              style="width: 120px; margin-left: 12px;" 
              :placeholder="$t('log.selectLevel')"
              allow-clear
            >
              <a-select-option v-for="level in logLevelOptions" :key="level" :value="level">
                <span :class="`level-${level?.toLowerCase()}`" class="log-level-option">
                  {{ level }}
                </span>
              </a-select-option>
            </a-select>
          </div>
          <div class="log-controls-right">
            <a-button @click="fetchLogs" :loading="loading" type="primary" size="small">
              <a-icon type="reload" />
              {{ $t('log.refresh') }}
            </a-button>
          </div>
        </div>

        <!-- Log Content -->
        <div class="log-content" ref="logContent">
          <a-spin :spinning="loading">
            <div v-if="logs.length === 0 && !loading" class="no-logs">
              <a-empty :description="$t('log.noLogs')" />
            </div>
            <div v-else class="log-list">
              <div 
                v-for="(log, index) in filteredLogs" 
                :key="index" 
                class="log-item"
                :class="getLogLevelClass(log.log_level)"
              >
                <div class="log-header">
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <span class="log-level" :class="`level-${log.log_level?.toLowerCase()}`">
                    {{ log.log_level }}
                  </span>
                  <span v-if="log.module" class="log-module">{{ log.module }}</span>
                </div>
                <div class="log-message">{{ log.log_content }}</div>
              </div>
            </div>
          </a-spin>
        </div>
        
                  <!-- Pagination -->
          <div style="text-align: right; margin-top: 16px; padding-top: 16px; margin-right: 16px; margin-bottom: 24px;">
            <a-pagination 
              :current="currentPage" 
              :total="total" 
              :page-size="pageSize"
              @change="handlePageChange"
              size="small"
            />
          </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import axios from '@/api/axiosInstance';

export default {
  name: 'LogViewer',
  data() {
    return {
      visible: false,
      loading: false,
      logs: [],
      pageSize: 100, // 固定每页显示100条
      currentPage: 1, // 当前页数
      total: 0, // 总数据量
      selectedLogLevel: null, // 筛选的日志级别
    };
  },
  computed: {
    ...mapState(['nodes', 'selectedNodeIp', 'currentProject']),
    
    // 当前节点信息显示
    currentNodeInfo() {
      if (!this.selectedNodeIp) {
        return this.$t('log.noNodeSelected');
      }
      const currentNode = this.nodes.find(node => node.ip === this.selectedNodeIp);
      return currentNode ? `${currentNode.ip}` : this.selectedNodeIp;
    },
    
    // 筛选后的日志列表（分页已在后端处理，这里只处理级别筛选）
    filteredLogs() {
      if (!this.selectedLogLevel) {
        return this.logs;
      }
      return this.logs.filter(log => log.log_level === this.selectedLogLevel);
    },
    
    // 可用的日志级别选项
    logLevelOptions() {
      const levels = new Set(this.logs.map(log => log.log_level).filter(Boolean));
      return Array.from(levels).sort();
    }
  },
  methods: {
    showLogModal() {
      this.visible = true;
      // 重置分页状态
      this.currentPage = 1;
      this.selectedLogLevel = null;
      if (this.selectedNodeIp) {
        this.fetchLogs();
      }
    },
    
    handleCancel() {
      this.visible = false;
    },
    
    async fetchLogs() {
      if (!this.selectedNodeIp) {
        this.$message.warning(this.$t('log.noNodeSelected'));
        return;
      }
      
      if (!this.currentProject) {
        this.$message.warning(this.$t('common.selectProjectFirst'));
        return;
      }
      
      this.loading = true;
      try {
        const response = await axios.get(`/api/agent_log/${this.selectedNodeIp}`, {
          params: {
            page: this.currentPage,
            page_size: this.pageSize,
            dbFile: this.currentProject
          }
        });
        
        // 假设后端返回分页数据格式: { data: [], total: number }
        if (response.data && typeof response.data === 'object' && response.data.data) {
          this.logs = response.data.data || [];
          this.total = response.data.total || 0;
        } else {
          // 兼容旧格式，如果后端还没改为分页格式
          this.logs = (response.data || []).sort((a, b) => {
            const timeA = new Date(a.timestamp).getTime();
            const timeB = new Date(b.timestamp).getTime();
            return timeA - timeB;
          });
          this.total = this.logs.length;
        }
        
        // 重置级别筛选
        this.selectedLogLevel = null;
        
        // 滚动到顶部显示当前页日志
        this.$nextTick(() => {
          const logContent = this.$refs.logContent;
          if (logContent) {
            logContent.scrollTop = 0;
          }
        });
        
      } catch (error) {
        console.error('Failed to fetch logs:', error);
        this.$message.error(this.$t('log.fetchError'));
        this.logs = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },
    
    // 处理页码变化
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchLogs();
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    
    getLogLevelClass(level) {
      if (!level) return '';
      return `log-level-${level.toLowerCase()}`;
    }
  }
};
</script>

<style scoped>
.log-button {
  display: inline-block;
  cursor: pointer;
  padding: 4px;
}

.log-modal-content {
  height: 900px;
  display: flex;
  flex-direction: column;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color, #f0f0f0);
  background: var(--modal-bg, #fafafa);
}

.log-controls-left {
  display: flex;
  align-items: center;
}

.current-node-info {
  font-weight: 500;
  color: var(--text-color, #333);
  margin-right: 16px;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 16px 0 16px;
  background: var(--modal-bg, #fff);
}



.no-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.log-list {
  font-family: monospace;
  font-size: 13px;
  line-height: 2.0;
}

.log-item {
  padding: 4px 0;
  color: var(--text-color, #333);
  border-bottom: 1px solid var(--border-color, transparent);
}

.log-item:hover {
  background-color: var(--hover-bg, rgba(0,0,0,0.02));
}

.log-header {
  display: inline;
}

.log-time {
  color: var(--input-text, #666);
  margin-right: 8px;
}

.log-level {
  margin-right: 8px;
  font-weight: bold;
}

.log-module {
  color: var(--input-text, #666);
  margin-right: 8px;
}

/* 简化的日志级别颜色 */
.level-info {
  color: #1890ff;
}

.level-warn {
  color: #fa8c16;
}

.level-error {
  color: #f5222d;
}

.level-debug {
  color: #52c41a;
}

.level-warning {
  color: #fa8c16;
}

.level-critical {
  color: #ff4d4f;
}

.log-message {
  display: inline;
  word-break: break-word;
}
</style> 