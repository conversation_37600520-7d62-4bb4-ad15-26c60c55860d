(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b5ec4b6e"],{"03fa":function(e,t,o){},"13d5":function(e,t,o){"use strict";var s=o("23e7"),a=o("d58f").left,r=o("a640"),l=o("2d00"),n=o("605d"),i=r("reduce"),c=!n&&l>79&&l<83;s({target:"Array",proto:!0,forced:!i||c},{reduce:function(e){return a(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}})},"4f4d":function(e,t,o){"use strict";o("0643"),o("2382");t["a"]={methods:{addTaskCompletionNotification({taskId:e,taskType:t,nodes:o,projectId:s,titles:a={},templates:r={},statusMapping:l={}}){const n=`${t}Notified_${s}_${e}`;if(localStorage.getItem(n))return;const i={success:["success","completed"],failure:["failed"]},c={success:[...l.success||[],...i.success],failure:[...l.failure||[],...i.failure]},d=o.filter(e=>c.success.includes(e.status)&&!e.error_detail).length,u=o.filter(e=>c.failure.includes(e.status)||e.error_detail).length,h=u>0;let p,f;p=h?a.error||this.getDefaultErrorTitle(t):a.success||this.getDefaultSuccessTitle(t),f=h?r.error||`${d} nodes completed successfully, ${u} nodes failed.`:r.success||`All ${o.length} nodes completed successfully.`,this.addNotification({title:p,message:f,type:h?"error":"success",taskId:e}),localStorage.setItem(n,"true")},getDefaultSuccessTitle(e){const t={task:"Task Completed",upload:"File Upload Completed",download:"File Download Completed",tool:"Tool Execution Completed"};return t[e]||"Operation Completed"},getDefaultErrorTitle(e){const t={task:"Task Completed with Errors",upload:"File Upload Completed with Errors",download:"File Download Completed with Errors",tool:"Tool Execution Completed with Errors"};return t[e]||"Operation Completed with Errors"},clearTaskNotificationMark(e,t,o){const s=`${t}Notified_${o}_${e}`;localStorage.removeItem(s)}}}},"5a3b":function(e,t,o){"use strict";var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"proxy-selector"},[t("a-button",{staticClass:"nav-style-button",attrs:{loading:e.isDetecting,disabled:e.disabled},on:{click:e.fetchReachableIps}},[e._v(" "+e._s(e.$t("common.detectReachableIps")||"检测可达IP")+" ")]),e.reachableIps.length?t("a-select",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{placeholder:e.$t("tool.selectReachableIp")||"选择可达IP",disabled:e.disabled},model:{value:e.selectedIpValue,callback:function(t){e.selectedIpValue=t},expression:"selectedIpValue"}},e._l(e.reachableIps,(function(o){return t("a-select-option",{key:o,attrs:{value:o}},[e._v(" "+e._s(o)+" ")])})),1):e._e()],1)},a=[],r=o("fec3"),l={name:"ProxySelector",props:{disabled:{type:Boolean,default:!1},value:{type:String,default:null}},data(){return{isDetecting:!1,reachableIps:[],selectedIpValue:this.value}},computed:{},watch:{value(e){this.selectedIpValue=e},selectedIpValue(e){this.$emit("input",e),this.$emit("change",e)}},methods:{async fetchReachableIps(){this.isDetecting=!0;try{const e=await r["a"].get("/api/proxy/detect");this.reachableIps=e.data.reachable_ips,this.reachableIps.length&&(this.selectedIpValue=this.reachableIps[0])}catch(e){console.error("Error detecting reachable IPs:",e),this.$notify.error({title:"Error",message:"Failed to detect reachable IPs"})}finally{this.isDetecting=!1}}}},n=l,i=o("2877"),c=Object(i["a"])(n,s,a,!1,null,"46f0b65a",null);t["a"]=c.exports},"605d":function(e,t,o){var s=o("c6b6"),a=o("da84");e.exports="process"==s(a.process)},"76d6":function(e,t,o){"use strict";var s=o("23e7"),a=o("2266"),r=o("1c0b"),l=o("825a");s({target:"Iterator",proto:!0,real:!0},{every:function(e){return l(this),r(e),!a(this,(function(t,o){if(!e(t))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9a9a":function(e,t,o){"use strict";var s=o("23e7"),a=o("2266"),r=o("1c0b"),l=o("825a");s({target:"Iterator",proto:!0,real:!0},{some:function(e){return l(this),r(e),a(this,(function(t,o){if(e(t))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},"9d4a":function(e,t,o){"use strict";var s=o("23e7"),a=o("2266"),r=o("1c0b"),l=o("825a");s({target:"Iterator",proto:!0,real:!0},{reduce:function(e){l(this),r(e);var t=arguments.length<2,o=t?void 0:arguments[1];if(a(this,(function(s){t?(t=!1,o=s):o=e(o,s)}),{IS_ITERATOR:!0}),t)throw TypeError("Reduce of empty iterator with no initial value");return o}})},a640:function(e,t,o){"use strict";var s=o("d039");e.exports=function(e,t){var o=[][e];return!!o&&s((function(){o.call(null,t||function(){throw 1},1)}))}},d58f:function(e,t,o){var s=o("1c0b"),a=o("7b0b"),r=o("44ad"),l=o("50c4"),n=function(e){return function(t,o,n,i){s(o);var c=a(t),d=r(c),u=l(c.length),h=e?u-1:0,p=e?-1:1;if(n<2)while(1){if(h in d){i=d[h],h+=p;break}if(h+=p,e?h<0:u<=h)throw TypeError("Reduce of empty array with no initial value")}for(;e?h>=0:u>h;h+=p)h in d&&(i=o(i,d[h],h,c));return i}};e.exports={left:n(!1),right:n(!0)}},dc7d:function(e,t,o){"use strict";o.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{padding:"2px"}},[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 640 512",height:"20",width:"20"}},[t("path",{attrs:{fill:"currentColor",d:"M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.fileDownload")))])]),t("a-button",{staticClass:"nav-style-button",attrs:{loading:e.downloading},on:{click:e.handleDownload}},[e._v(" "+e._s(e.$t("fileDownload.startDownload"))+" ")])],1),t("div",{staticClass:"main-content"},[t("div",{staticClass:"left-section"},[t("a-card",{staticClass:"compact-card",attrs:{size:"small",title:e.$t("common.configureProxy")}},[t("proxy-selector",{attrs:{disabled:e.downloading},on:{change:e.handleProxyChange},model:{value:e.selectedProxyIp,callback:function(t){e.selectedProxyIp=t},expression:"selectedProxyIp"}}),t("p",{staticStyle:{"margin-top":"16px",color:"gray","font-size":"12px"}},[e._v(" Default download path: \\infocollect\\cache\\download\\ ")])],1)],1),t("div",{staticClass:"right-section config-table"},[t("a-card",{staticClass:"compact-card",attrs:{size:"small",title:e.$t("common.configureNodes")}},[t("a-table",{staticClass:"bordered-nodes-table",attrs:{dataSource:e.nodesWithPath,columns:e.columns,rowKey:"ip",pagination:{pageSize:10,total:e.nodes.length,showSizeChanger:!1},rowSelection:e.rowSelection},scopedSlots:e._u([{key:"remotePath",fn:function(o,s){return[t("a-input",{attrs:{placeholder:e.$t("fileDownload.enterDownloadPath")},on:{change:t=>e.updateRemotePath(s.ip,t.target.value)},model:{value:s.remotePath,callback:function(t){e.$set(s,"remotePath",t)},expression:"record.remotePath"}})]}}])})],1)],1)]),t("a-card",{staticClass:"compact-card",staticStyle:{"margin-top":"16px"},attrs:{title:e.$t("fileDownload.downloadProgress")}},[t("template",{slot:"extra"},[t("span",[e._v("Overall Progress: "+e._s(e.downloadProgress)+"%")])]),t("a-progress",{staticStyle:{"margin-bottom":"16px"},attrs:{percent:e.downloadProgress,status:e.progressBarStatus}}),t("a-table",{attrs:{dataSource:e.downloadProgressTableData,columns:e.downloadProgressColumns,rowKey:"ip",pagination:!1},scopedSlots:e._u([{key:"errorDetail",fn:function(o,s){return[s&&s.error_detail?t("a-popover",{attrs:{placement:"topLeft"}},[t("template",{slot:"content"},[t("p",[e._v("Time: "+e._s(s.error_detail.time))]),t("p",[e._v("Type: "+e._s(s.error_detail.type))]),t("p",[e._v("Message: "+e._s(s.error_detail.message))])]),t("a-icon",{staticStyle:{color:"#ff4d4f"},attrs:{type:"info-circle"}})],2):e._e()]}}])})],2)],1)},a=[],r=(o("13d5"),o("0643"),o("76d6"),o("2382"),o("a573"),o("9d4a"),o("9a9a"),o("2f62")),l=o("fec3"),n=o("4f4d"),i=o("5a3b"),c={name:"FileDownload",mixins:[n["a"]],components:{ProxySelector:i["a"]},data(){return{selectedNodes:[],nodeRemotePaths:{},downloading:!1,selectedProxyIp:null,pollInterval:null}},computed:{...Object(r["e"])(["nodes","activeDownloadTask","currentProject","sidebarColor"]),activeTask:{get(){return this.activeDownloadTask},set(e){this.$store.dispatch("updateDownloadTask",e)}},columns(){return[{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name"},{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip"},{title:"Remote Path",dataIndex:"remotePath",scopedSlots:{customRender:"remotePath"}}]},nodesWithPath(){return this.nodes.map(e=>({...e,remotePath:this.nodeRemotePaths[e.ip]||""}))},rowSelection(){return{selectedRowKeys:this.selectedNodes,onChange:e=>{this.selectedNodes=e}}},downloadProgressColumns(){const e=this.$createElement;return[{title:this.$t("hostConfig.columns.ipAddress"),dataIndex:"ip",key:"ip",width:"120px"},{title:this.$t("hostConfig.columns.hostName"),dataIndex:"host_name",key:"host_name",width:"150px",ellipsis:!0},{title:this.$t("tool.columns.status"),dataIndex:"status",key:"status",width:"100px",customRender:t=>{const o={pending:"#1890ff",downloading:"#1890ff",completed:"#52c41a",failed:"#f5222d"}[t]||"#000";return e("span",{style:{color:o}},[t])}},{title:this.$t("tool.columns.progress"),dataIndex:"progress",key:"progress",width:"200px",customRender:t=>e("a-progress",{attrs:{percent:t||0,size:"small"}})},{title:this.$t("tool.columns.speed"),dataIndex:"speed",key:"speed",width:"100px"},{title:this.$t("tool.columns.fileSize"),dataIndex:"file_size",key:"file_size",width:"100px",customRender:e=>this.formatBytes(e)},{title:this.$t("tool.columns.errorDetails"),dataIndex:"error_detail",key:"error_detail",width:"60px",scopedSlots:{customRender:"errorDetail"}}]},downloadProgressTableData(){return this.activeDownloadTask&&this.activeDownloadTask.nodes?Object.keys(this.activeDownloadTask.nodes).map(e=>{const t=this.activeDownloadTask.nodes[e];return{ip:e,host_name:t.host_name,status:t.status,progress:t.progress||0,speed:t.speed||"-",error_detail:t.error_detail}}):[]},downloadProgress(){if(!this.activeDownloadTask||!this.activeDownloadTask.nodes)return 0;const e=Object.values(this.activeDownloadTask.nodes);if(0===e.length)return 0;const t=e.reduce((e,t)=>e+(t.progress||0),0);return Math.round(t/e.length)},progressBarStatus(){if(!this.activeDownloadTask)return"normal";const e=Object.values(this.activeDownloadTask.nodes);return e.some(e=>"failed"===e.status)?"exception":e.every(e=>"completed"===e.status)?"success":"active"}},created(){if(!this.checkDatabaseStatus())return;this.$store.dispatch("fetchNodes");const e=localStorage.getItem("downloadTask_"+this.currentProject);if(e){const{projectFile:t}=JSON.parse(e);t===this.currentProject?this.checkActiveDownloadTask():(localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject),this.$store.dispatch("updateDownloadTask",null))}},methods:{...Object(r["b"])(["addNotification"]),checkDatabaseStatus(){return!!this.currentProject||(this.$notify.error({title:"Database Error",message:"No project database selected. Please select a project first."}),this.$router.push("/projects"),!1)},handleProxyChange(e){console.log("Proxy IP changed:",e),this.selectedProxyIp=e},validatePath(e){return e.startsWith("/")?e.includes("..")||e.includes("./")||e.includes("~")?"Path cannot contain ./, ../ or ~":null:"Path must be absolute (start with /)"},updateRemotePath(e,t){const o=this.validatePath(t);if(o)return this.$message.error(o),void this.$set(this.nodeRemotePaths,e,"");this.$set(this.nodeRemotePaths,e,t)},validatePaths(){const e=this.selectedNodes.map(e=>({ip:e,path:this.nodeRemotePaths[e]||""})).filter(e=>e.path);if(!e.length)return this.$message.error("Please enter remote paths for selected nodes"),null;for(const{ip:t,path:o}of e){const e=this.validatePath(o);if(e)return this.$message.error(`Invalid path for ${t}: ${e}`),null}return e},formatBytes(e){if(!e)return"0 B";const t=1024,o=["B","KB","MB","GB"],s=Math.floor(Math.log(e)/Math.log(t));return`${parseFloat((e/Math.pow(t,s)).toFixed(2))} ${o[s]}`},async handleDownload(){if(!this.selectedNodes.length)return void this.$message.warning("Please select at least one node");if(!this.selectedProxyIp)return void this.$message.warning("Please select a proxy IP");const e=this.selectedNodes.map(e=>({ip:e,path:this.nodeRemotePaths[e]||""}));try{this.downloading=!0;const t=localStorage.getItem("downloadTask_"+this.currentProject);if(t)try{const{taskId:e}=JSON.parse(t);e&&this.clearTaskNotificationMark(e,"download",this.currentProject)}catch(o){console.error("Error clearing previous download notification:",o)}const s=await l["a"].post("/api/file_transfer/download/start?dbFile="+encodeURIComponent(this.currentProject),{nodes:e,proxyIp:this.selectedProxyIp}),a=s.data.task_id;localStorage.setItem("downloadTask_"+this.currentProject,JSON.stringify({taskId:a,projectFile:this.currentProject})),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject),this.startPolling(a)}catch(s){var t;console.error("Download error:",s),this.downloading=!1,this.$message.error((null===(t=s.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.error)||"Failed to start download")}},async startPolling(e){this.pollInterval&&clearInterval(this.pollInterval);const t=async()=>{try{if(!this.currentProject)throw new Error("No project database selected");const t=await l["a"].get(`/api/file_transfer/download/status/${e}?dbFile=${encodeURIComponent(this.currentProject)}`);this.$store.dispatch("updateDownloadTask",t.data);const o=Object.values(t.data.nodes).every(e=>["completed","failed"].includes(e.status));if(o){clearInterval(this.pollInterval),this.downloading=!1,localStorage.setItem("downloadTaskCompleted_"+this.currentProject,"true");const o=Object.values(t.data.nodes);this.addTaskCompletionNotification({taskId:e,taskType:"download",nodes:o,projectId:this.currentProject,templates:{success:`File downloaded successfully to all ${o.length} nodes.`,error:`${o.filter(e=>"completed"===e.status).length} nodes completed file download successfully, ${o.filter(e=>"failed"===e.status).length} nodes failed.`},statusMapping:{success:["completed"],failure:["failed"]}})}}catch(o){var t;console.error("Error polling status:",o),404===(null===(t=o.response)||void 0===t?void 0:t.status)&&(clearInterval(this.pollInterval),this.downloading=!1,localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject))}};await t(),this.pollInterval=setInterval(t,1e4)},async checkActiveDownloadTask(){try{const e=localStorage.getItem("downloadTask_"+this.currentProject),t=localStorage.getItem("downloadTaskCompleted_"+this.currentProject);if(e){const{taskId:o,projectFile:s}=JSON.parse(e);if(s!==this.currentProject)throw new Error("Task belongs to different project");if(!this.currentProject)throw new Error("No project database selected");const a=await l["a"].get(`/api/file_transfer/download/status/${o}?dbFile=${encodeURIComponent(this.currentProject)}`);if(a.data){this.$store.dispatch("updateDownloadTask",a.data);const e=Object.values(a.data.nodes).every(e=>["completed","failed"].includes(e.status));if(e||t){if(e){this.downloading=!1,localStorage.setItem("downloadTaskCompleted_"+this.currentProject,"true");const e=Object.values(a.data.nodes);this.addTaskCompletionNotification({taskId:o,taskType:"download",nodes:e,projectId:this.currentProject,templates:{success:`File downloaded successfully to all ${e.length} nodes.`,error:`${e.filter(e=>"completed"===e.status).length} nodes completed file download successfully, ${e.filter(e=>"failed"===e.status).length} nodes failed.`},statusMapping:{success:["completed"],failure:["failed"]}})}}else this.downloading=!0,this.startPolling(o)}}}catch(e){console.error("Error checking active download task:",e),localStorage.removeItem("downloadTask_"+this.currentProject),localStorage.removeItem("downloadTaskCompleted_"+this.currentProject)}},beforeDestroy(){this.pollInterval&&clearInterval(this.pollInterval),this.$store.dispatch("updateDownloadTask",null)},watch:{currentProject:{handler(e,t){e!==t&&(this.$store.dispatch("updateDownloadTask",null),this.pollInterval&&clearInterval(this.pollInterval),this.checkActiveDownloadTask())},immediate:!0}}}},d=c,u=(o("e099"),o("2877")),h=Object(u["a"])(d,s,a,!1,null,"c8f24356",null);t["default"]=h.exports},e099:function(e,t,o){"use strict";o("03fa")}}]);
//# sourceMappingURL=chunk-b5ec4b6e.1df9223a.js.map