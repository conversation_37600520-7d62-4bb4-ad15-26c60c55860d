{"version": 3, "sources": ["webpack:///./src/components/Cards/FilesystemInfo.vue?8477", "webpack:///./src/views/Filesystem.vue", "webpack:///./src/components/Cards/FilesystemInfo.vue", "webpack:///src/components/Cards/FilesystemInfo.vue", "webpack:///./src/components/Cards/FilesystemInfo.vue?b323", "webpack:///./src/components/Cards/FilesystemInfo.vue?323d", "webpack:///src/views/Filesystem.vue", "webpack:///./src/views/Filesystem.vue?b9c5", "webpack:///./src/views/Filesystem.vue?3794", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "fetchFilesystem", "proxy", "columns", "filesystemItems", "record", "device", "pagination", "loading", "components", "RefreshButton", "name", "data", "title", "dataIndex", "width", "ellipsis", "pageSize", "computed", "mapState", "watch", "selectedNodeIp", "newIp", "mounted", "methods", "console", "error", "response", "axios", "get", "params", "dbFile", "currentProject", "map", "item", "index", "mount_point", "component", "FilesystemInfo", "$event", "$emit", "text", "props", "type", "String", "default"], "mappings": "yIAAA,W,2CCAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,mBAAmB,IAAI,IAAI,IAE3MI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,sCAAsCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,IAAKC,YAAYR,EAAIS,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACT,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACU,MAAM,QAAQZ,EAAIa,aAAeT,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,YAAY,UAAU,EAAI,0SAA0S,YAAY,iBAAiBF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIc,GAAGd,EAAIe,GAAGf,EAAIgB,GAAG,yBAAyBd,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACe,GAAG,CAAC,QAAUjB,EAAIkB,oBAAoB,OAAOC,OAAM,MAAS,CAACjB,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIoB,QAAQ,WAAapB,EAAIqB,gBAAgB,OAAUC,GAAWA,EAAOZ,KAAOY,EAAOC,OAAO,WAAavB,EAAIwB,WAAW,QAAUxB,EAAIyB,YAAY,IAEluCnB,EAAkB,G,0DCqCP,GACfoB,WAAA,CACAC,sBAEAC,KAAA,iBACAC,OACA,OACAR,gBAAA,GACAI,SAAA,EACAL,QAAA,CACA,CACAU,MAAA,SACAC,UAAA,SACArB,IAAA,SACAsB,MAAA,MACAC,UAAA,GAEA,CACAH,MAAA,cACAC,UAAA,cACArB,IAAA,cACAsB,MAAA,MACAC,UAAA,GAEA,CACAH,MAAA,mBACAC,UAAA,UACArB,IAAA,UACAsB,MAAA,MACAC,UAAA,GAEA,CACAH,MAAA,gBACAC,UAAA,gBACArB,IAAA,gBACAsB,MAAA,MACAC,UAAA,IAGAT,WAAA,CACAU,SAAA,OAIAC,SAAA,IACAC,eAAA,qDAEAC,MAAA,CACAC,eAAAC,GACA,KAAArB,oBAGAsB,UACA,KAAAtB,mBAEAuB,QAAA,CACA,wBACA,SAAAH,eAGA,OAFAI,QAAAC,MAAA,+BACA,KAAAtB,gBAAA,IAGA,IAEA,KAAAI,SAAA,EACA,MAAAmB,QAAAC,OAAAC,IAAA,wBAAAR,eAAA,CACAS,OAAA,CACAC,OAAA,KAAAC,kBAKA,KAAA5B,gBAAAuB,EAAAf,KAAAqB,IAAA,CAAAC,EAAAC,KAAA,IACAD,EAEAzC,IAAA,GAAAyC,EAAA5B,UAAA4B,EAAAE,eAAAD,OAEA,MAAAT,GACAD,QAAAC,MAAA,6BAAAA,GACA,KAAAtB,gBAAA,GACA,QAEA,KAAAI,SAAA,MCzHsW,I,wBCQlW6B,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACf5B,WAAA,CACA6B,mBCjBmV,ICO/U,EAAY,eACd,EACAxD,EACAO,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACU,MAAM,CAAC,iBAAkB,QAAQZ,EAAIa,cAAgBT,MAAM,CAAC,KAAO,UAAUa,GAAG,CAAC,MAAQ,SAASuC,GAAQ,OAAOxD,EAAIyD,MAAM,cAAc,CAACzD,EAAIc,GAAG,IAAId,EAAIe,GAAGf,EAAI0D,MAAQ1D,EAAIgB,GAAG,mBAAmB,QAEhRV,EAAkB,G,YCWP,GACf6B,SAAA,IACAC,eAAA,mBAEAR,KAAA,gBACA+B,MAAA,CACAD,KAAA,CACAE,KAAAC,OACAC,QAAA,MCrBqW,I,YCOjWR,EAAY,eACd,EACAvD,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAAgD,E", "file": "static/js/chunk-7155ac4e.c5071787.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FilesystemInfo.vue?vue&type=style&index=0&id=659b42ba&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('FilesystemInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full filesystem-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 16 16\"}},[_c('path',{attrs:{\"fill\":'currentColor',\"fill-rule\":\"evenodd\",\"d\":\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\",\"clip-rule\":\"evenodd\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.mount')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchFilesystem}})],1)])]},proxy:true}])},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"dataSource\":_vm.filesystemItems,\"rowKey\":(record) => record.key || record.device,\"pagination\":_vm.pagination,\"loading\":_vm.loading}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full filesystem-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n              <path :fill=\"'currentColor'\" fill-rule=\"evenodd\" d=\"m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z\" clip-rule=\"evenodd\"/>\r\n            </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.mount') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchFilesystem\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :dataSource=\"filesystemItems\"\r\n      :rowKey=\"(record) => record.key || record.device\"\r\n      :pagination=\"pagination\"\r\n      :loading=\"loading\"\r\n    >\r\n    </a-table>\r\n  </a-card>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  name: 'FilesystemInfo',\r\n  data() {\r\n    return {\r\n      filesystemItems: [],\r\n      loading: false,\r\n      columns: [\r\n        {\r\n          title: 'Device',\r\n          dataIndex: 'device',\r\n          key: 'device',\r\n          width: '20%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Point',\r\n          dataIndex: 'mount_point',\r\n          key: 'mount_point',\r\n          width: '25%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'File System Type',\r\n          dataIndex: 'fs_type',\r\n          key: 'fs_type',\r\n          width: '15%',\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: 'Mount Options',\r\n          dataIndex: 'mount_options',\r\n          key: 'mount_options',\r\n          width: '40%',\r\n          ellipsis: true,\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      this.fetchFilesystem();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchFilesystem();\r\n  },\r\n  methods: {\r\n    async fetchFilesystem() {\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        this.filesystemItems = [];\r\n        return;\r\n      }\r\n      try {\r\n        // 显示加载状态\r\n        this.loading = true;\r\n        const response = await axios.get(`/api/filesystem/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject\r\n          }\r\n        });\r\n\r\n        // 处理数据，确保每条记录有唯一的key\r\n        this.filesystemItems = response.data.map((item, index) => ({\r\n          ...item,\r\n          // 使用组合键作为唯一标识，防止device重复导致的渲染问题\r\n          key: `${item.device}_${item.mount_point}_${index}`\r\n        }));\r\n      } catch (error) {\r\n        console.error('Error fetching filesystem:', error);\r\n        this.filesystemItems = [];\r\n      } finally {\r\n        // 无论成功失败，都关闭加载状态\r\n        this.loading = false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.filesystem-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n  position: relative; /* 确保卡片有相对定位，防止内部元素溢出 */\r\n  z-index: 1; /* 设置合适的z-index，防止与其他元素冲突 */\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px; /* 匹配卡片圆角 */\r\n}\r\n\r\n/* 表头样式 */\r\n.ant-table-thead > tr > th {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #f0f0f0; /* 浅色分割线 */\r\n  color: #666;\r\n}\r\n\r\n/* 悬停效果 */\r\n.ant-table-tbody > tr:hover > td {\r\n  background-color: #fafafa !important;\r\n}\r\n\r\n/* 优化表格滚动行为 */\r\n::v-deep .ant-table-body {\r\n  overflow-x: auto !important;\r\n}\r\n\r\n/* 确保表格内容不会溢出 */\r\n::v-deep .ant-table-row td {\r\n  word-break: break-word;\r\n  white-space: normal;\r\n}\r\n\r\n/* 优化表格在移动设备上的显示 */\r\n@media (max-width: 768px) {\r\n  ::v-deep .ant-pagination {\r\n    font-size: 12px;\r\n  }\r\n\r\n  ::v-deep .ant-pagination-options {\r\n    margin-left: 8px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FilesystemInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FilesystemInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./FilesystemInfo.vue?vue&type=template&id=659b42ba&scoped=true\"\nimport script from \"./FilesystemInfo.vue?vue&type=script&lang=js\"\nexport * from \"./FilesystemInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./FilesystemInfo.vue?vue&type=style&index=0&id=659b42ba&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"659b42ba\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<FilesystemInfo></FilesystemInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport FilesystemInfo from \"@/components/Cards/FilesystemInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        FilesystemInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Filesystem.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Filesystem.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Filesystem.vue?vue&type=template&id=78999d15\"\nimport script from \"./Filesystem.vue?vue&type=script&lang=js\"\nexport * from \"./Filesystem.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}