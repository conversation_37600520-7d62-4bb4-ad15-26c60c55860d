{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Cards\\TestCaseInfo.vue", "mtime": 1751879158768}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TestCaseInfo.vue"], "names": [], "mappings": ";AA+GA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TestCaseInfo.vue", "sourceRoot": "src/components/Cards", "sourcesContent": ["<template>\r\n  <div class=\"layout-content\">\r\n    <a-card :bordered=\"false\" class=\"criclebox\">\r\n      <template #title>\r\n        <div class=\"card-header-wrapper\">\r\n          <div class=\"header-wrapper\">\r\n            <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" height=\"20\" width=\"20\" :class=\"`text-${sidebarColor}`\">\r\n                <path :fill=\"'currentColor'\" d=\"M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z\"/>\r\n              </svg>\r\n            </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.testcase') }}</h6>\r\n          </div>\r\n          <div>\r\n            <RefreshButton @refresh=\"fetchTestcases(currentPage)\" />\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <a-form layout=\"inline\" @submit.prevent=\"handleSearch\">\r\n          <a-form-item label=\"Name\">\r\n            <a-input v-model=\"searchForm.name\" placeholder=\"Search by name\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Level\">\r\n            <a-select v-model=\"searchForm.level\" placeholder=\"Select level\" style=\"width: 120px\" allowClear>\r\n              <a-select-option value=\"level 0\">Level 0</a-select-option>\r\n              <a-select-option value=\"level 1\">Level 1</a-select-option>\r\n              <a-select-option value=\"level 2\">Level 2</a-select-option>\r\n              <a-select-option value=\"level 3\">Level 3</a-select-option>\r\n              <a-select-option value=\"level 4\">Level 4</a-select-option>\r\n            </a-select>\r\n          </a-form-item>\r\n          <a-form-item label=\"Prepare Condition\">\r\n            <a-input v-model=\"searchForm.prepare_condition\" placeholder=\"Search in prepare condition\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Test Steps\">\r\n            <a-input v-model=\"searchForm.test_steps\" placeholder=\"Search in test steps\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item label=\"Expected Result\">\r\n            <a-input v-model=\"searchForm.expected_result\" placeholder=\"Search in expected result\" allowClear />\r\n          </a-form-item>\r\n          <a-form-item>\r\n            <a-button html-type=\"submit\" :class=\"`bg-${sidebarColor}`\" style=\"color: white\" :loading=\"loading\">\r\n              <a-icon type=\"search\" />\r\n              Search\r\n            </a-button>\r\n            <a-button style=\"margin-left: 8px\" @click=\"resetSearch\">\r\n              <a-icon type=\"reload\" />\r\n              Reset\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n        <div class=\"search-result-count\" v-if=\"testcases.length > 0\">\r\n          <a-tag color=\"blue\">Found: {{ total }} test cases</a-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Table -->\r\n      <a-table\r\n        :columns=\"columns\"\r\n        :data-source=\"testcases\"\r\n        :loading=\"loading\"\r\n        :pagination=\"{\r\n          total: total,\r\n          pageSize: 100,\r\n          current: currentPage,\r\n          showSizeChanger: false,\r\n          showQuickJumper: true,\r\n          onChange: handlePageChange\r\n        }\"\r\n        :scroll=\"{ x: 1500 }\"\r\n      >\r\n        <!-- Custom column renders -->\r\n        <template #Testcase_LastResult=\"{ text }\">\r\n          <a-tag :color=\"getResultColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #Testcase_Level=\"{ text }\">\r\n          <a-tag :color=\"getLevelColor(text)\">\r\n            {{ text || 'N/A' }}\r\n          </a-tag>\r\n        </template>\r\n\r\n        <template #lastModified=\"{ text }\">\r\n          {{ formatDate(text) }}\r\n        </template>\r\n\r\n        <template #action=\"{ record }\">\r\n          <a-space>\r\n            <a-button type=\"link\" @click=\"viewDetails(record)\">\r\n              View Details\r\n            </a-button>\r\n          </a-space>\r\n        </template>\r\n      </a-table>\r\n\r\n      <!-- Details Modal -->\r\n      <TestCaseDetailModal\r\n        :visible=\"detailsVisible\"\r\n        :testcase=\"selectedTestcase\"\r\n        @close=\"detailsVisible = false\"\r\n      />\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from '@/api/axiosInstance';\r\nimport moment from 'moment';\r\nimport {mapState} from \"vuex\";\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\nimport TestCaseDetailModal from '../Widgets/TestCaseDetailModal.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton,\r\n    TestCaseDetailModal\r\n  },\r\n  name: 'TestCases',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      testcases: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      detailsVisible: false,\r\n      selectedTestcase: null,\r\n      searchForm: {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      },\r\n      columns: [\r\n        {\r\n          title: '#',\r\n          dataIndex: 'index',\r\n          key: 'index',\r\n          width: 100,\r\n          align: 'center',\r\n          customRender: (text, record, index) => {\r\n            return ((this.currentPage - 1) * 100) + index + 1;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.number'),\r\n          dataIndex: 'Testcase_Number',\r\n          key: 'Testcase_Number',\r\n          width: 130,\r\n          ellipsis: true,\r\n          customRender: (text, record) => {\r\n            return <a onClick={() => this.viewDetails(record)} style=\"color: #1890ff; cursor: pointer;\">{text}</a>;\r\n          }\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.name'),\r\n          dataIndex: 'Testcase_Name',\r\n          key: 'Testcase_Name',\r\n          width: 200,\r\n          // ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.level'),\r\n          dataIndex: 'Testcase_Level',\r\n          key: 'Testcase_Level',\r\n          width: 100,\r\n          slots: { customRender: 'Testcase_Level' },\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.prepareCondition'),\r\n          dataIndex: 'Testcase_PrepareCondition',\r\n          key: 'Testcase_PrepareCondition',\r\n          width: 250,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.testSteps'),\r\n          dataIndex: 'Testcase_TestSteps',\r\n          key: 'Testcase_TestSteps',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        {\r\n          title: this.$t('testcase.columns.expectedResult'),\r\n          dataIndex: 'Testcase_ExpectedResult',\r\n          key: 'Testcase_ExpectedResult',\r\n          width: 400,\r\n          ellipsis: true,\r\n        },\r\n        // {\r\n        //   title: 'Action',\r\n        //   key: 'action',\r\n        //   fixed: 'right',\r\n        //   width: 120,\r\n        //   slots: { customRender: 'action' },\r\n        // },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchTestcases();\r\n  },\r\n    computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  methods: {\r\n    async fetchTestcases(page = 1) {\r\n      this.loading = true;\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: page,\r\n          page_size: 100\r\n        };\r\n\r\n        // 添加搜索参数\r\n        if (this.searchForm.name) params.name = this.searchForm.name;\r\n        if (this.searchForm.level) params.level = this.searchForm.level;\r\n        if (this.searchForm.prepare_condition) params.prepare_condition = this.searchForm.prepare_condition;\r\n        if (this.searchForm.test_steps) params.test_steps = this.searchForm.test_steps;\r\n        if (this.searchForm.expected_result) params.expected_result = this.searchForm.expected_result;\r\n\r\n        const response = await axios.get('/api/testcase/', { params });\r\n        this.testcases = response.data.data;\r\n        this.total = response.data.total;\r\n      } catch (error) {\r\n        console.error('Error fetching testcases:', error);\r\n        this.$message.error('Failed to load test cases');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理函数\r\n    handleSearch() {\r\n      this.currentPage = 1; // 重置到第一页\r\n      this.fetchTestcases(1);\r\n    },\r\n\r\n    // 重置搜索表单\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        name: '',\r\n        level: undefined,\r\n        prepare_condition: '',\r\n        test_steps: '',\r\n        expected_result: ''\r\n      };\r\n      this.currentPage = 1;\r\n      this.fetchTestcases(1);\r\n    },\r\n    formatDate(date) {\r\n      return date ? moment(date).format('YYYY-MM-DD HH:mm') : 'N/A';\r\n    },\r\n    getResultColor(result) {\r\n      const colors = {\r\n        'PASS': 'success',\r\n        'FAIL': 'error',\r\n        'BLOCKED': 'warning',\r\n        'NOT RUN': 'default',\r\n      };\r\n      return colors[result] || 'default';\r\n    },\r\n    getLevelColor(level) {\r\n      const colors = {\r\n        'level 0': 'red',\r\n        'level 1': 'orange',\r\n        'level 2': 'green',\r\n        'level 3': 'blue',\r\n        'level 4': 'purple',\r\n      };\r\n      return colors[level] || 'default';\r\n    },\r\n    viewDetails(record) {\r\n      this.selectedTestcase = record;\r\n      this.detailsVisible = true;\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      this.fetchTestcases(page);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.criclebox {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n  padding: 16px;\r\n  background-color: #fafafa;\r\n  border-radius: 8px;\r\n\r\n  .ant-form-item {\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .search-result-count {\r\n    margin-top: 1px;\r\n    padding: 0 1px;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n"]}]}