{"remainingRequest": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue?vue&type=template&id=17687e1a&scoped=true", "dependencies": [{"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\src\\components\\Widgets\\TestCaseDetailModal.vue", "mtime": 1751874855810}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\babel.config.js", "mtime": 1751014516614}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751014595607}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751014596705}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751014594539}, {"path": "D:\\_Projects_python\\Tools\\console-vue2\\muse-vue-ant-design-dashboard-main\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751014596151}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "visible", "title", "width", "footer", "on", "cancel", "handleClose", "testcase", "bordered", "label", "span", "staticClass", "_v", "_s", "Testcase_Number", "Testcase_Name", "color", "getLevelColor", "Testcase_Level", "similarity", "undefined", "percent", "Math", "round", "size", "getSimilarityColor", "staticStyle", "toFixed", "_e", "Testcase_PrepareCondition", "Testcase_TestSteps", "Testcase_ExpectedResult", "staticRenderFns", "_withStripped"], "sources": ["D:/_Projects_python/Tools/console-vue2/muse-vue-ant-design-dashboard-main/src/components/Widgets/TestCaseDetailModal.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"a-modal\",\n    {\n      attrs: {\n        visible: _vm.visible,\n        title: \"测试用例详情\",\n        width: \"800px\",\n        footer: null\n      },\n      on: { cancel: _vm.handleClose }\n    },\n    [\n      _vm.testcase\n        ? _c(\n            \"a-descriptions\",\n            { attrs: { bordered: \"\" } },\n            [\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"用例编号\", span: 3 } },\n                [\n                  _c(\"div\", { staticClass: \"testcase-content\" }, [\n                    _vm._v(\" \" + _vm._s(_vm.testcase.Testcase_Number) + \" \")\n                  ])\n                ]\n              ),\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"用例名称\", span: 3 } },\n                [\n                  _c(\"div\", { staticClass: \"testcase-content\" }, [\n                    _vm._v(\" \" + _vm._s(_vm.testcase.Testcase_Name) + \" \")\n                  ])\n                ]\n              ),\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"用例级别\", span: 3 } },\n                [\n                  _c(\n                    \"a-tag\",\n                    {\n                      attrs: {\n                        color: _vm.getLevelColor(_vm.testcase.Testcase_Level)\n                      }\n                    },\n                    [_vm._v(\" \" + _vm._s(_vm.testcase.Testcase_Level) + \" \")]\n                  )\n                ],\n                1\n              ),\n              _vm.testcase.similarity !== undefined\n                ? _c(\n                    \"a-descriptions-item\",\n                    { attrs: { label: \"相似度\", span: 3 } },\n                    [\n                      _c(\"a-progress\", {\n                        attrs: {\n                          percent: Math.round(_vm.testcase.similarity * 100),\n                          size: \"small\",\n                          \"stroke-color\": _vm.getSimilarityColor(\n                            _vm.testcase.similarity\n                          )\n                        }\n                      }),\n                      _c(\"span\", { staticStyle: { \"margin-left\": \"8px\" } }, [\n                        _vm._v(\n                          _vm._s((_vm.testcase.similarity * 100).toFixed(1)) +\n                            \"%\"\n                        )\n                      ])\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"准备条件\", span: 3 } },\n                [\n                  _c(\"div\", { staticClass: \"testcase-content\" }, [\n                    _vm._v(\n                      \" \" + _vm._s(_vm.testcase.Testcase_PrepareCondition) + \" \"\n                    )\n                  ])\n                ]\n              ),\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"测试步骤\", span: 3 } },\n                [\n                  _c(\"div\", { staticClass: \"testcase-content\" }, [\n                    _vm._v(\" \" + _vm._s(_vm.testcase.Testcase_TestSteps) + \" \")\n                  ])\n                ]\n              ),\n              _c(\n                \"a-descriptions-item\",\n                { attrs: { label: \"预期结果\", span: 3 } },\n                [\n                  _c(\"div\", { staticClass: \"testcase-content\" }, [\n                    _vm._v(\n                      \" \" + _vm._s(_vm.testcase.Testcase_ExpectedResult) + \" \"\n                    )\n                  ])\n                ]\n              )\n            ],\n            1\n          )\n        : _vm._e()\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,SAAS,EACT;IACEE,KAAK,EAAE;MACLC,OAAO,EAAEJ,GAAG,CAACI,OAAO;MACpBC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAET,GAAG,CAACU;IAAY;EAChC,CAAC,EACD,CACEV,GAAG,CAACW,QAAQ,GACRV,EAAE,CACA,gBAAgB,EAChB;IAAEE,KAAK,EAAE;MAAES,QAAQ,EAAE;IAAG;EAAE,CAAC,EAC3B,CACEX,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEc,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7Cf,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACO,eAAe,CAAC,GAAG,GAAG,CAAC,CACzD,CAAC,CAEN,CAAC,EACDjB,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEc,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7Cf,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACQ,aAAa,CAAC,GAAG,GAAG,CAAC,CACvD,CAAC,CAEN,CAAC,EACDlB,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CACA,OAAO,EACP;IACEE,KAAK,EAAE;MACLiB,KAAK,EAAEpB,GAAG,CAACqB,aAAa,CAACrB,GAAG,CAACW,QAAQ,CAACW,cAAc;IACtD;EACF,CAAC,EACD,CAACtB,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACW,cAAc,CAAC,GAAG,GAAG,CAAC,CAC1D,CAAC,CACF,EACD,CACF,CAAC,EACDtB,GAAG,CAACW,QAAQ,CAACY,UAAU,KAAKC,SAAS,GACjCvB,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACpC,CACEb,EAAE,CAAC,YAAY,EAAE;IACfE,KAAK,EAAE;MACLsB,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAC3B,GAAG,CAACW,QAAQ,CAACY,UAAU,GAAG,GAAG,CAAC;MAClDK,IAAI,EAAE,OAAO;MACb,cAAc,EAAE5B,GAAG,CAAC6B,kBAAkB,CACpC7B,GAAG,CAACW,QAAQ,CAACY,UACf;IACF;EACF,CAAC,CAAC,EACFtB,EAAE,CAAC,MAAM,EAAE;IAAE6B,WAAW,EAAE;MAAE,aAAa,EAAE;IAAM;EAAE,CAAC,EAAE,CACpD9B,GAAG,CAACgB,EAAE,CACJhB,GAAG,CAACiB,EAAE,CAAC,CAACjB,GAAG,CAACW,QAAQ,CAACY,UAAU,GAAG,GAAG,EAAEQ,OAAO,CAAC,CAAC,CAAC,CAAC,GAChD,GACJ,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,GACD/B,GAAG,CAACgC,EAAE,CAAC,CAAC,EACZ/B,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEc,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7Cf,GAAG,CAACgB,EAAE,CACJ,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACsB,yBAAyB,CAAC,GAAG,GACzD,CAAC,CACF,CAAC,CAEN,CAAC,EACDhC,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEc,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7Cf,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACuB,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAC5D,CAAC,CAEN,CAAC,EACDjC,EAAE,CACA,qBAAqB,EACrB;IAAEE,KAAK,EAAE;MAAEU,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAE;EAAE,CAAC,EACrC,CACEb,EAAE,CAAC,KAAK,EAAE;IAAEc,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7Cf,GAAG,CAACgB,EAAE,CACJ,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACjB,GAAG,CAACW,QAAQ,CAACwB,uBAAuB,CAAC,GAAG,GACvD,CAAC,CACF,CAAC,CAEN,CAAC,CACF,EACD,CACF,CAAC,GACDnC,GAAG,CAACgC,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxBrC,MAAM,CAACsC,aAAa,GAAG,IAAI;AAE3B,SAAStC,MAAM,EAAEqC,eAAe", "ignoreList": []}]}