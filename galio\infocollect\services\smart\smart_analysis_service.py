# from typing import List, Dict, Any, Optional
# import json
# from log.logger import log_debug, log_info, log_error, log_warning
# from intelligent_executor.vectordb.manager import VectorDBManager
# from app.ssh.client import SSHClient
# from db.init_db import get_db
# from datamodel.node_datamodel import Node
#
# class IntelligentTestCaseService:
#     """智能测试用例服务，根据收集的信息匹配并执行相关测试用例"""
#
#     def __init__(self):
#         self.vector_db_manager = VectorDBManager()
#
#     def analyze_collected_info_and_execute_testcases(self, node_id: int, info_type: str, collected_data: Dict[str, Any]) -> Dict[str, Any]:
#         """
#         分析收集的信息并执行相关测试用例
#
#         Args:
#             node_id: 节点ID
#             info_type: 信息类型 (process, package, hardware, filesystem, port, docker, kubernetes)
#             collected_data: 收集到的数据
#
#         Returns:
#             Dict: 分析和执行结果
#         """
#         try:
#             log_info(f"开始分析节点 {node_id} 的 {info_type} 信息并匹配测试用例")
#
#             # 1. 根据收集的信息生成查询文本
#             query_text = self._generate_query_from_collected_data(info_type, collected_data)
#
#             if not query_text:
#                 log_warning(f"无法从 {info_type} 数据生成查询文本")
#                 return {
#                     'status': 'warning',
#                     'message': '无法生成有效的查询文本',
#                     'matched_testcases': [],
#                     'execution_results': []
#                 }
#
#             # 2. 使用向量数据库搜索相关测试用例
#             matched_testcases = self._search_relevant_testcases(query_text, info_type)
#
#             if not matched_testcases:
#                 log_info(f"未找到与 {info_type} 相关的测试用例")
#                 return {
#                     'status': 'info',
#                     'message': f'未找到与 {info_type} 相关的测试用例',
#                     'query_text': query_text,
#                     'matched_testcases': [],
#                     'execution_results': []
#                 }
#
#             # 3. 获取节点信息
#             node = self._get_node_info(node_id)
#             if not node:
#                 log_error(f"未找到节点 {node_id}")
#                 return {
#                     'status': 'error',
#                     'message': f'未找到节点 {node_id}',
#                     'matched_testcases': matched_testcases,
#                     'execution_results': []
#                 }
#
#             # 4. 在节点上执行测试用例
#             execution_results = self._execute_testcases_on_node(node, matched_testcases, collected_data)
#
#             log_info(f"完成节点 {node_id} 的 {info_type} 信息分析，匹配到 {len(matched_testcases)} 个测试用例")
#
#             return {
#                 'status': 'success',
#                 'message': f'成功分析并执行了 {len(matched_testcases)} 个相关测试用例',
#                 'query_text': query_text,
#                 'info_type': info_type,
#                 'node_id': node_id,
#                 'matched_testcases': matched_testcases,
#                 'execution_results': execution_results
#             }
#
#         except Exception as e:
#             log_error(f"分析收集信息并执行测试用例时发生错误: {e}")
#             return {
#                 'status': 'error',
#                 'message': f'分析过程中发生错误: {str(e)}',
#                 'matched_testcases': [],
#                 'execution_results': []
#             }
#
#     def _generate_query_from_collected_data(self, info_type: str, collected_data: Dict[str, Any]) -> str:
#         """
#         根据收集的数据生成查询文本
#
#         Args:
#             info_type: 信息类型
#             collected_data: 收集到的数据
#
#         Returns:
#             str: 生成的查询文本
#         """
#         try:
#             if info_type == 'process':
#                 # 从进程信息中提取关键词
#                 processes = collected_data.get('processes', [])
#                 if processes:
#                     # 提取进程名称和描述作为查询关键词
#                     process_names = [p.get('name', '') for p in processes[:5]]  # 取前5个进程
#                     return f"进程 {' '.join(process_names)} 测试 监控 检查"
#
#             elif info_type == 'package':
#                 # 从安装包信息中提取关键词
#                 packages = collected_data.get('packages', [])
#                 if packages:
#                     package_names = [p.get('name', '') for p in packages[:5]]
#                     return f"软件包 {' '.join(package_names)} 安装 版本 测试"
#
#             elif info_type == 'hardware':
#                 # 从硬件信息中提取关键词
#                 cpu_info = collected_data.get('cpu', {})
#                 memory_info = collected_data.get('memory', {})
#                 query_parts = ['硬件', '性能', '测试']
#                 if cpu_info:
#                     query_parts.append('CPU')
#                 if memory_info:
#                     query_parts.append('内存')
#                 return ' '.join(query_parts)
#
#             elif info_type == 'filesystem':
#                 # 从文件系统信息中提取关键词
#                 filesystems = collected_data.get('filesystems', [])
#                 if filesystems:
#                     fs_types = [fs.get('fstype', '') for fs in filesystems[:3]]
#                     return f"文件系统 {' '.join(fs_types)} 挂载 磁盘 测试"
#
#             elif info_type == 'port':
#                 # 从端口信息中提取关键词
#                 ports = collected_data.get('ports', [])
#                 if ports:
#                     port_numbers = [str(p.get('port', '')) for p in ports[:5]]
#                     return f"端口 {' '.join(port_numbers)} 网络 连接 测试"
#
#             elif info_type == 'docker':
#                 # 从Docker信息中提取关键词
#                 containers = collected_data.get('containers', [])
#                 images = collected_data.get('images', [])
#                 query_parts = ['Docker', '容器', '测试']
#                 if containers:
#                     query_parts.append('容器运行')
#                 if images:
#                     query_parts.append('镜像')
#                 return ' '.join(query_parts)
#
#             elif info_type == 'kubernetes':
#                 # 从Kubernetes信息中提取关键词
#                 pods = collected_data.get('pods', [])
#                 services = collected_data.get('services', [])
#                 query_parts = ['Kubernetes', 'K8s', '集群', '测试']
#                 if pods:
#                     query_parts.append('Pod')
#                 if services:
#                     query_parts.append('Service')
#                 return ' '.join(query_parts)
#
#             # 默认情况
#             return f"{info_type} 测试 检查 验证"
#
#         except Exception as e:
#             log_error(f"生成查询文本时发生错误: {e}")
#             return f"{info_type} 测试"
#
#     def _search_relevant_testcases(self, query_text: str, info_type: str) -> List[Dict[str, Any]]:
#         """
#         搜索相关的测试用例
#
#         Args:
#             query_text: 查询文本
#             info_type: 信息类型
#
#         Returns:
#             List[Dict]: 匹配的测试用例列表
#         """
#         try:
#             # 先搜索用例名称
#             name_results = self.vector_db_manager.search_testcases(
#                 query_text, search_type="name", top_k=3
#             )
#
#             # 再搜索用例步骤
#             steps_results = self.vector_db_manager.search_testcases(
#                 query_text, search_type="steps", top_k=3
#             )
#
#             # 合并结果并去重
#             all_results = name_results + steps_results
#             unique_results = []
#             seen_numbers = set()
#
#             for result in all_results:
#                 testcase_number = result.get('Testcase_Number')
#                 if testcase_number and testcase_number not in seen_numbers:
#                     seen_numbers.add(testcase_number)
#                     unique_results.append(result)
#
#             # 限制返回数量
#             return unique_results[:5]
#
#         except Exception as e:
#             log_error(f"搜索测试用例时发生错误: {e}")
#             return []
#
#     def _get_node_info(self, node_id: int) -> Optional[Node]:
#         """
#         获取节点信息
#
#         Args:
#             node_id: 节点ID
#
#         Returns:
#             Node: 节点对象
#         """
#         try:
#             with get_db() as db:
#                 node = db.query(Node).filter(Node.id == node_id).first()
#                 return node
#         except Exception as e:
#             log_error(f"获取节点信息时发生错误: {e}")
#             return None
#
#     def _execute_testcases_on_node(self, node: Node, testcases: List[Dict[str, Any]], context_data: Dict[str, Any]) -> List[Dict[str, Any]]:
#         """
#         在节点上执行测试用例
#
#         Args:
#             node: 节点对象
#             testcases: 测试用例列表
#             context_data: 上下文数据
#
#         Returns:
#             List[Dict]: 执行结果列表
#         """
#         execution_results = []
#
#         try:
#             # 创建SSH连接
#             ssh_client = SSHClient(node)
#             ssh_client.connect()
#
#             if not ssh_client.is_connected():
#                 log_error(f"无法连接到节点 {node.ip}")
#                 return [{
#                     'status': 'error',
#                     'message': f'无法连接到节点 {node.ip}',
#                     'testcase_number': None
#                 }]
#
#             for testcase in testcases:
#                 try:
#                     result = self._execute_single_testcase(ssh_client, testcase, context_data)
#                     execution_results.append(result)
#                 except Exception as e:
#                     log_error(f"执行测试用例 {testcase.get('Testcase_Number')} 时发生错误: {e}")
#                     execution_results.append({
#                         'testcase_number': testcase.get('Testcase_Number'),
#                         'testcase_name': testcase.get('Testcase_Name'),
#                         'status': 'error',
#                         'message': f'执行出错: {str(e)}',
#                         'commands': [],
#                         'outputs': []
#                     })
#
#             # 断开SSH连接
#             ssh_client.disconnect()
#
#         except Exception as e:
#             log_error(f"执行测试用例时发生错误: {e}")
#             execution_results.append({
#                 'status': 'error',
#                 'message': f'执行过程中发生错误: {str(e)}',
#                 'testcase_number': None
#             })
#
#         return execution_results
#
#     def _execute_single_testcase(self, ssh_client: SSHClient, testcase: Dict[str, Any], context_data: Dict[str, Any]) -> Dict[str, Any]:
#         """
#         执行单个测试用例
#
#         Args:
#             ssh_client: SSH客户端
#             testcase: 测试用例
#             context_data: 上下文数据
#
#         Returns:
#             Dict: 执行结果
#         """
#         try:
#             testcase_number = testcase.get('Testcase_Number')
#             testcase_name = testcase.get('Testcase_Name')
#             test_steps = testcase.get('Testcase_TestSteps', '')
#
#             log_info(f"开始执行测试用例: {testcase_number} - {testcase_name}")
#
#             # 解析测试步骤中的命令
#             commands = self._extract_commands_from_steps(test_steps)
#
#             if not commands:
#                 return {
#                     'testcase_number': testcase_number,
#                     'testcase_name': testcase_name,
#                     'status': 'warning',
#                     'message': '未找到可执行的命令',
#                     'commands': [],
#                     'outputs': []
#                 }
#
#             # 执行命令
#             command_results = []
#             for cmd in commands:
#                 try:
#                     exit_code, output, error = ssh_client.execute_command(cmd)
#                     command_results.append({
#                         'command': cmd,
#                         'exit_code': exit_code,
#                         'output': output,
#                         'error': error,
#                         'success': exit_code == 0
#                     })
#                 except Exception as e:
#                     command_results.append({
#                         'command': cmd,
#                         'exit_code': -1,
#                         'output': '',
#                         'error': str(e),
#                         'success': False
#                     })
#
#             # 分析执行结果
#             success_count = sum(1 for result in command_results if result['success'])
#             total_count = len(command_results)
#
#             if success_count == total_count:
#                 status = 'success'
#                 message = f'所有命令执行成功 ({success_count}/{total_count})'
#             elif success_count > 0:
#                 status = 'partial'
#                 message = f'部分命令执行成功 ({success_count}/{total_count})'
#             else:
#                 status = 'failed'
#                 message = f'所有命令执行失败 ({success_count}/{total_count})'
#
#             return {
#                 'testcase_number': testcase_number,
#                 'testcase_name': testcase_name,
#                 'status': status,
#                 'message': message,
#                 'commands': commands,
#                 'outputs': command_results,
#                 'expected_result': testcase.get('Testcase_ExpectedResult', '')
#             }
#
#         except Exception as e:
#             log_error(f"执行测试用例 {testcase.get('Testcase_Number')} 时发生错误: {e}")
#             return {
#                 'testcase_number': testcase.get('Testcase_Number'),
#                 'testcase_name': testcase.get('Testcase_Name'),
#                 'status': 'error',
#                 'message': f'执行出错: {str(e)}',
#                 'commands': [],
#                 'outputs': []
#             }
#
#     def _extract_commands_from_steps(self, test_steps: str) -> List[str]:
#         """
#         从测试步骤中提取可执行的命令
#
#         Args:
#             test_steps: 测试步骤文本
#
#         Returns:
#             List[str]: 命令列表
#         """
#         try:
#             if not test_steps:
#                 return []
#
#             commands = []
#             lines = test_steps.split('\n')
#
#             for line in lines:
#                 line = line.strip()
#                 # 查找包含命令的行
#                 if any(keyword in line.lower() for keyword in ['执行', '运行', '命令', '检查', 'ps', 'ls', 'cat', 'grep', 'netstat', 'systemctl', 'docker', 'kubectl']):
#                     # 提取命令部分
#                     if '：' in line:
#                         cmd_part = line.split('：', 1)[1].strip()
#                     elif ':' in line:
#                         cmd_part = line.split(':', 1)[1].strip()
#                     else:
#                         cmd_part = line
#
#                     # 清理命令文本
#                     cmd_part = cmd_part.replace('`', '').replace('"', '').replace('"', '').replace('"', '')
#
#                     # 如果看起来像命令，则添加到列表
#                     if self._is_valid_command(cmd_part):
#                         commands.append(cmd_part)
#
#             return commands[:5]  # 限制最多5个命令
#
#         except Exception as e:
#             log_error(f"提取命令时发生错误: {e}")
#             return []
#
#     def _is_valid_command(self, cmd: str) -> bool:
#         """
#         判断是否为有效的命令
#
#         Args:
#             cmd: 命令文本
#
#         Returns:
#             bool: 是否为有效命令
#         """
#         if not cmd or len(cmd) < 2:
#             return False
#
#         # 常见的Linux命令开头
#         valid_prefixes = [
#             'ps', 'ls', 'cat', 'grep', 'awk', 'sed', 'find', 'which', 'whereis',
#             'netstat', 'ss', 'lsof', 'systemctl', 'service', 'chkconfig',
#             'docker', 'kubectl', 'crictl', 'podman',
#             'df', 'du', 'mount', 'lsblk', 'fdisk',
#             'top', 'htop', 'free', 'vmstat', 'iostat',
#             'uname', 'lscpu', 'lsmem', 'lspci', 'lsusb',
#             'rpm', 'yum', 'apt', 'dpkg', 'pip',
#             'echo', 'printf', 'test', '[', 'id', 'whoami'
#         ]
#
#         cmd_lower = cmd.lower().strip()
#         return any(cmd_lower.startswith(prefix) for prefix in valid_prefixes)