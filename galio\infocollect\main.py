from gevent import monkey
monkey.patch_all()

import argparse
import os
import sys

current_dir = os.path.dirname(__file__)
sys.path.append(os.path.join(current_dir, '../'))


from db.init_db import init_project_database, init_testcase_database
from app import create_app
from app.websocket.run_websocket import create_websocket_app
from app.websocket.socketio_instance import socketio
from log.logger import log_info, log_error
from services.vector_testcase_service import VectorTestcaseService


def start_webserver(web_app):
    log_info('Initializing web service...')
    web_app = create_websocket_app(web_app)
    log_info(f'Server running on http://127.0.0.1:9998 [debug:{web_app.debug}]')
    socketio.run(web_app, host='0.0.0.0', port=9998, log_output=False)


def start_vector_sync_task():
    try:
        log_info("Start vector database sync task...")
        vector_service = VectorTestcaseService()
        
        vector_service.async_sync_testcases(batch_size=100)
        
    except Exception as e:
        log_error(f"Failed to start vector database sync task: {e}")


def parseargs():
    parser = argparse.ArgumentParser(description="Galio InfoCollect - Security Testing Platform")
    parser.add_argument("-s", "--server",
                        help="Start web server",
                        action="store_true")
    parser.add_argument("--init-vector-db",
                        help="Force rebuild vector database (otherwise auto-check and sync if needed)",
                        action="store_true")
    return parser.parse_args()


if __name__ == '__main__':
    parsed_args = parseargs()

    if parsed_args.server:
        engine = init_project_database()
        try:
            testcase_engine = init_testcase_database()
            log_info("SQLite testcase database initialized successfully")

            start_vector_sync_task()
        except Exception as e:
            log_error(f"Failed to initialize testcase database: {str(e)}")

        app = create_app()
        start_webserver(app)
    else:
        log_error("Invalid usage. Use the '-s' parameter to start the web server.")
