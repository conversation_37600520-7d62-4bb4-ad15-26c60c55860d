<template>
  <div>
    <div v-if="mounts.length === 0">N/A</div>
    <div v-else style="font-size: 12px;">
      <div v-if="mounts.length > 0" style="padding: 2px 0;">
        <div>
          <span class="source-path">{{ mounts[0].host_path || 'N/A' }}</span>
          <span class="path-arrow"> → </span>
          <span class="dest-path">{{ mounts[0].container_path || 'N/A' }}</span>
          <span class="mount-tag">{{ mounts[0].readonly ? 'RO' : 'RW' }}</span>
          <span v-if="mounts[0].propagation" class="mount-tag">{{ mounts[0].propagation.replace('PROPAGATION_', '') }}</span>
        </div>
        <div v-if="mounts.length > 1" style="margin-top: 4px;">
          <a-button
            type="link"
            size="small"
            @click="showDetails"
            style="padding-left: 0;"
          >
            +{{ mounts.length - 1 }} more...
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CriMountCell',
  props: {
    record: {
      type: Object,
      required: true
    }
  },
  computed: {
    mounts() {
      try {
        return typeof this.record.mounts === 'string'
          ? JSON.parse(this.record.mounts)
          : this.record.mounts || [];
      } catch (e) {
        console.error('Failed to parse mounts:', e);
        return [];
      }
    }
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.record);
    }
  }
}
</script>

<style scoped lang="scss">
.mount-tag {
  background: var(--input-bg, #f5f5f5);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color, #666);
  margin-left: 5px;
}

.source-path {
  color: var(--primary-color, #1890ff);
}

.path-arrow {
  color: var(--disabled-color, #999);
}

.dest-path {
  color: var(--success-color, #52c41a);
}
</style>
