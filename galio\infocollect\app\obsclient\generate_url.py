from .obs_client_manager import ObsClientManager
from app.obsclient.logger import log_warning, log_error, log_debug


class GenerateUrl:
    def __init__(self, access_key_id, secret_access_key, server, node):
        self.client = ObsClientManager.get_instance(access_key_id, secret_access_key, server)
        self.node = node

    def generate_download_url(self, bucket_name, object_key, expires=3600):
        # generate a signed URL for downloading the object.
        try:
            res = self.client.createSignedUrl(method='GET', bucketName=bucket_name, objectKey=object_key,
                                              expires=expires)
            return res.signedUrl
        except Exception as e:
            log_error(f"Error generating signed URL: {str(e)}")
            return None

    def generate_upload_url(self, bucket_name, object_key, expires=3600):
        # generate a signed URL for uploading the object.
        try:
            res = self.client.createSignedUrl(method='PUT', bucketName=bucket_name, objectKey=object_key,
                                              expires=expires)

            return res.signedUrl
        except Exception as e:
            log_error(f"Error generating upload URL: {str(e)}")
            return None

    @staticmethod
    def curl_command(url):
        if url is None:
            return False
        return f'curl "{url}" -o application_snapshot_agent.zip'

    def build_command(self, bucket_name, bucket_store_dir, object_key,
                      upload_signed_url, upload_high_risk_port_signed_url):
        # build the complete command sequence
        object_key = "/".join([bucket_store_dir, object_key])
        download_url = self.generate_download_url(bucket_name, object_key)

        if download_url is None:
            log_error("Failed to generate download URL.")
            return ""
        cmd_list = [
            f'curl "{download_url}" -o application_snapshot_agent.zip',
            'unzip -o application_snapshot_agent.zip',
            'cd application_snapshot_agent',
            'chmod +x ./run_env/bin/python.exe',
            'python util/generate_single_node_report.py 0 >/dev/null',
            'sleep 0.5',
            'mv node_snapshot_0 node_snapshot',
            f'curl -T "node_snapshot" "{upload_signed_url}"',
            # high port scan
            f'thread_num=$(( $(netstat -tlnp | grep tcp | wc -l) / 3 + 1 ))',
            'cd high_risk_port_tool',
            "unzip -o testssl.sh-3.2.zip",
            "chmod +x ./testssl.sh-3.2 -R",
            "cp hexdump /usr/bin/",
            "chmod +x /usr/bin/hexdump",
            'python port_snapshot.py -scan --workers $thread_num',
            'sleep 0.5',
            f'curl -T "high_risk_port_scan.log" "{upload_high_risk_port_signed_url}"',
            'cd ../../',
            # 'rm -rf application_snapshot_agent.zip',
            # 'rm -rf application_snapshot_agent',
            # 'rm -rf generated_script.sh'
        ]

        script_create_cmd = f'rm -rf ~/.test && mkdir ~/.test && cd ~/.test && echo -e \'#!/bin/bash\\n' + '\\n'.join(
            cmd.replace("!", "\\!") for cmd in cmd_list) + f'\' > generated_script.sh && chmod +x generated_script.sh'
        log_warning(
              f"The automatically generated command is as follows. Please check and ensure it is executed correctly：")
        log_debug(f"{script_create_cmd}")
        script_exec_cmd = f'cd ~/.test && nohup sh generated_script.sh > output.txt 2>&1 && echo exec_success && wait'

        return [script_create_cmd, script_exec_cmd]

    def build_obs2node_download_command(self, bucket_name, object_key, download_file_name):
        download_url = self.generate_download_url(bucket_name, object_key)

        if download_url is None:
            log_error("Failed to generate download URL.")
            return ""

        cmd_list = [
            'mkdir -p ~/.test/Uploads',
            'cd ~/.test/Uploads',
            f'curl "{download_url}" -o {download_file_name}',
        ]

        cmd = " && ".join(cmd_list)
        log_warning(
              f"The automatically generated command is as follows. Please check and ensure it is executed correctly：")
        log_debug(f"{cmd}")

        return cmd

    def build_node2obs_upload_command(self, bucket_name, object_key, upload_file):
        upload_url = self.generate_upload_url(bucket_name, object_key)

        if upload_url is None:
            log_error("Failed to generate upload URL.")
            return False

        cmd_list = [
            f'curl -T {upload_file} "{upload_url}"',
        ]

        cmd = " && ".join(cmd_list)
        log_warning(
              f"The automatically generated command is as follows. Please check and ensure it is executed correctly：")
        log_debug(f"{cmd}")

        return cmd

    def build_spider_command(self, bucket_name, upload_object_key, node_name, upload_sign_url):
        # build s-spider command
        object_key = upload_object_key
        # generate url for obs to node
        download_url = self.generate_download_url(bucket_name, object_key)

        if download_url is None:
            log_error("Failed to generate download URL.")
            return ""

        cmd_list = [
            f'curl "{download_url}" -o scan_tools.zip',
            'unzip -o scan_tools.zip',
            'cd scan_tools',
            'chmod 777 run.sh',
            'dos2unix run.sh || true',
            'sleep 0.5',
            f'sh ./run.sh {self.node.ip} {node_name} _scanclassic_multithreading_offline_scanmemall',
            'sleep 1',
            f'curl -T "{node_name}.tar" "{upload_sign_url}"',
            'sleep 3'
            # 'rm -rf application_snapshot_agent.zip',
            # 'rm -rf application_snapshot_agent',
            # 'rm -rf generated_script.sh'
        ]

        script_create_cmd = f'rm -rf ~/.test && mkdir ~/.test && cd ~/.test && echo -e \'#!/bin/bash\\n' + '\\n'.join(
            cmd.replace("!", "\\!") for cmd in cmd_list) + f'\' > generated_script.sh && chmod +x generated_script.sh'

        # script_create_cmd = f'cd ~/.test && echo -e \'#!/bin/bash\\n' + '\\n'.join(
        #     cmd.replace("!", "\\!") for cmd in cmd_list) + f'\' > generated_script.sh && chmod +x generated_script.sh'
        log_warning(
              f"The automatically generated command is as follows. Please check and ensure it is executed correctly：")
        log_debug(f"{script_create_cmd}")
        script_exec_cmd = f'cd ~/.test && nohup sh generated_script.sh > output.txt 2>&1 && echo exec_success && wait'

        return [script_create_cmd, script_exec_cmd]
