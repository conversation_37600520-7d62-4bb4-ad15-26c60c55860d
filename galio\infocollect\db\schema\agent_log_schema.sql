-- DROP TABLE IF EXISTS agent_log;

CREATE TABLE IF NOT EXISTS agent_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id INTEGER NOT NULL,
    log_level VARCHAR(10) NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')),
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    module VARCHAR(100),
    log_content TEXT NOT NULL,
    FOREIGN KEY(node_id) REFERENCES host_config(id) ON DELETE CASCADE
);