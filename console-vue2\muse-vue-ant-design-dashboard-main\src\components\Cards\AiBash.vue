<template>
  <a-card
      :bordered="false"
      class="header-solid h-full cli-card"
      :bodyStyle="{ padding: 0 }"
  >
    <template #title>
      <div class="card-header-wrapper">
        <div class="header-wrapper">
          <div class="logo-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" :class="`text-${sidebarColor}`">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m6.75 7.5l3 2.25l-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25"/>
            </svg>
          </div>
          <h6 class="font-semibold m-0">{{ $t('headTopic.aiBash') }}</h6>
        </div>
        <div class="controls-wrapper">
          <a-tree-select
              v-model="selectedIps"
              style="width: 200px; margin-right: 16px"
              placeholder="Select target hosts"
              allow-clear
              tree-checkable
              :show-checked-strategy="SHOW_PARENT"
              :tree-data="treeData"
              :max-tag-count="0"
              :max-tag-placeholder="maxTagPlaceholder"
              :tree-default-expand-all="true"
          >
          </a-tree-select>
          <a-switch
              v-model="isAiBashMode"
              checked-children="AIBash"
              un-checked-children="Normal"
              style="margin-right: 16px"
          />
        </div>
      </div>
    </template>

    <div class="terminal-container" ref="terminal"></div>
  </a-card>
</template>

<script>
import 'xterm/css/xterm.css';
import {Terminal} from 'xterm';
import {FitAddon} from 'xterm-addon-fit';
import {io} from 'socket.io-client';
import { mapState } from 'vuex';
import { TreeSelect } from 'ant-design-vue';


export default {
  data() {
    return {
      terminal: null,
      fitAddon: null,
      currentCommand: '',
      commandHistory: [],
      historyIndex: 0,
      prompt: '\r\n$ ',
      socket: null,
      isConnected: false,
      lock: false,
      selectedIps: [],
      isAiBashMode: false,
      SHOW_PARENT: TreeSelect.SHOW_PARENT,
    };
  },

  computed: {
    ...mapState(['nodes', 'currentProject', 'selectedNodeIp', 'sidebarColor']),

    treeData() {
      return this.nodes.map(node => ({
        title: `${node.host_name} (${node.ip})`,
        key: node.ip,
        value: node.ip,
      }));
    },
  },

  watch: {
    selectedIps(newVal) {
      if (newVal.length === 0) {
        return;
      }
    },
    selectedNodeIp: {
      immediate: true,
      handler(newIp) {
        if (newIp && !this.selectedIps.includes(newIp)) {
          this.selectedIps = [newIp];
        }
      }
    },
  },

  mounted() {
    this.initializeTerminal();
    this.connectWebSocket();
    // 确保节点数据已加载
    if (this.nodes.length === 0) {
      this.$store.dispatch('fetchNodes');
    }
  },

  methods: {
    connectWebSocket() {
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${wsProtocol}//${window.location.hostname}:9998`;
      console.log('[WebSocket] Connecting to:', wsUrl);

      this.socket = io(wsUrl, {
        path: '/socket.io',
        transports: ['websocket'],
        reconnection: false,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000,
        query: {
          project: this.currentProject
        }
      });

      this.socket.on('connect', () => {
        console.log('[WebSocket] Connected with ID:', this.socket.id);
        this.isConnected = true;
        this.terminal.write('Connected to server.\r\n$ ');
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Disconnected from server:', reason);
        this.terminal.write('Disconnected from server\r\n$ ');
      });


      this.socket.on('message', (data) => {
        console.log('[WebSocket] Message received:', data);
        this.lock = false;
        try {
          if (typeof data === 'string') {
            try {
              data = JSON.parse(data);
            } catch (e) {
              this.terminal.write(data + '\r\n$ ');
              return;
            }
          }

          if (data.type === 'output') {
            this.terminal.write('\r\n');
            this.terminal.write(data.message);
            if (!data.message.endsWith('\n')) {
              this.terminal.write('\r\n');
            }
            this.terminal.write('$ ');
          } else if (data.type === 'error') {
            this.terminal.write('\r\n' + data.message + '\r\n$ ');
          } else if (data.type === 'system') {
            this.terminal.write('\r\n' + data.message + '\r\n$ ');
          }
        } catch (error) {
          console.error('[WebSocket] Error processing message:', error);
          this.terminal.write('\r\nError processing server response\r\n$ ');
        }
      });

      this.socket.on('connect_error', (error) => {
        console.error('[WebSocket] Connection error:', error);
      });
    },

    handleTerminalInput(data) {
      if (data.charCodeAt(0) === 13) { // Enter key
        if (this.currentCommand.trim() && this.isConnected) {
          const command = this.currentCommand.trim();
          console.log('[Terminal] Sending command:', command);
          this.lock = true;

          this.commandHistory.unshift(command);
          this.historyIndex = 0;

          if (command === "clear") {
            this.terminal.clear()
            this.terminal.write('\r\n$ ');
            return
          }

          try {
            const message = {
              event: 'input',
              data: {
                command,
                aibash: this.isAiBashMode,
                ip: this.selectedIps.length > 0 ? this.selectedIps.join(',') : (this.selectedNodeIp || '*'),
                project: this.currentProject,
                timestamp: Date.now()
              }
            };
            this.socket.emit('message', JSON.stringify(message));
          } catch (error) {
            console.error('[Terminal] Error sending command:', error);
            this.terminal.write('\r\nError sending command\r\n$ ');
          }
        } else if (!this.isConnected) {
          this.terminal.write('\r\nNot connected to server\r\n$ ');
        } else {
          this.terminal.write('\r\n$ ');
        }
        this.currentCommand = '';
      } else if (data.charCodeAt(0) === 127) { // Backspace
        this.handleBackspace();
      } else {
        if (
            // 检查是否为英文或数字
            (data.charCodeAt(0) >= 32 && data.charCodeAt(0) <= 126) ||
            // 检查是否为中文字符
            (data.charCodeAt(0) >= 0x4E00 && data.charCodeAt(0) <= 0x9FA5)
        ) {
          this.currentCommand += data;
          this.terminal.write(data);
        }

      }
    },

    handleBackspace() {
      if (this.currentCommand.length > 0) {
        // 计算删除的字符长度，考虑到中文字符可能占用多个字节
        let deleterlimte = '\b \b';
        const lastChar = this.currentCommand.slice(-1);
        this.currentCommand = this.currentCommand.slice(0, this.currentCommand.length - 1);
        if (lastChar.charCodeAt(0) >= 0x4E00 && lastChar.charCodeAt(0) <= 0x9FFF) {
          // 如果是中文字符，占用两个字节
          deleterlimte = '\b\b \b';

        }
        this.terminal.write(deleterlimte);

      }
    },

    // 处理上箭头键
    handleUpArrow() {
      if (this.historyIndex < this.commandHistory.length) {
        const command = this.commandHistory[this.historyIndex];

        // 清除当前行
        let deleterlimte = '';
        let currentCommand_length = this.currentCommand.length;
        for (let i = 0; i < currentCommand_length; i++) {
          const lastChar = this.currentCommand.slice(-1);
          if (lastChar.charCodeAt(0) >= 0x4E00 && lastChar.charCodeAt(0) <= 0x9FFF) {
            // 如果是中文字符，占用两个字节
            deleterlimte = '\b\b \b';
          } else {
            deleterlimte = '\b \b';
          }

          this.terminal.write(deleterlimte);
          this.currentCommand = this.currentCommand.slice(0, -1);
        }


        // 写入历史命令
        this.terminal.write(command);
        this.currentCommand = command;
        this.historyIndex++;
      }
    },

// 处理下箭头键
    handleDownArrow() {
      if (this.historyIndex > 0) {
        let command = '';
        if (this.historyIndex > 1) {
          command = this.commandHistory[this.historyIndex - 2];
        }

        // 清除当前行
        let deleterlimte = '';
        let currentCommand_length = this.currentCommand.length;
        for (let i = 0; i < currentCommand_length; i++) {
          const lastChar = this.currentCommand.slice(-1);
          if (lastChar.charCodeAt(0) >= 0x4E00 && lastChar.charCodeAt(0) <= 0x9FFF) {
            // 如果是中文字符，占用两个字节
            deleterlimte = '\b\b \b';
          } else {
            deleterlimte = '\b \b';
          }
          this.terminal.write(deleterlimte);
          this.currentCommand = this.currentCommand.slice(0, -1);
        }


        // 写入历史命令
        this.terminal.write(command);
        this.currentCommand = command;
        this.historyIndex--;
      }
    },

    handleDelete() {
      // 清除当前行
      let deleterlimte = '';
      let currentCommand_length = this.currentCommand.length;
      for (let i = 0; i < currentCommand_length; i++) {
        const lastChar = this.currentCommand.slice(-1);
        if (lastChar.charCodeAt(0) >= 0x4E00 && lastChar.charCodeAt(0) <= 0x9FFF) {
          // 如果是中文字符，占用两个字节
          deleterlimte = '\b\b \b';
        } else {
          deleterlimte = '\b \b';
        }
        this.terminal.write(deleterlimte);
        this.currentCommand = this.currentCommand.slice(0, -1);
      }
    },

    maxTagPlaceholder(omittedValues) {
      const count = omittedValues.length;
      return `${count} ${count > 1 ? 'hosts' : 'host'} selected`;
    },

    initializeTerminal() {
      this.terminal = new Terminal({
        cursorBlink: true,
        theme: {
          background: '#1e1e1e',
          foreground: '#ffffff',
          cursor: '#ffffff'
        },
        fontSize: 14,
        fontFamily: 'Menlo, Monaco, "Courier New", monospace',
        convertEol: true,
        scrollback: 1000,
        allowTransparency: true,
        rendererType: 'canvas'
      });

      this.fitAddon = new FitAddon();
      this.terminal.loadAddon(this.fitAddon);


      this.terminal.open(this.$refs.terminal);
      this.fitAddon.fit();

      this.terminal.write('$ ');
      this.terminal.onData(this.handleTerminalInput);

      const keymap = [
        {"key": "ArrowUp", "shiftKey": false, "handler": this.handleUpArrow},
        {"key": "ArrowDown", "shiftKey": false, "handler": this.handleDownArrow},
        {"key": "Delete", "shiftKey": false, "handler": this.handleDelete}
      ];

      this.terminal.attachCustomKeyEventHandler(ev => {
        if (this.lock) {
          return false;
        }
        if (ev.type === 'keydown') {
          for (let i in keymap) {
            if (keymap[i].key == ev.key && keymap[i].shiftKey == ev.shiftKey) {
              keymap[i].handler();
              return false;
            }
          }
        }
      });

    }
  },

  beforeDestroy() {
    if (this.socket) {
      this.socket.disconnect();
    }
    if (this.terminal) {
      this.terminal.dispose();
    }
  }
};
</script>

<style scoped lang="scss">
.cli-card {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
}

.terminal-container {
  height: 1000px;
  padding: 5px;
  background-color: #1e1e1e;
  margin: 8px 16px 16px 16px;
  overflow: hidden;
  border-radius: 4px;

::v-deep .xterm {
    padding: 8px;
  }

::v-deep .xterm-viewport {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }
}

.controls-wrapper {
  display: flex;
  align-items: center;
}


::v-deep .ant-switch {
  $padding: 18px;
  $text-margin: 4px;
  min-width: 70px;

  // 通用按钮样式
  .ant-switch-handle::before {
    left: 0;
  }

  // 未选中状态 (Normal)
  &:not(.ant-switch-checked) {
    padding-left: $padding;
    padding-right: 0;

    .ant-switch-inner {
      margin-left: $text-margin;
    }
  }

  // 选中状态 (AIBash)
  &.ant-switch-checked {
    padding-left: 0;
    padding-right: $padding;

    .ant-switch-inner {
      margin-right: $text-margin;
    }
  }
}
</style>
