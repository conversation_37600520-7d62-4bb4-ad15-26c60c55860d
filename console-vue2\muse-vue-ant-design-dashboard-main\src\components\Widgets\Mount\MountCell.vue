<template>
  <div>
    <div v-if="mounts.length === 0">N/A</div>
    <div v-else style="font-size: 12px;">
      <div v-if="mounts.length > 0" style="padding: 2px 0;">
        <div>
          <span class="source-path">{{ mounts[0].Source || 'N/A' }}</span>
          <span class="path-arrow"> → </span>
          <span class="dest-path">{{ mounts[0].Destination || 'N/A' }}</span>
          <span class="mount-tag" style="margin-left: 5px;">{{ mounts[0].Mode || '' }}</span>
          <span class="mount-tag">{{ mounts[0].RW ? 'RW' : 'RO' }}</span>
        </div>
        <div v-if="mounts.length > 1" style="margin-top: 4px;">
          <a-button
            type="link"
            size="small"
            @click="showDetails"
            style="padding-left: 0;"
          >
            +{{ mounts.length - 1 }} more...
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MountCell',
  props: {
    record: {
      type: Object,
      required: true
    },
    parseJsonField: {
      type: Function,
      default: (field, defaultValue) => {
        try {
          if (Array.isArray(field)) {
            return field;
          }
          if (typeof field === 'object' && field !== null) {
            return field;
          }
          return typeof field === 'string' ? JSON.parse(field) : defaultValue;
        } catch (e) {
          console.warn('Failed to parse JSON field:', e);
          return defaultValue;
        }
      }
    }
  },
  computed: {
    mounts() {
      try {
        return this.parseJsonField(this.record.mounts, []);
      } catch (e) {
        console.error('Failed to parse mounts:', e);
        return [];
      }
    }
  },
  methods: {
    showDetails() {
      this.$emit('show-details', this.record);
    }
  }
}
</script>

<style scoped lang="scss">
.mount-tag {
  background: var(--input-bg, #f5f5f5);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color, #666);
}

.source-path {
  color: var(--primary-color, #1890ff);
}

.path-arrow {
  color: var(--disabled-color, #999);
}

.dest-path {
  color: var(--success-color, #52c41a);
}
</style>
