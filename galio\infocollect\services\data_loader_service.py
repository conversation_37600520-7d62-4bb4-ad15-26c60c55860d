import pickle
import os
from typing import Op<PERSON>, Dict, Any
from sqlalchemy import delete, and_

from log.logger import log_error, log_debug, log_warning
from services.node_service import NodeService
from services.os_package_service import PackageService
from services.process_service import ProcessService
from services.hardware_service import HardwareService
from services.filesystem_service import FilesystemService
from services.kubernetes_service import KubernetesService
from services.docker_service import DockerService
from services.crictl_service import CrictlService
from services.port_service import PortService
from datamodel.os_package_datamodel import OsPackageSnapshot
from datamodel.process_datamodel import ProcessSnapshot
from datamodel.hardware_datamodel import HardwareSnapshot
from datamodel.filesystem_datamodel import Filesystem<PERSON>napshot
from datamodel.port_datamodel import Tcp<PERSON>ort<PERSON>napshot, UdpPortSnapshot, UnixSocketSnapshot
from datamodel.kubernetes_datamodel import (
    K8sAPIServer, K8s<PERSON>ngress, K8sGateway, K8sVirtualService,
    K8sSer<PERSON>, <PERSON><PERSON>sNetworkPolicy, <PERSON><PERSON>s<PERSON><PERSON>, <PERSON><PERSON>s<PERSON><PERSON>,
    <PERSON>8s<PERSON>ec<PERSON>, K8sConfigMap, K8sRole, K8sRoleBinding,
    K8sClusterRole, K8sClusterRoleBinding, K8sServiceAccountPermission
)
from datamodel.docker_datamodel import DockerHostConfig, DockerContainer, DockerNetwork
from datamodel.crictl_datamodel import CrictlHostConfig, CrictlPod, CrictlContainer
from datamodel.agent_log_datamodel import AgentLog
from services.agent_log_service import AgentLogService


class DataLoaderService:
    def __init__(self, db):
        self.db = db
        self.node_service = NodeService(db)

    @staticmethod
    def load_data_from_pkl(pkl_file_path: str) -> Optional[Dict[str, Any]]:
        try:
            with open(pkl_file_path, 'rb') as f:
                data = pickle.load(f)
            return data
        except FileNotFoundError:
            log_error(f"Pickle 文件未找到: {pkl_file_path}")
        except pickle.UnpicklingError as e:
            log_error(f"解封 Pickle 文件时出错 ({pkl_file_path}): {e}")
        except Exception as e:
            log_error(f"加载 Pickle 文件时发生未知错误 ({pkl_file_path}): {e}")
        return None

    def clear_node_data_by_type(self, node_id: int, data_type: str):
        try:
            tables_map = {
                'os_package_info': [OsPackageSnapshot],
                'process_info': [ProcessSnapshot],
                'hardware_info': [HardwareSnapshot],
                'filesystem_info': [FilesystemSnapshot],
                'port_info': [TcpPortSnapshot, UdpPortSnapshot, UnixSocketSnapshot],
                'k8s_info': [
                    K8sAPIServer, K8sIngress, K8sGateway, K8sVirtualService,
                    K8sService, K8sNetworkPolicy, K8sPod, K8sNode,
                    K8sSecret, K8sConfigMap, K8sRole, K8sRoleBinding,
                    K8sClusterRole, K8sClusterRoleBinding, K8sServiceAccountPermission
                ],
                'docker_info': [DockerHostConfig, DockerContainer, DockerNetwork],
                'crictl_info': [CrictlHostConfig, CrictlPod, CrictlContainer],
                'agent_log': [AgentLog]
            }

            data_type = data_type.lower()
            tables = tables_map.get(data_type, [])

            if not tables:
                log_warning(f"Clear node data, Unknown data type: {data_type}")
                return

            for table in tables:
                stmt = delete(table).where(and_(table.node_id == node_id))
                self.db.execute(stmt)

            self.db.commit()
            log_debug(f"Successfully cleared {data_type} data for node_id: {node_id}")

        except Exception as e:
            self.db.rollback()
            log_error(f"Error clearing {data_type} data for node_id {node_id}: {str(e)}")
            raise

    def insert_data_from_pkl(self, pkl_file_path, hostname, ip):
        # 首先获取文件名以确定数据类型
        file_name = os.path.basename(pkl_file_path)
        data_type = None
        for type_name in ['os_package_info', 'process_info', 'hardware_info', 'filesystem_info', 'port_info',
                          'k8s_info', 'docker_info', 'crictl_info', 'agent.log']:
            if type_name in file_name:
                data_type = type_name
                break

        if not data_type:
            log_error(f"Cannot determine data type from file name: {file_name}")
            return

        if "agent.log" in pkl_file_path:
            node_id = self.node_service.add_node(hostname, ip)
            self.clear_node_data_by_type(node_id, data_type)
            
            agent_log_service = AgentLogService(self.db)
            insert_objects = agent_log_service.get_insert_objects(pkl_file_path, node_id)
            
            if insert_objects:
                self.db.bulk_save_objects(insert_objects)
                self.db.commit()
            return

        # 对于其他文件类型，加载 pickle 数据
        node_data = self.load_data_from_pkl(pkl_file_path)
        if node_data is None:
            log_warning("No data loaded from the specified pickle file.")
            return

        node_id = self.node_service.add_node(hostname, ip)

        self.clear_node_data_by_type(node_id, data_type)

        insert_objects = []
        if "os_package_info" in pkl_file_path:
            package_service = PackageService(self.db)
            insert_objects.extend(package_service.get_insert_objects(node_data, node_id))

        elif "process_info" in pkl_file_path:
            process_service = ProcessService(self.db)
            insert_objects.extend(process_service.get_insert_objects(node_data, node_id))

        elif "hardware_info" in pkl_file_path:
            hardware_service = HardwareService(self.db)
            insert_objects.extend(hardware_service.get_insert_objects(node_data, node_id))

        elif "filesystem_info" in pkl_file_path:
            filesystem_service = FilesystemService(self.db)
            insert_objects.extend(filesystem_service.get_insert_objects(node_data, node_id))

        elif "port_info" in pkl_file_path:
            port_service = PortService(self.db)
            port_objects = port_service.get_insert_objects(node_data, node_id)
            insert_objects.extend(port_objects.get('tcp', []))
            insert_objects.extend(port_objects.get('udp', []))
            insert_objects.extend(port_objects.get('unix_socket', []))

        elif "k8s_info" in pkl_file_path:
            kubernetes_service = KubernetesService(self.db)
            api_server_data = node_data.get('api_server')
            ingress_data = node_data.get('ingresses')
            gateway_data = node_data.get('gateways')
            virtualservice_data = node_data.get('virtualservices')
            service_data = node_data.get('services')
            network_policy_data = node_data.get('network_policies')
            pod_data = node_data.get('pods')
            node_info = node_data.get('node')
            secrets_data = node_data.get('secrets')
            configmaps_data = node_data.get('configmaps')
            roles_data = node_data.get('roles')
            rolebindings_data = node_data.get('rolebindings')
            clusterroles_data = node_data.get('clusterroles')
            clusterrolebindings_data = node_data.get('clusterrolebindings')
            serviceaccount_permissions_data = node_data.get('serviceaccount_permissions')

            if api_server_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_api_server(api_server_data, node_id))
            if ingress_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_ingress(ingress_data, node_id))
            if gateway_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_gateway(gateway_data, node_id))
            if virtualservice_data:
                insert_objects.extend(kubernetes_service.
                                      get_insert_objects_virtual_service(virtualservice_data, node_id))
            if service_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_service(service_data, node_id))
            if network_policy_data:
                insert_objects.extend(kubernetes_service.
                                      get_insert_objects_network_policy(network_policy_data, node_id))
            if pod_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_pod(pod_data, node_id))
            if node_info:
                insert_objects.extend(kubernetes_service.get_insert_objects_node(node_info, node_id))
            if secrets_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_secret(secrets_data, node_id))
            if configmaps_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_config_map(configmaps_data, node_id))
            if roles_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_role(roles_data, node_id))
            if rolebindings_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_role_binding(rolebindings_data, node_id))
            if clusterroles_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_cluster_role(clusterroles_data, node_id))
            if clusterrolebindings_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_cluster_role_binding(clusterrolebindings_data, node_id))
            if serviceaccount_permissions_data:
                insert_objects.extend(kubernetes_service.get_insert_objects_serviceaccount_permissions(serviceaccount_permissions_data, node_id))

        elif "docker_info" in pkl_file_path:
            docker_service = DockerService(self.db)
            host_data = node_data.get("host_security")
            networks_data = node_data.get("networks", [])
            container_data = node_data.get("container_security", [])

            if host_data:
                insert_objects.extend(docker_service.get_insert_objects_host_config(host_data, node_id))
            if container_data:
                insert_objects.extend(docker_service.get_insert_objects_containers(container_data, node_id))
            if networks_data:
                insert_objects.extend(docker_service.get_insert_objects_networks(networks_data, node_id))

        elif 'crictl_info.pkl' in pkl_file_path:
            crictl_service = CrictlService(self.db)
            host_data = node_data.get('host_security', {})
            container_data = node_data.get('container_security', [])
            pods_data = node_data.get('pod_info', [])

            if host_data:
                insert_objects.extend(crictl_service.get_insert_objects_host_config(host_data, node_id))
            if container_data:
                insert_objects.extend(crictl_service.get_insert_objects_containers(container_data, node_id))
            if pods_data:
                insert_objects.extend(crictl_service.get_insert_objects_pods(pods_data, node_id))

        if insert_objects:
            self.db.bulk_save_objects(insert_objects)
            self.db.commit()
