(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d588eafa"],{"17c0":function(e,t,a){},"283a":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",[t("a-row",{attrs:{type:"flex",gutter:24}},[t("a-col",{staticClass:"mb-24",attrs:{span:24}},[t("PackageInfo")],1)],1)],1)},c=[],o=function(){var e=this,t=e._self._c;return t("a-card",{staticClass:"header-solid h-full package-card",attrs:{bordered:!1,bodyStyle:{padding:0},headStyle:{borderBottom:"1px solid #e8e8e8"}},scopedSlots:e._u([{key:"title",fn:function(){return[t("div",{staticClass:"card-header-wrapper"},[t("div",{staticClass:"header-wrapper"},[t("div",{staticClass:"logo-wrapper"},[t("svg",{class:"text-"+e.sidebarColor,attrs:{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 16 16"}},[t("path",{attrs:{fill:"currentColor","fill-rule":"evenodd",d:"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z","clip-rule":"evenodd"}})])]),t("h6",{staticClass:"font-semibold m-0"},[e._v(e._s(e.$t("headTopic.package")))])]),t("div",[t("RefreshButton",{on:{refresh:e.fetchPackages}})],1)])]},proxy:!0}])},[t("a-table",{attrs:{columns:e.columns,"data-source":e.packages,rowKey:e=>e.package_name,pagination:e.pagination},scopedSlots:e._u([{key:"bodyCell",fn:function({column:a,record:s}){return["package_name"===a.key?[t("div",{staticClass:"table-package-info"},[t("span",[e._v(e._s(s.package_name))]),t("span",[e._v(e._s(s.package_type))])])]:"action"===a.key?[t("a-button",{staticClass:"btn-edit",attrs:{type:"link"}},[e._v("Edit")])]:e._e()]}}])})],1)},r=[],n=a("2f62"),l=a("fec3"),i=a("f188"),d={components:{RefreshButton:i["a"]},data(){return{packages:[],columns:[{title:"Package Name",dataIndex:"package_name",key:"package_name"},{title:"Package Type",dataIndex:"package_type",key:"package_type"}],pagination:{pageSize:100}}},computed:{...Object(n["e"])(["selectedNodeIp","currentProject","sidebarColor"])},watch:{selectedNodeIp(e){this.fetchPackages()}},mounted(){this.fetchPackages()},methods:{async fetchPackages(){if(console.log("Selected Node IP:",this.selectedNodeIp),this.selectedNodeIp)try{const e=await l["a"].get("/api/packages/"+this.selectedNodeIp,{params:{dbFile:this.currentProject}});this.packages=e.data}catch(e){console.error("Error fetching packages:",e)}else console.error("Node IP is not defined")}}},p=d,u=(a("9402"),a("2877")),f=Object(u["a"])(p,o,r,!1,null,"28c58e84",null),h=f.exports,g={components:{PackageInfo:h}},k=g,m=Object(u["a"])(k,s,c,!1,null,null,null);t["default"]=m.exports},9402:function(e,t,a){"use strict";a("17c0")},f188:function(e,t,a){"use strict";var s=function(){var e=this,t=e._self._c;return t("a-button",{class:["refresh-button","text-"+e.sidebarColor],attrs:{icon:"reload"},on:{click:function(t){return e.$emit("refresh")}}},[e._v(" "+e._s(e.text||e.$t("common.refresh"))+" ")])},c=[],o=a("2f62"),r={computed:{...Object(o["e"])(["sidebarColor"])},name:"RefreshButton",props:{text:{type:String,default:""}}},n=r,l=a("2877"),i=Object(l["a"])(n,s,c,!1,null,"80cb1374",null);t["a"]=i.exports}}]);
//# sourceMappingURL=chunk-d588eafa.339ed414.js.map