<template>
    <!-- Main Sidebar -->
    <a-layout-sider
        v-show="!sidebarCollapsed"
        :trigger="null"
        collapsible
        :collapsed-width="0"
        width="220"
        class="sider-primary"
        :class="[
            'ant-layout-sider-' + sidebarColor,
            'ant-layout-sider-' + sidebarTheme
        ]"
        theme="light"
        :style="{ backgroundColor: 'transparent' }">
        <div class="sidebar-container">
            <!-- Brand Area at Top -->
            <div class="brand-section">
                <div class="brand-header">
                    <div class="logo-container" :class="`text-${sidebarColor}`">
                      <svg xmlns="http://www.w3.org/2000/svg" width="36px" height="36px" viewBox="0 0 48 48" class="ios-logo">
                        <!-- 背景矩形 -->
                        <rect width="48" height="48" rx="10" fill="currentColor" opacity="0.1"></rect>
                        <!-- 盾牌轮廓-->
                        <path fill="none" stroke="currentColor" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"
                              d="M24 43.5c9.043-3.117 15.488-10.363 16.5-19.589c.28-4.005.256-8.025-.072-12.027a2.54 2.54 0 0 0-2.467-2.366c-4.091-.126-8.846-.808-12.52-4.427a2.05 2.05 0 0 0-2.881 0c-3.675 3.619-8.43 4.301-12.52 4.427a2.54 2.54 0 0 0-2.468 2.366A79.4 79.4 0 0 0 7.5 23.911C8.51 33.137 14.957 40.383 24 43.5z">
                          <animate attributeName="stroke-dasharray" values="0 150;150 0;0 150" dur="5s" calcMode="linear" repeatCount="indefinite" />
                        </path>
                        <!-- 盾牌内部装饰线 -->
                        <path fill="none" stroke="currentColor" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round" opacity="0.6"
                              d="M24 39c7.5-2.5 12.5-8.5 13.5-16c.2-3 .2-6 0-9"></path>
                        <path fill="none" stroke="currentColor" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round" opacity="0.6"
                              d="M24 39c-7.5-2.5-12.5-8.5-13.5-16c-.2-3-.2-6 0-9"></path>
                        <!-- 锁图标 -->
                        <circle cx="24" cy="20.206" r="4.5" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></circle>
                        <!-- 锁底部 -->
                        <path fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                              d="M31.589 32.093a7.589 7.589 0 1 0-15.178 0"></path>

                        <!-- 钥匙孔 -->
                        <path fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                              d="M24 20.2v2.5"></path>
                      </svg>
                    </div>
                    <div class="brand-text-container">
                        <div class="brand-name">SecTest Copilot</div>
                    </div>
                </div>
                <div class="brand-divider"></div>
            </div>

            <!-- Sidebar Navigation Menu -->
            <div class="navigation-section">
                <a-menu theme="light" mode="inline"
                    :default-open-keys="['fileTransfer', 'aiBash', 'testCases', 'smartAnalysis']"
                    :inline-collapsed="sidebarCollapsed"
                    class="sidebar-menu">

            <!-- Info Collection 分组 -->
            <a-sub-menu key="infoCollection">
                <template slot="title" slot-scope="{ open }">
                    <span class="enhanced-title">{{ $t('sidebar.infoCollection') }}</span>
                </template>
                <a-menu-item key="process">
                    <router-link to="/process">
                        <span class="icon">
                          <svg width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path d="M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 64zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-288 0zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.processInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <!-- 其余子菜单项保持不变 -->
                <a-menu-item key="package">
                    <router-link to="/package">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16">
                            <path fill="currentColor" fill-rule="evenodd" d="M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z" clip-rule="evenodd"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.packageInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="hardware">
                    <router-link to="/hardware">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 512 512">
                            <path fill="#ffffff" d="M160 160h192v192H160z"/>
                            <path fill="#ffffff" d="M480 198v-44h-32V88a24 24 0 0 0-24-24h-66V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32h-36V32h-44v32H88a24 24 0 0 0-24 24v66H32v44h32v36H32v44h32v36H32v44h32v66a24 24 0 0 0 24 24h66v32h44v-32h36v32h44v-32h36v32h44v-32h66a24 24 0 0 0 24-24v-66h32v-44h-32v-36h32v-44h-32v-36Zm-352-70h256v256H128Z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.hardwareInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="filesystem">
                    <router-link to="/filesystem">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 16 16">
                            <path fill="#ffffff" fill-rule="evenodd" d="m6.44 4.06l.439.44H12.5A1.5 1.5 0 0 1 14 6v5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11V4.5A1.5 1.5 0 0 1 3.5 3h1.257a1.5 1.5 0 0 1 1.061.44zM.5 4.5a3 3 0 0 1 3-3h1.257a3 3 0 0 1 2.122.879L7.5 3h5a3 3 0 0 1 3 3v5a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3zm4.25 2a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5z" clip-rule="evenodd"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.filesystemInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="high-port-info">
                    <router-link to="/port">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width="20" height="20">
                            <path d="M96 32C78.3 32 64 46.3 64 64l0 192-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32-32 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l32 0 0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32 160 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-160 0 0-32 112 0c79.5 0 144-64.5 144-144s-64.5-144-144-144L96 32zM240 256l-112 0 0-160 112 0c44.2 0 80 35.8 80 80s-35.8 80-80 80z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.portInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="docker">
                    <router-link to="/docker">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 48 48">
                            <path d="M 22.5 6 C 22.224 6 22 6.224 22 6.5 L 22 9.5 C 22 9.776 22.224 10 22.5 10 L 25.5 10 C 25.776 10 26 9.776 26 9.5 L 26 6.5 C 26 6.224 25.776 6 25.5 6 L 22.5 6 z M 10.5 12 C 10.224 12 10 12.224 10 12.5 L 10 15.5 C 10 15.776 10.224 16 10.5 16 L 13.5 16 C 13.776 16 14 15.776 14 15.5 L 14 12.5 C 14 12.224 13.776 12 13.5 12 L 10.5 12 z M 16.5 12 C 16.224 12 16 12.224 16 12.5 L 16 15.5 C 16 15.776 16.224 16 16.5 16 L 19.5 16 C 19.776 16 20 15.776 20 15.5 L 20 12.5 C 20 12.224 19.776 12 19.5 12 L 16.5 12 z M 22.5 12 C 22.224 12 22 12.224 22 12.5 L 22 15.5 C 22 15.776 22.224 16 22.5 16 L 25.5 16 C 25.776 16 26 15.776 26 15.5 L 26 12.5 C 26 12.224 25.776 12 25.5 12 L 22.5 12 z M 37.478516 14.300781 L 37.025391 14.951172 C 36.458391 15.825172 36.045734 16.787828 35.802734 17.798828 C 35.343734 19.731828 35.621422 21.546656 36.607422 23.097656 C 35.416422 23.758656 33.386 23.986 33 24 L 2 24 C 0.895 24 0 24.895 0 26 C 0 28 0.43371875 30.924625 1.3867188 33.515625 C 2.4757187 36.359625 4.0970781 38.454328 6.2050781 39.736328 C 8.5670781 41.177328 12.404859 42 16.755859 42 C 18.720859 42.006 20.683234 41.828703 22.615234 41.470703 C 25.301234 40.979703 27.885719 40.045078 30.261719 38.705078 C 32.219719 37.576078 33.981469 36.139172 35.480469 34.451172 C 37.985469 31.627172 39.477891 28.4815 40.587891 25.6875 C 40.592891 25.6845 40.596562 25.683688 40.601562 25.679688 C 43.598562 25.800687 45.412625
                                24.642688 46.390625 23.679688 C 47.008625 23.095688 47.491688 22.38275 47.804688 21.59375 L 48 21.021484 L 47.527344 20.650391 C 47.397344 20.547391 46.182141 19.632812 43.619141 19.632812 C 42.942141 19.635813 42.266609 19.694641 41.599609 19.806641 C 41.103609 16.421641 38.293969 14.769313 38.167969 14.695312 L 37.478516 14.300781 z M 4.5 18 C 4.224 18 4 18.224 4 18.5 L 4 21.5 C 4 21.776 4.224 22 4.5 22 L 7.5 22 C 7.776 22 8 21.776 8 21.5 L 8 18.5 C 8 18.224 7.776 18 7.5 18 L 4.5 18 z M 10.5 18 C 10.224 18 10 18.224 10 18.5 L 10 21.5 C 10 21.776 10.224 22 10.5 22 L 13.5 22 C 13.776 22 14 21.776 14 21.5 L 14 18.5 C 14 18.224 13.776 18 13.5 18 L 10.5 18 z M 16.5 18 C 16.224 18 16 18.224 16 18.5 L 16 21.5 C 16 21.776 16.224 22 16.5 22 L 19.5 22 C 19.776 22 20 21.776 20 21.5 L 20 18.5 C 20 18.224 19.776 18 19.5 18 L 16.5 18 z M 22.5 18 C 22.224 18 22 18.224 22 18.5 L 22 21.5 C 22 21.776 22.224 22 22.5 22 L 25.5 22 C 25.776 22 26 21.776 26 21.5 L 26 18.5 C 26 18.224 25.776 18 25.5 18 L 22.5 18 z M 28.5 18 C 28.224 18 28 18.224 28 18.5 L 28 21.5 C 28 21.776 28.224 22 28.5 22 L 31.5 22 C 31.776 22 32 21.776 32 21.5 L 32 18.5 C 32 18.224 31.776 18 31.5 18 L 28.5 18 z"></path>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.dockerInfo') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="kubernetes">
                    <router-link to="/kubernetes">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 32 32">
                            <path fill="#ffffff" d="m29.223 17.964l-3.304-.754a9.78 9.78 0 0 0-1.525-6.624l2.54-2.026l-1.247-1.564l-2.539 2.024A9.97 9.97 0 0 0 17 6.05V3h-2v3.05a9.97 9.97 0 0 0-6.148 2.97l-2.54-2.024L5.066 8.56l2.54 2.025a9.78 9.78 0 0 0-1.524 6.625l-3.304.754l.446 1.95l3.297-.753a10.04 10.04 0 0 0 4.269 5.358l-1.33 2.763l1.802.867l1.329-2.76a9.8 9.8 0 0 0 6.82 0l1.33 2.76l1.802-.868l-1.33-2.762a10.04 10.04 0 0 0 4.269-5.358l3.297.752ZM24 16q-.002.385-.039.763l-5-1.142a3 3 0 0 0-.137-.594l3.996-3.187A7.94 7.94 0 0 1 24 16m-9 0a1 1 0 1 1 1 1a1 1 0 0 1-1-1m6.576-5.726l-3.996 3.187a3 3 0 0 0-.58-.277V8.07a7.98 7.98 0 0 1 4.576 2.205M15 8.07v5.115a3 3 0 0 0-.58.277l-3.996-3.187A7.98 7.98 0 0 1 15 8.07M8 16a7.94 7.94 0 0 1 1.18-4.16l3.996 3.187a3 3 0 0 0-.137.594l-5 1.141A8 8 0 0 1 8 16m.484 2.712l4.975-1.136a3 3 0 0 0 .414.537L11.66 22.71a8.03 8.03 0 0 1-3.176-3.998M16 24a8 8 0 0 1-2.54-.42l2.22-4.612A3 3 0 0 0 16 19a3 3 0 0 0 .319-.032l2.221 4.612A8 8 0 0 1 16 24m4.34-1.29l-2.213-4.598a3 3 0 0 0 .414-.536l4.976 1.136a8.03 8.03 0 0 1-3.176 3.998"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.k8sInfo') }}</span>
                    </router-link>
                </a-menu-item>
            </a-sub-menu>

            <!-- File Transfer 分组 -->
            <a-sub-menu key="fileTransfer">
                <template slot="title" slot-scope="{ open }">
                    <span class="enhanced-title">{{ $t('sidebar.fileTransfer') }}</span>
                </template>
                <a-menu-item key="file-upload">
                    <router-link to="/upload">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" height="20" width="20">
                            <path d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.fileUpload') }}</span>
                    </router-link>
                </a-menu-item>
                <a-menu-item key="file-download">
                    <router-link to="/download">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" height="20" width="20">
                            <path d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39L344 184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 134.1-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.fileDown') }}</span>
                    </router-link>
                </a-menu-item>
            </a-sub-menu>

            <!-- AI Bash 分组 -->
            <a-sub-menu key="aiBash">
                <template slot="title" slot-scope="{ open }">
                    <span class="enhanced-title">{{ $t('sidebar.aiBash') }}</span>
                </template>
                <a-menu-item key="ai">
                    <router-link to="/aiBash">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" height="20" width="20">
                            <path d="M9.4 86.6C-3.1 74.1-3.1 53.9 9.4 41.4s32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L178.7 256 9.4 86.6zM256 416l288 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-288 0c-17.7 0-32-14.3-32-32s14.3-32 32-32z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.aiBash') }}</span>
                    </router-link>
                </a-menu-item>
            </a-sub-menu>

            <!-- Test Cases 分组 -->
            <a-sub-menu key="testCases">
                <template slot="title" slot-scope="{ open }">
                    <span class="enhanced-title">{{ $t('sidebar.testCases') }}</span>
                </template>
                <a-menu-item key="case-list">
                    <router-link to="/testcase">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" height="20" width="20">
                            <path d="M384 96l0 128-128 0 0-128 128 0zm0 192l0 128-128 0 0-128 128 0zM192 224L64 224 64 96l128 0 0 128zM64 288l128 0 0 128L64 416l0-128zM64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.testCase') }}</span>
                    </router-link>
                </a-menu-item>
            </a-sub-menu>

            <!-- Smart Analysis 分组 -->
            <a-sub-menu key="smartAnalysis">
                <template slot="title" slot-scope="{ open }">
                    <span class="enhanced-title">{{ $t('sidebar.smartAnalysis') }}</span>
                </template>
                <a-menu-item key="smart-orchestration">
                    <router-link to="/smart-orchestration">
                        <span class="icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" height="20" width="20">
                            <path d="M320 0c17.7 0 32 14.3 32 32l0 64 120 0c39.8 0 72 32.2 72 72l0 272c0 39.8-32.2 72-72 72l-464 0c-39.8 0-72-32.2-72-72L-64 168c0-39.8 32.2-72 72-72l120 0 0-64c0-17.7 14.3-32 32-32l160 0zM208 128c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zm96 0c-8.8 0-16 7.2-16 16l0 64c0 8.8 7.2 16 16 16l32 0c8.8 0 16-7.2 16-16l0-64c0-8.8-7.2-16-16-16l-32 0zM264 256a40 40 0 1 0 -80 0 40 40 0 1 0 80 0zm152 40a40 40 0 1 0 0-80 40 40 0 1 0 0 80zM48 224l16 0 0 192-16 0c-26.5 0-48-21.5-48-48l0-96c0-26.5 21.5-48 48-48zm544 0c26.5 0 48 21.5 48 48l0 96c0 26.5-21.5 48-48 48l-16 0 0-192 16 0z"/>
                          </svg>
                        </span>
                        <span class="label">{{ $t('sidebar.smartOrchestration') }}</span>
                    </router-link>
                </a-menu-item>
            </a-sub-menu>

                </a-menu>
            </div>

            <!-- 在侧边栏底部添加动画组件 -->
            <div class="footer-section">
                <footer-animation class="sidebar-footer"></footer-animation>
            </div>
        </div>
    </a-layout-sider>
    <!-- / Main Sidebar -->
</template>

<script>
import {mapState} from "vuex";
import FooterAnimation from '../Widgets/FooterAnimation.vue';

export default {
    components: {
        FooterAnimation
    },
    computed: {
        ...mapState(['selectedNodeIp', 'currentProject']),
    },
    props: {
        // Sidebar collapsed status.
        sidebarCollapsed: {
            type: Boolean,
            default: false,
        },

        // Main sidebar color.
        sidebarColor: {
            type: String,
            default: "primary",
        },

        // Main sidebar theme : light, white, dark.
        sidebarTheme: {
            type: String,
            default: "light",
        },
    },
    data() {
        return {};
    }
}

</script>

<style scoped lang="scss">
.enhanced-title {
  font-size: 15px;
  font-weight: 600;
  position: relative;
  padding-left: 2px;

  &::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 0;
    border-radius: 3px;
    opacity: 0;
  }

  &:hover::before {
    height: 60%;
    opacity: 1;
  }
}

.ios-logo {
  width: 36px;
  height: 36px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigation-section {
  padding-top: 10px;

  // 添加滚动条美化
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(140, 140, 140, 0.4); /* 使用淡灰色，透明度40% */
    border-radius: 2px; /* 减小圆角半径，与更窄的滚动条匹配 */

    &:hover {
      background-color: rgba(140, 140, 140, 0.6); /* 悬停时透明度增加到60% */
    }
  }
}
</style>
