{"version": 3, "sources": ["webpack:///./src/views/PackageInfo.vue", "webpack:///./src/components/Cards/PackageInfo.vue", "webpack:///src/components/Cards/PackageInfo.vue", "webpack:///./src/components/Cards/PackageInfo.vue?5bd4", "webpack:///./src/components/Cards/PackageInfo.vue?106c", "webpack:///src/views/PackageInfo.vue", "webpack:///./src/views/PackageInfo.vue?f772", "webpack:///./src/views/PackageInfo.vue?e456", "webpack:///./src/components/Cards/PackageInfo.vue?fbf9", "webpack:///./src/components/Widgets/RefreshButton.vue", "webpack:///src/components/Widgets/RefreshButton.vue", "webpack:///./src/components/Widgets/RefreshButton.vue?9cc7", "webpack:///./src/components/Widgets/RefreshButton.vue?7be6"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticClass", "staticRenderFns", "padding", "borderBottom", "scopedSlots", "_u", "key", "fn", "class", "sidebarColor", "_v", "_s", "$t", "on", "fetchPackages", "proxy", "columns", "packages", "record", "package_name", "pagination", "column", "package_type", "_e", "components", "RefreshButton", "data", "title", "dataIndex", "pageSize", "computed", "mapState", "watch", "selectedNodeIp", "newIp", "mounted", "methods", "console", "log", "response", "axios", "get", "params", "dbFile", "currentProject", "error", "component", "PackageInfo", "$event", "$emit", "text", "name", "props", "type", "String", "default"], "mappings": "kJAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,OAAO,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACG,YAAY,QAAQD,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,gBAAgB,IAAI,IAAI,IAExMI,EAAkB,GCFlBP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,SAAS,CAACG,YAAY,mCAAmCD,MAAM,CAAC,UAAW,EAAM,UAAY,CAAEG,QAAS,GAAI,UAAY,CAAEC,aAAc,sBAAuBC,YAAYT,EAAIU,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACV,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,MAAM,CAACW,MAAM,QAAQb,EAAIc,aAAeV,MAAM,CAAC,MAAQ,6BAA6B,MAAQ,KAAK,OAAS,KAAK,QAAU,cAAc,CAACF,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,eAAe,YAAY,UAAU,EAAI,qNAAqN,YAAY,iBAAiBF,EAAG,KAAK,CAACG,YAAY,qBAAqB,CAACL,EAAIe,GAAGf,EAAIgB,GAAGhB,EAAIiB,GAAG,2BAA2Bf,EAAG,MAAM,CAACA,EAAG,gBAAgB,CAACgB,GAAG,CAAC,QAAUlB,EAAImB,kBAAkB,OAAOC,OAAM,MAAS,CAAClB,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIqB,QAAQ,cAAcrB,EAAIsB,SAAS,OAAUC,GAAWA,EAAOC,aAAa,WAAaxB,EAAIyB,YAAYhB,YAAYT,EAAIU,GAAG,CAAC,CAACC,IAAI,WAAWC,GAAG,UAAS,OAAEc,EAAM,OAAEH,IAAU,MAAO,CAAiB,iBAAfG,EAAOf,IAAwB,CAACT,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,OAAO,CAACF,EAAIe,GAAGf,EAAIgB,GAAGO,EAAOC,iBAAiBtB,EAAG,OAAO,CAACF,EAAIe,GAAGf,EAAIgB,GAAGO,EAAOI,oBAAoC,WAAfD,EAAOf,IAAkB,CAACT,EAAG,WAAW,CAACG,YAAY,WAAWD,MAAM,CAAC,KAAO,SAAS,CAACJ,EAAIe,GAAG,WAAWf,EAAI4B,aAAa,IAEhhDtB,EAAkB,G,oCCoDP,GACfuB,WAAA,CACAC,sBAEAC,OACA,OACAT,SAAA,GACAD,QAAA,CACA,CACAW,MAAA,eACAC,UAAA,eACAtB,IAAA,gBAEA,CACAqB,MAAA,eACAC,UAAA,eACAtB,IAAA,iBAGAc,WAAA,CACAS,SAAA,OAIAC,SAAA,IACAC,eAAA,qDAEAC,MAAA,CACAC,eAAAC,GAEA,KAAApB,kBAGAqB,UACA,KAAArB,iBAEAsB,QAAA,CACA,sBAEA,GADAC,QAAAC,IAAA,yBAAAL,gBACA,KAAAA,eAIA,IACA,MAAAM,QAAAC,OAAAC,IAAA,sBAAAR,eAAA,CACAS,OAAA,CACAC,OAAA,KAAAC,kBAGA,KAAA3B,SAAAsB,EAAAb,KACA,MAAAmB,GACAR,QAAAQ,MAAA,2BAAAA,QAXAR,QAAAQ,MAAA,6BC9FmW,I,wBCQ/VC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCJA,GACftB,WAAA,CACAuB,gBCjBoV,ICOhV,EAAY,eACd,EACArD,EACAO,GACA,EACA,KACA,KACA,MAIa,e,2CClBf,W,kCCAA,IAAIP,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACW,MAAM,CAAC,iBAAkB,QAAQb,EAAIc,cAAgBV,MAAM,CAAC,KAAO,UAAUc,GAAG,CAAC,MAAQ,SAASmC,GAAQ,OAAOrD,EAAIsD,MAAM,cAAc,CAACtD,EAAIe,GAAG,IAAIf,EAAIgB,GAAGhB,EAAIuD,MAAQvD,EAAIiB,GAAG,mBAAmB,QAEhRX,EAAkB,G,YCWP,GACf6B,SAAA,IACAC,eAAA,mBAEAoB,KAAA,gBACAC,MAAA,CACAF,KAAA,CACAG,KAAAC,OACAC,QAAA,MCrBqW,I,YCOjWT,EAAY,eACd,EACApD,EACAO,GACA,EACA,KACA,WACA,MAIa,OAAA6C,E", "file": "static/js/chunk-d588eafa.339ed414.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-row',{attrs:{\"type\":\"flex\",\"gutter\":24}},[_c('a-col',{staticClass:\"mb-24\",attrs:{\"span\":24}},[_c('PackageInfo')],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-card',{staticClass:\"header-solid h-full package-card\",attrs:{\"bordered\":false,\"bodyStyle\":{ padding: 0 },\"headStyle\":{ borderBottom: '1px solid #e8e8e8' }},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('svg',{class:`text-${_vm.sidebarColor}`,attrs:{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 16 16\"}},[_c('path',{attrs:{\"fill\":\"currentColor\",\"fill-rule\":\"evenodd\",\"d\":\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\",\"clip-rule\":\"evenodd\"}})])]),_c('h6',{staticClass:\"font-semibold m-0\"},[_vm._v(_vm._s(_vm.$t('headTopic.package')))])]),_c('div',[_c('RefreshButton',{on:{\"refresh\":_vm.fetchPackages}})],1)])]},proxy:true}])},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.packages,\"rowKey\":(record) => record.package_name,\"pagination\":_vm.pagination},scopedSlots:_vm._u([{key:\"bodyCell\",fn:function({ column, record }){return [(column.key === 'package_name')?[_c('div',{staticClass:\"table-package-info\"},[_c('span',[_vm._v(_vm._s(record.package_name))]),_c('span',[_vm._v(_vm._s(record.package_type))])])]:(column.key === 'action')?[_c('a-button',{staticClass:\"btn-edit\",attrs:{\"type\":\"link\"}},[_vm._v(\"Edit\")])]:_vm._e()]}}])})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <!-- Packages Table Card -->\r\n  <a-card\r\n    :bordered=\"false\"\r\n    class=\"header-solid h-full package-card\"\r\n    :bodyStyle=\"{ padding: 0 }\"\r\n    :headStyle=\"{ borderBottom: '1px solid #e8e8e8' }\"\r\n  >\r\n\r\n    <template #title>\r\n      <div class=\"card-header-wrapper\">\r\n        <div class=\"header-wrapper\">\r\n          <div class=\"logo-wrapper\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 16 16\" :class=\"`text-${sidebarColor}`\">\r\n                <path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M5.72 2.5L2.92 6h4.33V2.5zm3.03 0V6h4.33l-2.8-3.5zm-6.25 11v-6h11v6zM5.48 1a1 1 0 0 0-.78.375L1.22 5.726a1 1 0 0 0-.22.625V14a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6.35a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 10.52 1z\" clip-rule=\"evenodd\"/>\r\n              </svg>\r\n          </div>\r\n          <h6 class=\"font-semibold m-0\">{{ $t('headTopic.package') }}</h6>\r\n        </div>\r\n        <div>\r\n          <RefreshButton @refresh=\"fetchPackages\" />\r\n        </div>\r\n      </div>\r\n    </template>\r\n\r\n\r\n\r\n    <a-table\r\n      :columns=\"columns\"\r\n      :data-source=\"packages\"\r\n      :rowKey=\"(record) => record.package_name\"\r\n      :pagination=\"pagination\"\r\n    >\r\n       <template #bodyCell=\"{ column, record }\">\r\n        <template v-if=\"column.key === 'package_name'\">\r\n          <div class=\"table-package-info\">\r\n            <span>{{ record.package_name }}</span>\r\n            <span>{{ record.package_type }}</span>\r\n          </div>\r\n        </template>\r\n        <template v-else-if=\"column.key === 'action'\">\r\n          <a-button type=\"link\" class=\"btn-edit\">Edit</a-button>\r\n        </template>\r\n      </template>\r\n    </a-table>\r\n  </a-card>\r\n  <!-- / Packages Table Card -->\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\nimport RefreshButton from '../Widgets/RefreshButton.vue';\r\n\r\nexport default {\r\n  components: {\r\n    RefreshButton\r\n  },\r\n  data() {\r\n    return {\r\n      packages: [],\r\n      columns: [\r\n        {\r\n          title: 'Package Name',\r\n          dataIndex: 'package_name',\r\n          key: 'package_name',\r\n        },\r\n        {\r\n          title: 'Package Type',\r\n          dataIndex: 'package_type',\r\n          key: 'package_type',\r\n        },\r\n      ],\r\n      pagination: {\r\n        pageSize: 100,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n  },\r\n  watch: {\r\n    selectedNodeIp(newIp) {\r\n      // 当 selectedNodeIp 变化时重新获取包数据\r\n      this.fetchPackages();\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchPackages(); // 初始加载时调用\r\n  },\r\n  methods: {\r\n    async fetchPackages() {\r\n      console.log('Selected Node IP:', this.selectedNodeIp);\r\n      if (!this.selectedNodeIp) {\r\n        console.error('Node IP is not defined');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await axios.get(`/api/packages/${this.selectedNodeIp}`, {\r\n          params: {\r\n            dbFile: this.currentProject // 添加 dbFile 参数\r\n          }\r\n        });\r\n        this.packages = response.data;\r\n      } catch (error) {\r\n        console.error('Error fetching packages:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.package-card {\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\r\n  border-radius: 8px;\r\n}\r\n\r\n.ant-table {\r\n  border-radius: 0 0 8px 8px; /* 匹配卡片圆角 */\r\n}\r\n\r\n/* 表头样式 */\r\n.ant-table-thead > tr > th {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #f0f0f0; /* 浅色分割线 */\r\n  color: #666;\r\n}\r\n\r\n/* 悬停效果 */\r\n.ant-table-tbody > tr:hover > td {\r\n  background-color: #fafafa !important;\r\n}\r\n\r\n.table-package-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PackageInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PackageInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PackageInfo.vue?vue&type=template&id=28c58e84&scoped=true\"\nimport script from \"./PackageInfo.vue?vue&type=script&lang=js\"\nexport * from \"./PackageInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./PackageInfo.vue?vue&type=style&index=0&id=28c58e84&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28c58e84\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<div>\r\n\t\t<a-row type=\"flex\" :gutter=\"24\">\r\n\t\t\t<a-col :span=\"24\" class=\"mb-24\">\r\n\t\t\t\t<PackageInfo></PackageInfo>\r\n\t\t\t</a-col>\r\n\t\t\t<!-- / Your Transactions Column -->\r\n\t\t</a-row>\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport PackageInfo from \"@/components/Cards/PackageInfo.vue\";\r\n\r\nexport default {\r\n    components: {\r\n        PackageInfo,\r\n    },\r\n};\r\n</script>\r\n", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PackageInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PackageInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PackageInfo.vue?vue&type=template&id=7194b7a2\"\nimport script from \"./PackageInfo.vue?vue&type=script&lang=js\"\nexport * from \"./PackageInfo.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PackageInfo.vue?vue&type=style&index=0&id=28c58e84&prod&scoped=true&lang=scss\"", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-button',{class:['refresh-button', `text-${_vm.sidebarColor}`],attrs:{\"icon\":\"reload\"},on:{\"click\":function($event){return _vm.$emit('refresh')}}},[_vm._v(\" \"+_vm._s(_vm.text || _vm.$t('common.refresh'))+\" \")])\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-button\r\n    icon=\"reload\"\r\n    @click=\"$emit('refresh')\"\r\n    :class=\"['refresh-button', `text-${sidebarColor}`]\"\r\n  >\r\n    {{ text || $t('common.refresh') }}\r\n  </a-button>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n    computed: {\r\n    ...mapState(['sidebarColor']),\r\n  },\r\n  name: 'RefreshButton',\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./RefreshButton.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./RefreshButton.vue?vue&type=template&id=80cb1374&scoped=true\"\nimport script from \"./RefreshButton.vue?vue&type=script&lang=js\"\nexport * from \"./RefreshButton.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"80cb1374\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}