-- 代码仓配置表
CREATE TABLE IF NOT EXISTS code_repository (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    microservice_name VARCHAR(255) NOT NULL,
    repository_url VARCHAR(500) NOT NULL,
    branch_name VARCHAR(255) NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_code_repository_microservice ON code_repository(microservice_name);
CREATE INDEX IF NOT EXISTS idx_code_repository_url ON code_repository(repository_url);
