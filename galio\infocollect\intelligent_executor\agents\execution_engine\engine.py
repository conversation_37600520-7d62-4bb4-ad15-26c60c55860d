"""
执行引擎 - 负责执行分析生成的命令和操作
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI

from infocollect.log.logger import log_info, log_error, log_debug


class ExecutionEngine:
    """
    执行引擎
    
    主要功能:
    1. 命令执行
    2. 结果收集
    3. 错误处理
    4. 执行监控
    """
    
    def __init__(self, llm: Optional[ChatOpenAI] = None):
        """
        初始化执行引擎
        
        Args:
            llm: LangChain LLM实例
        """
        self.llm = llm or ChatOpenAI(temperature=0.1)
        self.execution_history: List[Dict[str, Any]] = []
        
        log_info("ExecutionEngine initialized")

    async def execute_commands(self, commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        执行命令列表
        
        Args:
            commands: 命令列表
            
        Returns:
            执行结果
        """
        try:
            log_info(f"Starting execution of {len(commands)} commands")
            
            results = []
            total_start_time = time.time()
            
            for i, command in enumerate(commands):
                log_debug(f"Executing command {i+1}/{len(commands)}: {command.get('description', 'Unknown')}")
                
                result = await self._execute_single_command(command)
                results.append(result)
            
            total_duration = time.time() - total_start_time
            
            execution_summary = {
                "total_commands": len(commands),
                "executed_commands": len(results),
                "successful_commands": sum(1 for r in results if r.get('success', False)),
                "failed_commands": sum(1 for r in results if not r.get('success', False)),
                "total_duration": total_duration,
                "results": results
            }
            
            # 保存到执行历史
            self.execution_history.append(execution_summary)
            
            log_info(f"Execution completed: {execution_summary['successful_commands']}/{execution_summary['executed_commands']} successful")
            return execution_summary
            
        except Exception as e:
            log_error(f"Error in command execution: {e}")
            return {
                "total_commands": len(commands) if commands else 0,
                "executed_commands": 0,
                "successful_commands": 0,
                "failed_commands": 0,
                "total_duration": 0,
                "error": str(e),
                "results": []
            }

    async def _execute_single_command(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个命令
        
        Args:
            command: 命令信息
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 模拟命令执行
            await asyncio.sleep(0.1)  # 模拟执行时间
            
            duration = time.time() - start_time
            
            return {
                'command': command,
                'success': True,
                'output': f"Command executed: {command.get('description', 'Unknown')}",
                'error': '',
                'duration': duration,
                'timestamp': time.time()
            }
            
        except Exception as e:
            duration = time.time() - start_time
            log_error(f"Error executing command: {e}")
            
            return {
                'command': command,
                'success': False,
                'output': '',
                'error': str(e),
                'duration': duration,
                'timestamp': time.time()
            }

    def get_execution_history(self) -> List[Dict[str, Any]]:
        """获取执行历史"""
        return self.execution_history.copy()

    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        if not self.execution_history:
            return {
                "total_executions": 0,
                "total_commands": 0,
                "success_rate": 0.0,
                "average_duration": 0.0
            }
        
        total_executions = len(self.execution_history)
        total_commands = sum(h.get('total_commands', 0) for h in self.execution_history)
        successful_commands = sum(h.get('successful_commands', 0) for h in self.execution_history)
        total_duration = sum(h.get('total_duration', 0) for h in self.execution_history)
        
        return {
            "total_executions": total_executions,
            "total_commands": total_commands,
            "successful_commands": successful_commands,
            "failed_commands": total_commands - successful_commands,
            "success_rate": successful_commands / total_commands if total_commands > 0 else 0.0,
            "average_duration": total_duration / total_executions if total_executions > 0 else 0.0
        }

    async def dry_run_commands(self, commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        干运行命令（不实际执行）
        
        Args:
            commands: 命令列表
            
        Returns:
            干运行结果
        """
        try:
            log_info(f"Dry run for {len(commands)} commands")
            
            analysis_results = []
            
            for i, command in enumerate(commands):
                analysis = {
                    'command_index': i,
                    'command': command,
                    'estimated_duration': command.get('estimated_time', 5),
                    'risk_level': self._assess_command_risk(command),
                    'dependencies': command.get('dependencies', []),
                    'validation_available': 'expected_result' in command
                }
                analysis_results.append(analysis)
            
            total_estimated_time = sum(a['estimated_duration'] for a in analysis_results)
            high_risk_commands = sum(1 for a in analysis_results if a['risk_level'] == 'high')
            
            return {
                'total_commands': len(commands),
                'total_estimated_time': total_estimated_time,
                'high_risk_commands': high_risk_commands,
                'analysis_results': analysis_results,
                'recommendations': self._generate_execution_recommendations(analysis_results)
            }
            
        except Exception as e:
            log_error(f"Error in dry run: {e}")
            return {'error': str(e)}

    def _assess_command_risk(self, command: Dict[str, Any]) -> str:
        """评估命令风险等级"""
        command_content = command.get('command', '').lower()
        
        # 高风险关键词
        high_risk_keywords = ['rm ', 'delete', 'drop', 'truncate', 'format', 'kill']
        
        # 中风险关键词
        medium_risk_keywords = ['modify', 'update', 'install', 'uninstall', 'restart']
        
        if any(keyword in command_content for keyword in high_risk_keywords):
            return 'high'
        elif any(keyword in command_content for keyword in medium_risk_keywords):
            return 'medium'
        else:
            return 'low'

    def _generate_execution_recommendations(self, analysis_results: List[Dict[str, Any]]) -> List[str]:
        """生成执行建议"""
        recommendations = []
        
        high_risk_count = sum(1 for a in analysis_results if a['risk_level'] == 'high')
        if high_risk_count > 0:
            recommendations.append(f"发现 {high_risk_count} 个高风险命令，建议谨慎执行")
        
        total_time = sum(a['estimated_duration'] for a in analysis_results)
        if total_time > 300:  # 5分钟
            recommendations.append(f"预估执行时间较长（{total_time/60:.1f}分钟），建议分批执行")
        
        commands_without_validation = sum(1 for a in analysis_results if not a['validation_available'])
        if commands_without_validation > 0:
            recommendations.append(f"{commands_without_validation} 个命令缺少验证，建议添加预期结果")
        
        return recommendations 