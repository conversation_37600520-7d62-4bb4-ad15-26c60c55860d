{"version": 3, "sources": ["webpack:///./src/components/Cards/ProcessDetail.vue", "webpack:///src/components/Cards/ProcessDetail.vue", "webpack:///./src/components/Cards/ProcessDetail.vue?c7ce", "webpack:///./src/components/Cards/ProcessDetail.vue?9fc9", "webpack:///./src/components/Cards/ProcessDetail.vue?995e"], "names": ["render", "_vm", "this", "_c", "_self", "currentProject", "staticStyle", "class", "sidebarColor", "attrs", "on", "goToProcessList", "_v", "goBack", "staticClass", "paddingTop", "paddingBottom", "scopedSlots", "_u", "key", "fn", "_s", "pid", "proxy", "_l", "orderedFields", "field", "_vm$processDetails", "fieldDescriptions", "processDetails", "goToProjects", "staticRenderFns", "data", "$route", "params", "fromPage", "query", "page", "computed", "mapState", "watch", "immediate", "handler", "newVal", "fetchProcessDetails", "selectedNodeIp", "methods", "response", "axios", "get", "fields", "join", "dbFile", "error", "console", "$message", "$router", "push", "name", "currentPid", "$store", "dispatch", "toString", "component"], "mappings": "yHAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAAEF,EAAII,eAAgB,CAACF,EAAG,MAAM,CAACG,YAAY,CAAC,gBAAgB,SAAS,CAACH,EAAG,WAAW,CAACI,MAAM,MAAMN,EAAIO,aAAeF,YAAY,CAAC,eAAe,OAAO,MAAQ,SAASG,MAAM,CAAC,KAAO,cAAcC,GAAG,CAAC,MAAQT,EAAIU,kBAAkB,CAACV,EAAIW,GAAG,uBAAuBT,EAAG,WAAW,CAACI,MAAM,MAAMN,EAAIO,aAAeF,YAAY,CAAC,eAAe,OAAO,MAAQ,SAASG,MAAM,CAAC,KAAO,cAAcC,GAAG,CAAC,MAAQT,EAAIY,SAAS,CAACZ,EAAIW,GAAG,qBAAqB,GAAGT,EAAG,SAAS,CAACW,YAAY,sBAAsBL,MAAM,CAAC,UAAW,EAAM,UAAY,CAACM,WAAY,OAAQC,cAAe,SAASC,YAAYhB,EAAIiB,GAAG,CAAC,CAACC,IAAI,QAAQC,GAAG,WAAW,MAAO,CAACjB,EAAG,MAAM,CAACW,YAAY,uBAAuB,CAACX,EAAG,MAAM,CAACW,YAAY,kBAAkB,CAACX,EAAG,MAAM,CAACW,YAAY,gBAAgB,CAACX,EAAG,SAAS,CAACI,MAAM,QAAQN,EAAIO,aAAeF,YAAY,CAAC,YAAY,QAAQG,MAAM,CAAC,KAAO,cAAc,GAAGN,EAAG,KAAK,CAACW,YAAY,cAAc,CAACb,EAAIW,GAAG,2BAA2BX,EAAIoB,GAAGpB,EAAIqB,cAAcC,OAAM,IAAO,MAAK,EAAM,aAAa,CAACpB,EAAG,QAAQ,CAACM,MAAM,CAAC,OAAS,CAAC,GAAI,MAAM,CAACN,EAAG,QAAQ,CAACM,MAAM,CAAC,KAAO,KAAK,CAACN,EAAG,MAAM,CAACW,YAAY,mBAAmBb,EAAIuB,GAAIvB,EAAIwB,eAAe,SAASC,GAAM,IAAAC,EAAC,OAAOxB,EAAG,MAAM,CAACgB,IAAIO,EAAMZ,YAAY,iBAAiB,CAACX,EAAG,YAAY,CAACM,MAAM,CAAC,UAAY,QAAQ,MAAQR,EAAI2B,kBAAkBF,EAAQ,OAAO,CAACvB,EAAG,OAAO,CAACW,YAAY,eAAe,CAACb,EAAIW,GAAGX,EAAIoB,GAAGK,GAAO,OAAOvB,EAAG,OAAO,CAACW,YAAY,eAAe,CAACb,EAAIW,GAAGX,EAAIoB,IAAqB,QAAlBM,EAAA1B,EAAI4B,sBAAc,IAAAF,OAAA,EAAlBA,EAAqBD,KAAU,aAAa,MAAK,MAAM,IAAI,IAAI,CAACvB,EAAG,UAAU,CAACM,MAAM,CAAC,QAAU,sBAAsB,YAAc,yDAAyD,KAAO,UAAU,YAAY,IAAIQ,YAAYhB,EAAIiB,GAAG,CAAC,CAACC,IAAI,cAAcC,GAAG,WAAW,MAAO,CAACnB,EAAIW,GAAG,4DAA4DT,EAAG,WAAW,CAACM,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,MAAQT,EAAI6B,eAAe,CAAC7B,EAAIW,GAAG,sBAAsBW,OAAM,SAAY,IAEr6DQ,EAAkB,G,wBCoEP,GACfC,OACA,OACAV,IAAA,KAAAW,OAAAC,OAAAZ,IACAO,eAAA,KACAM,SAAA,KAAAF,OAAAG,MAAAC,MAAA,EACAZ,cAAA,CACA,oCACA,kDAEAG,kBAAA,CACA,8BACA,gCACA,kCACA,mCACA,4BACA,gCACA,0BACA,8CACA,mCACA,yCAIAU,SAAA,IACAC,eAAA,qDAEAC,MAAA,CACAnC,eAAA,CACAoC,WAAA,EACAC,QAAAC,GACAA,GACA,KAAAC,wBAIAC,eAAA,CACAJ,WAAA,EACAC,QAAAC,GACAA,GAAA,KAAAtC,gBACA,KAAAuC,yBAKAE,QAAA,CACA,4BACA,QAAAD,gBAAA,KAAAxC,eAIA,IACA,MAAA0C,QAAAC,OAAAC,IAAA,uBAAAJ,eAAA,CACAX,OAAA,CACAZ,IAAA,KAAAA,IACA4B,OAAA,KAAAzB,cAAA0B,KAAA,KACAC,OAAA,KAAA/C,kBAGA,KAAAwB,eAAAkB,EAAAf,KACA,MAAAqB,GACAC,QAAAD,MAAA,kCAAAA,GACA,KAAAE,SAAAF,MAAA,wCAdAC,QAAAD,MAAA,sCAiBAxC,SACA,KAAA2C,QAAAC,KAAA,CACAC,KAAA,OACAtB,MAAA,CAAAC,KAAA,KAAAF,aAGAxB,kBAEA,MAAAgD,EAAA,KAAA1B,OAAAC,OAAAZ,IAGA,KAAAsC,OAAAC,SAAA,kCAAAF,EAAAG,YAGA,KAAAN,QAAAC,KAAA,CAAAC,KAAA,aAEA5B,eACA,KAAA0B,QAAAC,KAAA,gBCxJqW,I,wBCQjWM,EAAY,eACd,EACA/D,EACA+B,GACA,EACA,KACA,WACA,MAIa,aAAAgC,E,2CCnBf,W", "file": "static/js/chunk-05936751.874a18d6.js", "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[(_vm.currentProject)?[_c('div',{staticStyle:{\"margin-bottom\":\"16px\"}},[_c('a-button',{class:`bg-${_vm.sidebarColor}`,staticStyle:{\"margin-right\":\"16px\",\"color\":\"white\"},attrs:{\"icon\":\"arrow-left\"},on:{\"click\":_vm.goToProcessList}},[_vm._v(\" Back to Process \")]),_c('a-button',{class:`bg-${_vm.sidebarColor}`,staticStyle:{\"margin-right\":\"16px\",\"color\":\"white\"},attrs:{\"icon\":\"arrow-left\"},on:{\"click\":_vm.goBack}},[_vm._v(\" Back to Port \")])],1),_c('a-card',{staticClass:\"header-solid h-full\",attrs:{\"bordered\":false,\"bodyStyle\":{paddingTop: '16px', paddingBottom: '16px'}},scopedSlots:_vm._u([{key:\"title\",fn:function(){return [_c('div',{staticClass:\"card-header-wrapper\"},[_c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"logo-wrapper\"},[_c('a-icon',{class:`text-${_vm.sidebarColor}`,staticStyle:{\"font-size\":\"18px\"},attrs:{\"type\":\"profile\"}})],1),_c('h5',{staticClass:\"card-title\"},[_vm._v(\"Process details for pid \"+_vm._s(_vm.pid))])])])]},proxy:true}],null,false,2952082604)},[_c('a-row',{attrs:{\"gutter\":[24, 24]}},[_c('a-col',{attrs:{\"span\":24}},[_c('div',{staticClass:\"process-details\"},_vm._l((_vm.orderedFields),function(field){return _c('div',{key:field,staticClass:\"process-field\"},[_c('a-tooltip',{attrs:{\"placement\":\"right\",\"title\":_vm.fieldDescriptions[field + ':']}},[_c('span',{staticClass:\"field-label\"},[_vm._v(_vm._s(field)+\":\")]),_c('span',{staticClass:\"field-value\"},[_vm._v(_vm._s(_vm.processDetails?.[field] || 'N/A'))])])],1)}),0)])],1)],1)]:[_c('a-alert',{attrs:{\"message\":\"No Project Selected\",\"description\":\"Please select a project first to view process details.\",\"type\":\"warning\",\"show-icon\":\"\"},scopedSlots:_vm._u([{key:\"description\",fn:function(){return [_vm._v(\" Please select a project first to view process details. \"),_c('a-button',{attrs:{\"type\":\"link\"},on:{\"click\":_vm.goToProjects}},[_vm._v(\"Go to Projects\")])]},proxy:true}])})]],2)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div>\r\n        <template v-if=\"currentProject\">\r\n            <div style=\"margin-bottom: 16px;\">\r\n                <a-button\r\n                    :class=\"`bg-${sidebarColor}`\"\r\n                    icon=\"arrow-left\"\r\n                    @click=\"goToProcessList\"\r\n                    style=\"margin-right: 16px; color: white\"\r\n                >\r\n                    Back to Process\r\n                </a-button>\r\n                <a-button\r\n                    :class=\"`bg-${sidebarColor}`\"\r\n                    icon=\"arrow-left\"\r\n                    @click=\"goBack\"\r\n                    style=\"margin-right: 16px; color: white\"\r\n                >\r\n                    Back to Port\r\n                </a-button>\r\n            </div>\r\n            <a-card :bordered=\"false\" class=\"header-solid h-full\" :bodyStyle=\"{paddingTop: '16px', paddingBottom: '16px'}\">\r\n                <template #title>\r\n                    <div class=\"card-header-wrapper\">\r\n                        <div class=\"header-wrapper\">\r\n                            <div class=\"logo-wrapper\">\r\n                                <a-icon\r\n                                    type=\"profile\"\r\n                                    style=\"font-size: 18px\"\r\n                                    :class=\"`text-${sidebarColor}`\"\r\n                                />\r\n                            </div>\r\n                            <h5 class=\"card-title\">Process details for pid {{ pid }}</h5>\r\n                        </div>\r\n                    </div>\r\n                </template>\r\n                <a-row :gutter=\"[24, 24]\">\r\n                    <a-col :span=\"24\">\r\n                        <div class=\"process-details\">\r\n                            <div v-for=\"field in orderedFields\" :key=\"field\" class=\"process-field\">\r\n                                <a-tooltip placement=\"right\" :title=\"fieldDescriptions[field + ':']\">\r\n                                    <span class=\"field-label\">{{field}}:</span>\r\n                                    <span class=\"field-value\">{{processDetails?.[field] || 'N/A'}}</span>\r\n                                </a-tooltip>\r\n                            </div>\r\n                        </div>\r\n                    </a-col>\r\n                </a-row>\r\n            </a-card>\r\n        </template>\r\n        <template v-else>\r\n            <a-alert\r\n                message=\"No Project Selected\"\r\n                description=\"Please select a project first to view process details.\"\r\n                type=\"warning\"\r\n                show-icon\r\n            >\r\n                <template #description>\r\n                    Please select a project first to view process details.\r\n                    <a-button type=\"link\" @click=\"goToProjects\">Go to Projects</a-button>\r\n                </template>\r\n            </a-alert>\r\n        </template>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nimport axios from '@/api/axiosInstance';\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            pid: this.$route.params.pid,\r\n            processDetails: null,\r\n            fromPage: this.$route.query.page || 1,\r\n            orderedFields: [\r\n                'pid', 'uid', 'gid', 'cmdline', 'state',\r\n                'exe', 'cwd', 'capability', 'environ', 'memory_maps'\r\n            ],\r\n            fieldDescriptions: {\r\n                'pid:': '进程唯一标识符 - 每个进程的唯一标识号',\r\n                'uid:': '用户标识符 - 启动该进程的用户的唯一标识符',\r\n                'gid:': '组标识符 - 启动该进程的用户所属组的唯一标识符',\r\n                'cmdline:': '命令行 - 启动进程时使用的完整命令行参数',\r\n                'state:': '进程状态 - 进程当前的运行状态',\r\n                'exe:': '可执行文件 - 获取指定进程的可执行文件路径',\r\n                'cwd:': '工作目录 - 进程当前工作的目录',\r\n                'capability:': '进程能力 - 与进程相关的能力信息，指示该进程具有哪些特权',\r\n                'environ:': '环境变量 - 与该进程相关联的环境变量列表',\r\n                'memory_maps:': '内存映射 - 该进程使用的内存区域的信息'\r\n            }\r\n        };\r\n    },\r\n    computed: {\r\n        ...mapState(['selectedNodeIp', 'currentProject', 'sidebarColor']),\r\n    },\r\n    watch: {\r\n        currentProject: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal) {\r\n                    this.fetchProcessDetails();\r\n                }\r\n            }\r\n        },\r\n        selectedNodeIp: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal && this.currentProject) {\r\n                    this.fetchProcessDetails();\r\n                }\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        async fetchProcessDetails() {\r\n            if (!this.selectedNodeIp || !this.currentProject) {\r\n                console.error('Node IP or project is not defined');\r\n                return;\r\n            }\r\n            try {\r\n                const response = await axios.get(`/api/processes/${this.selectedNodeIp}`, {\r\n                    params: {\r\n                        pid: this.pid,\r\n                        fields: this.orderedFields.join(','),\r\n                        dbFile: this.currentProject\r\n                    }\r\n                });\r\n                this.processDetails = response.data;\r\n            } catch (error) {\r\n                console.error('Error fetching process details:', error);\r\n                this.$message.error('Failed to fetch process details');\r\n            }\r\n        },\r\n        goBack() {\r\n            this.$router.push({\r\n                name: 'Port',\r\n                query: { page: this.fromPage }\r\n            });\r\n        },\r\n        goToProcessList() {\r\n            // 确保在返回前已经设置了lastViewedPid\r\n            const currentPid = this.$route.params.pid;\r\n\r\n            // 使用字符串类型的PID，确保类型匹配\r\n            this.$store.dispatch('processList/updateLastViewedPid', currentPid.toString());\r\n\r\n            // 返回进程列表页面\r\n            this.$router.push({ name: 'Process' });\r\n        },\r\n        goToProjects() {\r\n            this.$router.push('/projects');\r\n        }\r\n    },\r\n};\r\n</script>\r\n\r\n\r\n<style scoped lang=\"scss\">\r\n.process-details {\r\n    word-wrap: break-word;\r\n    overflow-wrap: break-word;\r\n    white-space: pre-wrap;\r\n    word-break: break-all;\r\n    background-color: #ffffff;\r\n    padding: 16px;\r\n    border-radius: 4px;\r\n    border: 1px solid #d9d9d9;\r\n}\r\n\r\n.process-field {\r\n    margin: 8px 0;\r\n    line-height: 1.5;\r\n\r\n    .field-label {\r\n        color: #d10d7d;\r\n        font-size: 0.95em;\r\n        min-width: 80px;\r\n        display: inline-block;\r\n        margin-right: 8px;\r\n    }\r\n\r\n    .field-value {\r\n        word-break: break-all;\r\n    }\r\n}\r\n\r\n.font-semibold {\r\n    display: block;\r\n    width: 100%;\r\n    padding: 10px 15px;\r\n    background: #fff;\r\n    color: #333;\r\n    font-size: 16px;\r\n    font-family: 'Arial', sans-serif;\r\n    border-radius: 6px;\r\n    margin-bottom: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n    box-shadow: none;\r\n}\r\n\r\n.field-color {\r\n    color: red !important;\r\n}\r\n\r\n.card-header-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n}\r\n\r\n.header-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.logo-wrapper {\r\n    margin-right: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.card-title {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetail.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetail.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ProcessDetail.vue?vue&type=template&id=07393566&scoped=true\"\nimport script from \"./ProcessDetail.vue?vue&type=script&lang=js\"\nexport * from \"./ProcessDetail.vue?vue&type=script&lang=js\"\nimport style0 from \"./ProcessDetail.vue?vue&type=style&index=0&id=07393566&prod&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"07393566\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProcessDetail.vue?vue&type=style&index=0&id=07393566&prod&scoped=true&lang=scss\""], "sourceRoot": ""}