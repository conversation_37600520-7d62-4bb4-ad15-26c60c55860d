// 自定义滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(140, 140, 140, 0.4); // 中性灰色，40%透明度
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 120, 0.5); // 悬停时稍深一点，透明度降低
}

// 针对侧边栏的滚动条样式
.layout-dashboard .ant-layout-sider.sider-primary .navigation-section::-webkit-scrollbar-thumb {
  background: rgba(140, 140, 140, 0.4); // 中性灰色，40%透明度
}

.layout-dashboard .ant-layout-sider.sider-primary .navigation-section::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 120, 0.5); // 悬停时稍深一点，透明度降低
}
