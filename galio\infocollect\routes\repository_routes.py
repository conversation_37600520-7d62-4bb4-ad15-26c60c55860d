#!/usr/bin/env python3
# coding: utf-8

from flask import Blueprint, request, jsonify, send_file
from services.repository_service import CodeRepositoryService
from routes.database_wraps import with_database_session
import os
import csv
from werkzeug.utils import secure_filename

bp = Blueprint('repository', __name__)

# 配置上传文件夹
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache', 'uploads')
ALLOWED_EXTENSIONS = {'csv'}


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@bp.route('/repositories', methods=['GET'])
@with_database_session
def get_repositories(db):
    """获取所有代码仓配置"""
    try:
        service = CodeRepositoryService(db)
        repositories = service.get_all_repositories()
        return jsonify({"success": True, "data": repositories})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories', methods=['POST'])
@with_database_session
def save_repositories(db):
    """保存代码仓配置"""
    try:
        data = request.get_json()
        if not data or 'repositories' not in data:
            return jsonify({"success": False, "error": "Invalid data format"}), 400

        service = CodeRepositoryService(db)
        result = service.save_repositories(data['repositories'])
        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/<int:repo_id>', methods=['DELETE'])
@with_database_session
def delete_repository(db, repo_id):
    """删除指定代码仓配置"""
    try:
        service = CodeRepositoryService(db)
        service.delete_repository(repo_id)
        return jsonify({"success": True, "message": "Repository deleted successfully"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/batch-delete', methods=['POST'])
@with_database_session
def batch_delete_repositories(db):
    """批量删除代码仓配置"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({"success": False, "error": "Invalid data format"}), 400

        service = CodeRepositoryService(db)
        service.batch_delete_repositories(data['ids'])
        return jsonify({"success": True, "message": "Repositories deleted successfully"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/export', methods=['POST'])
@with_database_session
def export_repositories(db):
    """导出选中的代码仓配置"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({"success": False, "error": "Invalid data format"}), 400

        service = CodeRepositoryService(db)
        all_repositories = service.get_all_repositories()

        # 过滤选中的代码仓
        selected_repositories = [repo for repo in all_repositories if repo['id'] in data['ids']]

        if not selected_repositories:
            return jsonify({"success": False, "error": "No repositories found for the given IDs"}), 404

        # 创建CSV文件
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        csv_filename = 'repositories_export.csv'
        csv_path = os.path.join(UPLOAD_FOLDER, csv_filename)

        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['microservice_name', 'repository_url', 'branch_name']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for repo in selected_repositories:
                writer.writerow({
                    'microservice_name': repo['microservice_name'],
                    'repository_url': repo['repository_url'],
                    'branch_name': repo['branch_name']
                })

        return send_file(csv_path, as_attachment=True, download_name=csv_filename)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/template', methods=['GET'])
def download_template():
    """下载代码仓配置模板"""
    try:
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        template_filename = 'repository_template.csv'
        template_path = os.path.join(UPLOAD_FOLDER, template_filename)

        # 创建模板文件
        with open(template_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['microservice_name', 'repository_url', 'branch_name']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # 添加两种格式的演示数据
            # 第一种格式：SSH克隆链接
            writer.writerow({
                'microservice_name': 'user-service',
                'repository_url': 'ssh://***************************:2222/CloudAppService/server/UserService.git',
                'branch_name': 'release_25.1.0.2'
            })

            # 第二种格式：HTTPS克隆链接
            writer.writerow({
                'microservice_name': 'order-service',
                'repository_url': 'https://codehub-dg-g.huawei.com/CloudAppService/server/OrderService.git',
                'branch_name': 'main'
            })

            # 第三个示例：另一个HTTPS克隆链接
            writer.writerow({
                'microservice_name': 'payment-service',
                'repository_url': 'https://codehub-dg-g.huawei.com/CloudAppService/server/PaymentService.git',
                'branch_name': 'develop'
            })

        return send_file(template_path, as_attachment=True, download_name=template_filename)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/upload', methods=['POST'])
@with_database_session
def upload_repositories(db):
    """上传代码仓配置文件"""
    try:
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "No file selected"}), 400

        if not allowed_file(file.filename):
            return jsonify({"success": False, "error": "Invalid file type. Only CSV files are allowed"}), 400

        # 保存上传的文件
        filename = secure_filename(file.filename)
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)

        # 读取CSV文件
        repositories_data = []
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if row.get('microservice_name') and row.get('repository_url') and row.get('branch_name'):
                    repositories_data.append({
                        'microservice_name': row['microservice_name'].strip(),
                        'repository_url': row['repository_url'].strip(),
                        'branch_name': row['branch_name'].strip()
                    })

        if not repositories_data:
            return jsonify({"success": False, "error": "No valid repository data found in the file"}), 400

        # 保存到数据库
        service = CodeRepositoryService(db)
        result = service.save_repositories(repositories_data)

        # 删除临时文件
        os.remove(file_path)

        successful = result.get('successful', [])
        failed = result.get('failed', [])

        return jsonify({
            "success": True,
            "message": f"Successfully imported {len(successful)} repositories, {len(failed)} failed",
            "data": {
                "successful": successful,
                "failed": failed
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/download', methods=['POST'])
@with_database_session
def download_repositories(db):
    """启动异步下载代码仓任务"""
    try:
        data = request.get_json()
        if not data or 'repositories' not in data or 'download_path' not in data:
            return jsonify({"success": False, "error": "Invalid data format"}), 400

        repositories = data['repositories']
        download_path = data['download_path']

        if not repositories:
            return jsonify({"success": False, "error": "No repositories provided"}), 400

        # 使用代码仓服务启动异步下载任务
        service = CodeRepositoryService(db)
        task_id = service.start_download_task(repositories, download_path)

        return jsonify({
            "success": True,
            "data": {
                "task_id": task_id,
                "message": "Repository download task started successfully"
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@bp.route('/repositories/download/status/<task_id>', methods=['GET'])
@with_database_session
def get_download_status(db, task_id):
    """查询代码仓下载任务状态"""
    try:
        service = CodeRepositoryService(db)
        status = service.get_download_task_status(task_id)
        
        if not status:
            return jsonify({"success": False, "error": "Task not found"}), 404
            
        return jsonify({
            "success": True,
            "data": status
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
