<template>
  <a-modal
    :visible="localVisible"
    title="Process Detailed Information"
    width="800px"
    @cancel="handleClose"
    class="process-detail-modal"
  >
    <template v-slot:footer>
      <a-button @click="handleClose">Close</a-button>
    </template>
    <template v-if="processInfo">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="Basic Info">
          <a-descriptions bordered :column="1">
            <a-descriptions-item label="PID">{{ processInfo.pid }}</a-descriptions-item>
            <a-descriptions-item label="PPID">{{ processInfo.ppid }}</a-descriptions-item>
            <a-descriptions-item label="UID">{{ processInfo[userField] || 'N/A' }}</a-descriptions-item>
            <a-descriptions-item label="GID">{{ processInfo.gid || 'N/A' }}</a-descriptions-item>
            <a-descriptions-item label="State">{{ processInfo.state || 'N/A' }}</a-descriptions-item>
            <a-descriptions-item label="Command">{{ processInfo.cmd }}</a-descriptions-item>
            <a-descriptions-item label="Executable Path">{{ processInfo.exe || 'N/A' }}</a-descriptions-item>
            <a-descriptions-item label="Working Directory">{{ processInfo.cwd || 'N/A' }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="2" tab="Capabilities" v-if="processInfo.capability">
          <pre class="detail-pre">{{ processInfo.capability }}</pre>
        </a-tab-pane>
        <a-tab-pane key="3" tab="Environment Variables" v-if="processInfo.environ">
          <pre class="detail-pre">{{ processInfo.environ }}</pre>
        </a-tab-pane>
        <a-tab-pane key="4" tab="Memory Maps" v-if="processInfo.memory_maps">
          <pre class="detail-pre">{{ processInfo.memory_maps }}</pre>
        </a-tab-pane>
      </a-tabs>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'ProcessDetailModal',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    processInfo: {
      type: Object,
      default: null
    },
    // 允许自定义用户字段名称（uid或user）
    userField: {
      type: String,
      default: 'user'
    }
  },
  data() {
    return {
      localVisible: this.visible
    };
  },
  watch: {
    visible(newValue) {
      this.localVisible = newValue;
    }
  },
  methods: {
    handleClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
}
</script>

<style scoped lang="scss">
.detail-pre {
  max-height: 300px;
  margin: 0;
  overflow-y: auto;
  background-color: transparent !important; /* 移除背景色 */
  background: none !important; /* 确保没有背景 */
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 确保Tab内容区域没有背景色 */
.ant-tabs-tabpane {
  background-color: transparent !important;
  background: none !important;
}

/* 确保Ant Design Vue的Tab内容区域没有背景色 */
.ant-tabs-content {
  background-color: transparent !important;
  background: none !important;
}

/* 确保Ant Design Vue的Tab面板没有背景色 */
.ant-tabs-tabpane-active {
  background-color: transparent !important;
  background: none !important;
}
</style>
