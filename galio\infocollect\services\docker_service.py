import json

from log.logger import log_error
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datamodel.config_datamodel import HostConfig
from datamodel.docker_datamodel import DockerHostConfig, DockerContainer, DockerNetwork


class DockerService:
    def __init__(self, db: Session):
        self.db = db

    def get_node(self, node_ip: str) -> Optional[HostConfig]:
        """根据IP获取节点"""
        return self.db.query(HostConfig).filter_by(ip=node_ip).first()

    def get_docker_host_config(self, node_id: int) -> Optional[Dict]:
        """获取指定节点的 Docker Host 配置信息"""
        host_config = self.db.query(DockerHostConfig).filter_by(node_id=node_id).first()
        if not host_config:
            return None

        # 将存储的字符串格式的daemon_config转换为字典
        try:
            daemon_config = json.loads(host_config.daemon_config)
        except (json.JSONDecodeError, TypeError):
            daemon_config = {}

        return {
            'docker_version': host_config.docker_version,
            'daemon_config': daemon_config,
            'user_in_docker_group': host_config.user_in_docker_group,
            'is_root_user': host_config.is_root_user
        }

    def get_docker_containers(self, node_id: int) -> List[Dict]:
        """获取指定节点的所有 Docker 容器信息"""
        containers = self.db.query(DockerContainer).filter_by(node_id=node_id).all()
        return [container.to_dict() for container in containers] if containers else []

    def get_docker_networks(self, node_id: int) -> List[Dict]:
        """获取指定节点的所有 Docker 网络信息，并转换为可序列化的字典"""
        networks = self.db.query(DockerNetwork).filter_by(node_id=node_id).all()
        return [network.to_dict() for network in networks] if networks else []

    @staticmethod
    def get_insert_objects_host_config(host_data: Dict, node_id: int) -> List[DockerHostConfig]:
        """获取要插入的Docker Host配置对象列表"""
        insert_objects = []

        try:
            # 确保daemon_config是有效的JSON字符串
            daemon_config = host_data.get('daemon_config', {})
            if isinstance(daemon_config, dict):
                daemon_config = json.dumps(daemon_config)
            elif isinstance(daemon_config, str):
                # 验证是否为有效的JSON字符串
                json.loads(daemon_config)
            else:
                daemon_config = '{}'

            user_privileges = host_data.get('user_privileges', {})

            config = DockerHostConfig(
                node_id=node_id,
                docker_version=host_data.get('docker_version', 'N/A'),
                daemon_config=daemon_config,
                user_in_docker_group=bool(user_privileges.get('docker_group', False)),
                is_root_user=bool(user_privileges.get('root_user', False))
            )
            insert_objects.append(config)
        except Exception as e:
            log_error(f"Error creating host config object: {e}")

        return insert_objects

    @staticmethod
    def get_insert_objects_containers(containers: List[Dict], node_id: int) -> List[DockerContainer]:
        """获取要插入的Docker容器对象列表"""
        insert_objects = []
        for container in containers:
            # 确保所有JSON字段都有有效的默认值
            try:
                mounts = json.dumps(container.get('Mounts', []) or [])
                processes = json.dumps(container.get('processes', {}) or {})
                exposures = json.dumps(container.get('exposed_services', {}) or {})
                env = json.dumps(container.get('Env', []) or [])

                # 处理inspect_data
                inspect_data = json.dumps(container.get('inspect_data', {})) if container.get('inspect_data') else '{}'

                new_container = DockerContainer(
                    node_id=node_id,
                    container_id=container.get('container_id', ''),
                    mounts=mounts,
                    processes=processes,
                    exposures=exposures,
                    env=env,
                    inspect_data=inspect_data
                )
                insert_objects.append(new_container)
            except Exception as e:
                log_error(f"Error creating container object: {e}")
                continue
        return insert_objects

    @staticmethod
    def get_insert_objects_networks(networks: List[Dict], node_id: int) -> List[DockerNetwork]:
        """获取要插入的Docker网络对象列表"""
        insert_objects = []
        for network in networks:
            # 确保布尔值的正确转换
            ipv6 = str(network.get('ipv6', '')).lower() == 'true'
            internal = str(network.get('internal', '')).lower() == 'true'

            # 确保labels是有效的JSON字符串
            labels = network.get('labels', '')
            if isinstance(labels, str) and labels.strip() == '""':
                labels = '{}'
            elif isinstance(labels, dict):
                labels = json.dumps(labels)
            else:
                labels = '{}'

            new_network = DockerNetwork(
                node_id=node_id,
                network_id=network.get('network_id', ''),
                name=network.get('name', ''),
                driver=network.get('driver', ''),
                scope=network.get('scope', ''),
                created_at=network.get('created_at', ''),
                ipv6=ipv6,
                internal=internal,
                labels=labels,
            )
            insert_objects.append(new_network)
        return insert_objects
